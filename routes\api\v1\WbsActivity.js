const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const WbsControl = require('../../../controllers/api/v1/WbsActivity');
const WbsSchema = require('../../../schema-validation/WbsActivity');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(WbsSchema.createOrUpdateWbsActivity),
  ErrorHandleHelper.requestValidator,
  WbsControl.createWbsActivity
);

router.get(
  '',
  checkSchema(WbsSchema.getWbsActivity),
  ErrorHandleHelper.requestValidator,
  WbsControl.getWbsActivity
);

router.put(
  '/:id',
  checkSchema(WbsSchema.createOrUpdateWbsActivity),
  ErrorHandleHelper.requestValidator,
  WbsControl.putWbsActivity
);

router.get('/:id/numbering', WbsControl.getActivitiesByNumbering);

router.post(
  '/:id/comment',
  checkSchema(WbsSchema.addWbsComment),
  ErrorHandleHelper.requestValidator,
  WbsControl.addWbsActivityComment
);

router.delete('/:id/comment/:commentId', WbsControl.deleteWbsActivityComment);

router.post(
  '/:id/document',
  checkSchema(WbsSchema.addWbsDocument),
  ErrorHandleHelper.requestValidator,
  WbsControl.addWbsActivityDocument
);

router.delete(
  '/:id/document/:documentId',
  WbsControl.deleteWbsActivityDocument
);

router.post(
  '/category',
  checkSchema(WbsSchema.createOrUpdateWbsActivityCategory),
  ErrorHandleHelper.requestValidator,
  WbsControl.createWbsActivityCategory
);

router.put(
  '/category/:id',
  checkSchema(WbsSchema.createOrUpdateWbsActivityCategory),
  ErrorHandleHelper.requestValidator,
  WbsControl.putWbsActivityCategory
);

module.exports = router;
