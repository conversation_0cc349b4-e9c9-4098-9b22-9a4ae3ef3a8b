'use strict';

const {
  paymentStageCalculationType,
  paymentTriggerType,
  paymentPlanType,
} = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const QuotationPaymentPlan = sequelize.define(
    'QuotationPaymentPlan',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      planType: {
        type: DataTypes.ENUM(...paymentPlanType.paymentPlanTypeArray()),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      calculationType: {
        type: DataTypes.ENUM(
          ...paymentStageCalculationType.paymentStageCalculationTypeArray()
        ),
        allowNull: true,
      },
      amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
      },
      triggerType: {
        type: DataTypes.ENUM(...paymentTriggerType.paymentTriggerTypeArray()),
        allowNull: true,
      },
      dueOn: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      netPayableAmount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  QuotationPaymentPlan.associate = (models) => {
    QuotationPaymentPlan.belongsTo(models.Quotation, {
      foreignKey: 'quotationId',
      as: 'quotation',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  };

  return QuotationPaymentPlan;
};
