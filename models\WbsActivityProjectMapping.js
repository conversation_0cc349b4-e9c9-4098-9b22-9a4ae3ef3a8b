'use strict';

module.exports = (sequelize, DataTypes) => {
  const WbsActivityProjectMapping = sequelize.define(
    'WbsActivityProjectMapping',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WbsActivityProjectMapping.associate = (models) => {
    WbsActivityProjectMapping.belongsTo(models.WbsActivity, {
      foreignKey: 'wbsActivityId',
      as: 'wbsActivity',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    WbsActivityProjectMapping.belongsTo(models.Project, {
      foreignKey: 'subProjectId',
      as: 'subProject',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivityProjectMapping.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      as: 'unit',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivityProjectMapping.belongsTo(models.Floor, {
      foreignKey: 'floorId',
      as: 'floor',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivityProjectMapping.belongsTo(models.Space, {
      foreignKey: 'spaceId',
      as: 'space',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  };

  return WbsActivityProjectMapping;
};
