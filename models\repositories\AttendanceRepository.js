const { Attendance, User, Address, UserOrganization, Employee } = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { Op } = require('sequelize');
const moment = require('moment-timezone');
const options = require('@config/options');
const {
  formatTime,
  formatTotalDuration,
} = require('@helpers/AttendanceHelper');
const { getDistanceFromLatLonInMeters } = require('@helpers/geolocation');

const LOCAL_TIMEZONE = 'Asia/Kolkata';

exports.checkIn = async (data, loggedInUser) => {
  try {
    const {
      date = new Date().toISOString().split('T')[0],
      inTime,
      latitude,
      longitude,
    } = data;

    const createdBy = loggedInUser.id;

    const existingUserOrganization = await UserOrganization.findOne({
      where: {
        userId: createdBy,
        organizationId: loggedInUser.currentOrganizationId,
      },
    });

    switch (existingUserOrganization?.attendanceMarkType) {
      case options.calculationType.ATTENDANCE_WITH_LOCATION:
        if (!latitude || !longitude) {
          return {
            success: false,
            message: errorMessage.REQUIRED('Latitude & Longitude'),
          };
        }
        break;
      case options.calculationType.ATTENDANCE_WITHOUT_LOCATION:
      case options.calculationType.ATTENDANCE_MARK_MANUALLY:
      case options.calculationType.ATTENDANCE_OPTIONAL:
      case null:
      case undefined:
        break;
      default:
        break;
    }

    const user = await User.findByPk(createdBy, {
      include: {
        model: Address,
        as: 'Address',
      },
    });

    const officeLat = user?.Address?.latitude || null;
    const officeLong = user?.Address?.longitude || null;

    if (
      existingUserOrganization?.attendanceMarkType ===
        options.calculationType.ATTENDANCE_WITH_LOCATION &&
      officeLat !== null &&
      officeLong !== null
    ) {
      const distance = getDistanceFromLatLonInMeters(
        latitude,
        longitude,
        officeLat,
        officeLong
      );

      if (distance > 200) {
        return {
          success: false,
          message: errorMessage.OUT_OF_RANGE('Check-in'),
        };
      }
    }

    const existingAttendance = await Attendance.findOne({
      where: { date, createdBy },
    });

    if (existingAttendance) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('checkedIn'),
      };
    }

    const checkInTime = inTime
      ? moment.utc(inTime).tz(LOCAL_TIMEZONE).format('HH:mm:ss')
      : moment().tz(LOCAL_TIMEZONE).format('HH:mm:ss');

    const attendancePayload = {
      date,
      inTime: checkInTime,
      status: options.attendanceStatus.FULL_DAY,
      createdBy,
    };

    const attendance = await Attendance.create(attendancePayload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('checkedIn'),
      data: { checkInTime: attendance.inTime, latitude, longitude },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkOut = async (data, loggedInUser) => {
  try {
    const { outTime, latitude, longitude } = data;

    const createdBy = loggedInUser.id;

    const existingUserOrganization = await UserOrganization.findOne({
      where: {
        userId: createdBy,
        organizationId: loggedInUser.currentOrganizationId,
      },
    });

    switch (existingUserOrganization?.attendanceMarkType) {
      case options.calculationType.ATTENDANCE_WITH_LOCATION:
        if (!latitude || !longitude) {
          return {
            success: false,
            message: errorMessage.REQUIRED('Latitude & Longitude'),
          };
        }
        break;
      case options.calculationType.ATTENDANCE_WITHOUT_LOCATION:
      case options.calculationType.ATTENDANCE_MARK_MANUALLY:
      case options.calculationType.ATTENDANCE_OPTIONAL:
      case null:
      case undefined:
        break;
      default:
        break;
    }

    const user = await User.findByPk(createdBy, {
      include: {
        model: Address,
        as: 'Address',
      },
    });

    const officeLat = user?.Address?.latitude || null;
    const officeLong = user?.Address?.longitude || null;

    if (
      existingUserOrganization?.attendanceMarkType ===
        options.calculationType.ATTENDANCE_WITH_LOCATION &&
      officeLat !== null &&
      officeLong !== null
    ) {
      const distance = getDistanceFromLatLonInMeters(
        latitude,
        longitude,
        officeLat,
        officeLong
      );

      if (distance > 200) {
        return {
          success: false,
          message: errorMessage.OUT_OF_RANGE('Check-out'),
        };
      }
    }

    const existingAttendance = await Attendance.findOne({
      where: {
        createdBy,
        outTime: null,
      },
      order: [['createdAt', 'DESC']],
    });

    if (!existingAttendance) {
      return {
        success: false,
        message: errorMessage.NO_USER('checkIn'),
      };
    }

    const checkOutTime = outTime
      ? moment.utc(outTime).tz(LOCAL_TIMEZONE).format('HH:mm:ss')
      : moment().tz(LOCAL_TIMEZONE).format('HH:mm:ss');

    await existingAttendance.update({ outTime: checkOutTime });

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('checkedOut'),
      data: { outTime: checkOutTime },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.listAttendance = async (query, loggedInUser) => {
  try {
    const {
      start = 0,
      limit = 10,
      fromDate,
      toDate,
      status,
      inTime,
      outTime,
      totalDuration,
    } = query;
    const createdBy = loggedInUser.id;

    const whereConditions = {
      createdBy,
    };

    if (fromDate) {
      whereConditions.date = {
        ...whereConditions.date,
        [Op.gte]: fromDate,
      };
    }

    if (toDate) {
      whereConditions.date = {
        ...whereConditions.date,
        [Op.lte]: toDate,
      };
    }

    if (status) {
      whereConditions.status = {
        [Op.in]: Array.isArray(status) ? status : [status],
      };
    }

    if (inTime) {
      const formattedInTime = formatTime(inTime);
      whereConditions.inTime = {
        [Op.gte]: formattedInTime,
        [Op.lt]: `${formattedInTime.split(':')[0]}:59:59`,
      };
    }

    if (outTime) {
      const formattedOutTime = formatTime(outTime);
      whereConditions.outTime = {
        [Op.gte]: formattedOutTime,
        [Op.lt]: `${formattedOutTime.split(':')[0]}:59:59`,
      };
    }

    if (totalDuration) {
      const formattedTotalDuration = formatTotalDuration(totalDuration);

      const lowerBound = parseFloat(formattedTotalDuration);

      const upperBound = lowerBound + 0.59;

      whereConditions.totalDuration = {
        [Op.gte]: lowerBound,
        [Op.lt]: upperBound,
      };
    }

    const parsedStart = parseInt(start, 10);
    const parsedLimit = parseInt(limit, 10);

    const { count, rows } = await Attendance.findAndCountAll({
      where: {
        ...whereConditions,
        outTime: { [Op.not]: null },
      },
      limit: parsedLimit,
      offset: parsedStart,
      order: [['date', 'DESC']],
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('attendance'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: parsedStart,
          limit: parsedLimit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.listAllEmployeeAttendance = async (query) => {
  try {
    const {
      start = 0,
      limit = 10,
      fromDate,
      toDate,
      status,
      inTime,
      outTime,
      totalDuration,
      role,
      employeeId,
    } = query;

    if (!employeeId) {
      return res.status(400).json({
        success: false,
        message: 'employeeId is required',
        data: null,
      });
    }

    const whereConditions = {};

    if (fromDate) {
      whereConditions.date = {
        ...whereConditions.date,
        [Op.gte]: fromDate,
      };
    }

    if (toDate) {
      whereConditions.date = {
        ...whereConditions.date,
        [Op.lte]: toDate,
      };
    }

    if (status) {
      whereConditions.status = {
        [Op.in]: Array.isArray(status) ? status : [status],
      };
    }

    if (inTime) {
      const formattedInTime = formatTime(inTime);
      whereConditions.inTime = {
        [Op.gte]: formattedInTime,
        [Op.lt]: `${formattedInTime.split(':')[0]}:59:59`,
      };
    }

    if (outTime) {
      const formattedOutTime = formatTime(outTime);
      whereConditions.outTime = {
        [Op.gte]: formattedOutTime,
        [Op.lt]: `${formattedOutTime.split(':')[0]}:59:59`,
      };
    }

    if (totalDuration) {
      const formattedTotalDuration = formatTotalDuration(totalDuration);

      const lowerBound = parseFloat(formattedTotalDuration);

      const upperBound = lowerBound + 0.59;

      whereConditions.totalDuration = {
        [Op.gte]: lowerBound,
        [Op.lt]: upperBound,
      };
    }

    const employee = await Employee.findOne({
      where: { id: employeeId },
      attributes: ['userId'],
    });

    if (!employee) {
      return res.status(404).json({
        success: false,
        message: 'No employee found with the given employeeId',
        data: null,
      });
    }

    const userFilter = { id: employee.userId };
    if (role) {
      userFilter.role = role;
    }

    const user = await User.findOne({ where: userFilter, attributes: ['id'] });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'No user found for the given employeeId',
        data: null,
      });
    }

    whereConditions.createdBy = user.id;

    const parsedStart = parseInt(start, 10);
    const parsedLimit = parseInt(limit, 10);

    const { count, rows } = await Attendance.findAndCountAll({
      where: {
        ...whereConditions,
        outTime: { [Op.not]: null },
      },
      limit: parsedLimit,
      offset: parsedStart,
      order: [['date', 'DESC']],
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('attendance'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: parsedStart,
          limit: parsedLimit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getAttendanceByDate = async (query, loggedInUser) => {
  try {
    const { date } = query;
    const createdBy = loggedInUser.id;

    if (!date) {
      return {
        success: false,
        message: errorMessage.REQUIRED('date'),
      };
    }

    const attendance = await Attendance.findOne({
      where: {
        createdBy,
        date,
      },
      attributes: ['inTime', 'outTime', 'totalDuration'],
    });

    if (!attendance) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('attendance'),
        data: null,
      };
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('attendance-detail'),
      data: attendance,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateAttendance = async (data, loggedInUser) => {
  try {
    const { attendanceIds, updateData } = data;
    const createdBy = loggedInUser.id;

    if (!attendanceIds || !attendanceIds.length) {
      return {
        success: false,
        message: errorMessage.REQUIRED('attendanceIds'),
      };
    }

    const attendanceRecords = await Attendance.findAll({
      where: {
        id: {
          [Op.in]: attendanceIds,
        },
        createdBy,
      },
    });

    if (!attendanceRecords.length) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('attendanceRecord'),
      };
    }

    for (let record of attendanceRecords) {
      if (updateData.inTime) {
        record.inTime = updateData.inTime;
      }

      if (updateData.outTime) {
        record.outTime = updateData.outTime;
      }

      if (updateData.status) {
        record.status = updateData.status;
      }

      if (updateData.date) {
        record.date = updateData.date;
      }

      if (updateData.description) {
        record.description = updateData.description;
      }

      await record.save();
    }

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('attendanceWithIds'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getAttendanceStatusCount = async (loggedInUser) => {
  try {
    const data = {
      total: 0,
      status: {},
    };

    let statusMap = {};

    let queryOptions = {
      attributes: [
        'status',
        [
          Attendance.sequelize.fn('COUNT', Attendance.sequelize.col('status')),
          'count',
        ],
      ],
      where: { createdBy: loggedInUser.id },
      group: ['status'],
    };

    statusMap = Object.fromEntries(
      options.attendanceStatus
        .getAttendanceStatusArray()
        .map((status) => [status, 0])
    );

    const attendanceStatusCounts = await Attendance.findAll(queryOptions);

    attendanceStatusCounts.forEach((row) => {
      const status = row.dataValues.status;
      const count = parseInt(row.dataValues.count, 10);
      if (statusMap.hasOwnProperty(status)) {
        statusMap[status] = count;
      }
    });

    data.status = statusMap;
    data.total = Object.values(statusMap).reduce(
      (acc, count) => acc + count,
      0
    );

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('attendance-status-count'),
      data,
    };
  } catch (error) {
    throw error;
  }
};

exports.getAttendanceStatusDropdown = async () => {
  try {
    const statusOptions = options.attendanceStatus.getAttendanceStatusArray();

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE(
        'attendance-status-dropdown'
      ),
      data: statusOptions,
    };
  } catch (error) {
    throw error;
  }
};
