paths:
  /crm/quotation:
    post:
      tags:
        - "CRMQuotation"
      summary: "Create a quotation"
      description: "Creates a new quotation with the provided details."
      operationId: "createQuotation"
      parameters: []
      requestBody:
        description: "Quotation creation details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/quotation-creation-update"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Quotation created successfully"
        "400":
          description: "Validation errors or missing required fields"
        "500":
          description: "Internal Server Error"
  /crm/quotation/{id}:
    put:
      tags:
        - "CRMQuotation"
      summary: "Update a quotation"
      description: "Updates an existing quotation with the provided details."
      operationId: "updateQuotation"
      parameters:
        - in: path
          name: id
          required: true
          description: The unique identifier for the quotation
          schema:
            type: string
      requestBody:
        description: "Quotation update details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/quotation-creation-update"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Quotation updated successfully"
        "400":
          description: "Validation errors or missing required fields"
        "500":
          description: "Internal Server Error"
  /crm/quotation/{id}/payment-plan:
    put:
      tags:
        - "CRMQuotation"
      summary: "Create a payment plan for a quotation"
      description: "Creates a new payment plan for an existing quotation with the provided details."
      operationId: "createQuotationPaymentPlan"
      parameters:
        - in: path
          name: id
          required: true
          description: The unique identifier for the quotation
          schema:
            type: string
      requestBody:
        description: "Payment plan creation details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/quotation-payment-plan-creation"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Payment plan created successfully"
        "400":
          description: "Validation errors or missing required fields"
        "500":
          description: "Internal Server Error"
  /crm/quotation/{id}/broker-payment-plan:
    put:
      tags:
        - "CRMQuotation"
      summary: "Create a broker payment plan for a quotation"
      description: "Creates a new broker payment plan for an existing quotation with the provided details."
      operationId: "createBrokerPaymentPlan"
      parameters:
        - in: path
          name: id
          required: true
          description: The unique identifier for the quotation
          schema:
            type: string
      requestBody:
        description: "Broker payment plan creation details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/broker-payment-plan"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Broker payment plan created successfully"
        "400":
          description: "Validation errors or missing required fields"
        "500":
          description: "Internal Server Error"
  /crm/quotation/{id}/convert-booking:
    post:
      tags:
        - "CRMQuotation"
      summary: "Convert booking for a quotation"
      description: "Converts a booking for an existing quotation with the provided details."
      operationId: "convertBooking"
      parameters:
        - in: path
          name: id
          required: true
          description: The unique identifier for the quotation
          schema:
            type: string
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Booking converted successfully"
        "400":
          description: "Validation errors or missing required fields"
        "500":
          description: "Internal Server Error"
components:
  schemas:
    quotation-creation-update:
      type: object
      required:
        - unitId
      properties:
        unitId:
          type: integer
          example: 2
        customerId:
          type: integer
          example: 2
        termsAndCondition:
          type: string
          example: "Please refer to the attached document"
        expireDate:
          type: string
          format: date
          example: "2023-12-31"
        holdPropertyUntilExpiry:
          type: boolean
          example: false
        pricingRevision:
          type: string
          example: "Diwali Offer"
        pricingRevisionTotalAmount:
          type: string
          example: "1000.00"
        unitCosts:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "Service Tax"
              chargeType:
                type: string
                enum:
                  - agreement_value
                  - percentage_of_agreement_value
                  - fixed_amount
                  - super_built_up_area
                  - carpet_area
                  - tax
                example: "tax"
              rate:
                type: string
                example: "18%"
              isTaxable:
                type: boolean
                example: true
              taxRate:
                type: string
                example: "18.00"
              hsnCode:
                type: string
                example: "998313"
              taxAmount:
                type: string
                example: "18.00"
              amount:
                type: string
                example: "118.00"
              description:
                type: string
                example: "Service Tax Description"
    paymentPlans:
      type: array
      items:
        type: object
        properties:
          name:
            type: string
            example: "Quarterly Payment"
          description:
            type: string
            example: "Quarterly payment plan for 3 years"
          calculationType:
            type: string
            enum:
              - percentage_of_agreement_value
              - fixed_amount
              - percentage_of_total_sale_value
            example: "percentage_of_total_sale_value"
          triggerType:
            type: string
            enum:
              - broker_payout_plan
              - project_status
              - project_progress
              - date
              - none_manual
            example: "project_status"
          isTaxable:
            type: boolean
            example: false
          dueOn:
            type: string
            example: "2023-03-31"
          amount:
            type: string
            example: "250000.00"
          netPayableAmount:
            type: string
            example: "250000.00"
    quotation-payment-plan-creation:
      type: object
      properties:
        paymentPlan:
          type: string
          example: "Quarterly Installments"
        homeLoanRequired:
          type: boolean
          example: true
        paymentPlanTotalAmount:
          type: string
          description: The total amount of the payment plan
          example: "1000000.00"
        paymentPlanAgreementAmount:
          type: string
          description: The agreement amount for the payment plan
          example: "900000.00"
        paymentPlanBalance:
          type: string
          description: The balance amount of the payment plan
          example: "100000.00"
        paymentPlans:
          $ref: '#/components/schemas/paymentPlans'
    broker-payment-plan:
      type: object
      properties:
        brokerPaymentPlan:
          type: string
          description: Details about the broker payment plan
          example: "Broker Quarterly Installments"
        totalBrokerageAmount:
          type: float
          description: The total brokerage amount
          example: "50000.00"
        saleAgentId:
          type: integer
          description: The ID of the sale agent
          example: 1
        brokerAdditionTerm:
          type: string
          description: Additional terms for the broker
          example: "Broker agreement signed on XYZ date"
        paymentPlans:
          $ref: '#/components/schemas/paymentPlans'
    # unit-booking:
    #   type: object
    #   required:
    #     - unitId
    #   properties:
    #     unitId:
    #       type: integer
    #       example: 2