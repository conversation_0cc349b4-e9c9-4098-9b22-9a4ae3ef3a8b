module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('WorkOrderBOQMapping', 'boqQuantity');
    await queryInterface.removeColumn('WorkOrderBOQMapping', 'boqUnit');
    await queryInterface.removeColumn('WorkOrderBOQMapping', 'boqPrice');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('WorkOrderBOQMapping', 'boqQuantity', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
    await queryInterface.addColumn('WorkOrderBOQMapping', 'boqUnit', {
      type: Sequelize.STRING(50),
      allowNull: false,
    });
    await queryInterface.addColumn('WorkOrderBOQMapping', 'boqPrice', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
  },
};
