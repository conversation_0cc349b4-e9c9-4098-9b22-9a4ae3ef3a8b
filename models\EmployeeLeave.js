'use strict';
const { leaveType, leaveStatus } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const EmployeeLeave = sequelize.define(
    'EmployeeLeave',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      fromDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      toDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      leaveType: {
        type: DataTypes.ENUM(leaveType.getValues()),
        allowNull: true,
      },
      totalDays: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      status: {
        type: DataTypes.ENUM(leaveStatus.leaveStatusTypeArray()),
        defaultValue: leaveStatus.PENDING,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  EmployeeLeave.associate = function (models) {
    EmployeeLeave.belongsTo(models.EmployeeTemplateItem, {
      foreignKey: 'entityId',
      as: 'employeeTemplateItems',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      allowNull: true,
    });
    EmployeeLeave.belongsTo(models.Employee, {
      foreignKey: 'employeeId',
      as: 'employee',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      allowNull: true,
    });
    EmployeeLeave.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      allowNull: true,
    });
    EmployeeLeave.hasOne(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        requestType: 'leave_request',
      },
      as: 'request',
    });
  };

  return EmployeeLeave;
};
