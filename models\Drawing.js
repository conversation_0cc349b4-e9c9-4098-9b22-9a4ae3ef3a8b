'use strict';
const { drawingType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Drawing = sequelize.define(
    'Drawing',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(drawingType.getDrawingTypeArray()),
        allowNull: true,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Drawing.associate = function (models) {
    Drawing.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Drawing.belongsTo(models.Floor, {
      foreignKey: 'floorId',
      as: 'floor',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Drawing.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      as: 'unit',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Drawing.belongsTo(models.Space, {
      foreignKey: 'spaceId',
      as: 'space',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return Drawing;
};
