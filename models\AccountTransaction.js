'use strict';

const { transactionType, defaultStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const AccountTransaction = sequelize.define(
    'AccountTransaction',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      transactionType: {
        type: DataTypes.ENUM(transactionType.getTransactionTypeArray()),
        allowNull: false,
        defaultValue: transactionType.CREDIT,
      },
      amount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: false,
      },
      depositAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: false,
      },
      withdrawalAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: false,
      },
      balanceAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: defaultStatus.PENDING,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      invoiceId: {
        type: DataTypes.BIGINT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  AccountTransaction.associate = (models) => {
    AccountTransaction.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return AccountTransaction;
};
