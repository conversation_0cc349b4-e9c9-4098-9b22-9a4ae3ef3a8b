'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('Source', ['name']);

    await queryInterface.changeColumn('Source', 'name', {
      type: Sequelize.STRING(100),
      allowNull: true,
      unique: false,
    });

    await queryInterface.addColumn('Source', 'userId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addIndex('Source', ['name'], {
      unique: true,
    });

    await queryInterface.changeColumn('Source', 'name', {
      type: Sequelize.STRING(100),
      allowNull: false,
      unique: true,
    });

    await queryInterface.removeColumn('Source', 'userId');
  },
};
