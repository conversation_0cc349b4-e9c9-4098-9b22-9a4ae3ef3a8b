'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('WbsActivityProjectMapping', 'spaceId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Space',
        key: 'id',
      },
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('WbsActivityProjectMapping', 'spaceId');
  },
};
