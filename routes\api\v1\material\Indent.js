const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const IndentController = require('@controllers/v1/material/Indent');
const IndentSchema = require('@schema-validation/material/Indent');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(IndentSchema.createIndent),
  ErrorHandleHelper.requestValidator,
  IndentController.createIndent
);

router.put(
  '/:id',
  checkSchema(IndentSchema.updateIndent),
  ErrorHandleHelper.requestValidator,
  IndentController.updateIndent
);

router.patch(
  '/:id',
  checkSchema(IndentSchema.updateIndentStatus),
  ErrorHandleHelper.requestValidator,
  IndentController.updateIndentStatus
);

router.delete(
  '/:id',
  checkSchema(IndentSchema.deleteIndent),
  ErrorHandleHelper.requestValidator,
  IndentController.deleteIndent
);

router.patch(
  '/item/:id',
  checkSchema(IndentSchema.updateIndentItemStatus),
  ErrorHandleHelper.requestValidator,
  IndentController.updateIndentItemStatus
);

module.exports = router;
