'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('Request', 'Request_approvedBy_fkey');

    await queryInterface.changeColumn('Request', 'approvedBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('Request', 'Request_approvedBy_fkey');

    await queryInterface.changeColumn('Request', 'approvedBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Employee',
        key: 'id',
      },
      onDelete: 'SET NULL',
    });
  },
};
