const { Op } = require('sequelize');
const { Floor, Unit, Space, Drawing, Project } = require('..');
const {
  successMessage,
  errorMessage,
  drawingType,
  floorType,
  nonPrefixedRegex,
  prefixedRegex,
  prefixedAlphanumericRegex,
  nonPrefixedAlphanumericRegex,
} = require('@config/options');
const DrawingRepository = require('./DrawingRepository');
const { checkExistence } = require('@helpers/QueryHelper');

exports.findOne = async (query) => await Project.findOne(query);

exports.checkAndCreateFloor = async (projectId, data, loggedInUser) => {
  try {
    const project = await checkExistence(Project, { id: projectId });
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Project with Id ${projectId}`),
      };
    }

    if (data.floorType === floorType.UNIT) {
      const unit = await checkExistence(Unit, { id: data.unitId });
      if (!unit) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Unit with Id ${data.unitId}`),
        };
      }
    }

    const existingFloor = await Floor.findOne({
      where: {
        name: {
          [Op.iLike]: data.name,
        },
        projectId: projectId,
      },
      attributes: ['id'],
    });
    if (existingFloor) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(`Floor with name ${data.name}`),
      };
    }

    const existingFloors = await Floor.findAll({
      where: { projectId },
      order: [['orderIndex', 'DESC']],
      limit: 1,
    });
    const orderIndex =
      existingFloors.length > 0 ? existingFloors[0].orderIndex + 1 : 0;

    if (data.isNamingFormat != true) {
      data.namingPrefix = null;
    }

    const payload = {
      ...data,
      projectId,
      createdBy: loggedInUser.id,
      orderIndex,
    };

    const floor = await Floor.create(payload);
    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.FLOOR,
        floor.id,
        loggedInUser,
        project.id
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Floor'),
      data: floor,
    };
  } catch (error) {
    throw error;
  }
};

exports.swapFloorUp = async (floorId) => {
  try {
    const floor = await Floor.findOne({
      where: { id: floorId },
    });
    if (!floor) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Floor with Id ${floorId}`),
      };
    }

    const { projectId } = floor;
    const aboveFloor = await Floor.findOne({
      where: {
        projectId,
        orderIndex: { [Op.gt]: floor.orderIndex },
      },
      order: [['orderIndex', 'DESC']],
    });

    if (!aboveFloor) {
      return {
        success: false,
        message: errorMessage.TOP_FLOOR,
      };
    }

    const tempOrder = floor.orderIndex;
    floor.orderIndex = aboveFloor.orderIndex;
    aboveFloor.orderIndex = tempOrder;

    await floor.save();
    await aboveFloor.save();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE(
        'Floor swapped up successfully'
      ),
      data: floor,
    };
  } catch (e) {
    throw e;
  }
};

exports.swapFloorDown = async (floorId) => {
  try {
    const floor = await Floor.findOne({
      where: { id: floorId },
    });

    if (!floor) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Floor with Id ${floorId}`),
      };
    }

    const { projectId } = floor;
    const belowFloor = await Floor.findOne({
      where: {
        projectId,
        orderIndex: { [Op.lt]: floor.orderIndex },
      },
      order: [['orderIndex', 'DESC']],
    });

    if (!belowFloor) {
      return {
        success: false,
        message: errorMessage.LOWEST_FLOOR,
      };
    }

    const tempOrder = floor.orderIndex;
    floor.orderIndex = belowFloor.orderIndex;
    belowFloor.orderIndex = tempOrder;

    await floor.save();
    await belowFloor.save();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE(
        'Floor swapped down successfully'
      ),
      data: floor,
    };
  } catch (e) {
    throw e;
  }
};

exports.updateFloor = async (floorId, data, loggerInUser) => {
  try {
    const floor = await checkExistence(Floor, { id: floorId });
    if (!floor) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Floor with Id ${floorId}`),
      };
    }

    if (data.name) {
      const existingFloor = await Floor.findOne({
        where: {
          name: {
            [Op.iLike]: data.name,
          },
          projectId: floor.projectId,
          id: {
            [Op.ne]: floorId,
          },
        },
        attributes: ['id'],
      });
      if (existingFloor) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(`Floor with name ${data.name}`),
        };
      }
    }

    if (data.projectId) {
      const project = await checkExistence(Project, { id: data.projectId });
      if (!project) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Project with Id ${data.projectId}`
          ),
        };
      }
    }

    if (data.unitId) {
      const unit = await checkExistence(Project, { id: data.unitId });
      if (!unit) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Unit with Id ${data.unitId}`),
        };
      }
    }

    if (data.isNamingFormat != true) {
      data.namingPrefix = null;
    }

    Object.assign(floor, data);
    await floor.save();

    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.deleteDrawingsByFloorId(floor.id);
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.FLOOR,
        floor.id,
        loggerInUser,
        floor.projectId
      );
    }

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Floor'),
      data: floor,
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteFloor = async (floorId) => {
  try {
    const floor = await Floor.findOne({
      where: { id: floorId },
      include: [
        {
          model: Unit,
          as: 'unitsInFloor',
          attributes: ['id'],
        },
      ],
    });

    if (floor.unitsInFloor.length > 0) {
      const unitIds = floor.unitsInFloor.map((unit) => unit.id);
      await Unit.destroy({ where: { id: unitIds } });
    }

    if (!floor) {
      return {
        success: false,
        message: errorMessage.UNAUTHORIZED_ACCESS,
      };
    }

    await DrawingRepository.deleteDrawingsByFloorId(floor.id);

    await Floor.destroy({ where: { id: floorId } });

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Floor'),
    };
  } catch (error) {
    throw error;
  }
};

exports.createDuplicateFloor = async (floorId, projectId, loggedInUser) => {
  const query = {
    where: {
      id: floorId,
    },
    include: [
      {
        model: Drawing,
        as: 'drawings',
        attributes: {
          exclude: [
            'createdAt',
            'updatedAt',
            'id',
            'unitId',
            'projectId',
            'floorId',
          ],
        },
      },
      {
        model: Space,
        as: 'spaces',
        attributes: {
          exclude: ['createdAt', 'updatedAt', 'id', 'unitId', 'floorId'],
        },
      },
      {
        model: Unit,
        as: 'unitsInFloor',
        attributes: {
          exclude: ['createdAt', 'updatedAt', 'id', 'floorId'],
        },
        include: [
          {
            model: Drawing,
            as: 'drawings',
            attributes: {
              exclude: [
                'createdAt',
                'updatedAt',
                'id',
                'unitId',
                'projectId',
                'floorId',
              ],
            },
          },
          {
            model: Space,
            as: 'spaces',
            attributes: {
              exclude: ['createdAt', 'updatedAt', 'id', 'unitId', 'floorId'],
            },
          },
        ],
      },
    ],
    attributes: {
      exclude: ['createdAt', 'updatedAt', 'id', 'orderIndex'],
    },
  };

  const existingFloors = await Floor.findAll({
    where: { projectId },
    order: [['orderIndex', 'DESC']],
    limit: 1,
  });

  const orderIndex =
    existingFloors.length > 0 ? existingFloors[0].orderIndex + 1 : 0;

  const floor = await Floor.findOne(query);
  if (!floor) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('floor'),
    };
  }
  const payloadFloor = {
    ...(await floor.get({
      plain: true,
    })),
  };

  payloadFloor.orderIndex = orderIndex;
  if (Array.isArray(payloadFloor.spaces)) {
    payloadFloor.spaces.forEach((space) => {
      space.createdBy = loggedInUser.id;
    });
  }

  if (Array.isArray(payloadFloor.unitsInFloor)) {
    payloadFloor.unitsInFloor.forEach(async (unit) => {
      unit.name = this.duplicateFloorWiseUnitNames(unit.name, orderIndex);
      unit.createdBy = loggedInUser.id;
      if (Array.isArray(unit.spaces)) {
        unit.spaces.forEach((space) => {
          space.createdBy = loggedInUser.id;
        });
      }
    });
  }

  const duplicateFloor = await Floor.bulkCreate([payloadFloor], {
    include: [
      {
        model: Drawing,
        as: 'drawings',
      },
      {
        model: Space,
        as: 'spaces',
      },
      {
        model: Unit,
        as: 'unitsInFloor',
        include: [
          {
            model: Drawing,
            as: 'drawings',
          },
          {
            model: Space,
            as: 'spaces',
          },
        ],
      },
    ],
  });

  return {
    success: true,
    message: successMessage.DUPLICATE_ADD_SUCCESS_MESSAGE('Floor'),
    data: duplicateFloor,
  };
};
exports.duplicateFloorWiseUnitNames = (unitName, orderIndex) => {
  switch (true) {
    case prefixedAlphanumericRegex.test(unitName): {
      const [prefix, numberWithSuffix] = unitName.split('-');
      const alphabetPart = numberWithSuffix.match(/[A-Z]$/)[0];
      const newUnitNumber = `${orderIndex}${alphabetPart}`;
      return `${prefix}-${newUnitNumber}`;
    }
    case prefixedRegex.test(unitName): {
      const [prefix, number] = unitName.split('-');
      const newUnitNumber = `${orderIndex}${number.slice(-2)}`;
      return `${prefix}-${newUnitNumber}`;
    }
    case nonPrefixedAlphanumericRegex.test(unitName): {
      const alphabetPart = unitName.match(/[A-Z]$/)[0];
      return `${orderIndex}${alphabetPart}`;
    }
    case nonPrefixedRegex.test(unitName): {
      return `${orderIndex}${unitName.slice(-2)}`;
    }
    default: {
      throw new Error(`Invalid unit name format: ${unitName}`);
    }
  }
};

exports.addDrwaingsToFloor = async (floorId, data, loggedInUser) => {
  try {
    const floor = await checkExistence(Floor, { id: floorId });
    if (!floor) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Floor with Id ${floorId}`),
      };
    }

    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.FLOOR,
        floor.id,
        loggedInUser,
        floor.projectId
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Drawings'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
