'use strict';

const { calculationType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('UserOrganization', 'attendanceMarkType', {
      type: Sequelize.ENUM(...calculationType.getCalculationTypeArray()),
      allowNull: true,
    });

    await queryInterface.addColumn(
      'UserOrganization',
      'unpaidLeaveDeductionType',
      {
        type: Sequelize.ENUM(...calculationType.getCalculationTypeArray()),
        allowNull: true,
      }
    );

    await queryInterface.addColumn(
      'UserOrganization',
      'unpaidLeaveDeductionAmount',
      {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      }
    );

    await queryInterface.addColumn(
      'EmployeeTemplateItem',
      'employeeTemplateId',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'EmployeeTemplate',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      }
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('UserOrganization', 'attendanceMarkType');
    await queryInterface.removeColumn(
      'UserOrganization',
      'unpaidLeaveDeductionType'
    );
    await queryInterface.removeColumn(
      'UserOrganization',
      'unpaidLeaveDeductionAmount'
    );
    await queryInterface.removeColumn(
      'EmployeeTemplateItem',
      'employeeTemplateId'
    );
  },
};
