const { defaultStatus } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const BoqEntry = sequelize.define(
    'BoqEntry',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      length: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
      },
      breadth: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
      },
      height: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: true,
      },
      total: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      progressPercentage: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
        defaultValue: 75.5,
      },
      status: {
        type: DataTypes.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: defaultStatus.UN_ASSIGNED,
      },
      categoryRate: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      cost: {
        type: DataTypes.JSON,
        allowNull: true,
      },
      quantity: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  BoqEntry.associate = (models) => {
    BoqEntry.belongsTo(models.BOQCategory, {
      foreignKey: 'boqCategoryId',
      as: 'category',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    BoqEntry.belongsTo(models.BOQCategory, {
      foreignKey: 'boqSubCategoryId',
      as: 'subCategory',
      onDelete: 'CASCADE',
    });

    BoqEntry.belongsTo(models.BOQMetric, {
      foreignKey: 'boqMetricId',
      as: 'metric',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    BoqEntry.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    BoqEntry.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    BoqEntry.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return BoqEntry;
};
