const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

const ContractorControl = require('../../../controllers/api/v1/Contractor');
const ContractorSchema = require('../../../schema-validation/Contractor');

router.post(
  '',
  checkSchema(ContractorSchema.createContractor),
  ErrorHandleHelper.requestValidator,
  ContractorControl.createContractor
);

router.put(
  '/:id',
  checkSchema(ContractorSchema.updateContractor),
  ErrorHandleHelper.requestValidator,
  ContractorControl.updateContractor
);

module.exports = router;
