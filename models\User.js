const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');
const OPTIONS = require('@config/options');
const jwtOPTIONS = require('@config/jwtOptions');

module.exports = (sequelize, DataTypes) => {
  const User = sequelize.define(
    'User',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      role: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      countryCode: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      mobileNumber: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      email: {
        allowNull: false,
        type: DataTypes.STRING,
      },
      personalEmail: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      firstName: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      middleName: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      lastName: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      password: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      tempOtp: {
        allowNull: true,
        type: DataTypes.INTEGER,
      },
      tempOtpExpiresAt: {
        allowNull: true,
        type: DataTypes.DATE,
      },
      isMobileNumberVerified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isEmailVerified: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      lastSignInAt: {
        allowNull: true,
        type: DataTypes.DATE,
      },
      currentSignInIpAddress: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      registrationPlatform: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      status: {
        allowNull: false,
        type: DataTypes.STRING,
        defaultValue: OPTIONS.defaultStatus.PENDING,
      },
      profilePicture: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return OPTIONS.generateCloudFrontUrl(
            this.getDataValue('profilePicture')
          );
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'profilePicture',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      isFromAdmin: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      designationId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Designation',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      dateOfBirth: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      gender: {
        type: DataTypes.ENUM(OPTIONS.gender.getGenderArray()),
        allowNull: true,
      },
      alternateCountryCode: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      alternateMobileNumber: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      about: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      maritalStatus: {
        type: DataTypes.ENUM(OPTIONS.maritalStatus.getMaritalStatusArray()),
        allowNull: true,
      },
      currentOrganizationId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      panNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      isMfaEnabled: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: true,
      },
      isBusiness: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      businessName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      resetPasswordToken: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      resetPasswordTokenExpiry: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  User.prototype.generateHash = function (password) {
    return bcrypt.hashSync(password, bcrypt.genSaltSync(8));
  };
  User.prototype.validPassword = function (password) {
    return this.password ? bcrypt.compareSync(password, this.password) : false;
  };
  User.prototype.genToken = function () {
    const payload = {
      sub: this.id.toString(),
      'https://hasura.io/jwt/claims': {
        'x-hasura-allowed-roles': [this.role, 'admin'],
        'x-hasura-default-role': 'admin',
        'x-hasura-user-id': this.id.toString(),
      },
    };
    return jwt.sign(payload, jwtOPTIONS.secretOrKey, {
      algorithm: 'HS256',
      expiresIn: jwtOPTIONS.expiry,
    });
  };

  User.associate = (models) => {
    User.hasMany(models.AccessManagement, {
      foreignKey: 'userId',
      as: 'accessManagement',
    });

    User.hasOne(models.Address, {
      foreignKey: 'userId',
      as: 'address',
    });

    User.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation',
    });

    User.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    User.belongsTo(models.Address, {
      foreignKey: 'locationId',
      as: 'Address',
      onDelete: 'SET NULL',
    });

    User.belongsToMany(models.Project, {
      through: 'ProjectTeam',
      foreignKey: 'userId',
      otherKey: 'projectId',
      as: 'projects',
      onDelete: 'CASCADE',
    });

    User.belongsToMany(models.Organization, {
      through: 'UserOrganization',
      foreignKey: 'userId',
      otherKey: 'organizationId',
      as: 'organizations',
      onDelete: 'CASCADE',
    });

    User.belongsTo(models.Organization, {
      foreignKey: 'currentOrganizationId',
      as: 'currentOrganization',
      onDelete: 'SET NULL',
    });

    User.hasOne(models.UserOrganization, {
      foreignKey: 'organizationId',
      sourceKey: 'currentOrganizationId',
      as: 'currentUserOrganization',
      onDelete: 'SET NULL',
    });
  };

  return User;
};
