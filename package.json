{"name": "core_service", "version": "1.0.0", "description": "Backend api service", "main": "app.js", "license": "MIT", "scripts": {"start": "yarn migrate:up && yarn seed && node app", "lint": "eslint config/ controllers/ models/ routes/ schema-validation/ app.js --fix --cache", "prettier": "prettier --config ./.prettierrc \"*/**/*{.js,.json,.html}\" --write", "format": "yarn run prettier && yarn run lint", "migrate:up": "npx sequelize-cli db:migrate", "migrate:undo": "npx sequelize-cli db:migrate:undo", "seed": "npx sequelize-cli db:seed:all", "seed:undo": "npx sequelize-cli db:seed:undo"}, "husky": {"hooks": {"pre-commit": "yarn run format"}}, "dependencies": {"@aws-sdk/client-s3": "^3.637.0", "@aws-sdk/credential-provider-node": "^3.637.0", "@aws-sdk/s3-request-presigner": "^3.637.0", "@elastic/elasticsearch": "^8.15.0", "archiver": "7.0.1", "awesome-phonenumber": "^7.0.1", "axios": "1.7.9", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chalk": "^4.1.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.4.5", "errorhandler": "^1.5.1", "exceljs": "^4.4.0", "express": "^4.19.2", "express-basic-auth": "^1.2.1", "express-session": "^1.18.0", "express-useragent": "^1.0.15", "express-validator": "^7.2.0", "geoip-lite": "1.4.10", "jsonwebtoken": "^9.0.2", "lodash": "4.17.21", "lusca": "^1.7.0", "mime": "^4.0.4", "module-alias": "2.2.3", "moment-timezone": "0.5.47", "morgan": "^1.10.0", "morgan-body": "^2.6.9", "msg91": "2.2.4", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "nanoid": "^5.0.7", "nodemailer": "^6.9.15", "passport": "^0.7.0", "passport-local": "^1.0.0", "pdfmake": "^0.2.12", "pg": "^8.12.0", "pug": "^3.0.3", "sequelize": "^6.37.3", "shortid": "^2.2.16", "swagger-jsdoc": "^6.2.8", "swagger-ui": "^5.17.14", "swagger-ui-express": "^5.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"eslint": "^9.9.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.30.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.5", "prettier": "^3.3.3", "prettier-eslint": "^16.3.0"}, "resolutions": {"chalk": "4.1.2"}, "engines": {"node": ">=20.12.1", "yarn": ">=1.22.22"}, "_moduleAliases": {"@models": "models", "@repo": "models/repositories", "@controllers": "controllers/api", "@config": "config", "@schema-validation": "schema-validation", "@helpers": "models/helpers", "@scripts": "scripts"}}