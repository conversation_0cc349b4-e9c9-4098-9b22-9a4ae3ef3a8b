paths:
  /admin/department:
    post:
      summary: Create a new department
      tags:
        - Department
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createUpdateDepartment"
      produces:
        - application/json
      responses:
        '201':
          description: Department created successfully
        '400':
          description: Invalid Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    get:
      summary: List departments
      description: Fetches a list of all departments within the organization with pagination and search functionality.
      tags:
        - Department
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
        - in: query
          name: limit
          schema:
            type: integer
        - in: query
          name: search
          schema:
            type: string
      responses:
        '200':
          description: Successful response
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /admin/department/{id}:
    put:
      summary: Update an existing department
      tags:
        - Department
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createUpdateDepartment"
      responses:
        '200':
          description: Department updated successfully
        '400':
          description: Invalid Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    get:
      summary: Get a department by ID
      tags:
        - Department
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
      responses:
        "200":
          description: "Fetched department details successfully"
        "400":
          description: "Invalid Request"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"
components:
  schemas:
    createUpdateDepartment:
      type: object
      properties:
        name:
          type: string
          description: The name of the department
        description:
          type: string
          description: The description of the department