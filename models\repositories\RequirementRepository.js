const { Customer, Project, Requirement } = require('..');
const { errorMessage, successMessage } = require('@config/options');

exports.checkExistence = async (model, query) => {
  try {
    return await model.findOne({ where: query });
  } catch (error) {
    throw new Error('Error checking existence');
  }
};

exports.validateAndCreateRequirement = async (data) => {
  const { customerId, subProjectId } = data;
  try {
    const customer = await this.checkExistence(Customer, { id: customerId });
    if (!customer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Customer with Id: ${customerId}`),
      };
    }

    if (data.projectId) {
      const project = await this.checkExistence(Project, {
        id: data.projectId,
      });
      if (!project) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Project with Id: ${data.projectId}`
          ),
        };
      }
    }

    if (subProjectId) {
      const subProject = await this.checkExistence(Project, {
        id: subProjectId,
      });
      if (!subProject) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `SubProject with Id ${subProjectId}`
          ),
        };
      }
    }

    const createdRequirement = await Requirement.create(data);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Requirement'),
      data: createdRequirement,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndUpdateRequirement = async (requirementId, data) => {
  try {
    const requirement = await this.checkExistence(Requirement, {
      id: requirementId,
    });
    if (!requirement) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Requirement with Id: ${requirementId}`
        ),
      };
    }

    if (data.customerId) {
      const customer = await this.checkExistence(Customer, {
        id: data.customerId,
      });
      if (!customer) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Customer with Id: ${data.customerId}`
          ),
        };
      }
    }

    if (data.projectId) {
      const project = await this.checkExistence(Project, {
        id: data.projectId,
      });
      if (!project) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Project with Id: ${data.projectId}`
          ),
        };
      }
    }

    if (data.subProjectId) {
      const subProject = await this.checkExistence(Project, {
        id: data.subProjectId,
      });
      if (!subProject) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `SubProject with Id: ${data.subProjectId}`
          ),
        };
      }
    }

    Object.assign(requirement, data);
    const updatedRequirement = await requirement.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Requirement'),
      data: updatedRequirement,
    };
  } catch (error) {
    throw new Error(error);
  }
};
