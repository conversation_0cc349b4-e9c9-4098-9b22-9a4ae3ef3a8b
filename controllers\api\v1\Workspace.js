const WorkspaceRepository = require('../../../models/repositories/WorkspaceRepository');
const {
  genRes,
  errorMessage,
  resCode,
  successMessage,
} = require('../../../config/options');

exports.getWorkspaces = async (req, res) => {
  try {
    const data = await WorkspaceRepository.findAll();

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.DETAIL_MESSAGE('Workspaces'),
        data,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
