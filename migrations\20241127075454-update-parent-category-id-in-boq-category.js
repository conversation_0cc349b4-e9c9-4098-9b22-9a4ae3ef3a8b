'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('BOQCategory', 'parentCategoryId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'BOQCategory',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('BOQCategory', 'projectId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Project',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('BOQItems', 'projectId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Project',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('BOQItems', 'progressPercentage', {
      type: Sequelize.DECIMAL(5, 2),
      allowNull: false,
      defaultValue: 0.0,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQCategory', 'parentCategoryId');
    await queryInterface.removeColumn('BOQCategory', 'projectId');
    await queryInterface.removeColumn('BOQItems', 'projectId');
    await queryInterface.removeColumn('BOQItems', 'progressPercentage');
  },
};
