const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const SourceController = require('@controllers/v1/crm/Source');
const SourceSchema = require('@schema-validation/Source');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(SourceSchema.addUpdateSource),
  ErrorHandleHelper.requestValidator,
  SourceController.createSource
);

router.put(
  '/:id',
  checkSchema(SourceSchema.addUpdateSource),
  ErrorHandleHelper.requestValidator,
  SourceController.updateSource
);

router.put(
  '/lead/:id',
  checkSchema(SourceSchema.updateLeadSource),
  ErrorHandleHelper.requestValidator,
  SourceController.updateLeadSource
);

module.exports = router;
