'use strict';

const { costType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'SubCategoryCostItem',
      'CostItems_costId_fkey'
    );

    await queryInterface.dropTable('Cost');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.createTable('Cost', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM(costType.getCostTypeArray()),
        allowNull: false,
        defaultValue: costType.FIXED,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addConstraint('SubCategoryCostItem', {
      fields: ['costId'],
      type: 'foreign key',
      name: 'CostItems_costId_fkey',
      references: {
        table: 'Cost',
        field: 'id',
      },
      onDelete: 'CASCADE',
    });
  },
};
