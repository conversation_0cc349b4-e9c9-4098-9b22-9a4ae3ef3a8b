exports.createLeaveSummary = {
  maxAllowed: {
    in: ['body'],
    isInt: {
      errorMessage: 'maxAllowed must be an integer',
    },
    notEmpty: {
      errorMessage: 'maxAllowed is required',
    },
  },
  used: {
    in: ['body'],
    isInt: {
      errorMessage: 'used must be an integer',
    },
    notEmpty: {
      errorMessage: 'used is required',
    },
  },
  leaveType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'leaveType is required',
    },
  },
};

exports.updateLeaveSummary = {
  maxAllowed: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'maxAllowed must be an integer',
    },
  },
  used: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'used must be an integer',
    },
  },
  leaveType: {
    in: ['body'],
    optional: true,
  },
};
