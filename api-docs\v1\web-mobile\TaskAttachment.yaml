paths:
  /task/attachment:
    post:
      summary: Add a Task Attachment
      description: Add a attachment for a existing task
      operationId: addAttachment
      tags:
        - Task
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addAttachment"
      responses:
        "201":
          description: Attachment created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/attachment/{id}:
    delete:
      summary: Delete a Attachment
      description: Delete Attachment if you dont need it
      operationId: deleteAttachment
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of attachment
      responses:
        "201":
          description: Attachment deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    addAttachment:
      type: object
      required:
        - taskId
        - documentId
      properties:
        taskId:
          type: integer
          example: 1
          description: "The id of task for the attachment"
        document:
          type: array
          items:
            type: object
            properties:
              fileType:
                type: string
                example: "pdf"
                description: "Type of the file"
              fileSize:
                type: integer
                example: 1024
                description: "Size of the file in bytes"
              filePath:
                type: string
                example: "/path/to/file"
                description: "Path of the file"
              fileName:
                type: string
                example: "example.pdf"
                description: "Name of the file"
        

  