const {
  taskStatus,
  taskPriority,
  dependencyType,
  dependencyStatus,
} = require('@config/options');

exports.createTaskSchema = {
  title: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Title cannot be empty',
    },
    isString: {
      errorMessage: 'Title must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
  status: {
    in: ['body'],
    isIn: {
      options: [taskStatus.getValues()],
      errorMessage: 'Invalid status value',
    },
    optional: true,
  },
  startDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Start date must be a valid ISO 8601 date',
    },
  },
  endDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'End date must be a valid ISO 8601 date',
    },
  },
  projectId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Project ID must be an integer',
    },
  },
  assignedTo: {
    in: ['body'],
    isInt: {
      errorMessage: 'AssignedTo must be an integer',
    },
    optional: true,
  },
  assignedBy: {
    in: ['body'],
    isInt: {
      errorMessage: 'AssignedBy must be an integer',
    },
    optional: true,
  },
  taskTagsIds: {
    in: ['body'],
    isArray: {
      errorMessage: 'taskTagsId must be an array of integers',
    },
    optional: true,
  },
  'taskTagsIds.*': {
    isInt: {
      errorMessage: 'Each taskTagsIds must be an integer',
    },
  },
  unitIds: {
    in: ['body'],
    isArray: {
      errorMessage: 'unitIds must be an array of integers',
    },
    optional: true,
  },
  'unitIds.*': {
    isInt: {
      errorMessage: 'Each unitId must be an integer',
    },
  },
  dependencies: {
    in: ['body'],
    isArray: {
      errorMessage: 'Dependencies must be an array of objects',
    },
    optional: true,
  },
  'dependencies.*.dependencyType': {
    isIn: {
      options: [dependencyType.getValues()],
      errorMessage: `dependencyType must be either ${dependencyType.getValues()}`,
    },
  },
  'dependencies.*.status': {
    isIn: {
      options: [dependencyStatus.getValues()],
      errorMessage: `status must be one of ${dependencyStatus.getValues()}`,
    },
  },
  'dependencies.*.taskId': {
    isInt: {
      errorMessage: 'taskId in Dependencies must be an integer',
    },
  },
  subTasks: {
    in: ['body'],
    isArray: {
      errorMessage: 'SubTasks must be an array of objects',
    },
    optional: true,
  },
  'subTasks.*.status': {
    isIn: {
      options: [['pending', 'completed']],
      errorMessage: 'SubTask status must be either "pending" or "completed"',
    },
  },
  'subTasks.*.title': {
    isString: {
      errorMessage: 'SubTask title must be a string',
    },
    notEmpty: {
      errorMessage: 'SubTask title cannot be empty',
    },
  },
  'subTasks.*.startDate': {
    isISO8601: {
      errorMessage: 'SubTask startDate must be a valid ISO 8601 date',
    },
    optional: true,
  },
  'subTasks.*.dueDate': {
    isISO8601: {
      errorMessage: 'SubTask dueDate must be a valid ISO 8601 date',
    },
    optional: true,
  },
  followers: {
    in: ['body'],
    isArray: {
      errorMessage: 'Followers must be an array of integers',
    },
    optional: true,
  },
  'followers.*': {
    isInt: {
      errorMessage: 'Each follower must be an integer',
    },
  },
};

exports.updateTaskSchema = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Task Id is required',
    },
    isString: {
      errorMessage: 'Task Id must be a valid string',
    },
  },
  title: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Title cannot be empty',
    },
    isString: {
      errorMessage: 'Title must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
  status: {
    in: ['body'],
    isIn: {
      options: [taskStatus.getValues()],
      errorMessage: 'Invalid status value',
    },
    optional: true,
  },
  priority: {
    in: ['body'],
    isIn: {
      options: [taskPriority.getValues()],
      errorMessage: 'Invalid priority value',
    },
    optional: true,
  },
  startDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Start date must be a valid ISO 8601 date',
    },
  },
  endDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'End date must be a valid ISO 8601 date',
    },
  },
  assignedTo: {
    in: ['body'],
    isInt: {
      errorMessage: 'AssignedTo must be an integer',
    },
    optional: true,
  },
  assignedBy: {
    in: ['body'],
    isInt: {
      errorMessage: 'AssignedBy must be an integer',
    },
    optional: true,
  },
};

exports.updateTaskDetailsSchema = {
  title: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Title must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
  status: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [taskStatus.getValues()],
      errorMessage: 'Invalid status value',
    },
  },
  startDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Start date must be a valid ISO 8601 date',
    },
  },
  endDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'End date must be a valid ISO 8601 date',
    },
  },
  projectId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Project ID must be an integer',
    },
  },
  assignedTo: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'AssignedTo must be an integer',
    },
  },
  assignedBy: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'AssignedBy must be an integer',
    },
  },
  taskTagsIds: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'taskTagsIds must be an array of integers',
    },
  },
  'taskTagsIds.*': {
    isInt: {
      errorMessage: 'Each taskTagId must be an integer',
    },
  },
  unitIds: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'unitIds must be an array of integers',
    },
  },
  'unitIds.*': {
    isInt: {
      errorMessage: 'Each unitId must be an integer',
    },
  },
  dependencies: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Dependencies must be an array of objects',
    },
  },
  'dependencies.*.dependencyType': {
    isIn: {
      options: [dependencyType.getValues()],
      errorMessage: `dependencyType must be one of ${dependencyType.getValues()}`,
    },
  },
  'dependencies.*.status': {
    isIn: {
      options: [dependencyStatus.getValues()],
      errorMessage: `status must be one of ${dependencyStatus.getValues()}`,
    },
  },
  'dependencies.*.taskId': {
    isInt: {
      errorMessage: 'taskId in Dependencies must be an integer',
    },
  },
  subTasks: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'SubTasks must be an array of objects',
    },
  },
  'subTasks.*.status': {
    isIn: {
      options: [['pending', 'completed']],
      errorMessage: 'SubTask status must be either "pending" or "completed"',
    },
  },
  'subTasks.*.title': {
    isString: {
      errorMessage: 'SubTask title must be a string',
    },
    notEmpty: {
      errorMessage: 'SubTask title cannot be empty',
    },
  },
  followers: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Followers must be an array of integers',
    },
  },
  'followers.*': {
    isInt: {
      errorMessage: 'Each follower must be an integer',
    },
  },
};

exports.getTaskDetailsSchema = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Task Id is required',
    },
    isString: {
      errorMessage: 'Task Id must be a valid string',
    },
  },
};

exports.taskListingSchema = {
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
  startDate: {
    in: ['query'],
    optional: true,
    isISO8601: {
      errorMessage: 'Start date must be a valid ISO 8601 date',
    },
  },
  endDate: {
    in: ['query'],
    optional: true,
    isISO8601: {
      errorMessage: 'End date must be a valid ISO 8601 date',
    },
  },
  'taskTags.id': {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'Task Tag ID must be an integer',
    },
  },
  'units.name': {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'Unit name must be a string',
    },
  },
  organizationId: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'Search term must be a string',
    },
  },
};
