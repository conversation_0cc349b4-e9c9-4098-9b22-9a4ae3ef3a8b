'use strict';
const { taskTagType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_TaskTags_type" ADD VALUE IF NOT EXISTS 'priority_tag';
    `);

    await queryInterface.changeColumn('TaskTags', 'type', {
      type: Sequelize.ENUM(...taskTagType.getValues()),
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('TaskTags', 'type', {
      type: Sequelize.ENUM('priority_tag'),
      allowNull: false,
    });
  },
};
