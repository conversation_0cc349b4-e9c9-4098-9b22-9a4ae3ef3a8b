const { Tax, Organization, AccountData, sequelize } = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.validateAndCreateTax = async (organizationId, data, loggedInUser) => {
  const { accountId, ...taxData } = data;
  const { id: createdBy } = loggedInUser;
  const transaction = await sequelize.transaction();
  try {
    const organizationExists = await checkExistence(
      Organization,
      { id: organizationId },
      ['id']
    );
    if (!organizationExists) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(
          `Organization with ID: ${organizationId} does not exist`
        )
      );
    }

    if (accountId) {
      const accountExists = await checkExistence(
        AccountData,
        { id: accountId },
        ['id']
      );
      if (!accountExists) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Account with ID: ${accountId} does not exist`
          )
        );
      }
    }

    const newTaxData = {
      ...taxData,
      organizationId,
      accountId,
      createdBy,
    };

    const createdTax = await Tax.create(newTaxData, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Tax'),
      data: createdTax,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the tax',
    };
  }
};

exports.validateAndUpdateTax = async (taxId, data) => {
  const { accountId } = data;
  const transaction = await sequelize.transaction();
  try {
    const tax = await checkExistence(Tax, { id: taxId }, ['id']);
    if (!tax) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`Tax with ID: ${taxId} does not exist`)
      );
    }

    if (accountId) {
      const accountExists = await checkExistence(
        AccountData,
        { id: accountId },
        ['id']
      );
      if (!accountExists) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Account with ID: ${accountId} does not exist`
          )
        );
      }
    }

    await tax.update(data, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Tax'),
      data: tax,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the tax',
    };
  }
};

exports.validateAndDeleteTax = async (taxId) => {
  const transaction = await sequelize.transaction();
  try {
    const tax = await checkExistence(Tax, { id: taxId }, ['id']);
    if (!tax) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`Tax with ID: ${taxId} does not exist`)
      );
    }

    await Tax.destroy({ where: { id: taxId }, transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Tax'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the tax',
    };
  }
};
