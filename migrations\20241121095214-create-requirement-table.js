'use strict';

const { requirement } = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Requirement', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      purpose: {
        type: Sequelize.ENUM(...requirement.purpose.getPurposeArray()),
        allowNull: true,
      },
      possessionBy: {
        type: Sequelize.ENUM(
          ...requirement.possessionBy.getPossessionByArray()
        ),
        allowNull: true,
      },
      budgetRange: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      fundingType: {
        type: Sequelize.ENUM(...requirement.fundingType.getFundingTypeArray()),
        allowNull: true,
      },
      propertyType: {
        type: Sequelize.ENUM(
          ...requirement.propertyType.getPropertyTypeArray()
        ),
        allowNull: true,
      },
      configuration: {
        type: Sequelize.ENUM(
          ...requirement.configuration.getConfigurationArray()
        ),
        allowNull: true,
      },
      configurationType: {
        type: Sequelize.ENUM(
          ...requirement.configurationType.getConfigurationTypeArray()
        ),
        allowNull: true,
      },
      areaRange: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      locationPreference: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      furnishingType: {
        type: Sequelize.ENUM(
          ...requirement.furnishingType.getFurnishingTypeArray()
        ),
        allowNull: true,
      },
      directionPreference: {
        type: Sequelize.ENUM(
          ...requirement.directionPreference.getDirectionPreferenceArray()
        ),
        allowNull: true,
      },
      otherPreferences: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      customerId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Customer',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Project',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      subProjectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Project',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Requirement');
  },
};
