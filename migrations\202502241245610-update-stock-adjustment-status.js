'use strict';

const { stockAdjustmentStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription =
      await queryInterface.describeTable('StockAdjustment');

    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_StockAdjustment_status_new') THEN
          CREATE TYPE "enum_StockAdjustment_status_new" AS ENUM (${stockAdjustmentStatus
            .getValues()
            .map((status) => `'${status}'`)
            .join(', ')});
        END IF;
      END $$;
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "StockAdjustment" ALTER COLUMN "status" TYPE "enum_StockAdjustment_status_new"
        USING status::text::"enum_StockAdjustment_status_new";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_StockAdjustment_status') THEN
          DROP TYPE "enum_StockAdjustment_status";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_StockAdjustment_status_new" RENAME TO "enum_StockAdjustment_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment" ALTER COLUMN "status" SET DEFAULT '${stockAdjustmentStatus.DRAFT}';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription =
      await queryInterface.describeTable('StockAdjustment');

    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_StockAdjustment_status_old') THEN
          CREATE TYPE "enum_StockAdjustment_status_old" AS ENUM ('draft');
        END IF;
      END $$;
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "StockAdjustment" ALTER COLUMN "status" TYPE "enum_StockAdjustment_status_old"
        USING status::text::"enum_StockAdjustment_status_old";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_StockAdjustment_status') THEN
          DROP TYPE "enum_StockAdjustment_status";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_StockAdjustment_status_old" RENAME TO "enum_StockAdjustment_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment" ALTER COLUMN "status" SET DEFAULT 'draft';
    `);
  },
};
