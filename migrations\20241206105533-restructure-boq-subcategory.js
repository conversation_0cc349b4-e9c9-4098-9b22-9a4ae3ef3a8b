'use strict';

const { defaultStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQSubCategory', 'costItem');

    await queryInterface.removeColumn('BOQSubCategory', 'cost');

    await queryInterface.removeColumn('BOQSubCategory', 'unit');

    await queryInterface.addColumn('BOQSubCategory', 'name', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('BOQSubCategory', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    });

    await queryInterface.addColumn('BOQSubCategory', 'status', {
      type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
      allowNull: false,
      defaultValue: defaultStatus.UN_ASSIGNED,
    });

    await queryInterface.addColumn('BOQSubCategory', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    await queryInterface.addColumn('BOQSubCategory', 'projectId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Project',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQSubCategory', 'name');
    await queryInterface.removeColumn('BOQSubCategory', 'createdBy');
    await queryInterface.removeColumn('BOQSubCategory', 'status');
    await queryInterface.removeColumn('BOQSubCategory', 'organizationId');
    await queryInterface.removeColumn('BOQSubCategory', 'projectId');

    await queryInterface.addColumn('BOQSubCategory', 'costItem', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('BOQSubCategory', 'cost', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
    });

    await queryInterface.addColumn('BOQSubCategory', 'unit', {
      type: Sequelize.STRING(50),
      allowNull: true,
    });

    await queryInterface.addColumn('BOQSubCategory', 'description', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('BOQSubCategory', 'parentCategoryId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'BOQSubCategory',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    });
  },
};
