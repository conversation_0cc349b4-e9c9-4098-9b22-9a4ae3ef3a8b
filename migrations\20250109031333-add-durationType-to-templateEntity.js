'use strict';
const { durationType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('TemplateEntity', 'durationType', {
      type: Sequelize.ENUM(durationType.getDurationTypeArray()),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('TemplateEntity', 'durationType');
    await queryInterface.sequelize.query(
      `DROP TYPE IF EXISTS "enum_TemplateEntity_durationType";`
    );
  },
};
