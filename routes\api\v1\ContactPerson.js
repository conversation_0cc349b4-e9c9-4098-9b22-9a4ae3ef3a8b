const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ContactPersonController = require('@controllers/v1/crm/ContactPerson');
const ContactPersonSchema = require('@schema-validation/ContactPerson');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/customer/:id/contact-person',
  checkSchema(ContactPersonSchema.createContactPerson),
  ErrorHandleHelper.requestValidator,
  ContactPersonController.createContactPerson
);

router.put(
  '/customer/contact-person/:id',
  checkSchema(ContactPersonSchema.updateContactPerson),
  ErrorHandleHelper.requestValidator,
  ContactPersonController.updateContactPerson
);

router.delete(
  '/customer/contact-person/:id',
  checkSchema(ContactPersonSchema.removeContactPerson),
  ErrorHandleHelper.requestValidator,
  ContactPersonController.removeContactPerson
);

module.exports = router;
