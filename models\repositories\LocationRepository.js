const { Op } = require('sequelize');
const { OfficeLocation } = require('..');
const { errorMessage, successMessage } = require('../../config/options');

exports.getLocation = async (query) => await OfficeLocation.findOne(query);

exports.checkDuplicate = async (body, id) => {
  try {
    return await this.getLocation({
      where: {
        officeName: { [Op.iLike]: `${body.officeName}%` },
        ...(id && { id: { [Op.not]: id } }),
      },
    });
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndCreateOfficeLocation = async (body) => {
  try {
    const existingLocationCheck = await this.checkDuplicate(body);
    if (existingLocationCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Office Location with same name'),
      };
    }

    const location = await OfficeLocation.create(body);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('office location'),
      data: location,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getOfficeLocations = async ({ start, limit, search }) => {
  try {
    const whereClause = search
      ? {
          [Op.or]: [
            { officeName: { [Op.iLike]: `%${search}%` } },
            { address: { [Op.iLike]: `%${search}%` } },
          ],
        }
      : {};

    const { count, rows } = await OfficeLocation.findAndCountAll({
      where: whereClause,
      offset: start,
      limit: limit,
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Office Location'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndUpdateOfficeLocation = async (id, body) => {
  try {
    const existingLocation = await this.getLocation({ where: { id } });

    if (!existingLocation) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Office Location'),
      };
    }

    const duplicateNameCheck = await this.checkDuplicate(body, id);

    if (duplicateNameCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Office Location with this name'),
      };
    }

    existingLocation.officeName = body.officeName;
    existingLocation.address = body.address;
    await existingLocation.save();

    return {
      success: true,
      data: existingLocation,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Office Location'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
