const { OrganizationBrandMedia } = require('..');

exports.createMedia = async (data, organizationBrandId) => {
  try {
    const mediaData = data.map((media) => ({
      ...media,
      organizationBrandId: organizationBrandId,
    }));
    const createdMedia = await OrganizationBrandMedia.bulkCreate(mediaData);
    return createdMedia.map((media) => media.id);
  } catch (error) {
    throw new Error(error);
  }
};
