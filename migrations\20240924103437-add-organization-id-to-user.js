'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('User', 'organizationId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onDelete: 'SET NULL',
      allowNull: true,
    });

    await queryInterface.removeColumn('Organization', 'userId');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('User', 'organizationId');

    await queryInterface.addColumn('Organization', 'userId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'User',
        key: 'id',
      },
      onDelete: 'CASCADE',
      allowNull: false,
    });
  },
};
