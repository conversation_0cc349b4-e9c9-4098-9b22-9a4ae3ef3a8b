const { OrganizationBrand } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const OrganizationBrandMediaRepository = require('./OrganizationBrandMediaRepository');
exports.findOne = async (query) => await OrganizationBrand.findOne(query);

exports.createBrands = async (data) => {
  const query = {
    where: {
      name: data.name,
    },
  };
  const existBrand = await OrganizationBrand.findOne(query);

  if (existBrand) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Brand'),
    };
  }

  const brand = await OrganizationBrand.create(data);
  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Brand'),
    data: brand,
  };
};

exports.checkAndCreateOrgBrandWithMedia = async (data, organizationId) => {
  try {
    const payload = {
      name: data.name,
      organizationId: organizationId,
    };
    const { success, message, data: brand } = await this.createBrands(payload);

    if (!success) {
      return {
        success,
        message,
      };
    }

    if (data.organizationBrandMedia.length > 0) {
      await OrganizationBrandMediaRepository.createMedia(
        data.organizationBrandMedia,
        brand.id
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Brand'),
      data,
    };
  } catch (error) {
    throw new Error(error);
  }
};
