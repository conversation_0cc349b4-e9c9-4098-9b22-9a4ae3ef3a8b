const _ = require('lodash');

exports.createAccount = {
  organizationId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
  },
  accountCategory: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Account category cannot be empty',
    },
    isString: {
      errorMessage: 'Account category must be a string',
    },
  },
  accountType: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Account type cannot be empty',
    },
  },
  code: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Accoutn code cannot be empty',
    },
    isString: {
      errorMessage: 'Accoutn code must be a string',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  bankAccountNumber: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        const {
          body: { accountType },
        } = req;

        if (accountType === 'BANK' && _.isEmpty(value)) {
          throw new Error(
            'Bank account number is required when account type is BANK'
          );
        }
        return true;
      },
    },
  },
  ifscCode: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        const {
          body: { accountType },
        } = req;

        if (accountType === 'BANK' && _.isEmpty(value)) {
          throw new Error('IFSC Code is required when account type is BANK');
        }
        return true;
      },
    },
  },
  totalCredit: {
    in: ['body'],
    trim: true,
    isInt: {
      errorMessage: 'Total Credit must be a integer',
    },
  },
  totalDebit: {
    in: ['body'],
    trim: true,
    isInt: {
      errorMessage: 'Total Debit must be a integer',
    },
  },
};

exports.updateAccount = {
  accountCategory: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Account category cannot be empty',
    },
    isString: {
      errorMessage: 'Account category must be a string',
    },
  },
  accountType: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Account type cannot be empty',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  bankAccountNumber: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        const {
          body: { accountType },
        } = req;

        if (accountType === 'BANK' && _.isEmpty(value)) {
          throw new Error(
            'Bank account number is required when account type is BANK'
          );
        }
        return true;
      },
    },
  },
  ifscCode: {
    in: ['body'],
    trim: true,
    custom: {
      options: (value, { req }) => {
        const {
          body: { accountType },
        } = req;

        if (accountType === 'BANK' && _.isEmpty(value)) {
          throw new Error('IFSC Code is required when account type is BANK');
        }
        return true;
      },
    },
  },
  accountId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'accountId must be an integer',
    },
  },
};

exports.getAccount = {
  organizationId: {
    in: ['query'],
    optional: false,
    isInt: {
      errorMessage: 'organizationId ID must be an integer',
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'search must be a string',
    },
  },
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
};

exports.createDefaultAccount = {
  organizationId: {
    in: ['query'],
    optional: false,
    isInt: {
      errorMessage: 'organizationId ID must be an integer',
    },
  },
};

exports.deleteAccount = {
  accountId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'accountId must be an integer',
    },
  },
};

exports.getAccountById = {
  accountId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'accountId must be an integer',
    },
  },
};

exports.getTransactionEntries = {
  accountId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'accountId must be an integer',
    },
  },
};
