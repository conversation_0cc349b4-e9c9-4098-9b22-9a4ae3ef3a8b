const { priceChargeCalculationType } = require('../config/options');
module.exports = (sequelize, DataTypes) => {
  const PricingCharge = sequelize.define(
    'PricingCharge',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      chargeType: {
        type: DataTypes.ENUM(
          ...priceChargeCalculationType.priceChargeCalculationTypeArray()
        ),
        allowNull: false,
      },
      rate: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      isTaxable: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
      taxRate: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      hsnCode: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  PricingCharge.associate = (models) => {
    PricingCharge.belongsTo(models.PricingRevision, {
      foreignKey: 'pricingRevisionId',
      as: 'pricingRevision',
    });

    PricingCharge.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });
  };

  return PricingCharge;
};
