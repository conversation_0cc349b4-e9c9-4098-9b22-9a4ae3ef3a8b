exports.createOrganization = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Organization name cannot be empty',
    isString: {
      errorMessage: 'Organization name must be a string',
    },
  },
  gstinNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'GSTIN Number must be a string',
    },
  },
  gstinDocument: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'GSTIN Document must be a string',
    },
  },
  address: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Address cannot be empty',
    isString: {
      errorMessage: 'Address must be a string',
    },
  },
  city: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'City cannot be empty',
    isString: {
      errorMessage: 'City must be a string',
    },
  },
  pincode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Pincode cannot be empty',
    isString: {
      errorMessage: 'Pincode must be a string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'Pincode must be 6 characters long',
    },
  },
};

exports.getBankDetails = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Organization ID is required',
    },
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Organization ID must be a positive number',
    },
  },
};

exports.getOrganizationDetails = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Organization ID is required',
    },
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Organization ID must be a positive number',
    },
  },
};

exports.updateBasicInfo = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Organization ID is required',
    },
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Organization ID must be a positive number',
    },
  },
  name: {
    in: ['body'],
    optional: true,
  },
  type: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  mobile: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  website: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  address: {
    in: ['body'],
    optional: true,
  },
  panNumber: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  gstinNumber: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  tanNumber: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  country: {
    in: ['body'],
    optional: true,
  },
  about: {
    in: ['body'],
    optional: true,
  },
};

exports.updateLocalisation = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Organization ID is required',
    },
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Organization ID must be a positive number',
    },
  },
  dateFormat: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  measurementUnits: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  timeFormat: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  timeZone: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  numberFormat: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  currency: {
    in: ['body'],
    optional: true,
    trim: true,
  },
};

exports.deleteBankDetails = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Bank Details ID is required',
    },
    isInt: {
      errorMessage: 'Bank Details ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Bank Details ID must be a positive number',
    },
  },
};

exports.createBankDetails = {
  accountName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Account name cannot be empty',
    isString: {
      errorMessage: 'Account name must be a string',
    },
  },
  accountNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Account number cannot be empty',
    isString: {
      errorMessage: 'Account number must be a string',
    },
  },
  ifscCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'IFSC code cannot be empty',
    isString: {
      errorMessage: 'IFSC code must be a string',
    },
    isLength: {
      options: { min: 11, max: 11 },
      errorMessage: 'IFSC code must be 11 characters long',
    },
  },
  bankName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Bank name cannot be empty',
    isString: {
      errorMessage: 'Bank name must be a string',
    },
  },
  organizationId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
    toInt: true,
  },
};

exports.createCompanyTax = {
  id: {
    in: ['params'],
    notEmpty: true,
    isInt: {
      errorMessage: 'Organization ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Organization ID must be a positive number',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Tax name cannot be empty',
    isString: {
      errorMessage: 'Tax name must be a string',
    },
  },
  calculation: {
    in: ['body'],
    optional: true,
    trim: true,
  },
  accountId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Account ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Account ID must be a positive number',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
};

exports.updateCompanyTax = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Tax ID is required',
    },
    isInt: {
      errorMessage: 'Tax ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Tax ID must be a positive number',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Tax name must be a string',
    },
  },
  calculation: {
    in: ['body'],
    optional: true,
  },
  accountId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Account ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Account ID must be a positive number',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
};

exports.deleteCompanyTax = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Tax ID is required',
    },
    isInt: {
      errorMessage: 'Tax ID must be an integer',
    },
    toInt: true,
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Tax ID must be a positive number',
    },
  },
};
