paths:
  /settings/administration-document:
    post:
      tags:
        - "Settings"
      summary: "Create a new administration document"
      description: "This endpoint allows you to create a new administration document."
      operationId: "CreateAdministrationDocument"
      requestBody:
        description: "The details of the administration document to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/AdministrationDocumentsRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Administration document has been successfully created."
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error"

  /settings/administration-document/{roleId}:
    put:
      tags:
        - "Settings"
      summary: "Update an existing administration document for a specific role"
      description: "This endpoint allows you to update an existing administration document for the specified role ID."
      operationId: "UpdateAdministrationDocument"
      parameters:
        - name: "roleId"
          in: "path"
          description: "The ID of the role for which the administration document is being updated."
          required: true
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The details of the administration document to be updated."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/AdministrationDocumentUpdateRequest"
        required: true
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Administration document has been successfully updated."
        "400":
          description: "Invalid input data or document not found."
        "404":
          description: "Document not found for the specified role."
        "500":
          description: "Internal Server Error"

  /settings/administration-document/{id}:
    delete:
      tags:
        - "Settings"
      summary: "Delete an existing administration document"
      description: "This endpoint allows you to delete an existing administration document."
      operationId: "DeleteAdministrationDocument"
      parameters:
        - $ref: "#/components/schemas/AdministrationDocumentIdParam"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Administration document has been successfully deleted."
        "400":
          description: "Invalid input data or document not found."
        "404":
          description: "Document not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    AdministrationDocumentsRequest:
      type: object
      properties:
        roleId:
          type: integer
          example: 2
        documents:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "Aadhar card"
              isRequiredField:
                type: boolean
                example: true
              isUploadRequired:
                type: boolean
                example: false
      required:
        - roleId
        - documents

    AdministrationDocumentUpdateRequest:
      type: object
      properties:
        documents:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "Aadhar card"
              isRequiredField:
                type: boolean
                example: true
              isUploadRequired:
                type: boolean
                example: false

    AdministrationDocumentIdParam:
      name: id
      in: path
      required: true
      description: "The ID of the administration document to be updated or deleted."
      schema:
        type: integer
        example: 1
