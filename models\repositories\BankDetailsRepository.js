const sequelize = require('sequelize');
const { BankDetail, Organization } = require('..');
const {
  successMessage,
  errorMessage,
  activityType,
} = require('@config/options');
const { maskedAccountNumber } = require('@models/helpers/UtilHelper');

const aesKey = process.env.PG_AES_KEY;
exports.findAndCountAll = async (query) =>
  await Employee.findAndCountAll(query);

exports.findOne = async (query) => await BankDetail.findOne(query);
exports.findAll = async (query) => await BankDetail.findAll(query);

exports.createBankDetails = async (
  data,
  userId = null,
  loggedInUserId = null,
  transaction = null
) => {
  try {
    const {
      accountName,
      accountNumber,
      ifscCode,
      bankName,
      organizationId = null,
    } = data;

    let organization = null;
    if (organizationId) {
      organization = await Organization.findByPk(organizationId);
      if (!organization) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Organization with id: ${organizationId}`
          ),
        };
      }
    }

    const payload = {
      accountName,
      accountNumber: sequelize.fn('PGP_SYM_ENCRYPT', accountNumber, aesKey),
      ifscCode,
      bankName,
      userId,
      organizationId,
      createdBy: loggedInUserId,
    };

    if (transaction) {
      await BankDetail.create(payload, { transaction });
    } else {
      await BankDetail.create(payload);
    }

    if (organization) {
      await organization.createActivity({
        actionType: activityType.EDITED,
        activityDescription: `Added bank account ${maskedAccountNumber(accountNumber)}`,
        createdBy: loggedInUserId,
      });
    }
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Bank Details'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateBankDetails = async (bankDetails, userId, loggedInUserId) => {
  try {
    const { accountName, accountNumber, ifscCode, bankName } =
      bankDetails || {};

    const existingBankDetails = await BankDetail.findOne({ where: { userId } });

    const payload = {
      accountName: accountName || existingBankDetails?.accountName || null,
      accountNumber: accountNumber
        ? sequelize.fn('PGP_SYM_ENCRYPT', accountNumber, aesKey)
        : existingBankDetails?.accountNumber || null,
      ifscCode: ifscCode || existingBankDetails?.ifscCode,
      bankName: bankName || existingBankDetails?.bankName,
      userId,
      createdBy: existingBankDetails?.createdBy || loggedInUserId,
      updatedBy: loggedInUserId,
    };

    if (existingBankDetails) {
      Object.assign(existingBankDetails, payload);
      existingBankDetails.save();
      return {
        success: true,
        message: successMessage.UPDATE_SUCCESS_MESSAGE('Bank Details'),
      };
    }
    if (!existingBankDetails) {
      await BankDetail.create(payload);
      return {
        success: true,
        message: successMessage.ADD_SUCCESS_MESSAGE('Bank Details'),
      };
    }
    // if (existingBankDetails && !data.bankDetails) {
    //   await BankDetail.destroy({ where: { userId } });
    //   return {
    //     success: true,
    //     message: successMessage.DELETE_SUCCESS_MESSAGE('Bank Details'),
    //   };
    // }
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Bank Details'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getBankDetails = async (id) => {
  try {
    const query = {
      where: {
        userId: id,
      },
      attributes: [
        'id',
        'accountName',
        'bankName',
        'ifscCode',
        [
          sequelize.fn(
            'PGP_SYM_DECRYPT',
            sequelize.cast(sequelize.col('accountNumber'), 'bytea'),
            aesKey
          ),
          'accountNumber',
        ],
        'createdAt',
        'updatedAt',
      ],
    };
    return await BankDetail.findOne(query);
  } catch (error) {
    throw new Error(error);
  }
};

exports.getOrganizationBankDetails = async (id) => {
  try {
    const query = {
      where: {
        organizationId: id,
      },
      attributes: [
        'id',
        'accountName',
        'bankName',
        'ifscCode',
        [
          sequelize.fn(
            'PGP_SYM_DECRYPT',
            sequelize.cast(sequelize.col('accountNumber'), 'bytea'),
            aesKey
          ),
          'accountNumber',
        ],
        'createdAt',
        'updatedAt',
      ],
    };
    const data = await BankDetail.findAndCountAll(query);
    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE(
        `Organization Bank Details`
      ),
      data,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteOrganizationBankDetails = async (id) => {
  try {
    const bankDetail = await BankDetail.findOne({ where: { id } });

    if (!bankDetail) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Bank detail with ID: ${id}`),
      };
    }

    bankDetail.status = 'deleted';
    await bankDetail.save();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE(
        `Organization Bank Details`
      ),
    };
  } catch (error) {
    throw new Error(error);
  }
};
