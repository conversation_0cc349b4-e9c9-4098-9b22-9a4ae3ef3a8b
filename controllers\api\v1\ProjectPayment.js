const PricingRevisionRepository = require('@models/repositories/PricingRevisionRepository');
const PaymentPlanRepository = require('@models/repositories/PaymentPlanRepository');
const CustomerStatusRepository = require('@models/repositories/CustomerStatusRepository');
const {
  genRes,
  errorMessage,
  resCode,
  paymentPlanType,
} = require('@config/options');

exports.createPaymentRevision = async (req, res) => {
  const { id } = req.params;
  try {
    const { success, message, data } =
      await PricingRevisionRepository.createPaymentRevision(
        id,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putPaymentRevision = async (req, res) => {
  const { id, paymentRevisionId } = req.params;
  try {
    const { success, message, data } =
      await PricingRevisionRepository.checkAndUpdatePaymentRevision(
        id,
        paymentRevisionId,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createPaymentPlan = async (req, res) => {
  const { id } = req.params;
  try {
    const { success, message, data } =
      await PaymentPlanRepository.createPaymentPlan(id, req.body, req.user);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putPaymentPlan = async (req, res) => {
  const { id, paymentPlanId } = req.params;
  try {
    const { success, message, data } =
      await PaymentPlanRepository.checkAndUpdatePaymentPlan(
        id,
        paymentPlanId,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createBrokerPayoutPlan = async (req, res) => {
  const { id } = req.params;
  try {
    const { success, message, data } =
      await PaymentPlanRepository.createPaymentPlan(
        id,
        req.body,
        req.user,
        paymentPlanType.BROKER_PAYOUT_PLAN
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putBrokerPayoutPlan = async (req, res) => {
  const { id, brokerPayoutPlanId } = req.params;
  try {
    const { success, message, data } =
      await PaymentPlanRepository.checkAndUpdatePaymentPlan(
        id,
        brokerPayoutPlanId,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createCustomerStatus = async (req, res) => {
  const { id } = req.params;
  try {
    const { success, message, data } =
      await CustomerStatusRepository.createCustomerStatus(
        id,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
