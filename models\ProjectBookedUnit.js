const { defaultStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const ProjectBookedUnit = sequelize.define(
    'ProjectBookedUnit',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(...defaultStatus.getDefaultStatusArray()),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ProjectBookedUnit.associate = (models) => {
    ProjectBookedUnit.belongsTo(models.Quotation, {
      foreignKey: 'quotationId',
      as: 'quotation',
    });

    ProjectBookedUnit.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      as: 'unit',
    });

    ProjectBookedUnit.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      allowNull: true,
    });

    ProjectBookedUnit.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      allowNull: true,
    });
  };

  return ProjectBookedUnit;
};
