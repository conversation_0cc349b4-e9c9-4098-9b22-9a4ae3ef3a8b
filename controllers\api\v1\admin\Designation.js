const DesignationRepository = require('../../../../models/repositories/DesignationRepository.js');
const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
  successMessage,
} = require('../../../../config/options');

exports.createDesignation = async (req, res) => {
  try {
    const { success, message, data } =
      await DesignationRepository.checkAndCreateDesignation(req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }

    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message, data }));
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getDesignations = async (req, res) => {
  try {
    const { start = 0, limit = 10, search = '' } = req.query;

    const { message, data } = await DesignationRepository.getDesignations({
      start,
      limit,
      search,
    });

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.updateDesignation = async (req, res) => {
  try {
    const { success, message, data } =
      await DesignationRepository.checkAndUpdateDesignation(
        req.params.id,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getDesignationById = async (req, res) => {
  try {
    const designation =
      await DesignationRepository.getDesignationWithDepartment(req.params.id);
    if (!designation) {
      return res
        .status(resCode.HTTP_NOT_FOUND)
        .json(
          genRes(
            resCode.HTTP_NOT_FOUND,
            errorMessage.DOES_NOT_EXIST('designation'),
            errorTypes.ENTITY_NOT_FOUND
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.DETAIL_MESSAGE('designation'),
        data: designation,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
