const express = require('express');
const router = express.Router();
const AuthHandler = require('../../../models/helpers/AuthHelper');
const authHandler = AuthHandler.authenticateJWT();

// /**
//  * APIs routes.
//  */

const User = require('./User');
const AuthRouter = require('./Auth');
const SharedRouter = require('./Shared');
const AdminRouter = require('./admin/index');
const WorkspaceRouter = require('./Workspace');
const OrganizationBrandRouter = require('./OrganizationBrand');
const DocumentRouter = require('./Document');
const EmployeeRouter = require('./Employee');
const HasuraRouter = require('./Hasura');
const ProjectRouter = require('./Project');
const BoqRouter = require('./Boq');
const SpaceRouter = require('./Space');
const WorkOrderRouter = require('./WorkOrder');
const ChargeTypeRouter = require('./ChargeType');
const UnitTypeRouter = require('./UnitType');
const UnitRouter = require('./Unit');
const AmenityRouter = require('./Amenity');
const CustomerRouter = require('./Customer');
const RequirementRouter = require('./Requirement');
const TeamAssignmentRouter = require('./TeamAssignment');
const SourceRouter = require('./Source');
const FloorRouter = require('./Floor');
const WorkOrderBoqMapping = require('./WorkOrderBoqMapping');
const ContactPersonRouter = require('./ContactPerson');
const NotesRouter = require('./Notes');
const ProjectTeamRouter = require('./ProjectTeam');
const ProjectPaymentRouter = require('./ProjectPayment');
const ContractorRouter = require('./Contractor');
const DepartmentRouter = require('./settings/Department');
const CompanyRouter = require('./settings/Company');
const QuotationRouter = require('./Quotation');
const ProfileRouter = require('./settings/Profile');
const RoleRouter = require('./settings/Role');
const SecurityRouter = require('./settings/Security');
const ModuleRouter = require('./settings/Module');
const ApprovalWorkflow = require('./settings/ApprovalWorkflow');
const ModulePermissionRouter = require('./settings/ModulePermission');
const AdminDocRouter = require('./settings/AdministrationDocument');
const TemplateRouter = require('./settings/Template');
const UserManagementRouter = require('./settings/UserManagement');
const TaskRouter = require('./task/Task');
const TaskTagRouter = require('./task/TaskTag');
const FollowersRouter = require('./task/TaskFollowers');
const TaskAttachmentRouter = require('./task/TaskDocument');
const SubTaskRouter = require('./task/SubTask');
const TaskCommentsRouter = require('./task/TaskComments');
const TaskDependencyRouter = require('./task/TaskDependency');
const TaskTagMappingRouter = require('./task/TaskTagMapping');
const TaskUnitMappingRouter = require('./task/TaskUnitMapping');
const AccountRouter = require('./accounts/Account');
const JournalRouter = require('./accounts/Journal');
const BudgetRouter = require('./accounts/Budget');
const ExpenseRouter = require('./accounts/Expense');
const WbsActivityRouter = require('./WbsActivity');
const WarehouseRouter = require('./material/Warehouse');
const RequestRouter = require('./request/Request');
const RequestDocumentRouter = require('./request/RequestDocument');
const RequestCommentsRouter = require('./request/RequestComment');
const ItemCategoryRouter = require('./material/ItemCategory');
const ItemRouter = require('./material/Item');
const IndentRouter = require('./material/Indent');
const StockAdjustmentRouter = require('./material/StockAdjustment');
const ActivityLogRouter = require('./ActivityLog');
const AttendanceRouter = require('./Attendance');
const LeaveSummary = require('./LeaveSummary');
const PurchaseOrderRouter = require('./material/PurchaseOrder');
const GrnRouter = require('./material/Grn');
const OrganizationRouter = require('./organization/Organization');
const CatalogueRouter = require('./brand/Catalogue');

router.use('/user', User);
router.use('/auth', AuthRouter);
router.use('/shared', SharedRouter);
router.use('/admin', AdminRouter);
router.use('/workspace', WorkspaceRouter);
router.use('/organization', OrganizationRouter);
router.use('/organization-brand', authHandler, OrganizationBrandRouter);
router.use('/document', authHandler, DocumentRouter);
router.use('/employee', authHandler, EmployeeRouter);
router.use('/contractor', authHandler, ContractorRouter);
router.use('/wbs', authHandler, WbsActivityRouter);
router.use('/activitylog', authHandler, ActivityLogRouter);
router.use('/attendance', authHandler, AttendanceRouter);
router.use('/user-leave', authHandler, LeaveSummary);

const setupProjectRoutes = (mainRouter) => {
  const projectRouter = express.Router();
  projectRouter.use('/', authHandler, ProjectRouter);

  const nestedRoutes = [
    { path: '', auth: authHandler, router: BoqRouter },
    { path: '', auth: authHandler, router: SpaceRouter },
    { path: '', auth: authHandler, router: WorkOrderRouter },
    { path: '', auth: authHandler, router: ChargeTypeRouter },
    { path: '/unit-type', auth: authHandler, router: UnitTypeRouter },
    { path: '', auth: authHandler, router: UnitRouter },
    { path: '/amenity', auth: authHandler, router: AmenityRouter },
    { path: '', auth: authHandler, router: FloorRouter },
    { path: '', auth: authHandler, router: WorkOrderBoqMapping },
    { path: '', auth: authHandler, router: ProjectTeamRouter },
    { path: '', auth: authHandler, router: ProjectPaymentRouter },
  ];

  nestedRoutes.forEach(({ path, auth, router }) => {
    projectRouter.use(path, auth, router);
  });

  mainRouter.use('/project', authHandler, projectRouter);
};
setupProjectRoutes(router);

const setupCrmRoutes = (mainRouter) => {
  const crmRouter = express.Router();
  crmRouter.use('/', authHandler);

  const crmRoutes = [
    { path: '/customer', router: CustomerRouter },
    { path: '/requirement', router: RequirementRouter },
    { path: '/team-assignment', router: TeamAssignmentRouter },
    { path: '/source', router: SourceRouter },
    { path: '', router: ContactPersonRouter },
    { path: '/notes', router: NotesRouter },
    { path: '/quotation', router: QuotationRouter },
  ];

  crmRoutes.forEach(({ path, router }) => {
    crmRouter.use(path, authHandler, router);
  });
  mainRouter.use('/crm', crmRouter);
};
setupCrmRoutes(router);

const setupSettingsRoutes = (mainRouter) => {
  mainRouter.use('/settings/invite-user', UserManagementRouter);

  const settingsRouter = express.Router();
  settingsRouter.use('/department', DepartmentRouter);
  settingsRouter.use('', ProfileRouter);
  settingsRouter.use('/role', RoleRouter);
  settingsRouter.use('', SecurityRouter);
  settingsRouter.use('/module', ModuleRouter);
  settingsRouter.use('', ApprovalWorkflow);
  settingsRouter.use('/module-permission', ModulePermissionRouter);
  settingsRouter.use('/administration-document', AdminDocRouter);
  settingsRouter.use('/company', CompanyRouter);
  settingsRouter.use('', TemplateRouter);
  mainRouter.use('/settings', authHandler, settingsRouter);
};
setupSettingsRoutes(router);

const setupTaskRoutes = (mainRouter) => {
  const taskRouter = express.Router();
  taskRouter.use('', TaskRouter);
  taskRouter.use('/tag', TaskTagRouter);
  taskRouter.use('', FollowersRouter);
  taskRouter.use('', TaskAttachmentRouter);
  taskRouter.use('', SubTaskRouter);
  taskRouter.use('/comments', TaskCommentsRouter);
  taskRouter.use('', TaskDependencyRouter);
  taskRouter.use('', TaskTagMappingRouter);
  taskRouter.use('', TaskUnitMappingRouter);
  mainRouter.use('/task', authHandler, taskRouter);
};
setupTaskRoutes(router);

const setupMaterialRoutes = (mainRouter) => {
  const router = express.Router();
  router.use('/warehouse', WarehouseRouter);
  router.use('/item-category', ItemCategoryRouter);
  router.use('/item', ItemRouter);
  router.use('/indent', IndentRouter);
  router.use('/stock-adjustment', StockAdjustmentRouter);
  router.use('/purchase-order', PurchaseOrderRouter);
  router.use('/grn', GrnRouter);
  mainRouter.use('/material', authHandler, router);
};
setupMaterialRoutes(router);

const setupAccountRountes = (mainRouter) => {
  const accountRouter = express.Router();

  accountRouter.use('/journal', JournalRouter);
  accountRouter.use('/budget', BudgetRouter);
  accountRouter.use('/expense', ExpenseRouter);
  accountRouter.use('', AccountRouter);

  mainRouter.use('/account', authHandler, accountRouter);
};
setupAccountRountes(router);

const setupRequestRoutes = (mainRouter) => {
  const requestRouter = express.Router();
  requestRouter.use('', RequestRouter);
  requestRouter.use('/documents', RequestDocumentRouter);
  requestRouter.use('/comments', RequestCommentsRouter);
  mainRouter.use('/request', authHandler, requestRouter);
};
setupRequestRoutes(router);

const setupBrandRoutes = (mainRouter) => {
  const brandRouter = express.Router();
  brandRouter.use('/catalogue', CatalogueRouter);
  mainRouter.use('/brand', authHandler, brandRouter);
};
setupBrandRoutes(router);

router.use('', HasuraRouter);

module.exports = router;
