const { generateCloudFrontUrl, getUploadsPath } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const DesignationMedia = sequelize.define(
    'DesignationMedia',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      fileName: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      inputValue: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
        get() {
          const filPath = this.getDataValue('filePath');
          return generateCloudFrontUrl(filPath);
        },
        set(val) {
          if (val) {
            this.setDataValue('filePath', getUploadsPath(val));
          } else {
            this.setDataValue('filePath', null);
          }
        },
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  DesignationMedia.associate = (models) => {
    DesignationMedia.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    DesignationMedia.belongsTo(models.DocumentRequire, {
      foreignKey: 'requiredDocumentId',
      as: 'requiredDocument',
    });
  };

  return DesignationMedia;
};
