paths:
  /request/comments:
    post:
      summary: Add a new comment to a request
      description: Adds a new comment to a specific request
      operationId: addRequestComment
      tags:
        - Request
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createRequestComment"
      responses:
        "201":
          description: Comment added successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /request/comments/{id}:
    patch:
      summary: Update an existing comment
      description: Updates the comment text for a specific comment.
      operationId: updateRequestComment
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the comment to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateRequestComment"
      responses:
        "200":
          description: Comment updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    
    delete:
      summary: Delete a comment
      description: Deletes a specific comment from a request.
      operationId: deleteRequestComment
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the comment to delete
      responses:
        "200":
          description: Comment deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createRequestComment:
      type: object
      required:
        - comment
        - requestId
      properties:
        comment:
          type: string
          example: "This is a comment on the task."
          description: "The text of the comment."
        requestId:
          type: integer
          example: 1
          description: "The ID of the request the comment is associated with."

    updateRequestComment:
      type: object
      required:
        - comment
      properties:
        comment:
          type: string
          example: "Updated comment text."
          description: "The updated text of the comment."
