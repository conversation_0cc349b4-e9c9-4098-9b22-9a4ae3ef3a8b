'use strict';

module.exports = (sequelize, DataTypes) => {
  const DesignationModulePermission = sequelize.define(
    'DesignationModulePermission',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      canEdit: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      canCreate: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isEnabled: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isAssigned: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  DesignationModulePermission.associate = (models) => {
    DesignationModulePermission.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });

    DesignationModulePermission.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation',
      onDelete: 'CASCADE',
    });

    DesignationModulePermission.belongsTo(models.Module, {
      foreignKey: 'moduleId',
      as: 'module',
      onDelete: 'CASCADE',
    });

    DesignationModulePermission.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });
  };

  return DesignationModulePermission;
};
