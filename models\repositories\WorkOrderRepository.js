const sequelize = require('sequelize');
const {
  Project,
  WorkOrder,
  WorkOrderType,
  Contractor,
  Organization,
  Address,
  Request,
  ApprovalWorkflow,
  ActivityLog,
} = require('..');
const {
  successMessage,
  errorMessage,
  activityType,
} = require('../../config/options');
const options = require('@config/options');
const { Op } = sequelize;

exports.findOne = async (query) => await Project.findOne(query);

exports.checkAndCreateWorkOrder = async (projectId, data, loggedInUser) => {
  try {
    const query = {
      where: {
        id: projectId,
      },
    };
    const project = await this.findOne(query);

    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('project'),
      };
    }

    const organizationId = project.organizationId;

    const workOrderType = await WorkOrderType.findOne({
      where: {
        id: data.workOrderType,
      },
    });

    if (!workOrderType) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('workOrderType'),
      };
    }

    const existingWorkOrder = await WorkOrder.findOne({
      where: {
        workOrderNumber: data.workOrderNumber,
      },
    });

    if (existingWorkOrder) {
      return {
        success: false,
        message: errorMessage.EXISTS_USER('workOrderNumber'),
      };
    }

    const payload = {
      ...data,
      projectId: projectId,
      organizationId,
      createdBy: loggedInUser.id,
    };

    const Workorder = await WorkOrder.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('WorkOrder'),
      data: Workorder,
    };
  } catch (error) {
    throw error;
  }
};

exports.updateWorkOrder = async (workOrderId, data, loggedInUser) => {
  const { workOrderNumber, termsAndCondition, workOrderValue, workOrderType } =
    data;
  const transaction = await WorkOrder.sequelize.transaction();
  try {
    const workOrder = await WorkOrder.findOne({
      where: { id: workOrderId },
      include: [{ model: WorkOrderType, as: 'type' }],
    });
    if (!workOrder) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('WorkOrder'),
      };
    }
    if (workOrderType) {
      // Validate if the workOrderType exists
      const workOrderTypeExists = await WorkOrderType.findOne({
        where: { id: workOrderType },
      });

      if (!workOrderTypeExists) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('WorkOrderType'),
        };
      }

      // Fetch the name of the work order type
      const workOrderTypeName = workOrderTypeExists.name;

      // Validate if the current work order type is different from the new one
      if (workOrderType !== workOrder.workOrderType) {
        const currentWorkOrderTypeName = workOrder.type
          ? workOrder.type.name
          : null;

        await ActivityLog.create(
          {
            actionType: activityType.EDITED,
            activityDescription: `Work Order Type changed from ${currentWorkOrderTypeName} to ${workOrderTypeName}`,
            createdBy: loggedInUser.id,
            recordId: workOrder.id,
            activityOn: 'WorkOrder',
          },
          { transaction }
        );
      }
    }

    if (workOrderNumber && workOrderNumber !== workOrder.workOrderNumber) {
      await ActivityLog.create(
        {
          actionType: activityType.EDITED,
          activityDescription: `Work Order Number changed from ${workOrder.workOrderNumber} to ${workOrderNumber}`,
          createdBy: loggedInUser.id,
          recordId: workOrder.id,
          activityOn: 'WorkOrder',
        },
        { transaction }
      );
    }

    if (
      termsAndCondition &&
      termsAndCondition !== workOrder.termsAndCondition
    ) {
      await ActivityLog.create(
        {
          actionType: activityType.EDITED,
          activityDescription: `Terms and Condition changed from ${workOrder.termsAndCondition} to ${termsAndCondition}`,
          createdBy: loggedInUser.id,
          recordId: workOrder.id,
          activityOn: 'WorkOrder',
        },
        { transaction }
      );
    }

    if (workOrderValue && workOrderValue !== workOrder.workOrderValue) {
      await ActivityLog.create(
        {
          actionType: activityType.EDITED,
          activityDescription: `Work Order Value changed from ${workOrder.workOrderValue} to ${workOrderValue}`,
          createdBy: loggedInUser.id,
          recordId: workOrder.id,
          activityOn: 'WorkOrder',
        },
        { transaction }
      );
    }

    Object.assign(workOrder, data);
    await workOrder.save({ transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('WorkOrder'),
      data: workOrder,
    };
  } catch (error) {
    // throw error;
    console.log('updateWorkOrder....failed....', error);
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the task.',
    };
  }
};

exports.listWorkOrderDetails = async (workOrderId, loggedInUser) => {
  try {
    const workOrderData = await WorkOrder.findOne({
      where: { id: workOrderId },
      include: [
        {
          model: Request,
          as: 'request',
          required: false,
          where: {
            status: { [Op.notIn]: [options.defaultStatus.REJECTED] },
          },
          attributes: ['id', 'status', 'requestType', 'recordId'],
        },
        {
          model: WorkOrderType,
          as: 'type',
          attributes: ['name'],
        },
        {
          model: Contractor,
          as: 'contractor',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'businessName',
            'mobileNumber',
            'email',
            'address',
            'gstNumber',
            'panNumber',
          ],
        },
        {
          model: Project,
          as: 'project',
          attributes: ['id', 'name', 'logo'],
          include: [
            {
              model: Address,
              as: 'address',
              attributes: [
                'address',
                'country',
                'addressLine2',
                'state',
                'city',
              ],
            },
          ],
        },
        {
          model: Organization,
          as: 'organization',
          attributes: ['name', 'address', 'gstinNumber'],
        },
      ],
      attributes: [
        'workOrderNumber',
        'fromDate',
        'toDate',
        'activities',
        'termsAndCondition',
        'status',
      ],
    });

    if (!workOrderData) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('workOrderData'),
      };
    }

    const existingApprovalWorkflow = await ApprovalWorkflow.findOne({
      where: {
        moduleName: ['WorkOrder'],
        activityName: ['New Work Order'],
        organizationId: loggedInUser.currentOrganizationId,
      },
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('workOrderDetails'),
      data: {
        workOrderDetails: {
          workOrderNumber: workOrderData.workOrderNumber,
          fromDate: workOrderData.fromDate,
          toDate: workOrderData.toDate,
          termsAndCondition: workOrderData.termsAndCondition,
          workOrderTypeName: workOrderData.type?.name || null,
          contractorDetails: workOrderData.contractor,
          projectId: workOrderData.project?.id || null,
          projectName: workOrderData.project?.name || null,
          projectAddress: workOrderData.project?.address?.address || null,
          projectLogo: workOrderData.project?.logo || null,
          organizationDetails: workOrderData.organization,
          activities: workOrderData.activities,
          status: workOrderData.status,
          request: workOrderData.request,
          approvalWorkflow: existingApprovalWorkflow,
        },
      },
    };
  } catch (error) {
    throw error;
  }
};

exports.getWorkorderStatusCount = async (query) => {
  const { organizationId, projectId } = query;

  try {
    const data = {
      total: 0,
      status: {},
    };

    let statusMap = Object.fromEntries(
      [
        options.workOrderStatus.DRAFT,
        options.workOrderStatus.UNDER_APPROVAL,
        options.workOrderStatus.APPROVED,
        options.workOrderStatus.REJECTED,
        options.workOrderStatus.ALLOTTED,
        options.workOrderStatus.ONGOING,
        options.workOrderStatus.DELAYED,
        options.workOrderStatus.DUE_SOON,
        options.workOrderStatus.ESCALATED,
        options.workOrderStatus.ON_HOLD,
      ].map((status) => [status, 0])
    );

    let queryOptions = {
      attributes: [
        'status',
        [
          WorkOrder.sequelize.fn('COUNT', WorkOrder.sequelize.col('status')),
          'count',
        ],
      ],
      where: {},
      group: ['status'],
    };

    if (organizationId) {
      queryOptions.where.organizationId = organizationId;
    }
    if (projectId) {
      queryOptions.where.projectId = projectId;
    }

    const workOrderStatusCounts = await WorkOrder.findAll(queryOptions);

    workOrderStatusCounts.forEach((row) => {
      const status = row.dataValues.status;
      const count = parseInt(row.dataValues.count, 10);
      if (statusMap.hasOwnProperty(status)) {
        statusMap[status] = count;
      }
    });

    data.status = statusMap;
    data.total = Object.values(statusMap).reduce(
      (acc, count) => acc + count,
      0
    );

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('Workorder-status-count'),
      data,
    };
  } catch (error) {
    throw error;
  }
};

exports.allotWorkOrder = async (workOrderId, data) => {
  try {
    const workOrder = await WorkOrder.findOne({ where: { id: workOrderId } });

    if (!workOrder) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('WorkOrder'),
      };
    }

    if (!data.contractorId) {
      return {
        success: false,
        message: errorMessage.REQUIRED('contractorId'),
      };
    }

    const contractor = await Contractor.findOne({
      where: { id: data.contractorId },
    });

    if (!contractor) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Contractor'),
      };
    }

    workOrder.contractorId = data.contractorId;
    workOrder.status = 'allotted';
    await workOrder.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('WorkOrder'),
      data: workOrder,
    };
  } catch (error) {
    throw error;
  }
};
