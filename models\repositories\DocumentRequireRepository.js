const { DocumentRequire } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.getRequiredDocuments = async (designationId) => {
  try {
    const documentRequires = await DocumentRequire.findAll({
      where: { designationId },
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('DocumentRequire'),
      data: documentRequires,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkRequiredDocuments = async (designationId, body) => {
  try {
    const { data: documentRequires } =
      await this.getRequiredDocuments(designationId);

    const uploadedDocuments = body.organizationMedia || [];
    if (uploadedDocuments.length !== documentRequires.length) {
      return {
        success: false,
        message: errorMessage.UPLOAD_ALL_REQUIRED_DOCUMENTS,
      };
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('DocumentRequire'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
