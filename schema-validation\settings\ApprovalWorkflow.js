exports.addWorkflow = {
  approver: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Approver is required',
  },
  isApprovalRequired: {
    in: ['body'],
    errorMessage: 'isApprovalRequired must be a boolean',
  },
  isEscalationRequired: {
    in: ['body'],
    errorMessage: 'isEscalationRequired must be a boolean',
  },
  activityName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    isLength: {
      options: { max: 255 },
      errorMessage: 'Activity name must not exceed 255 characters',
    },
    errorMessage: 'Activity name is required',
  },
};

exports.updateWorkflow = {
  approver: {
    in: ['body'],
    optional: true,
  },
  isApprovalRequired: {
    in: ['body'],
    optional: true,
    errorMessage: 'isApprovalRequired must be a boolean',
  },
  isEscalationRequired: {
    in: ['body'],
    optional: true,
    errorMessage: 'isEscalationRequired must be a boolean',
  },
  activityName: {
    in: ['body'],
    optional: true,
    trim: true,
    isLength: {
      options: { max: 255 },
      errorMessage: 'Activity name must not exceed 255 characters',
    },
  },
};
