const { Op } = require('sequelize');
const { Designation, Department } = require('..');
const DepartmentRepository = require('./DepartmentRepository');
const { successMessage, errorMessage } = require('../../config/options');

exports.getDesignation = async (query) => await Designation.findOne(query);

exports.getDesignationWithDepartment = async (id) => {
  try {
    return await this.getDesignation({
      where: { id },
      attributes: ['id', 'name', 'description', 'departmentId'],
      include: [
        {
          model: Department,
          as: 'department',
          attributes: ['name'],
        },
      ],
    });
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndCreateDesignation = async (body) => {
  try {
    const query = {
      where: {
        id: body.departmentId,
      },
    };

    const department = await DepartmentRepository.getDepartment(query);

    if (!department) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Department'),
      };
    }

    const existingDesignationCheck = await this.checkDuplicate(body);

    if (existingDesignationCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(
          'Designation with same name and department'
        ),
      };
    }

    const designation = await Designation.create(body);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Designation'),
      data: designation,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndUpdateDesignation = async (id, body) => {
  try {
    const existingDesignation = await this.getDesignation({ where: { id } });

    if (!existingDesignation) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Designation'),
      };
    }
    if (existingDesignation.departmentId !== body.departmentId) {
      const query = {
        where: {
          id: body.departmentId,
        },
      };

      const department = await DepartmentRepository.getDepartment(query);

      if (!department) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Department'),
        };
      }
    }

    const duplicateNameCheck = await this.checkDuplicate(body, id);

    if (duplicateNameCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Designation with this name'),
      };
    }

    existingDesignation.name = body.name;
    existingDesignation.description = body.description;
    existingDesignation.departmentId = body.departmentId;
    await existingDesignation.save();

    return {
      success: true,
      data: existingDesignation,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Designation'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkDuplicate = async (body, id) => {
  try {
    return await this.getDesignation({
      where: {
        name: { [Op.iLike]: `${body.name}%` },
        departmentId: body.departmentId,
        ...(id && { id: { [Op.not]: id } }),
      },
    });
  } catch (error) {
    throw new Error(error);
  }
};

exports.getDesignations = async ({ start, limit, search }) => {
  try {
    const whereClause = search
      ? {
          [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }],
        }
      : {};

    const { count, rows } = await Designation.findAndCountAll({
      where: whereClause,
      offset: start,
      limit: limit,
      attributes: ['id', 'name', 'createdAt', 'updatedAt'],
      include: [
        {
          model: Department,
          as: 'department',
          required: false,
          attributes: ['id', 'name'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Designation'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};
