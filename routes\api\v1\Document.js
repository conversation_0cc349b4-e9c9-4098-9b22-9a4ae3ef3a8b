const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

const DocumentControl = require('../../../controllers/api/v1/Document');
const DocumentSchema = require('../../../schema-validation/Document');

router.get(
  '/',
  checkSchema(DocumentSchema.getDocumentOrActivity),
  ErrorHandleHelper.requestValidator,
  DocumentControl.getDocuments
);

router.get(
  '/signed-url',
  checkSchema(DocumentSchema.generateUrl),
  ErrorHandleHelper.requestValidator,
  DocumentControl.getPostDocumentSignedUrl
);

router.post(
  '/',
  checkSchema(DocumentSchema.createDocument),
  ErrorHandleHelper.requestValidator,
  DocumentControl.createDocument
);

router.get(
  '/download',
  checkSchema(DocumentSchema.downloadDocument),
  ErrorHandleHelper.requestValidator,
  DocumentControl.downloadDocument
);

router.put(
  '/:id',
  checkSchema(DocumentSchema.updateDocument),
  ErrorHandleHelper.requestValidator,
  DocumentControl.putDocument
);

router.get('/:id/permission', DocumentControl.getPermissionUsersByDocument);

router.post(
  '/:id/permission',
  checkSchema(DocumentSchema.addOrUpdateUserInDocument),
  ErrorHandleHelper.requestValidator,
  DocumentControl.addUserInDocument
);

router.put(
  '/:id/permission',
  checkSchema(DocumentSchema.addOrUpdateUserInDocument),
  ErrorHandleHelper.requestValidator,
  DocumentControl.updateUserDocumentPermission
);

router.delete(
  '/:id/permission/:userId',
  DocumentControl.removeUserFromDocument
);

router.get(
  '/:id/activities',
  checkSchema(DocumentSchema.getDocumentOrActivity),
  ErrorHandleHelper.requestValidator,
  DocumentControl.getDocumentActivity
);

module.exports = router;
