'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'WorkOrderBOQMapping',
      'WorkOrderBOQMapping_boqItemId_fkey'
    );

    await queryInterface.addConstraint('WorkOrderBOQMapping', {
      fields: ['boqItemId'],
      type: 'foreign key',
      name: 'WorkOrderBOQMapping_boqEntryId_fkey',
      references: {
        table: 'BoqEntry',
        field: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'WorkOrderBOQMapping',
      'WorkOrderBOQMapping_boqEntryId_fkey'
    );

    await queryInterface.addConstraint('WorkOrderBOQMapping', {
      fields: ['boqItemId'],
      type: 'foreign key',
      name: 'WorkOrderBOQMapping_boqItemId_fkey',
      references: {
        table: 'BOQItems',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },
};
