paths:
  /wbs:
    get:
      summary: Retrieve a Wbs Activity
      description: This endpoint retrieves an existing Wbs Activity for a project.
      operationId: getWbsActivity
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: parentActivityId
          description: The parentActivityId of the parent activity.
          schema:
            type: integer
        - in: query
          name: projectId
          description: The projectId of the project.
          schema:
            type: integer
        - in: query
          name: start
          description: The start offset for pagination.
          schema:
            type: integer
            default: 0
        - in: query
          name: limit
          description: The maximum number of items to return.
          schema:
            type: integer
            default: 10
        - in: query
          name: search
          description: The search term.
          schema:
            type: string
        - in: query
          name: status
          description: The status of the activity.
          schema:
            type: string
        - in: query
          name: startDate
          description: The start date for filtering activities.
          schema:
            type: string
            format: date
        - in: query
          name: endDate
          description: The end date for filtering activities.
          schema:
            type: string
            format: date       
        - in: query
          name: unitIds
          description: Array of unitId for filtering activities.
          schema:
            type: array
            items:
              type: integer
        - in: query
          name: subProjectIds
          description: Array of unitId for filtering activities.
          schema:
            type: array
            items:
              type: integer
        - in: query
          name: floorIds
          description: Array of unitId for filtering activities.
          schema:
            type: array
            items:
              type: integer                
      responses:
        '200':
          description: Wbs Activity retrieved successfully.
        '400':
          description: Bad request. The request is invalid or cannot be processed.
        '500':
          description: Internal server error. The server encountered an unexpected condition that prevented it from fulfilling the request.
    post:
      summary: Create a Wbs Activity
      description: Creates a new Wbs Activity for a project.
      operationId: createWbsActivity
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateWbsActivity"
      responses:
        '201':
          description: Wbs Activity added successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/{id}:
    put:
      summary: Update a Wbs Activity
      description: Updates an existing Wbs Activity and its quality control for a project.
      operationId: updateWbsActivity
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateWbsActivity"
      responses:
        '200':
          description: Wbs Activity updated successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/{id}/numbering:
    get:
      summary: Get Activities By Numbering
      description: Fetches activities by numbering for a project.
      operationId: getActivitiesByNumbering
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity to fetch activities by numbering
          schema:
            type: integer
      responses:
        '200':
          description: Activities fetched successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/{id}/comment:
    post:
      summary: Add a comment to a Wbs Activity
      description: Adds a new comment to an existing Wbs Activity for a project.
      operationId: addWbsActivityComment
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity to add a comment to
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addWbsComment"
      responses:
        '201':
          description: Comment added successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/{id}/comment/{commentId}:
    delete:
      summary: Delete a comment from a Wbs Activity
      description: Deletes a comment from an existing Wbs Activity for a project.
      operationId: deleteWbsActivityComment
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity to delete a comment from
          schema:
            type: integer
        - in: path
          name: commentId
          required: true
          description: The ID of the comment to delete
          schema:
            type: integer
      responses:
        '200':
          description: Comment deleted successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/{id}/document:
    post:
      summary: Add a document to a Wbs Activity
      description: Adds a new document to an existing Wbs Activity for a project.
      operationId: addWbsActivityDocument
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity to add a document to
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addWbsDocument"
      responses:
        '201':
          description: Document added successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/{id}/document/{documentId}:
    delete:
      summary: Delete a document from a Wbs Activity
      description: Deletes a document from an existing Wbs Activity for a project.
      operationId: deleteWbsActivityDocument
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity to delete a document from
          schema:
            type: integer
        - in: path
          name: documentId
          required: true
          description: The ID of the document to delete
          schema:
            type: integer
      responses:
        '200':
          description: Document deleted successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/category:
    post:
      summary: Create a Wbs Activity Category
      description: Creates a new Wbs Activity Category for a project.
      operationId: createWbsActivityCategory
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateWbsActivityCategory"
      responses:
        '201':
          description: Wbs Activity Category added successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.
  /wbs/category/{id}:
    put:
      summary: Update a Wbs Activity Category
      description: Updates an existing Wbs Activity Category for a project.
      operationId: updateWbsActivityCategory
      tags:
        - Wbs Activity
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          description: The ID of the Wbs Activity Category to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateWbsActivityCategory"
      responses:
        '200':
          description: Wbs Activity Category updated successfully.
        '400':
          description: Bad request.
        '500':
          description: Internal server error.

components:
  schemas:
    createOrUpdateWbsActivityCategory:
      type: object
      properties:
        iconUrl:
          type: string
          example: "https://example.com/icon.png"
        organizationId:
          type: integer
          example: 1
        color:
          type: string
          example: "#FF0000"
        projectId:
          type: integer
          example: 1
        name:
          type: string
          example: "Activity Category 1"
        shortCode:
          type: string
          example: "AC1"
    createOrUpdateWbsActivity:
      type: object
      properties:
        parentWbsActivityId:
          type: integer
          example: 1
        name:
          type: string
          example: "Activity 1"
        wbsCode:
          type: string
          example: "AC1"
        organizationId:
          type: integer
          example: 1
        projectId:
          type: integer
          example: 1
        description:
          type: string
          example: "This is the first activity"
        startDate:
          type: string
          format: date
          example: "2022-01-01"
        endDate:
          type: string
          format: date
          example: "2022-01-31"
        metricValue:
          type: string
          example: "10"
        unitIds:
          type: array
          items:
            type: integer
          example: [1, 2]
        floorIds:
          type: array
          items:
            type: integer
          example: [1, 2]
        metricType:
          type: string
          example: "type1"
        rate:
          type: number
          example: 1.5
        total:
          type: number
          example: 100.5
        estimatedActivityBudget:
          type: number
          example: 500.5
        accountId:
          type: integer
          example: 1
        dependencies:
          type: array
          items:
            type: object
            properties:
              dependencyType:
                type: string
                example: "blocking"
                enum: ["blocking", "blockedBy"]
              dependOn:
                type: string
                example: "task"
                enum: ["task", "request", "activity"]
              status:
                type: string
                example: "ff"
                enum: ["fs", "ff", "ss", "sf"]
              taskId:
                type: integer
                example: 1
              activityId:
                type: integer
                example: 1
        comments:
          type: array
          items:
            type: object
            properties:
              comment:
                type: string
                example: "This is a comment"
              userId:
                type: integer
                example: 1
        documents:
          type: array
          items:
            type: object
            properties:
              documentId:
                type: integer
                example: 1
              documentUrl:
                type: string
                example: "https://example.com/document"
        qualityControls:
          type: array
          items:
            type: object
            properties:
              templateId:
                type: integer
                example: 1
      required:
        - parentWbsActivityId
        - name
        - wbsCode
        - organizationId
    addWbsComment:
      type: object
      properties:
        comment:
          type: string
          example: "This is a comment"
      required:
        - comment
    addWbsDocument:
      type: object
      properties:
        documentId:
          type: integer
          example: 1
        documentUrl:
          type: string
          example: "https://example.com/document"