'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.bulkInsert('Workspace', [
      {
        name: 'Real Estate Developer',
        category: 'BUILDER',
        amount: 19999,
        totalAmount: 19999,
        discountAmount: 3000,
        description:
          'Limited-time projects with highly experienced individuals',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Vendor',
        category: 'VENDOR',
        amount: 19999,
        totalAmount: 19999,
        discountAmount: 3000,
        description: 'Best for small, friendly-pocket projects',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Brand',
        category: 'BRAND',
        amount: 19999,
        totalAmount: 19999,
        discountAmount: 3000,
        description: 'Unlimited term contracts',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Consultant',
        category: 'CONSULTANT',
        amount: 0,
        totalAmount: 0,
        discountAmount: 0,
        description: 'You need an invite to join as a consultant.',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Sales Agent',
        category: 'SALES_AGENT',
        amount: 0,
        totalAmount: 0,
        discountAmount: 0,
        description: 'You need an invite from a builder to start.',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.bulkDelete('Workspace', null, {});
  },
};
