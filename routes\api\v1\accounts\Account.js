const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const AccountController = require('../../../../controllers/api/v1/accounts/Accounts');
const AccountSchema = require('../../../../schema-validation/account/Account');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(AccountSchema.createAccount),
  ErrorHandleHelper.requestValidator,
  AccountController.createAccount
);

router.get('/verifyAccountCode/:code', AccountController.verifyAccountCode);

router.get(
  '/createDefaultAccount',
  checkSchema(AccountSchema.createDefaultAccount),
  ErrorHandleHelper.requestValidator,
  AccountController.createDefaultAccount
);

router.get(
  '/getAccountCategoryTypeList',
  AccountController.getAccountCategoryTypeList
);

router.get(
  '/getAccountEnteryTypeList',
  AccountController.getAccountEnteryTypeList
);

router.get(
  '/',
  checkSchema(AccountSchema.getAccount),
  ErrorHandleHelper.requestValidator,
  AccountController.getAccountList
);

router.get(
  '/:accountId/getTransactionEntries',
  checkSchema(AccountSchema.getTransactionEntries),
  ErrorHandleHelper.requestValidator,
  AccountController.getTransactionEntries
);

router.get(
  '/:accountId',
  checkSchema(AccountSchema.getAccountById),
  ErrorHandleHelper.requestValidator,
  AccountController.getAccountById
);

router.delete(
  '/:accountId',
  checkSchema(AccountSchema.deleteAccount),
  ErrorHandleHelper.requestValidator,
  AccountController.deleteAccount
);

router.patch(
  '/:accountId',
  checkSchema(AccountSchema.updateAccount),
  ErrorHandleHelper.requestValidator,
  AccountController.updateAccountDetails
);

module.exports = router;
