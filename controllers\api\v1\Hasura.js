const {
  resCode,
  genRes,
  errorTypes,
  errorMessage,
} = require('../../../config/options');
const axios = require('axios');

exports.hasuraAuth = async (req, res) => {
  try {
    axios({
      url: process.env.HASURA_ENDPOINT,
      method: req.method,
      headers: {
        Authorization: req.headers['authorization'],
        'Content-Type': 'application/json',
      },
      data: req.body,
    })
      .then((response) => {
        res.status(response.status).json(response.data);
      })
      .catch((error) => {
        console.log(error.response.data);
        res.status(error.response.status).json(error.response.data);
      });
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
