const { Address } = require('..');

exports.findOne = async (query) => await Address.findOne(query);
exports.findAll = async (query) => await Address.findAll(query);

exports.createOrUpdateAddress = async (
  data,
  addressType,
  loggedInUser,
  addressId = null
) => {
  const query = {
    where: {
      id: addressId,
    },
  };
  const existingAddress = await this.findOne(query);

  const payload = {
    ...data,
    addressType: addressType,
  };

  if (existingAddress) {
    Object.assign(existingAddress, payload);
    return await existingAddress.save();
  } else {
    payload.userId = loggedInUser.id;
    return await Address.create(payload);
  }
};

exports.createOrUpdateAddressForOrganization = async (
  data,
  addressType,
  organizationId,
  transaction
) => {
  const query = {
    where: {
      organizationId,
    },
  };
  const existingAddress = await this.findOne(query);

  const payload = {
    ...data,
    addressType: addressType,
    organizationId,
  };

  if (existingAddress) {
    Object.assign(existingAddress, payload);
    return await existingAddress.save({ transaction });
  } else {
    return await Address.create(payload, { transaction });
  }
};
