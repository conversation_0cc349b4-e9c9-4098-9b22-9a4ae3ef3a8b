const { loginType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('DeviceSession', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      deviceName: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      ipAddress: {
        type: Sequelize.STRING(45),
        allowNull: false,
      },
      loginType: {
        type: Sequelize.ENUM(loginType.loginTypeArray()),
        allowNull: false,
        defaultValue: loginType.CREDENTIALS_LOGIN,
      },
      lastActivityAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      sessionToken: {
        type: Sequelize.TEXT,
        allowNull: false,
        unique: true,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addIndex('DeviceSession', ['userId']);
    await queryInterface.addIndex('DeviceSession', ['sessionToken']);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('DeviceSession');
  },
};
