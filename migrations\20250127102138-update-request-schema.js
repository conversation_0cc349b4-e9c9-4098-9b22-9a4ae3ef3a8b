'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Request', 'shortDescription', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Request', 'reference', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Request', 'shortDescription');
    await queryInterface.removeColumn('Request', 'reference');
  },
};
