module.exports = (sequelize, DataTypes) => {
  const Catalogue = sequelize.define(
    'Catalogue',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      logo: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      backgroundImage: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Catalogue.associate = (models) => {
    Catalogue.belongsTo(models.MarketplaceCategory, {
      foreignKey: 'marketplaceCategoryId',
      as: 'marketplaceCategory',
      onDelete: 'SET NULL',
    });

    Catalogue.belongsTo(models.User, {
      foreignKey: 'brandId',
      as: 'brand',
      onDelete: 'CASCADE',
    });

    Catalogue.belongsTo(models.User, {
      foreignKey: 'vendorId',
      as: 'vendor',
      onDelete: 'CASCADE',
    });
  };

  return Catalogue;
};
