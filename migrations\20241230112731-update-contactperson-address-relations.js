'use strict';

/** @type {import('sequelize-cli').Migration} */
'use strict';

const checkConstraintExists = async (
  queryInterface,
  tableName,
  constraintName
) => {
  try {
    const query = `
      SELECT constraint_name 
      FROM information_schema.table_constraints 
      WHERE table_name = '${tableName}' 
      AND constraint_name = '${constraintName}'
    `;
    const result = await queryInterface.sequelize.query(query);
    return result[0].length > 0;
  } catch (error) {
    console.log(`Error checking constraint existence: ${error.message}`);
    return false;
  }
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      console.log('Starting migration...');

      const constraintExists = await checkConstraintExists(
        queryInterface,
        'Address',
        'Address_contactPersonId_fkey'
      );

      if (constraintExists) {
        await queryInterface.removeConstraint(
          'Address',
          'Address_contactPersonId_fkey'
        );
      }

      try {
        await queryInterface.removeColumn('Address', 'contactPersonId');
      } catch (error) {
        console.log(error.message);
      }

      try {
        await queryInterface.addColumn('ContactPerson', 'addressId', {
          type: Sequelize.INTEGER,
          allowNull: true,
          references: {
            model: 'Address',
            key: 'id',
          },
          onDelete: 'CASCADE',
        });
      } catch (error) {
        if (!error.message.includes('already exists')) {
          throw error;
        }
      }
    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      try {
        await queryInterface.removeColumn('ContactPerson', 'addressId');
      } catch (error) {
        console.log(error.message);
      }

      await queryInterface.addColumn('Address', 'contactPersonId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ContactPerson',
          key: 'id',
        },
        onDelete: 'CASCADE',
      });
    } catch (error) {
      console.error('Rollback failed:', error);
      throw error;
    }
  },
};
