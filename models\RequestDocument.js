module.exports = (sequelize, DataTypes) => {
  const RequestDocument = sequelize.define(
    'RequestDocument',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  RequestDocument.associate = (models) => {
    RequestDocument.belongsTo(models.Request, {
      foreignKey: 'requestId',
      as: 'request',
      onDelete: 'CASCADE',
    });
    RequestDocument.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });
    RequestDocument.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onDelete: 'SET NULL',
    });
  };

  return RequestDocument;
};
