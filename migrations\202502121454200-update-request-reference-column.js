module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Request', 'reference_temp', {
      type: Sequelize.JSONB,
      allowNull: true,
    });

    await queryInterface.sequelize.query(`
      UPDATE "Request"
      SET "reference_temp" = 
        CASE 
          WHEN "reference" IS NOT NULL THEN 
            (
              SELECT jsonb_agg(to_jsonb(val))
              FROM unnest("reference") AS val
            )
          ELSE NULL
        END
    `);

    await queryInterface.removeColumn('Request', 'reference');

    await queryInterface.renameColumn('Request', 'reference_temp', 'reference');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Request', 'reference_temp', {
      type: Sequelize.ARRAY(Sequelize.STRING),
      allowNull: true,
    });

    await queryInterface.sequelize.query(`
      UPDATE "Request"
      SET "reference_temp" = 
        CASE 
          WHEN "reference" IS NOT NULL THEN 
            ARRAY(
              SELECT jsonb_array_elements_text("reference")
            )
          ELSE NULL
        END
    `);

    await queryInterface.removeColumn('Request', 'reference');

    await queryInterface.renameColumn('Request', 'reference_temp', 'reference');
  },
};
