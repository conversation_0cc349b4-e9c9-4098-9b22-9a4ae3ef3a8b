const { budgetStatus } = require('../config/options');

const { getDefaultBudgetPeriods } = require('../config/defaultData');

module.exports = (sequelize, DataTypes) => {
  const Budget = sequelize.define(
    'Budget',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      organizationId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: false,
      },
      year: {
        type: DataTypes.INTEGER,
        defaultValue: 0,
        allowNull: false,
      },
      period: {
        type: DataTypes.ENUM(
          getDefaultBudgetPeriods().map((objEachPeriod) => objEachPeriod.key)
        ),
        defaultValue: getDefaultBudgetPeriods()[0].key,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(budgetStatus.getValues()),
        defaultValue: budgetStatus.DRAFT,
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Budget.associate = (models) => {
    Budget.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Budget.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
      onDelete: 'NO ACTION',
    });

    Budget.hasMany(models.BudgetEntry, {
      foreignKey: 'budgetId',
      as: 'budgetEntries',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return Budget;
};
