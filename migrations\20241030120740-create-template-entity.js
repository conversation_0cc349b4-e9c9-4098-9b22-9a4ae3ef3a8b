'use strict';
const { templateEntityType, calculationType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('TemplateEntity', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      entityType: {
        type: Sequelize.ENUM(templateEntityType.getTemplateEntityTypeArray()),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      templateId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'TemplateMaster',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      isVisible: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      monthlyAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
      },
      annualAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
      },
      calculationType: {
        type: Sequelize.ENUM(calculationType.getCalculationTypeArray()),
        allowNull: false,
      },
      maxAllowed: {
        type: Sequelize.DECIMAL(3, 1),
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('TemplateEntity');
  },
};
