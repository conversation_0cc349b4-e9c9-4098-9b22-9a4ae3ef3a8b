const sequelize = require('sequelize');
const { Op } = sequelize;

const { TemplateEntity, TemplateMaster } = require('..');

const {
  errorMessage,
  successMessage,
  calculationType,
  templateType,
  templateEntityType,
} = require('../../config/options');

exports.findOne = async (query) => await TemplateEntity.findOne(query);
exports.findAll = async (query) => await TemplateEntity.findAll(query);

exports.checkTemplate = async (data, organizationId, templateId = null) => {
  const templateMaster = await TemplateMaster.findOne({
    where: {
      name: data.name,
      organizationId,
      ...(templateId ? { id: { [Op.not]: templateId } } : {}),
    },
  });

  if (templateMaster) {
    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Master Template'),
      data: templateMaster,
    };
  }

  return {
    success: false,
    message: errorMessage.DOES_NOT_EXIST('Master Template'),
  };
};

exports.createMasterWithSalaryTemplateEntity = async (data) => {
  const { success } = await this.checkTemplate(data, data.organizationId);

  if (success) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Master Template Name'),
    };
  }

  const payload = {
    name: data.name,
    organizationId: data.organizationId,
    templateType: templateType.SALARY,
    description: data.description,
    isDefault: false,
    ...(data.templateEntities
      ? {
          templateEntities: data.templateEntities.map((salary) => {
            return {
              ...salary,
              isVisible: true,
              calculationType: salary.calculationType,
            };
          }),
        }
      : {}),
  };

  const template = await TemplateMaster.create(payload, {
    include: [
      {
        model: TemplateEntity,
        as: 'templateEntities',
      },
    ],
  });

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Master Template'),
    data: template,
  };
};

exports.putMasterWithSalaryTemplateEntity = async (
  templateId,
  data,
  loggedInUser
) => {
  const existingMasterTemplate = await TemplateMaster.findOne({
    where: {
      id: templateId,
    },
  });

  if (!existingMasterTemplate) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Template'),
    };
  }
  const { success } = await this.checkTemplate(
    data,
    data.organizationId,
    existingMasterTemplate.id
  );

  if (success) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Template Name'),
    };
  }

  existingMasterTemplate.name = data.name;
  existingMasterTemplate.description = data.description;
  await existingMasterTemplate.save();

  if (data.templateEntities) {
    await TemplateEntity.destroy({
      where: {
        templateId: existingMasterTemplate.id,
      },
    });

    const templateEntities = data.templateEntities.map((salary) => {
      return {
        ...salary,
        isVisible: true,
        calculationType: salary.calculationType,
        templateId: existingMasterTemplate.id,
      };
    });
    await TemplateEntity.bulkCreate(templateEntities);
  }

  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE('Master Template'),
  };
};

exports.getSalaryPayroll = async (templateId, data, loggedInUser) => {
  try {
    const existingMasterTemplate = await TemplateMaster.findOne({
      where: {
        id: templateId,
      },
      include: [
        {
          model: TemplateEntity,
          as: 'templateEntities',
        },
      ],
    });

    const plainExistingMasterTemplate = existingMasterTemplate.get({
      plain: true,
    });
    const earnings = [];
    const deductions = [];

    let totalEarnings = 0;
    let totalDeductions = 0;

    const sortedEntities = plainExistingMasterTemplate.templateEntities.sort(
      (a, b) => {
        const order = {
          [calculationType.FIXED]: 1,
          [calculationType.PERCENTAGE_OF_BASIC]: 2,
          [calculationType.PERCENTAGE_OF_GROSS_SALARY]: 3,
        };

        return order[a.calculationType] - order[b.calculationType];
      }
    );

    const basicEarning = sortedEntities.find((item) => item.name === 'Basic');

    const basicAmount = basicEarning
      ? parseFloat(basicEarning.monthlyAmount) || 0
      : 0;

    sortedEntities.forEach((item) => {
      switch (item.calculationType) {
        case calculationType.FIXED:
          item.calculation = parseFloat(item.monthlyAmount) || 0;
          break;

        case calculationType.PERCENTAGE_OF_BASIC:
          item.calculation =
            (parseFloat(item.calculation) / 100) * basicAmount || 0;
          item.monthlyAmount = item.calculation;
          item.annualAmount = item.calculation * 12;

          break;

        case calculationType.PERCENTAGE_OF_GROSS_SALARY:
          const currentGrossSalary = totalEarnings;
          item.calculation =
            (parseFloat(item.calculation) / 100) * currentGrossSalary || 0;
          item.monthlyAmount = item.calculation;
          item.annualAmount = item.calculation * 12;

          break;

        default:
          throw new Error(`Unknown calculation type: ${item.calculationType}`);
      }

      if (
        [templateEntityType.EARNING, templateEntityType.REIMBURSEMENT].includes(
          item.entityType
        )
      ) {
        earnings.push(item);
        totalEarnings += item.calculation;
      }

      if (
        [templateEntityType.DEDUCTION, templateEntityType.TAX].includes(
          item.entityType
        )
      ) {
        deductions.push(item);
        totalDeductions += item.calculation;
      }
    });

    const grossSalary = totalEarnings;

    const netSalary = grossSalary - totalDeductions;

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Employee Salary Payroll'),
      data: {
        id: plainExistingMasterTemplate.id,
        name: plainExistingMasterTemplate.name,
        description: plainExistingMasterTemplate.description,
        templateEntities: {
          grossSalary,
          netSalary,
          earnings,
          deductions,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.createMasterWithTemplateEntity = async (data) => {
  const { success } = await this.checkTemplate(data, data.organizationId);

  if (success) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Master Template Name'),
    };
  }

  const payload = {
    name: data.name,
    organizationId: data.organizationId,
    templateType: templateType.LEAVE,
    description: data.description,
    isDefault: false,
    templateEntities: data.templateEntities.map((leave) => ({
      ...leave,
      isVisible: true,
      entityType: templateEntityType.LEAVE_POLICY,
      calculationType: leave?.calculationType || calculationType.FIXED,
    })),
  };

  const template = await TemplateMaster.create(payload, {
    include: [
      {
        model: TemplateEntity,
        as: 'templateEntities',
      },
    ],
  });

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Master Template'),
    data: template,
  };
};

exports.putMasterWithTemplateEntity = async (
  templateId,
  data,
  loggedInUser
) => {
  const existingMasterTemplate = await TemplateMaster.findOne({
    where: {
      id: templateId,
    },
  });

  if (!existingMasterTemplate) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Template'),
    };
  }

  const { success } = await this.checkTemplate(
    data,
    data.organizationId,
    existingMasterTemplate.id
  );

  if (success) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Template Name'),
    };
  }

  existingMasterTemplate.name = data.name;
  existingMasterTemplate.description = data.description;

  await existingMasterTemplate.save();

  if (data.templateEntities) {
    await TemplateEntity.destroy({
      where: {
        templateId: existingMasterTemplate.id,
      },
    });

    const templateEntities = data.templateEntities.map((leave) => ({
      ...leave,
      isVisible: true,
      templateId: existingMasterTemplate.id,
      entityType: templateEntityType.LEAVE_POLICY,
      calculationType: leave?.calculationType || calculationType.FIXED,
    }));

    await TemplateEntity.bulkCreate(templateEntities);
  }

  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE('Master Template'),
  };
};

exports.deleteMasterWithTemplateEntity = async (
  templateId,
  data,
  loggedInUser
) => {
  const existingMasterTemplate = await TemplateMaster.findOne({
    where: {
      id: templateId,
    },
  });

  if (!existingMasterTemplate) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Template'),
    };
  }
  existingMasterTemplate.destroy();
  return {
    success: true,
    message: successMessage.DELETE_SUCCESS_MESSAGE('Master Template'),
  };
};
