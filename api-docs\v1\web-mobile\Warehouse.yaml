paths:
  /material/warehouse:
    post:
      tags:
        - "Material"
      summary: "Create a new warehouse"
      description: "This endpoint allows you to create a new warehouse in the system"
      operationId: "CreateWarehouse"
      requestBody:
        description: "Warehouse creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateWarehouseRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Warehouse created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/warehouse/{id}:
    patch:
      tags:
        - "Material"
      summary: "Update an existing warehouse"
      description: "This endpoint allows you to update an existing warehouse in the system"
      operationId: "UpdateWarehouse"
      parameters:
        - $ref: "#/components/parameters/WarehouseIdParam"
      requestBody:
        description: "Warehouse update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateWarehouseRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Warehouse updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Warehouse not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing warehouse"
      description: "This endpoint allows you to delete an existing warehouse in the system"
      operationId: "DeleteWarehouse"
      parameters:
        - $ref: "#/components/parameters/WarehouseIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Warehouse deleted successfully"
        "404":
          description: "Warehouse not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateWarehouseRequest:
      type: object
      properties:
        name:
          type: string
          example: "Main Warehouse"
        about:
          type: string
          example: "This is the main warehouse for storing materials."
        address:
          type: object
          properties:
            city:
              type: string
              example: "New York"
            state:
              type: string
              example: "NY"
            pincode:
              type: string
              example: "10001"
            country:
              type: string
              example: "USA"
            address:
              type: string
              example: "123 Main St"
            addressLine2:
              type: string
              example: "Suite 100"
            landmark:
              type: string
              example: "Near Central Park"
            latitude:
              type: number
              format: float
              example: 40.7128
            longitude:
              type: number
              format: float
              example: -74.0060
      required:
        - name
    UpdateWarehouseRequest:
      type: object
      properties:
        name:
          type: string
          example: "Main Warehouse"
        about:
          type: string
          example: "This is the main warehouse for storing materials."
        address:
          type: object
          properties:
            city:
              type: string
              example: "New York"
            state:
              type: string
              example: "NY"
            pincode:
              type: string
              example: "10001"
            country:
              type: string
              example: "USA"
            address:
              type: string
              example: "123 Main St"
            addressLine2:
              type: string
              example: "Suite 100"
            landmark:
              type: string
              example: "Near Central Park"
            latitude:
              type: number
              format: float
              example: 40.7128
            longitude:
              type: number
              format: float
              example: -74.0060
  parameters:
    WarehouseIdParam:
      name: "id"
      in: "path"
      description: "ID of the warehouse to be managed"
      required: true
      schema:
        type: string