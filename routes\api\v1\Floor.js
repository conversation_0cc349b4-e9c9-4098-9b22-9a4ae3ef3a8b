const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const FloorSchema = require('../../../schema-validation/Floor');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');
const FloorController = require('../../../controllers/api/v1/Floor');

router.post(
  '/:id/floor',
  checkSchema(FloorSchema.createFloor),
  ErrorHandleHelper.requestValidator,
  FloorController.createFloor
);

router.patch(
  '/floor/:id/move-up',
  ErrorHandleHelper.requestValidator,
  FloorController.moveFloorUp
);

router.patch(
  '/floor/:id/move-down',
  ErrorHandleHelper.requestValidator,
  FloorController.moveFloorDown
);

router.put(
  '/floor/:id',
  checkSchema(FloorSchema.updateFloor),
  ErrorHandleHelper.requestValidator,
  FloorController.updateFloor
);

router.delete(
  '/floor/:id',
  ErrorHandleHelper.requestValidator,
  FloorController.deleteFloor
);

router.post(
  '/:id/floor/:floorId/duplicate',
  FloorController.createDuplicateFloor
);

router.post(
  '/floor/:id/drawing',
  checkSchema(FloorSchema.validateAddDrawingsToFloor),
  ErrorHandleHelper.requestValidator,
  FloorController.addDrwaingsToFloor
);

module.exports = router;
