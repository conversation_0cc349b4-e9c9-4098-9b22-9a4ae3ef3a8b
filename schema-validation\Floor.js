const { floorType } = require('../config/options');

exports.createFloor = {
  isBasement: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isBasement must be a boolean',
    },
  },
  floorType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Floor Type is required',
    },
    isString: {
      errorMessage: 'Floor Type must be a valid string',
    },
    custom: {
      options: (value) => {
        const allowedValues = floorType.getFloorTypeArray();
        if (!allowedValues.includes(value)) {
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid floor type value provided',
    },
  },
  unitId: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value, { req }) => {
        if (req.body.floorType === floorType.UNIT && !value) {
          return false;
        }
        if (req.body.floorType === floorType.PROJECT && value) {
          return false;
        }
        return true;
      },
      errorMessage: `unitId is required when floorType is "Unit"`,
    },
    isInt: {
      errorMessage: 'unitId name must be a number',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Floor name cannot be empty',
    },
    isString: {
      errorMessage: 'Floor name must be a string',
    },
  },
  buildUpArea: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Build-up area cannot be empty',
    },
    isFloat: {
      errorMessage: 'Build-up area must be a valid number',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Build-up area must be greater than 0',
    },
  },
  isNamingFormat: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isNamingFormat must be a boolean',
    },
  },
  isAlphabets: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isAlphabets must be a boolean',
    },
  },
  isNumeric: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isNumeric must be a boolean',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};

exports.updateFloor = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Floor ID is required',
    },
    isInt: {
      errorMessage: 'Floor ID must be a valid integer',
    },
    toInt: true,
  },
  isBasement: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isBasement must be a boolean',
    },
  },
  floorType: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Floor Type must be a valid string',
    },
    custom: {
      options: (value) => {
        if (value) {
          const allowedValues = floorType.getFloorTypeArray();
          if (!allowedValues.includes(value)) {
            return false;
          }
        }
        return true;
      },
      errorMessage: 'Invalid floor type value provided',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Floor name must be a string',
    },
  },
  buildUpArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Build-up area must be a valid number',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'Build-up area must be greater than 0',
    },
  },
  isNamingFormat: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isNamingFormat must be a boolean',
    },
  },
  isAlphabets: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isAlphabets must be a boolean',
    },
  },
  isNumeric: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isNumeric must be a boolean',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};

exports.validateAddDrawingsToFloor = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Floor Id is required',
    },
    isInt: {
      errorMessage: 'Floor Id must be a valid integer',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};
