paths:
  /task/{id}/task-unit-mapping:
    post:
      summary: Creates a new TaskUnitMapping
      description: Creates a new TaskUnitMapping for specific Task
      operationId: createTaskUnitMapping
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of Task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createTaskUnitMapping"
      responses:
        "201":
          description: TaskUnitMapping created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/{id}/{unitId}/task-unit-mapping:  
    delete:
      summary: Delete a TaskUnit
      description: Delete TaskUnit if you dont need it
      operationId: deleteTaskUnit
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of task
        - name: unitId
          in: path
          required: true
          schema:
            type: integer
          description: ID of unit
      responses:
        "200":
          description: TaskUnitMapping deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createTaskUnitMapping:
      type: object
      required:
        - tagId
      properties:
        unitId:
          type: integer
          example: 1
          description: "The id of Tag"
        
