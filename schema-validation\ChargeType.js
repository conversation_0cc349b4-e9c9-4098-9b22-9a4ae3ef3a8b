exports.createChargeType = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  amount: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Amount must be a string if provided',
    },
  },
  isIncludedInBase: {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isIncludedInBase must be a boolean',
    },
  },
  isIncludedInAgreement: {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isIncludedInAgreement must be a boolean',
    },
  },
  isBrokerageConsidered: {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isBrokerageConsidered must be a boolean',
    },
  },
  isTdsApplicable: {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isTdsApplicable must be a boolean',
    },
  },
  allowDiscount: {
    in: ['body'],
    isBoolean: {
      errorMessage: 'allowDiscount must be a boolean',
    },
  },
  sacNumber: {
    in: ['body'],
    optional: true,
    isNumeric: {
      errorMessage: 'sacNumber must be a number if provided',
    },
  },
};

exports.updateChargeType = {
  name: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  amount: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Amount must be a string if provided',
    },
  },
  isIncludedInBase: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isIncludedInBase must be a boolean',
    },
  },
  isIncludedInAgreement: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isIncludedInAgreement must be a boolean',
    },
  },
  isBrokerageConsidered: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isBrokerageConsidered must be a boolean',
    },
  },
  isTdsApplicable: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isTdsApplicable must be a boolean',
    },
  },
  allowDiscount: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'allowDiscount must be a boolean',
    },
  },
  sacNumber: {
    in: ['body'],
    optional: true,
    isNumeric: {
      errorMessage: 'sacNumber must be a valid number if provided',
    },
  },
};
