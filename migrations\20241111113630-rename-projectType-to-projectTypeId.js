'use strict';
const { projectStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameColumn(
      'Project',
      'projectType',
      'projectTypeId'
    );

    await queryInterface.addColumn('Project', 'status', {
      type: Sequelize.ENUM(projectStatus.getProjectStatusArray()),
      allowNull: false,
      defaultValue: 'new_project',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Project', 'status');

    await queryInterface.renameColumn(
      'Project',
      'projectTypeId',
      'projectType'
    );
  },
};
