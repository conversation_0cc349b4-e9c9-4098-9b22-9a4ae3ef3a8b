// const { sequelize } = require('../../models/index');
const sequelize = require('sequelize');

const { Op } = sequelize;
const {
  WbsActivity,
  Unit,
  WbsActivityDependency,
  WbsComment,
  WbsActivityQualityControl,
  WbsDocument,
  WbsActivityProjectMapping,
  Floor,
} = require('..');
const ProjectRepository = require('./ProjectRepository');
const { checkExistence } = require('@helpers/QueryHelper');
const {
  successMessage,
  wbsActivityType,
  errorMessage,
  defaultStatus,
} = require('@config/options');

exports.findOne = async (query) => await WbsActivity.findOne(query);

exports.findAll = async (query) => await WbsActivity.findAll(query);
exports.findAndCountAll = async (query) =>
  await WbsActivity.findAndCountAll(query);

exports.checkAndCreateWbsActivityCategory = async (data, loggedInUser) => {
  try {
    const { success, message } = await ProjectRepository.checkAndGetProject(
      data.projectId
    );

    if (!success) {
      return {
        success: false,
        message,
      };
    }

    const payload = {
      ...data,
      wbsCode: '1',
      activityType: wbsActivityType.CATEGORY,
      status: defaultStatus.ACTIVE,
      createdBy: loggedInUser.id,
    };

    const wbsActivityCategory = await WbsActivity.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Wbs Activity Category'),
      data: wbsActivityCategory,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndPutWbsActivityCategory = async (
  wbsActivityId,
  data,
  loggedInUser
) => {
  try {
    const { success, message } = await ProjectRepository.checkAndGetProject(
      data.projectId
    );

    if (!success) {
      return {
        success: false,
        message,
      };
    }

    const existingWbsActivityCategory = await this.findOne({
      where: { id: wbsActivityId },
    });

    Object.assign(existingWbsActivityCategory, data);

    await existingWbsActivityCategory.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Wbs Activity Category'),
      data: existingWbsActivityCategory,
    };
  } catch (error) {
    throw new Error(error);
  }
};
exports.checkAndCreateWbsActivity = async (data, loggedInUser) => {
  try {
    const { success, message } = await ProjectRepository.checkAndGetProject(
      data.projectId
    );

    if (!success) {
      return {
        success: false,
        message,
      };
    }

    const query = {
      where: { id: data.parentWbsActivityId },
      include: [
        {
          model: WbsActivity,
          as: 'wbsCategory',
          attributes: ['id', 'wbsCode', 'activityType'],
        },
      ],
      attributes: ['id', 'activityType', 'wbsCode', 'wbsCategoryId'],
    };

    const existingParentWbsActivity = await this.findOne(query);

    if (!existingParentWbsActivity) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Parent Wbs Activity'),
      };
    }

    const projectMappings = [];
    if (data.unitIds && data.unitIds.length > 0) {
      for (const id of data.unitIds) {
        const unit = await checkExistence(Unit, { id }, [
          'id',
          'projectId',
          'name',
        ]);
        if (!unit) {
          return {
            success: false,
            message: errorMessage.DOES_NOT_EXIST('Unit'),
          };
        }
        projectMappings.push({ subProjectId: unit.projectId, unitId: id });
      }
    }

    if (data.floorIds && data.floorIds.length > 0) {
      for (const id of data.floorIds) {
        const floor = await checkExistence(Floor, { id }, [
          'id',
          'projectId',
          'name',
        ]);
        if (!floor) {
          return {
            success: false,
            message: errorMessage.DOES_NOT_EXIST('Floor'),
          };
        }
        projectMappings.push({ subProjectId: floor.projectId, floorId: id });
      }
    }

    const payload = {
      ...data,
      wbsCategoryId:
        existingParentWbsActivity.activityType === wbsActivityType.CATEGORY
          ? existingParentWbsActivity.id
          : existingParentWbsActivity.wbsCategoryId,
      status: data.status || defaultStatus.ACTIVE,
      ...(projectMappings.length > 0 ? { projectMappings } : {}),
      ...(data.dependencies.length > 0
        ? {
            dependencies: data.dependencies.map((dependency) => ({
              ...dependency,
            })),
          }
        : {}),
      ...(data.comments.length > 0
        ? {
            comments: data.comments.map((comment) => ({
              ...comment,
              userId: comment.userId || loggedInUser.id,
            })),
          }
        : {}),
      ...(data.documents.length > 0
        ? {
            documents: data.documents.map((document) => ({
              ...document,
              createdBy: loggedInUser.id,
            })),
          }
        : {}),
      ...(data.qualityControls.length > 0 ? data.qualityControls : {}),
      activityType: wbsActivityType.WBS_ACTIVITY,
      createdBy: loggedInUser.id,
    };

    const createWbsActivity = await WbsActivity.create(payload, {
      include: [
        {
          model: WbsActivityDependency,
          as: 'dependencies',
        },
        {
          model: WbsActivityProjectMapping,
          as: 'projectMappings',
        },
        {
          model: WbsComment,
          as: 'comments',
        },
        {
          model: WbsDocument,
          as: 'documents',
        },
        {
          model: WbsActivityQualityControl,
          as: 'qualityControls',
        },
      ],
    });

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Wbs Activity Category'),
      data: createWbsActivity,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndPutWbsActivity = async (wbsActivityId, data, loggedInUser) => {
  try {
    const { success, message } = await ProjectRepository.checkAndGetProject(
      data.projectId
    );

    if (!success) {
      return {
        success: false,
        message,
      };
    }
    const existingWbsActivity = await this.findOne({
      where: { id: wbsActivityId },
      include: [
        {
          model: WbsActivityDependency,
          as: 'dependencies',
        },
        {
          model: WbsActivityProjectMapping,
          as: 'projectMappings',
        },
      ],
    });

    const projectMappings = [];
    if (data.unitIds && data.unitIds.length > 0) {
      for (const id of data.unitIds) {
        const unit = await checkExistence(Unit, { id }, [
          'id',
          'projectId',
          'name',
        ]);
        if (!unit) {
          return {
            success: false,
            message: errorMessage.UNIT_NOT_FOUND(id),
          };
        }
        projectMappings.push({
          subProjectId: unit.projectId,
          unitId: id,
          wbsActivityId: existingWbsActivity.id,
        });
      }
    }

    if (data.floorId && data.floorIds.length > 0) {
      for (const id of data.floorIds) {
        const floor = await checkExistence(Floor, { id }, [
          'id',
          'projectId',
          'name',
        ]);
        if (!floor) {
          return {
            success: false,
            message: errorMessage.DOES_NOT_EXIST('Floor'),
          };
        }
        projectMappings.push({ subProjectId: floor.projectId, floorId: id });
      }
    }

    Object.assign(existingWbsActivity, data);

    existingWbsActivity.save();

    if (
      data.dependencies.length > 0 ||
      existingWbsActivity.dependencies.length > 0
    ) {
      await WbsActivityDependency.destroy({
        where: {
          wbsActivityId: existingWbsActivity.id,
        },
      });

      if (data.dependencies.length > 0) {
        await WbsActivityDependency.bulkCreate(
          data.dependencies.map((dependency) => ({
            ...dependency,
            wbsActivityId: existingWbsActivity.id,
          }))
        );
      }
    }

    if (
      projectMappings.length > 0 ||
      existingWbsActivity.projectMappings.length > 0
    ) {
      await WbsActivityProjectMapping.destroy({
        where: {
          wbsActivityId: existingWbsActivity.id,
        },
      });

      if (projectMappings.length > 0) {
        await WbsActivityProjectMapping.bulkCreate(projectMappings);
      }
    }

    if (
      data.qualityControls.length > 0 ||
      existingWbsActivity.qualityControls.length > 0
    ) {
      await WbsActivityQualityControl.destroy({
        where: {
          wbsActivityId: existingWbsActivity.id,
        },
      });

      if (data.qualityControls.length > 0) {
        await WbsActivityQualityControl.bulkCreate(
          data.qualityControls.map((qualityControl) => ({
            ...qualityControl,
            wbsActivityId: existingWbsActivity.id,
          }))
        );
      }
    }
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Wbs Activity Category'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndAddWbsActivityComment = async (
  wbsActivityId,
  data,
  loggedInUser
) => {
  try {
    const query = {
      where: {
        id: wbsActivityId,
        status: { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: ['id', 'name'],
    };
    const wbsActivity = await this.findOne(query);

    if (!wbsActivity) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Wbs Activity'),
      };
    }
    const payload = {
      ...data,
      wbsActivityId: wbsActivity.id,
      userId: loggedInUser.id,
    };

    const addWbsComment = await WbsComment.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Wbs Activity Comment'),
      data: addWbsComment,
    };
  } catch (error) {
    throw new Error(error);
  }
};
exports.checkAndDeleteWbsActivityComment = async (wbsActivityId, commentId) => {
  try {
    const query = {
      where: {
        id: commentId,
      },
      attributes: ['id', 'comment'],
    };
    const existingWbsComment = await WbsComment.findOne(query);

    if (!existingWbsComment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Wbs Activity'),
      };
    }

    await existingWbsComment.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Wbs Activity Comment'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndAddWbsActivityDocument = async (
  wbsActivityId,
  data,
  loggedInUser
) => {
  try {
    const query = {
      where: {
        id: wbsActivityId,
        status: { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: ['id', 'name'],
    };
    const wbsActivity = await this.findOne(query);

    if (!wbsActivity) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Wbs Activity'),
      };
    }
    const payload = {
      ...data,
      wbsActivityId: wbsActivity.id,
      createdBy: loggedInUser.id,
    };

    const addWbsDocument = await WbsDocument.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Wbs Activity Document'),
      data: addWbsDocument,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndDeleteWbsActivityDocument = async (
  wbsActivityId,
  documentId
) => {
  try {
    const query = {
      where: {
        id: documentId,
      },
      attributes: ['id', 'documentId', 'documentUrl'],
    };
    const existingWbsDocument = await WbsDocument.findOne(query);

    if (!existingWbsDocument) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Wbs Activity'),
      };
    }

    await existingWbsDocument.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Wbs Activity Document'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.fetchActivitiesByNumbering = async (parentWbsActivityId) => {
  try {
    const query = {
      where: { id: parentWbsActivityId },
      include: [
        {
          model: WbsActivity,
          as: 'subWbsActivities',
          attributes: ['id', 'wbsCode', 'activityType'],
        },
      ],
      attributes: ['id', 'wbsCode', 'activityType'],
    };

    const existingWbsActivity = await this.findOne(query);

    if (!existingWbsActivity) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Wbs Activity'),
      };
    }

    const subActivities = existingWbsActivity.subWbsActivities || [];
    const subActivitiesCount = subActivities.length;
    const baseNumbering = existingWbsActivity.wbsCode;

    let numbering;
    if (subActivitiesCount > 0) {
      numbering = `${baseNumbering}.${subActivitiesCount + 1}`;
    } else {
      numbering = `${baseNumbering}.1`;
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Wbs Activity'),
      data: { numbering },
    };
  } catch (error) {
    throw new Error(error);
  }
};

// exports.getWbsActivity = async (data, loggedInUser) => {
//   try {
//     const { limit = 10, start = 0 } = data;

//     const { success, message } = await ProjectRepository.checkAndGetProject(
//       data.projectId
//     );

//     if (!success) {
//       return {
//         success: false,
//         message,
//       };
//     }

//     const query = {
//       where: {
//         parentWbsActivityId: null,
//         projectId: data.projectId,
//         ...(data?.search
//           ? {
//               [Op.or]: [
//                 { name: { [Op.iLike]: `%${data.search}%` } },
//                 { wbsCode: { [Op.like]: `%${data.search}%` } },
//               ],
//             }
//           : {}),
//       },
//       attributes: [
//         'id',
//         'iconUrl',
//         'color',
//         'wbsCode',
//         'name',
//         'activityType',
//         'startDate',
//         'endDate',
//         'status',
//         'metricValue',
//         'metricType',
//         'rate',
//         'total',
//         'estimatedActivityBudget',
//       ],
//       order: [['createdAt', 'ASC']],
//     };

//     const include = await fetchNestedActivities(2, data);

//     if (include.length > 0) {
//       query.include = include;
//     }

//     const { rows, count } = await this.findAndCountAll(query, {
//       logging: console.log,
//     });

//     return {
//       success: true,
//       message: successMessage.DETAIL_MESSAGE('Wbs Activity'),
//       data: {
//         rows,
//         pagination: {
//           totalCount: count,
//           start,
//           limit,
//         },
//       },
//     };
//   } catch (error) {
//     throw new Error(error);
//   }
// };

// async function fetchNestedActivities(depth, data) {
//   if (depth === 0) return [];
//   const includeStructure = {
//     model: WbsActivity,
//     as: 'subWbsActivities',
//     attributes: [
//       'id',
//       'wbsCode',
//       'name',
//       'activityType',
//       'startDate',
//       'endDate',
//       'status',
//       'metricValue',
//       'metricType',
//       'rate',
//       'total',
//       'estimatedActivityBudget',
//     ],
//     ...(data?.search
//       ? {
//           where: {
//             [Op.or]: [
//               { name: { [Op.iLike]: `%${data.search}%` } },
//               { wbsCode: { [Op.like]: `%${data.search}%` } },
//             ],
//           },
//         }
//       : {}),
//     include: [
//       {
//         model: WbsActivityDependency,
//         as: 'dependencies',
//         attributes: ['id', 'dependencyType', 'dependOn', 'status'],
//       },
//       {
//         model: WbsActivityQualityControl,
//         as: 'qualityControls',
//         attributes: ['id', 'templateId', 'isCompleted'],
//       },
//       {
//         model: WbsActivityProjectMapping,
//         as: 'projectMappings',
//         attributes: ['id', 'unitId', 'floorId', 'subProjectId'],
//         required: !!(data?.unitIds || data?.subProjectIds || data?.floorIds),
//         // where: {
//         //   ...(data?.unitIds ? { unitId: { [Op.in]: data.unitIds } } : {}),
//         //   ...(data?.subProjectIds
//         //     ? { subProjectId: { [Op.in]: data.subProjectIds } }
//         //     : {}),
//         //   ...(data?.floorIds ? { floorId: { [Op.in]: data.floorIds } } : {}),
//         // },
//       },
//       ...(await fetchNestedActivities(depth - 1, data)),
//     ],
//     order: [['wbsCode', 'ASC']],
//   };

//   if (data?.startDate || data?.endDate) {
//     includeStructure.where = {
//       ...includeStructure.where,
//       ...(data?.startDate ? { startDate: { [Op.gte]: data.startDate } } : {}),
//       ...(data?.endDate ? { endDate: { [Op.lte]: data.endDate } } : {}),
//     };
//   }

//   return [includeStructure];
// }

exports.getWbsActivity = async (data, loggedInUser) => {
  try {
    // const { limit = 10, start = 0 } = data;
    const { success, message } = await ProjectRepository.checkAndGetProject(
      data.projectId
    );

    if (!success) {
      return {
        success: false,
        message,
      };
    }

    const maxLevel = await WbsActivity.max('level', {
      where: { projectId: data.projectId },
    });
    console.log('🚀 ~ exports.getWbsActivity= ~ maxLevel:', maxLevel);
    const activities = await this.fetchNestedActivities(maxLevel, data);
    console.log('🚀 ~ exports.getWbsActivity= ~ activities:', activities);

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Wbs Activity'),
      data: {
        rows: activities,
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.fetchNestedActivities = async (maxLevel, data) => {
  let result = [];
  let parentWbsActivityIds = [];

  const hasProjectMappingFilters =
    data?.unitIds?.length > 0 ||
    data?.subProjectIds?.length > 0 ||
    data?.floorIds?.length > 0;

  for (let i = maxLevel; i >= 0; i--) {
    const orConditions = [];
    if (hasProjectMappingFilters && parentWbsActivityIds.length > 0) {
      orConditions.push({ id: { [Op.in]: parentWbsActivityIds } });
    }

    if (data?.unitIds) {
      orConditions.push({
        '$projectMappings.unitId$': { [Op.in]: data.unitIds },
      });
    }
    if (data?.subProjectIds) {
      orConditions.push({
        '$projectMappings.subProjectId$': { [Op.in]: data.subProjectIds },
      });
    }
    if (data?.floorIds) {
      orConditions.push({
        '$projectMappings.floorId$': { [Op.in]: data.floorIds },
      });
    }

    const query = {
      where: {
        [Op.and]: [
          { level: i },
          { projectId: data.projectId },
          ...(orConditions.length > 0 ? [{ [Op.or]: orConditions }] : []),
        ],
      },
      include: [
        {
          model: WbsActivityDependency,
          as: 'dependencies',
          attributes: ['id', 'dependencyType', 'dependOn', 'status'],
        },
        {
          model: WbsActivityQualityControl,
          as: 'qualityControls',
          attributes: ['id', 'templateId', 'isCompleted'],
        },
        {
          model: WbsActivityProjectMapping,
          as: 'projectMappings',
          attributes: ['id', 'unitId', 'floorId', 'spaceId', 'subProjectId'],
        },
      ],
      attributes: [
        'id',
        'wbsCode',
        'iconUrl',
        'color',
        'name',
        'activityType',
        'parentWbsActivityId',
        'startDate',
        'endDate',
        'status',
        'metricValue',
        'metricType',
        'rate',
        'total',
        'estimatedActivityBudget',
      ],
    };

    const subWbsActivity = await WbsActivity.findAll(query);

    if (hasProjectMappingFilters && subWbsActivity.length > 0) {
      parentWbsActivityIds = subWbsActivity.map(
        (wbsActivity) => wbsActivity.parentWbsActivityId
      );
    }

    if (result.length > 0) {
      result = subWbsActivity.map((wbsActivity) => {
        const subWbsActivities = result.filter(
          (activity) => activity.parentWbsActivityId === wbsActivity.id
        );
        return { ...wbsActivity.toJSON(), subWbsActivities };
      });
    } else {
      result = subWbsActivity.map((wbsActivity) => wbsActivity.toJSON());
    }
  }

  return result;
};
