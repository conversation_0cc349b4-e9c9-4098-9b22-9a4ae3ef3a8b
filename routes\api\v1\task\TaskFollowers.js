const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskFollowerController = require('@controllers/v1/task/TaskFollowers');
const TaskFollowerSchema = require('@schema-validation/task/TaskFollowers');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/follower',
  checkSchema(TaskFollowerSchema.addFollowers),
  ErrorHandleHelper.requestValidator,
  TaskFollowerController.addFollowers
);

router.delete(
  '/:id/:userId/follower',
  ErrorHandleHelper.requestValidator,
  TaskFollowerController.deleteFollowers
);

module.exports = router;
