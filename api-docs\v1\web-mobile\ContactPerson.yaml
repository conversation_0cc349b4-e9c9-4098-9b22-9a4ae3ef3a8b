paths:
  /crm/customer/{id}/contact-person:
    post:
      tags:
        - "CRM"
      summary: "Create a new Contact Person"
      description: "This endpoint allows you to register a new contact person by providing all necessary details."
      operationId: "CreateContact<PERSON>erson"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the associated customer."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The details of the new contact person to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/contact-person"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Contact Person has been created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/contact-person'
        "400":
          description: "Invalid input data or contact person already exists."
        "500":
          description: "Internal Server Error"

  /crm/customer/contact-person/{id}:      
    put:
      tags:
        - "CRM"
      summary: "Update an existing Contact Person"
      description: "This endpoint allows you to update the details of an existing contact person by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateContact<PERSON>erson"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the contact person to be updated."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The updated information for the contact person. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/contact-person-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Contact Person has been updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/contact-person'
        "400":
          description: "Invalid input data, contact person not found, or contact person already exists."
        "404":
          description: "Contact Person not found."
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "CRM"
      summary: "Delete an existing Contact Person"
      description: "This endpoint allows you to delete an existing contact person by providing its `id` in the URL path."
      operationId: "DeleteContactPerson"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the contact person to be deleted."
          schema:
            type: integer
            example: 123
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Contact Person has been deleted successfully."
        "400":
          description: "Invalid input data, or contact person could not be deleted."
        "404":
          description: "Contact Person not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    contact-person:
      type: object
      properties:
        contactCategory:
          type: string
          description: "The category of the contact person."
          example: "individual"
        logo:
          type: string
          description: "The logo of the contact person."
          example: "uploads/logo.jpg"
        businessName:
          type: string
          description: "The business name of the contact person."
          example: "ABC Corp"
        firstName:
          type: string
          description: "The first name of the contact person."
          example: "John"
        lastName:
          type: string
          description: "The last name of the contact person."
          example: "Doe"
        countryCode:
          type: string
          description: "The country code for the contact person's phone number."
          example: "+1"
        contactNumber:
          type: string
          description: "The contact number of the contact person."
          example: "1234567890"
        email:
          type: string
          description: "The email address of the contact person."
          example: "<EMAIL>"
        panNumber:
          type: string
          description: "The PAN number of the contact person."
          example: "**********"
        gstNumber:
          type: string
          description: "The GST number of the contact person."
          example: "22AAAAA0000A1Z5"
        contactType:
          type: string
          description: "The type of the contact person."
          example: "primary"
        about:
          type: string
          description: "Information about the contact person."
          example: "Experienced supplier of raw materials."
        details:
          type: string
          description: "Additional details about the contact person."
          example: "Has been in business for over 20 years."
        address:
          type: object
          description: "Address of the contact person."
          properties:
            city:
              type: string
              description: "City"
              example: "New York"
            state:
              type: string
              description: "State"
              example: "New York"
            pincode:
              type: string
              description: "Pincode"
              example: "10001"
            country:
              type: string
              description: "Country"
              example: "USA"
            address:
              type: string
              description: "Address Line 1"
              example: "123 Main Street"
            addressLine2:
              type: string
              description: "Address Line 2"
              example: "Suite 5B"
            landmark:
              type: string
              description: "Landmark"
              example: "Near Central Park"
            latitude:
              type: number
              format: float
              description: "Latitude"
              example: 40.748817
            longitude:
              type: number
              format: float
              description: "Longitude"
              example: -73.985428
      required:
        - customerId
        - contactCategory
        - countryCode
        - contactNumber
        - email
      additionalProperties: false

    contact-person-update:
      type: object
      properties:
        contactCategory:
          type: string
          description: "The category of the contact person."
          example: "business"
        logo:
          type: string
          description: "The logo of the contact person."
          example: "https://your-cloudfront-url.com/uploads/logo.jpg"
        businessName:
          type: string
          description: "The business name of the contact person."
          example: "ABC Corp"
        firstName:
          type: string
          description: "The first name of the contact person."
          example: "Jane"
        lastName:
          type: string
          description: "The last name of the contact person."
          example: "Smith"
        countryCode:
          type: string
          description: "The country code for the contact person's phone number."
          example: "+44"
        contactNumber:
          type: string
          description: "The contact number of the contact person."
          example: "9876543210"
        email:
          type: string
          description: "The email address of the contact person."
          example: "<EMAIL>"
        panNumber:
          type: string
          description: "The PAN number of the contact person."
          example: "**********"
        gstNumber:
          type: string
          description: "The GST number of the contact person."
          example: "22AAAAA0000A1Z5"
        contactType:
          type: string
          description: "The type of the contact person."
          example: "secondary"
        about:
          type: string
          description: "Information about the contact person."
          example: "Experienced supplier of raw materials."
        details:
          type: string
          description: "Additional details about the contact person."
          example: "Has been in business for over 20 years."
        address:
          type: object
          description: "Address of the contact person."
          properties:
            city:
              type: string
              description: "City"
              example: "London"
            state:
              type: string
              description: "State"
              example: "England"
            pincode:
              type: string
              description: "Pincode"
              example: "EC1A"
            country:
              type: string
              description: "Country"
              example: "United Kingdom"
            address:
              type: string
              description: "Address Line 1"
              example: "123 Main Street"
            addressLine2:
              type: string
              description: "Address Line 2"
              example: "Office 3B"
            landmark:
              type: string
              description: "Landmark"
              example: "Near Oxford Street"
            latitude:
              type: number
              format: float
              description: "Latitude"
              example: 51.5074
            longitude:
              type: number
              format: float
              description: "Longitude"
              example: -0.1278
      required: []
      additionalProperties: false
