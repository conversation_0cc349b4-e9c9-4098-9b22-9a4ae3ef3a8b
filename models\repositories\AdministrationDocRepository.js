const { Op } = require('sequelize');
const { Designation, DocumentRequire, sequelize } = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.validateAndCreateDocument = async (data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const { roleId, documents } = data;
  const selectFields = ['id', 'name'];
  const transaction = await sequelize.transaction();
  try {
    const role = await checkExistence(
      Designation,
      { id: roleId, organizationId },
      selectFields
    );
    if (!role) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`Role with Id: ${roleId} does not exist`)
      );
    }

    for (const document of documents) {
      const { name } = document;
      const existingDocument = await DocumentRequire.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
          designationId: roleId,
        },
      });
      if (existingDocument) {
        throw new Error(
          `Document with the name: ${name} already exists for the specified role.`
        );
      }
    }

    const documentsToCreate = documents.map((document) => ({
      ...document,
      designationId: roleId,
    }));

    const createdDocuments = await DocumentRequire.bulkCreate(
      documentsToCreate,
      { transaction }
    );

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Documents'),
      data: createdDocuments,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while creating the documents',
    };
  }
};

exports.replaceDocumentsByRoleId = async (roleId, data) => {
  const { documents } = data;
  const transaction = await sequelize.transaction();
  try {
    await DocumentRequire.destroy({
      where: { designationId: roleId },
      transaction,
    });

    for (const document of documents) {
      const { name } = document;
      const existingDocument = await DocumentRequire.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
          designationId: roleId,
        },
        transaction,
      });

      if (existingDocument) {
        throw new Error(
          `Document with the name: ${name} already exists for the specified role.`
        );
      }
    }

    const documentsWithRoleId = documents.map((document) => ({
      ...document,
      designationId: roleId,
    }));

    await DocumentRequire.bulkCreate(documentsWithRoleId, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Documents'),
      data: documents,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while replacing the Documents',
    };
  }
};

exports.deleteDocument = async (documentId) => {
  const transaction = await sequelize.transaction();
  try {
    const document = await checkExistence(DocumentRequire, { id: documentId }, [
      'id',
    ]);
    if (!document) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(
          `Document with id: ${documentId} does not exist`
        )
      );
    }

    await DocumentRequire.destroy({ where: { id: documentId }, transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Document'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the document',
    };
  }
};
