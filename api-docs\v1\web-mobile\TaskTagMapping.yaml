paths:
  /task/{id}/task-tag-mapping:
    post:
      summary: Creates a new TaskTagMapping
      description: Creates a new TaskTagMapping for specific Task
      operationId: createTaskTagMapping
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of Task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createTaskTagMapping"
      responses:
        "201":
          description: TaskTagMapping created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/{id}/{tagId}/task-tag-mapping:  
    delete:
      summary: Delete a TaskTag
      description: Delete TaskTag if you dont need it
      operationId: deleteTaskTag
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of task
        - name: tagId
          in: path
          required: true
          schema:
            type: integer
          description: ID of tag
      responses:
        "200":
          description: TaskTagMapping deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createTaskTagMapping:
      type: object
      required:
        - tagId
      properties:
        tagId:
          type: integer
          example: 1
          description: "The id of Tag"
        
