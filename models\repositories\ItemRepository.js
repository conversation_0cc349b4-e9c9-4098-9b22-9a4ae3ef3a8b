const {
  Item,
  ItemCategory,
  ItemVariant,
  ItemMedia,
  Tax,
  ItemTax,
  sequelize,
} = require('..');
const {
  successMessage,
  errorMessage,
  itemMediaType,
} = require('@config/options');
const { collectChanges, logActivities } = require('@helpers/ActivityLogHelper');

exports.validateAndCreateItem = async (data, loggedInUser) => {
  const { categoryId, variant: variants, taxIds, media } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const categoryExists = await ItemCategory.findOne({
      where: {
        id: categoryId,
        organizationId: organizationId,
      },
    });

    if (!categoryExists) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Category'));
    }

    const item = await Item.create(
      {
        ...data,
        createdBy: loggedInUser.id,
        organizationId: organizationId,
      },
      { transaction }
    );

    if (taxIds && taxIds.length > 0) {
      for (const taxId of taxIds) {
        const tax = await Tax.findOne({
          where: {
            id: taxId,
            organizationId: organizationId,
          },
        });

        if (!tax) {
          throw new Error(errorMessage.TAX_ID_INVALID(taxId));
        }

        await ItemTax.create(
          {
            itemId: item.id,
            taxId: taxId,
          },
          { transaction }
        );
      }
    }

    if (variants && variants.length > 0) {
      for (const variant of variants) {
        const createdVariant = await ItemVariant.create(
          {
            ...variant,
            itemId: item.id,
            createdBy: loggedInUser.id,
            organizationId: organizationId,
          },
          { transaction }
        );

        if (variant.media && variant.media.length > 0) {
          for (const mediaItem of variant.media) {
            await ItemMedia.create(
              {
                ...mediaItem,
                mediaType: itemMediaType.VARIANT,
                itemId: item.id,
                itemVariantId: createdVariant.id,
              },
              { transaction }
            );
          }
        }
      }
    }

    if (media && media.length > 0) {
      for (const mediaItem of media) {
        await ItemMedia.create(
          {
            ...mediaItem,
            mediaType: itemMediaType.ITEM,
            itemId: item.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Item'),
      data: item,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the item',
    };
  }
};

exports.validateAndCloneItem = async (itemId, loggedInUser) => {
  const transaction = await sequelize.transaction();
  try {
    // Find the existing item and include itemTax associations
    const existingItem = await Item.findOne({
      where: {
        id: itemId,
        organizationId: loggedInUser.currentOrganizationId,
      },
      include: [
        {
          model: ItemVariant,
          as: 'itemVariant',
          include: [
            {
              model: ItemMedia,
              as: 'itemVariantMedia',
              where: {
                mediaType: 'variant',
              },
            },
          ],
        },
        {
          model: ItemMedia,
          as: 'itemMedia',
          where: {
            mediaType: 'item',
          },
        },
        {
          model: Tax,
          as: 'itemTaxes',
          through: { attributes: [] },
        },
      ],
      transaction,
    });

    if (!existingItem) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item'));
    }

    // Clone the item name and prepare data for cloning
    const updatedItemName = `${existingItem.name} (Cloned from ID: ${existingItem.id})`;

    const itemData = existingItem.toJSON();
    delete itemData.id;
    delete itemData.createdAt;
    delete itemData.updatedAt;

    // Create cloned item
    const clonedItem = await Item.create(
      {
        ...itemData,
        name: updatedItemName,
        createdBy: loggedInUser.id,
        organizationId: loggedInUser.currentOrganizationId,
      },
      { transaction }
    );

    // Clone item tax associations
    if (existingItem.itemTaxes && existingItem.itemTaxes.length > 0) {
      for (const tax of existingItem.itemTaxes) {
        await ItemTax.create(
          {
            itemId: clonedItem.id,
            taxId: tax.id,
          },
          { transaction }
        );
      }
    }

    // Clone variants and their media (if any)
    if (existingItem.itemVariant && existingItem.itemVariant.length > 0) {
      for (const variant of existingItem.itemVariant) {
        const variantData = variant.toJSON();
        delete variantData.id;
        delete variantData.createdAt;
        delete variantData.updatedAt;

        const clonedVariant = await ItemVariant.create(
          {
            ...variantData,
            itemId: clonedItem.id,
            createdBy: loggedInUser.id,
            organizationId: loggedInUser.currentOrganizationId,
          },
          { transaction }
        );

        // Clone media associated with the variant
        if (variant.itemVariantMedia && variant.itemVariantMedia.length > 0) {
          for (const mediaItem of variant.itemVariantMedia) {
            const mediaData = mediaItem.toJSON();
            delete mediaData.id;
            delete mediaData.createdAt;
            delete mediaData.updatedAt;

            await ItemMedia.create(
              {
                ...mediaData,
                itemId: clonedItem.id,
                itemVariantId: clonedVariant.id,
              },
              { transaction }
            );
          }
        }
      }
    }

    // Clone media associated with the item
    if (existingItem.itemMedia && existingItem.itemMedia.length > 0) {
      for (const mediaItem of existingItem.itemMedia) {
        const mediaData = mediaItem.toJSON();
        delete mediaData.id;
        delete mediaData.createdAt;
        delete mediaData.updatedAt;

        await ItemMedia.create(
          {
            ...mediaData,
            itemId: clonedItem.id,
          },
          { transaction }
        );
      }
    }

    // Commit transaction if everything is successful
    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Item'),
      data: clonedItem,
    };
  } catch (error) {
    if (!transaction.finished) {
      await transaction.rollback();
    }
    return {
      success: false,
      message: error.message || 'An error occurred while cloning the item',
    };
  }
};

exports.validateAndUpdateItem = async (itemId, data, loggedInUser) => {
  const { categoryId, variant: variants, media, taxIds } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();

  try {
    const item = await Item.findOne({
      where: {
        id: itemId,
      },
    });

    if (!item) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item'));
    }

    // Collect changes
    const fieldsToCheck = [
      'name',
      'categoryId',
      'sku',
      'unitOfMeasurement',
      'hsn',
      'reOrderPoint',
      'reOrderPointScope',
      'description',
    ];
    const changes = await collectChanges(item, data, fieldsToCheck);

    // Log all changes at once
    await logActivities(item, changes, loggedInUser, transaction);

    // Validate and update category
    if (categoryId) {
      const categoryExists = await ItemCategory.findOne({
        where: {
          id: categoryId,
          organizationId: organizationId,
        },
      });

      if (!categoryExists) {
        throw new Error(errorMessage.DOES_NOT_EXIST('Category'));
      }
    }

    // Update item data
    data.updatedBy = loggedInUser.id;
    await item.update(data, { transaction });

    // Handle taxIds if provided
    if (taxIds && taxIds.length > 0) {
      await ItemTax.destroy({
        where: {
          itemId: itemId,
        },
        transaction,
      });

      for (const taxId of taxIds) {
        const validTax = await Tax.findOne({
          where: {
            id: taxId,
            organizationId: organizationId,
          },
        });

        if (!validTax) {
          throw new Error(errorMessage.TAX_ID_INVALID(taxId));
        }

        await ItemTax.create(
          {
            itemId: item.id,
            taxId: taxId,
          },
          { transaction }
        );
      }
    }

    // Handle variants and media for the item
    if (variants && variants.length > 0) {
      await ItemVariant.destroy({
        where: { itemId: itemId },
        transaction,
      });

      for (const variant of variants) {
        const createdVariant = await ItemVariant.create(
          {
            ...variant,
            itemId: item.id,
            createdBy: loggedInUser.id,
            organizationId: organizationId,
          },
          { transaction }
        );

        if (variant.media && variant.media.length > 0) {
          // Frontend is not sending the whole payload of media
          // await ItemMedia.destroy({
          //   where: {
          //     itemVariantId: createdVariant.id,
          //     mediaType: itemMediaType.VARIANT,
          //   },
          //   transaction,
          // });

          for (const mediaItem of variant.media) {
            await ItemMedia.create(
              {
                ...mediaItem,
                mediaType: itemMediaType.VARIANT,
                itemId: item.id,
                itemVariantId: createdVariant.id,
              },
              { transaction }
            );
          }
        }
      }
    }

    // Handle media for the item
    if (media && media.length > 0) {
      // Frontend is not sending the whole payload of media
      // await ItemMedia.destroy({
      //   where: {
      //     itemId: itemId,
      //     mediaType: itemMediaType.ITEM,
      //   },
      //   transaction,
      // });

      for (const mediaItem of media) {
        await ItemMedia.create(
          {
            ...mediaItem,
            itemId: item.id,
            mediaType: itemMediaType.ITEM,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Item'),
      data: item,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the item',
    };
  }
};

exports.validateAndUpdateItemStatus = async (itemId, data, loggedInUser) => {
  const transaction = await sequelize.transaction();
  try {
    const item = await Item.findOne({
      where: {
        id: itemId,
      },
    });

    if (!item) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item'));
    }

    // Collect changes
    const fieldsToCheck = ['status'];
    const changes = await collectChanges(item, data, fieldsToCheck);
    await logActivities(item, changes, loggedInUser, transaction);

    data.updatedBy = loggedInUser.id;
    await item.update(data, { transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Item Status'),
      data: item,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the item',
    };
  }
};

exports.validateAndDeleteItem = async (itemId) => {
  const transaction = await sequelize.transaction();
  try {
    const item = await Item.findOne({
      where: {
        id: itemId,
      },
    });

    if (!item) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item'));
    }

    await item.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Item'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the item',
    };
  }
};

exports.validateAndDeleteItemMedia = async (itemMediaId) => {
  const transaction = await sequelize.transaction();
  try {
    const itemMedia = await ItemMedia.findOne({
      where: {
        id: itemMediaId,
      },
    });

    if (!itemMedia) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item Media'));
    }

    await itemMedia.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Item Media'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the item',
    };
  }
};
