const { getAccountCategoryAndTypes } = require('@config/defaultData');
const {
  genRes,
  errorMessage,
  resCode,
  successMessage,
  transactionTypeForEntries,
} = require('@config/options');

const AccountRepository = require('@models/repositories/AccountRepository');
const TransactionEntryRepository = require('@models/repositories/TransactionEntryRepository');

exports.createAccount = async (req, res) => {
  try {
    const { user } = req;

    const { success, message, data } =
      await AccountRepository.validateAndCreateAccount({
        ...req.body,
        createdBy: user.id,
      });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyAccountCode = async (req, res) => {
  try {
    const {
      params: { code },
    } = req;

    const { success, message, data } =
      await AccountRepository.verifyUniqueAccountCode(code);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getAccountList = async (req, res) => {
  try {
    const { query } = req;

    const { success, message, data } =
      await AccountRepository.getAllAccountsData(query);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getTransactionEntries = async (req, res) => {
  try {
    const {
      params: { accountId },
      query: { startDate, endDate, transactionType, start, limit },
    } = req;

    const objParams = {
      accountId,
      startDate,
      endDate,
      transactionType,
      start,
      limit,
    };

    const { success, message, data } =
      await TransactionEntryRepository.getAllTransactionEntries(objParams);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getAccountById = async (req, res) => {
  try {
    const {
      params: { accountId },
    } = req;

    const { success, message, data } =
      await AccountRepository.getAllAccountsData({ accountId });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createDefaultAccount = async (req, res) => {
  try {
    const {
      query: { organizationId },
      user,
    } = req;

    const { success, message, data } =
      await AccountRepository.createDefaultAccount(organizationId, user.id);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getAccountCategoryTypeList = async (req, res) => {
  try {
    const arrAccountCategoryAndTypes = getAccountCategoryAndTypes();

    const data = arrAccountCategoryAndTypes.map((objEachAccountCategory) => {
      const tmpAccountCategory = {
        ...objEachAccountCategory,
      };

      tmpAccountCategory.arrAccountTypes =
        objEachAccountCategory.arrAccountTypes.map((objEachAccountType) => {
          const tmpAccountType = { ...objEachAccountType };

          delete tmpAccountType.arrDefaultAccounts;
          return tmpAccountType;
        });

      return tmpAccountCategory;
    });

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.ACCOUNT_CATEGORY_TYPE,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getAccountEnteryTypeList = async (req, res) => {
  try {
    const arrTransactionTypeForEntries = transactionTypeForEntries.getValues();

    const data = arrTransactionTypeForEntries.map((objEachTransactionType) => {
      let objReturn = {};
      switch (objEachTransactionType) {
        case transactionTypeForEntries.JOURNAL:
          objReturn = {
            label: 'Journal',
            value: transactionTypeForEntries.JOURNAL,
          };
          break;
        case transactionTypeForEntries.EXPENSE:
          objReturn = {
            label: 'Expense',
            value: transactionTypeForEntries.EXPENSE,
          };
          break;
        case transactionTypeForEntries.PAYMENT_RECEIVED:
          objReturn = {
            label: 'Payment Received',
            value: transactionTypeForEntries.PAYMENT_RECEIVED,
          };
          break;
      }
      return objReturn;
    });

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.ACCOUNT_ENTRY_TYPE,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateAccountDetails = async (req, res) => {
  try {
    const {
      user,
      params: { accountId },
    } = req;

    const { success, message, data } =
      await AccountRepository.updateAccountDetails({
        ...req.body,
        createdBy: user.id,
        accountId,
      });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteAccount = async (req, res) => {
  try {
    const {
      params: { accountId },
    } = req;

    const { success, message, data } =
      await AccountRepository.deleteAccount(accountId);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
