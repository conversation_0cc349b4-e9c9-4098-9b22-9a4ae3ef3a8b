paths:
  /settings/approval-workflow:
    post:
      summary: Creates a new Approval Workflow
      description: Creates a new Approval Workflow with the provided details.
      operationId: addApprovalWorkflow
      tags:
        - "Settings"
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AddApprovalWorkflow"
      responses:
        "201":
          description: Approval Workflow created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /settings/approval-workflow/{id}:
    put:
      summary: Updates an existing Approval Workflow
      description: Updates the Approval Workflow with the specified ID using the provided details.
      operationId: updateApprovalWorkflow
      tags:
        - "Settings"
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the Approval Workflow to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateApprovalWorkflow"
      responses:
        "200":
          description: Approval Workflow updated successfully
        "400":
          description: Invalid request
        "404":
          description: Approval Workflow not found
        "500":
          description: Internal Server Error

components:
  schemas:
    AddApprovalWorkflow:
      type: object
      properties:
        approver:
          type: string
          example: 'member'
        escalation:
          type: string
          example: 'role'
        isApprovalRequired:
          type: boolean
          example: false
        isEscalationRequired:
          type: boolean
          example: false
        activityName:
          type: string
          example: 'Boq approval'
        approverUserId:
          type: integer
          example: 1
        approverMemberId:
          type: integer
          example: 1
        escalationUserId:
          type: integer
          example: 1
        escalationDesignationId:
          type: integer
          example: 1
        approverDesignationId:
          type: integer
          example: 1
        organizationId:
          type: integer
          example: 1
      required:
        - approver
        - isApprovalRequired
        - isEscalationRequired
        - activityName

    UpdateApprovalWorkflow:
      type: object
      properties:
        approver:
          type: string
          example: 'member'
        isApprovalRequired:
          type: boolean
          example: false
        isEscalationRequired:
          type: boolean
          example: false
        activityName:
          type: string
          example: 'Boq approval'
        approverUserId:
          type: integer
          example: 1
        approverMemberId:
          type: integer
          example: 1
        escalationUserId:
          type: integer
          example: 1
        escalationDesignationId:
          type: integer
          example: 1
   