paths:
  /request/documents:
    post:
      summary: Add a Request Attachment
      description: Add a attachment for a existing Request
      operationId: addRequestAttachment
      tags:
        - Request
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addRequestAttachment"
      responses:
        "201":
          description: Attachment created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /request/documents/{id}:
    delete:
      summary: Delete a Attachment
      description: Delete Attachment if you dont need it
      operationId: deleteRequestAttachment
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of attachment
      responses:
        "201":
          description: Attachment deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    addRequestAttachment:
      type: object
      required:
        - requestId
        - document
      properties:
        requestId:
          type: integer
          example: 1
          description: "The id of request for the attachment"
        document:
          type: array
          items:
            type: object
            properties:
              fileType:
                type: string
                example: "pdf"
                description: "Type of the file"
              fileSize:
                type: integer
                example: 1024
                description: "Size of the file in bytes"
              filePath:
                type: string
                example: "/path/to/file"
                description: "Path of the file"
              fileName:
                type: string
                example: "example.pdf"
                description: "Name of the file"

  