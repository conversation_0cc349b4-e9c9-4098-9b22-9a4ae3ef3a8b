const { customerType, customerStatus } = require('@config/options');

exports.createCustomer = {
  type: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Customer type is required',
    },
    custom: {
      options: (value) => {
        const allowedValues = customerType.getCustomerTypeArray();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid customer type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid type provided',
    },
  },
  profilePicture: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Profile picture must be a string',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Business name must be a string',
    },
  },
  firstName: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'First name cannot be empty',
    },
    isString: {
      errorMessage: 'First name must be a string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Last name must be a string',
    },
  },
  email: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Email cannot be empty',
    },
    isEmail: {
      errorMessage: 'Email must be a valid email address',
    },
  },
  countryCode: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Country code cannot be empty',
    },
  },
  contactNumber: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Contact number cannot be empty',
    },
    isNumeric: {
      errorMessage: 'Contact number must be numeric',
    },
  },
  sourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Source ID must be a valid integer',
    },
  },
  subSourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'SubSource ID must be a valid integer',
    },
  },
};

exports.updateCustomer = {
  profilePicture: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Profile picture must be a string',
    },
  },
  type: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = customerType.getCustomerTypeArray();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid customer type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid type provided',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Business name must be a string',
    },
  },
  firstName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'First name must be a string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Last name must be a string',
    },
  },
  email: {
    in: ['body'],
    optional: true,
    isEmail: {
      errorMessage: 'Email must be a valid email address',
    },
  },
  countryCode: {
    in: ['body'],
    optional: true,
    isLength: {
      options: { max: 5 },
      errorMessage: 'Country code should be no longer than 5 characters',
    },
  },
  contactNumber: {
    in: ['body'],
    optional: true,
    isNumeric: {
      errorMessage: 'Contact number must be numeric',
    },
  },
  sourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Source ID must be a valid integer',
    },
  },
  subSourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'SubSource ID must be a valid integer',
    },
  },
  isArchived: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isArchived must be a boolean',
    },
  },
};

exports.updateCustomerStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Customer ID is required',
    },
    isString: {
      errorMessage: 'Customer ID must be a string',
    },
  },
  status: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Status is required',
    },
    custom: {
      options: (value) => {
        const allowedValues = customerStatus.getCustomerStatusArray();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid status provided',
    },
  },
};

exports.deleteCustomer = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Customer ID is required',
    },
    isString: {
      errorMessage: 'Customer ID must be a string',
    },
  },
};
