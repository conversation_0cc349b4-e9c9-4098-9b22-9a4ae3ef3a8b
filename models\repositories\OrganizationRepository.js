const { Op } = require('sequelize');
const {
  Organization,
  sequelize,
  TemplateEntity,
  TemplateMaster,
  UserOrganization,
  Designation,
  DocumentRequire,
  User,
} = require('..');
const {
  successMessage,
  errorMessage,
  activityType,
  organizationUpdateType,
  templateType,
  inviteStatus,
  addressType,
} = require('@config/options');
exports.findOne = async (query) => await Organization.findOne(query);
const { predefinedWorkFlow } = require('@config/defaultData');
const approvalWorkflowRepository = require('@repo/ApprovalWorkflowRepository');
const AddressRepository = require('@repo/AddressRepository');
const WarehouseRepository = require('@repo/WarehouseRepository');

exports.checkExistOrganizationName = async (name) => {
  try {
    const existingOrganization = await Organization.findOne({
      where: { name },
      attributes: ['id', 'name'],
    });

    return {
      success: !existingOrganization,
      message: existingOrganization
        ? errorMessage.ALREADY_EXIST('organization')
        : null,
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.checkDuplicate = async (body, id) => {
  try {
    return await this.findOne({
      where: {
        [Op.and]: [
          { name: body.name },
          ...(id ? [{ id: { [Op.not]: id } }] : []),
        ],
      },
    });
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndCreateOrganization = async (
  data,
  loggedInUser,
  countryCode,
  mobileNumber
) => {
  const transaction = await sequelize.transaction();
  const { organizationId } = loggedInUser;
  try {
    const organizationDetails = await Organization.findOne({
      where: { id: organizationId },
    });

    const workspaceId = organizationDetails?.workspaceId || null;

    const existingOrganizationCheck = await this.checkDuplicate({
      name: data.name,
    });

    if (existingOrganizationCheck) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Organization with same name'),
      };
    }

    if (data.gstinNumber) {
      const checkDuplicateGstin = await this.checkDuplicateGstinNumber(
        data.gstinNumber,
        organizationDetails?.id
      );
      if (checkDuplicateGstin.success) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(
            'Organization with same GSTIN number'
          ),
        };
      }
    }

    const createOrganization = {
      name: data.name,
      gstinNumber: data.gstinNumber,
      address: data.address,
      city: data.city,
      pincode: data.pincode,
      workspaceId: workspaceId,
      logo: data.logo,
    };

    if (countryCode != null) {
      createOrganization.countryCode = countryCode;
    }

    if (mobileNumber != null) {
      createOrganization.mobile = mobileNumber;
    }

    if (data.gstinDocument) {
      createOrganization.gstinDocument = data.gstinDocument;
    }

    const organization = await Organization.create(createOrganization, {
      transaction,
    });

    await AddressRepository.createOrUpdateAddressForOrganization(
      {
        address: data.address,
        city: data.city,
        pincode: data.pincode,
      },
      addressType.ORGANIZATION,
      organization.id,
      transaction
    );

    await UserOrganization.create(
      {
        userId: loggedInUser.id,
        designationId: loggedInUser.designationId,
        organizationId: organization.id,
        inviteStatus: inviteStatus.ACCEPTED,
        isPrimary: true,
      },
      { transaction }
    );

    await WarehouseRepository.createDefaultWarehouseByOrgId(
      organization.id,
      loggedInUser.id,
      transaction
    );

    await TemplateMaster.bulkCreate(
      [
        {
          templateType: templateType.SALARY,
          name: 'Default Salary Template',
          description: 'Default template for salary structure.',
          isDefault: true,
          organizationId: organization.id,
        },
        {
          templateType: templateType.LEAVE,
          name: 'Default Leave Template',
          description: 'Default template for leave policies.',
          isDefault: true,
          organizationId: organization.id,
        },
        {
          templateType: templateType.ATTENDANCE,
          name: 'Default Attendance Template',
          description: 'Default template for attendance policies.',
          isDefault: true,
          organizationId: organization.id,
          templateEntities: [
            {
              isVisible: true,
              name: 'Attendance not mandatory',
              calculationType: 'attendance_optional',
              entityType: 'attendance',
            },
            {
              isVisible: true,
              name: 'Mark manually',
              calculationType: 'attendance_mark_manually',
              entityType: 'attendance',
            },
            {
              isVisible: true,
              name: 'Without location limits',
              calculationType: 'attendance_without_location',
              entityType: 'attendance',
            },
            {
              isVisible: true,
              name: 'Within work location limits',
              calculationType: 'attendance_with_location',
              entityType: 'attendance',
            },
          ],
        },
        {
          templateType: templateType.UNPAID_LEAVE_DEDUCTION,
          name: 'Default Attendance Template',
          description: 'Default template for attendance policies.',
          isDefault: true,
          organizationId: organization.id,
          templateEntities: [
            {
              isVisible: true,
              name: 'Deduct manually ',
              calculationType: 'unpaid_deduction_manual',
              entityType: 'unpaid_leave',
            },
            {
              isVisible: true,
              name: 'Deduct fixed amount',
              calculation: 5000,
              calculationType: 'unpaid_deduction_fixed',
              entityType: 'unpaid_leave',
            },
            {
              isVisible: true,
              name: 'Deduct on prorate basis',
              calculationType: 'unpaid_deduction_prorated',
              entityType: 'unpaid_leave',
            },
          ],
        },
      ],
      {
        transaction,
        include: [{ model: TemplateEntity, as: 'templateEntities' }],
      }
    );

    const payload = await predefinedWorkFlow(organization.id);
    await approvalWorkflowRepository.addApprovalWorkflows(payload, transaction);

    await transaction.commit();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('organization'),
      data: organization,
    };
  } catch (error) {
    console.log('error...', error);
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.checkOrganizationExists = async (body) => {
  const query = {
    where: { id: body.organizationId },
  };

  const organization = await this.findOne(query);

  return {
    success: !organization,
    message: organization ? null : errorMessage.DOES_NOT_EXIST('organization'),
    data: organization,
  };
};

exports.getOrganizationDetails = async (id) => {
  const query = {
    where: { id },
  };

  const organization = await this.findOne(query);

  return {
    success: organization ? true : false,
    message: organization
      ? successMessage.FETCH_SUCCESS_MESSAGE('organization')
      : errorMessage.DOES_NOT_EXIST('organization'),
    data: organization,
  };
};

exports.updateOrganization = async (id, userId, updateData) => {
  let updateType = organizationUpdateType.LOCALISATION_INFORMATION;
  const transaction = await sequelize.transaction();
  try {
    const organization = await Organization.findOne({ where: { id } });

    if (!organization) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('organization'),
      };
    }

    if (updateData.gstinNumber) {
      const checkDuplicateGstin = await this.checkDuplicateGstinNumber(
        updateData.gstinNumber,
        organization.id
      );

      if (checkDuplicateGstin.success) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(
            'Organization with same GSTIN number'
          ),
        };
      }
    }

    if (updateData.name) {
      updateType = organizationUpdateType.BASIC_INFORMATION;

      const existingOrganizationCheck = await Organization.findOne({
        where: {
          name: updateData.name,
          id: {
            [Op.ne]: id,
          },
        },
      });

      if (existingOrganizationCheck) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST('Organization with same name'),
        };
      }
    }

    await organization.update(updateData, { transaction });

    await AddressRepository.createOrUpdateAddressForOrganization(
      {
        address: updateData?.address,
        city: updateData?.city,
        pincode: updateData?.pincode,
      },
      addressType.ORGANIZATION,
      organization.id,
      transaction
    );

    await organization.createActivity(
      {
        actionType: activityType.EDITED,
        activityDescription: `Updated Company's ${updateType}`,
        createdBy: userId,
      },
      { transaction }
    );
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('organization'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.checkDuplicateGstinNumber = async (
  gstinNumber,
  organizationId = null
) => {
  const existingOrganization = await Organization.findOne({
    where: {
      gstinNumber,
      ...(organizationId ? { id: { [Op.ne]: organizationId } } : {}),
    },
  });

  if (existingOrganization) {
    return {
      success: true,
      message: errorMessage.ALREADY_EXIST('organization gstin number'),
    };
  }

  return {
    success: false,
    message: errorMessage.DOES_NOT_EXIST('organization gstin number'),
  };
};

exports.getOrganizationByInviteId = async (inviteId) => {
  try {
    const organization = await UserOrganization.findOne({
      where: { id: inviteId },
      attributes: ['id'],
      include: [
        {
          model: Organization,
          attributes: ['id', 'name'],
          as: 'organization',
          required: false,
        },
        {
          model: Designation,
          attributes: ['id', 'name'],
          as: 'designation',
          required: false,
          include: [
            {
              model: DocumentRequire,
              as: 'documentRequires',
              attributes: ['id', 'name', 'isRequiredField', 'isUploadRequired'],
            },
          ],
        },
        {
          model: User,
          attributes: ['id', 'role'],
          as: 'user',
          required: false,
        },
      ],
    });

    if (!organization) {
      return {
        success: false,
        message: 'No organization found with the provided invite ID',
      };
    }

    return {
      success: true,
      data: organization,
      message: 'Organization retrieved successfully',
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.message || 'An error occurred while retrieving the organization',
    };
  }
};
