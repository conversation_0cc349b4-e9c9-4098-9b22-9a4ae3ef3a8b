'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('WbsActivity', 'wbsCategoryId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'WbsActivity',
        key: 'id',
      },
      allowNull: true,
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('WbsActivity', 'wbsCategoryId');
  },
};
