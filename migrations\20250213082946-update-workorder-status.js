'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_WorkOrder_status" ADD VALUE IF NOT EXISTS 'under_approval';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" DROP DEFAULT;
    `);
  },
};
