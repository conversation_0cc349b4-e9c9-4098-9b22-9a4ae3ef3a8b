const { ActivityLog } = require('..');
const { activityType } = require('@config/options');

// Function to fetch related records dynamically
const fetchRelatedRecords = async (modelInstance, fieldsToCheck) => {
  const relatedRecords = {};
  const modelDefinition = modelInstance.constructor; // Get model dynamically

  for (const field of fieldsToCheck) {
    if (field.endsWith('Id')) {
      // Find associated model using Sequelize associations
      const association = modelDefinition.associations
        ? Object.values(modelDefinition.associations).find(
            (assoc) => assoc.foreignKey === field
          )
        : null;

      if (association) {
        const relatedModel = association.target;
        const availableFields = Object.keys(relatedModel.rawAttributes);
        relatedRecords[field] = { model: relatedModel, availableFields };
      }
    }
  }

  return relatedRecords;
};

// Function to collect changes dynamically for specified fields
exports.collectChanges = async (modelInstance, newData, fieldsToCheck) => {
  const changes = [];
  const relatedRecords = await fetchRelatedRecords(
    modelInstance,
    fieldsToCheck
  );

  for (const field of fieldsToCheck) {
    const oldValue = modelInstance[field];
    const newValue = newData[field];

    // Skip logging if newValue is undefined
    if (newValue === undefined) continue;

    if (relatedRecords[field]) {
      // Fetch only name-related fields dynamically
      const { model, availableFields } = relatedRecords[field];

      const nameFields = availableFields.filter((attr) =>
        ['name', 'title', 'workOrderNumber'].includes(attr)
      );

      if (nameFields.length > 0) {
        const oldRecord = oldValue
          ? await model.findByPk(oldValue, { attributes: nameFields })
          : null;
        const newRecord = newValue
          ? await model.findByPk(newValue, { attributes: nameFields })
          : null;

        // Compare only name-related fields
        nameFields.forEach((attr) => {
          const oldAttrValue = oldRecord ? oldRecord[attr] : null;
          const newAttrValue = newRecord ? newRecord[attr] : null;

          if (oldAttrValue !== newAttrValue) {
            changes.push({
              fieldName: field.replace('Id', ''), // e.g., "category"
              oldValue: oldAttrValue,
              newValue: newAttrValue,
            });
          }
        });
      }
    } else if (newValue !== undefined && newValue !== oldValue) {
      // If it's a normal field, track changes normally
      changes.push({
        fieldName: field,
        oldValue: oldValue,
        newValue: newValue,
      });
    }
  }

  return changes;
};

// Function to log activities for the specified changes
exports.logActivities = async (
  modelInstance,
  changes,
  loggedInUser,
  transaction
) => {
  if (!changes.length) return;

  const logs = changes.map(({ fieldName, oldValue, newValue }) => ({
    actionType: activityType.EDITED,
    activityDescription: `Updated ${fieldName} from ${oldValue} to ${newValue}`,
    createdBy: loggedInUser.id,
    recordId: modelInstance.id,
    activityOn: modelInstance.constructor.name,
  }));

  await ActivityLog.bulkCreate(logs, { transaction });
};
