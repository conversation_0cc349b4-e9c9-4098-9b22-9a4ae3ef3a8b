'use strict';
const { priceChargeCalculationType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('QuotationUnitCost', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      quotationId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Quotation',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      chargeType: {
        type: Sequelize.ENUM(
          ...priceChargeCalculationType.priceChargeCalculationTypeArray()
        ),
        allowNull: false,
      },
      rate: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      isTaxable: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      taxRate: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: true,
      },
      hsnCode: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      taxAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('QuotationUnitCost');
  },
};
