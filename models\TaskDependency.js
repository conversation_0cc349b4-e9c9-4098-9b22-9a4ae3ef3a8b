const { dependencyType, dependencyStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const TaskDependency = sequelize.define(
    'TaskDependency',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      dependencyType: {
        type: DataTypes.ENUM(dependencyType.getValues()),
        allowNull: false,
        defaultValue: dependencyType.BLOCKING,
      },
      status: {
        type: DataTypes.ENUM(dependencyStatus.getValues()),
        allowNull: false,
        defaultValue: dependencyStatus.FF,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TaskDependency.associate = (models) => {
    TaskDependency.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'task',
    });

    TaskDependency.belongsTo(models.Tasks, {
      foreignKey: 'dependentTaskId',
      as: 'dependentTask',
    });
  };

  return TaskDependency;
};
