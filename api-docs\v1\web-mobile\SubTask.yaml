paths:
  /task/{id}/subtask:
    post:
      summary: Creates a new SubTask
      description: Creates a new SubTask for specific Task
      operationId: createSubTask
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of Task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSubTask"
      responses:
        "201":
          description: SubTask created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/subtask/{id}:
    patch:
      summary: Update Existing SubTask
      description: Update a Existing SubTask with Id
      operationId: updateSubTask
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of SubTask
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateSubTask"
      responses:
        "200":
          description: Custom Tag updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    
    delete:
      summary: Delete a SubTask
      description: Delete SubTask if you dont need it
      operationId: deleteSubTask
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of SubTask
      responses:
        "200":
          description: SubTask deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createSubTask:
      type: object
      required:
        - title
        - status
      properties:
        title:
          type: string
          example: "subTask Title"
          description: "The title of the subtask"
        status:
          type: string
          example: "pending"
          description: "The status of the sub task, which must be 'pending','completed'"
        startDate:
          type: string
          format: date-time
          example: "2025-01-16 10:00:00"
          description: "Start date of the sub task"
        dueDate:
          type: string
          format: date-time
          example: "2025-01-20 18:00:00"
          description: "End date of the sub task"

    updateSubTask:
      type: object
      properties:
        title:
          type: string
          example: "subTask Title"
          description: "The title of the subtask"
        status:
          type: string
          example: "pending"
          description: "The status of the sub task, which must be 'pending','completed'"
        startDate:
          type: string
          format: date-time
          example: "2025-01-16 10:00:00"
          description: "Start date of the sub task"
        dueDate:
          type: string
          format: date-time
          example: "2025-01-20 18:00:00"
          description: "End date of the sub task"