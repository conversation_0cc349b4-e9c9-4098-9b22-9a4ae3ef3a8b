'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment" 
      ALTER COLUMN status DROP DEFAULT,
      ALTER COLUMN status TYPE VARCHAR(255);
    `);

    // Create the new enum type with updated values
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_StockAdjustment_status";
      CREATE TYPE "enum_StockAdjustment_status" AS ENUM (
        'draft',
        'pending_approval',
        'approved',
        'rejected',
        'adjusted',
        'cancelled',
        'escalated'
      );
    `);

    // Update existing records to use new status values
    await queryInterface.sequelize.query(`
      UPDATE "StockAdjustment"
      SET status = CASE 
        WHEN status IN ('pending', 'due_soon', 'delayed', 'ongoing') THEN 'pending_approval'
        ELSE 'draft'
      END;
    `);

    // Set the column type back to enum with the new type and default
    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment"
      ALTER COLUMN status TYPE "enum_StockAdjustment_status" USING status::"enum_StockAdjustment_status",
      ALTER COLUMN status SET DEFAULT 'pending_approval';
    `);
  },

  async down(queryInterface, Sequelize) {
    // First, remove the enum type constraints
    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment" 
      ALTER COLUMN status DROP DEFAULT,
      ALTER COLUMN status TYPE VARCHAR(255);
    `);

    // Create the old enum type
    await queryInterface.sequelize.query(`
      DROP TYPE IF EXISTS "enum_StockAdjustment_status";
      CREATE TYPE "enum_StockAdjustment_status" AS ENUM (
        'pending',
        'due_soon',
        'delayed',
        'ongoing',
        'draft'
      );
    `);

    // Update existing records to use old status values
    await queryInterface.sequelize.query(`
      UPDATE "StockAdjustment"
      SET status = CASE 
        WHEN status IN ('pending_approval', 'approved', 'rejected', 'adjusted', 'cancelled', 'escalated') THEN 'pending'
        ELSE status
      END;
    `);

    // Set the column type back to enum with the old type and default
    await queryInterface.sequelize.query(`
      ALTER TABLE "StockAdjustment"
      ALTER COLUMN status TYPE "enum_StockAdjustment_status" USING status::"enum_StockAdjustment_status",
      ALTER COLUMN status SET DEFAULT 'pending';
    `);
  },
};
