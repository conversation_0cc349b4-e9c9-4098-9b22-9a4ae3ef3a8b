'use strict';

module.exports = (sequelize, DataTypes) => {
  const ItemTax = sequelize.define(
    'ItemTax',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      itemId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Item',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      taxId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Tax',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  return ItemTax;
};
