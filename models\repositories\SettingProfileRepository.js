const { User, Employee, Address } = require('..');
const {
  successMessage,
  errorMessage,
  addressType,
} = require('@config/options');
const UserRepository = require('@models/repositories/UserRepository');
const AddressRepository = require('@models/repositories/AddressRepository');
const { usersRoles } = require('@config/options');
const { Op } = require('sequelize');
const BankDetailsRepository = require('@repo/BankDetailsRepository');

exports.updateUserProfile = async (userId, data) => {
  try {
    const existingUser = await User.findByPk(userId, {
      include: [
        {
          model: Address,
          as: 'address',
          required: false,
        },
      ],
    });

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NOT_FOUND('User'),
      };
    }

    const updateUser = await UserRepository.createAndUpdateUser(
      data,
      existingUser.id
    );

    let addressTypeData = 'employee';

    if (existingUser.role === 'EMPLOYEE') {
      addressTypeData = addressType.EMPLOYEE;
    }

    await AddressRepository.createOrUpdateAddress(
      data.addressDetails,
      addressTypeData,
      existingUser,
      existingUser.address ? existingUser.address.id : null
    );

    if (existingUser.role === usersRoles.EMPLOYEE) {
      const existingEmployee = await Employee.findOne({
        where: { userId: existingUser.id },
      });

      if (!existingEmployee) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Employee details'),
        };
      }

      if (data.employeeCode) {
        const isEmployeeCodeTaken = await Employee.findOne({
          where: {
            employeeCode: data.employeeCode,
            userId: { [Op.ne]: existingUser.id },
          },
        });

        if (isEmployeeCodeTaken) {
          return {
            success: false,
            message: errorMessage.ALREADY_EXIST('Employee code'),
          };
        }
      }

      existingEmployee.employeeCode = data.employeeCode;
      existingEmployee.dateOfJoining = data.dateOfJoining;
      existingEmployee.maritalStatus = data.maritalStatus;
      existingEmployee.reportedTo = data.reportedTo;
      await existingEmployee.save();
    }

    if (data.bankDetails) {
      await BankDetailsRepository.updateBankDetails(
        data.bankDetails,
        existingUser.id,
        userId
      );
    }

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE(
        'User and Employee details'
      ),
      data: { updateUser },
    };
  } catch (error) {
    throw new Error(error);
  }
};
