exports.createItemCategory = {
  name: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
};

exports.updateItemCategory = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'ItemCategoryId is required',
    },
    isInt: {
      errorMessage: 'ItemCategoryId must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
};

exports.deleteItemCategory = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'ItemCategoryId is required',
    },
    isInt: {
      errorMessage: 'ItemCategoryId must be a valid integer',
    },
  },
};
