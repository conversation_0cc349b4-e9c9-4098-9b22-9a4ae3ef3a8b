const { RequestComment, Request } = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.findOne = async (query) => await RequestComment.findOne(query);

exports.addComment = async (loggedInUser, data) => {
  try {
    const request = await checkExistence(Request, {
      id: data.requestId,
    });
    if (!request) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          'Request with ID: ' + data.requestId
        ),
      };
    }

    const requestCommentPayload = {
      userId: loggedInUser.id,
      ...data,
    };

    const comment = await RequestComment.create(requestCommentPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('comment'),
      data: comment,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateRequestComment = async (commentId, loggedInUser, data) => {
  try {
    const comment = await RequestComment.findOne({
      where: {
        id: commentId,
        userId: loggedInUser.id,
      },
    });
    if (!comment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Comment with ID: ' + commentId),
      };
    }

    Object.assign(comment, data);
    await comment.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('comment'),
      data: comment,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteRequestComment = async (loggedInUser, commentId) => {
  try {
    const comment = await RequestComment.findOne({
      where: {
        id: commentId,
        userId: loggedInUser.id,
      },
    });
    if (!comment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Comment with ID: ' + commentId),
      };
    }

    await comment.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('comment'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
