const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const AttendanceController = require('@controllers/v1/Attendance');
const AttendanceSchema = require('@schema-validation/Attendance');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/checkin',
  checkSchema(AttendanceSchema.checkInValidation),
  ErrorHandleHelper.requestValidator,
  AttendanceController.createCheckIn
);

router.post(
  '/checkout',
  ErrorHandleHelper.requestValidator,
  AttendanceController.createCheckOut
);

router.get(
  '/list',
  ErrorHandleHelper.requestValidator,
  AttendanceController.listOfAttendance
);

router.get(
  '/user-list',
  ErrorHandleHelper.requestValidator,
  AttendanceController.listOfEmployeeAttendance
);

router.get(
  '/list-by-date',
  ErrorHandleHelper.requestValidator,
  AttendanceController.listOfAttendanceByDate
);

router.patch(
  '/update',
  ErrorHandleHelper.requestValidator,
  AttendanceController.updateAttendance
);

router.get(
  '/status-count',
  ErrorHandleHelper.requestValidator,
  AttendanceController.attendanceStatusCount
);

router.get(
  '/status-dropdown',
  ErrorHandleHelper.requestValidator,
  AttendanceController.attendanceStatusDropdown
);

module.exports = router;
