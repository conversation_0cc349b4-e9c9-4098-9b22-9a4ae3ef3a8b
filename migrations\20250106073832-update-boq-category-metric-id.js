module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('BOQCategory', 'boqMetricId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'BOQMetric',
        key: 'id',
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('BOQCategory', 'boqMetricId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'BOQMetric',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    });
  },
};
