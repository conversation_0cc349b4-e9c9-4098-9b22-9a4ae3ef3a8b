const { defaultStatus } = require('@config/options');

exports.createDocument = {
  'document.*.parentFolderId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'parent folder ID is required',
    },
    isInt: {
      errorMessage: 'parent folder ID must be number',
    },
  },
  'document.*.name': {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'Document name cannot be empty',
    },
    isString: {
      errorMessage: 'Document name must be string',
    },
  },
  'document.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  'document.*.projectId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'project ID is required',
    },
    isInt: {
      errorMessage: 'Project ID must be number',
    },
  },
  'document.*.isFolder': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'isFolder is required',
    },
    isBoolean: {
      errorMessage: 'isFolder must be boolean',
    },
  },
  'document.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    custom: {
      options: (value, { req, path }) => {
        const index = path.match(/\d+/)[0];
        return req.body.document[index].isFolder === false ? !!value : true;
      },
      errorMessage: 'fileType is required for documents',
    },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'document.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    custom: {
      options: (value, { req, path }) => {
        const index = path.match(/\d+/)[0];
        return req.body.document[index].isFolder === false ? !!value : true;
      },
      errorMessage: 'fileSize is required for documents',
    },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'document.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    custom: {
      options: (value, { req, path }) => {
        const index = path.match(/\d+/)[0];
        return req.body.document[index].isFolder === false ? !!value : true;
      },
      errorMessage: 'filePath is required for documents',
    },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'document.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    custom: {
      options: (value, { req, path }) => {
        const index = path.match(/\d+/)[0];
        return req.body.document[index].isFolder === false ? !!value : true;
      },
      errorMessage: 'fileName is required for documents',
    },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
  'permissions.*.userId': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'userId is required',
    },
    isInt: {
      errorMessage: 'userId must be integer',
    },
  },
  'permissions.*.canView': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canView is required',
    },
    isBoolean: {
      errorMessage: 'canView must be boolean',
    },
  },
  'permissions.*.canEdit': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canEdit is required',
    },
    isBoolean: {
      errorMessage: 'canEdit must be boolean',
    },
  },
  'permissions.*.canDelete': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canDelete is required',
    },
    isBoolean: {
      errorMessage: 'canDelete must be boolean',
    },
  },
};

exports.updateDocument = {
  name: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'Document name cannot be empty',
    },
    isString: {
      errorMessage: 'Document name must be string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  isFavorites: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'isFavorites is required',
    },
    isBoolean: {
      errorMessage: 'isFavorites must be boolean',
    },
  },
  'permissions.*.userId': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'userId is required',
    },
    isInt: {
      errorMessage: 'userId must be integer',
    },
  },
  'permissions.*.canView': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canView is required',
    },
    isBoolean: {
      errorMessage: 'canView must be boolean',
    },
  },
  'permissions.*.canEdit': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canEdit is required',
    },
    isBoolean: {
      errorMessage: 'canEdit must be boolean',
    },
  },
  'permissions.*.canDelete': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canDelete is required',
    },
    isBoolean: {
      errorMessage: 'canDelete must be boolean',
    },
  },
};

exports.generateUrl = {
  fileName: {
    in: ['query'],
    notEmpty: true,
    errorMessage: 'File name cannot be empty',
  },
  parentFolderId: {
    in: ['query'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'project ID is required',
    },
    isInt: {
      errorMessage: 'parentFolderId must be an integer',
    },
    toInt: true,
  },
};

exports.getDocumentOrActivity = {
  userId: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'userEmployeeId must be an integer',
    },
  },
  peopleIds: {
    in: ['query'],
    optional: true,
    custom: {
      options: (value, { req }) => {
        if (!Array.isArray(value)) {
          return [value];
        }
        return value;
      },
    },
  },
  projectIds: {
    in: ['query'],
    optional: true,
    custom: {
      options: (value, { req }) => {
        if (!Array.isArray(value)) {
          return [value];
        }
        return value;
      },
    },
  },
  isFolder: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isBoolean: {
      errorMessage: 'isFolder must be a boolean',
    },
  },
  parentFolderId: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'parentFolderId must be an integer',
    },
  },
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
  isFavorites: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'isFavorites is required',
    },
    isBoolean: {
      errorMessage: 'isFavorites must be boolean',
    },
  },
  search: {
    in: ['query'],
    notEmpty: false,
  },
  status: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [defaultStatus.getDefaultStatusArray()],
      errorMessage:
        'status must be one of the following values: active, archived, deleted',
    },
  },
};

exports.addOrUpdateUserInDocument = {
  userId: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'userId is required',
    },
    isInt: {
      errorMessage: 'userId must be integer',
    },
  },
  canView: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canView is required',
    },
    isBoolean: {
      errorMessage: 'canView must be boolean',
    },
  },
  canEdit: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canEdit is required',
    },
    isBoolean: {
      errorMessage: 'canEdit must be boolean',
    },
  },
  canDelete: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'canDelete is required',
    },
    isBoolean: {
      errorMessage: 'canDelete must be boolean',
    },
  },
};

exports.downloadDocument = {
  documentId: {
    in: ['query'],
    notEmpty: {
      errorMessage: 'documentId is required',
    },
    isInt: {
      errorMessage: 'documentId must be an integer',
    },
  },
};
