const sequelize = require('sequelize');
const { Op } = sequelize;
const { AccountData, Expense, User } = require('..');
const {
  successMessage,
  recordStatus,
  expenseState: expenseStateOptions,
} = require('@config/options');
const options = require('@config/options');
const { handleTransactionsEnteries } = require('./TransactionEntryRepository');

exports.getAccount = async (query) => await Expense.findOne(query);

exports.findAndCountAll = async (query) => await Expense.findAndCountAll(query);

exports.createExpense = async (objParams) => {
  try {
    const createdExpense = await Expense.create(objParams);

    const objPramsForTransactionEnteriesExpene = {
      transactionId: 0,
      accountId: objParams.expenseAccountId,
      description: `Expense Account - ${objParams.notes}`,
      creditAmount: objParams.amount,
      debitAmount: 0,
      createdBy: objParams.createdBy,
      referenceId: createdExpense.id,
      referenceType: options.transactionTypeForEntries.EXPENSE,
    };
    await handleTransactionsEnteries(objPramsForTransactionEnteriesExpene);

    const objPramsForTransactionEnteriesPaidThrough = {
      transactionId: 0,
      accountId: objParams.paidThroughAccountId,
      description: `Paid through Account - ${objParams.notes}`,
      creditAmount: 0,
      debitAmount: objParams.amount,
      createdBy: objParams.createdBy,
      referenceId: createdExpense.id,
      referenceType: options.transactionTypeForEntries.EXPENSE,
    };
    await handleTransactionsEnteries(objPramsForTransactionEnteriesPaidThrough);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Expense'),
      data: createdExpense,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getAllExpenseData = async (objParams) => {
  try {
    const {
      organizationId = null,
      search = '',
      start = 0,
      limit = 10,
      expenseState = expenseStateOptions.APPROVED,
      expenseAccountId = null,
      vendorId = null,
      paidThroughAccountId = null,
      expenseId = null,
    } = objParams;

    const query = {
      where: {
        ...(organizationId && { organizationId }),
        ...(expenseAccountId && { expenseAccountId }),
        ...(vendorId && { vendorId }),
        ...(paidThroughAccountId && { paidThroughAccountId }),
        ...(expenseId && { id: expenseId }),
        ...(expenseState && { expenseState }),
        status: recordStatus.ACTIVE,
      },
      include: [
        {
          model: User,
          as: 'createdByUser',
          attributes: ['id', 'firstName', 'middleName', 'lastName'],
        },
        {
          model: AccountData,
          as: 'expenseAccount',
          required: false,
          where: {
            status: recordStatus.ACTIVE,
          },
        },
        {
          model: AccountData,
          as: 'paidThroughAccount',
          required: false,
          where: {
            status: recordStatus.ACTIVE,
          },
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
    };

    if (search) {
      query.where = {
        ...query.where,
        [Op.or]: [
          { invoiceNumber: { [Op.iLike]: `%${search}%` } },
          { notes: { [Op.iLike]: `%${search}%` } },
        ],
      };
    }

    const { rows, count } = await this.findAndCountAll(query, { logger: true });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Expense'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteExpense = async (expenseId) => {
  try {
    await Expense.update(
      {
        status: recordStatus.DELETED,
      },
      {
        where: {
          id: expenseId,
        },
      }
    );

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Expense'),
      data: {},
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateExpense = async (data) => {
  try {
    console.log(data);
    const { expenseId } = data;
    const createdAmenity = await Expense.update(
      { ...data },
      {
        where: {
          id: expenseId,
        },
      }
    );

    // TODO : need to update account transaction entry

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Expense'),
      data: createdAmenity,
    };
  } catch (error) {
    throw new Error(error);
  }
};
