'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION set_base_path()
      RETURNS TRIGGER AS $$
      BEGIN

        IF NEW."parentFolderId" IS NOT NULL THEN

          SELECT "basePath" INTO NEW."basePath"
          FROM "Document"
          WHERE id = NEW."parentFolderId";
          
          IF NEW."basePath" IS NOT NULL THEN
            NEW."basePath" := CONCAT(NEW."basePath", '/', NEW."parentFolderId");
          ELSE
            NEW."basePath" := CONCAT('/', NEW."parentFolderId");
          END IF;
        ELSE
          NEW."basePath" := NULL;
        END IF;

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await queryInterface.sequelize.query(`
      CREATE TRIGGER set_base_path_trigger
      BEFORE INSERT ON "Document"
      FOR EACH ROW
      EXECUTE FUNCTION set_base_path();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      'DROP TRIGGER IF EXISTS set_base_path_trigger ON "Document";'
    );
    await queryInterface.sequelize.query(
      'DROP FUNCTION IF EXISTS set_base_path;'
    );
  },
};
