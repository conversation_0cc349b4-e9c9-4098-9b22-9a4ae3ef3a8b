paths:
  /material/item-category:
    post:
      tags:
        - "Material"
      summary: "Create a new item category"
      description: "This endpoint allows you to create a new item category in the system"
      operationId: "CreateItemCategory"
      requestBody:
        description: "Item category creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateItemCategoryRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Item category created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/item-category/{id}:
    patch:
      tags:
        - "Material"
      summary: "Update an existing item category"
      description: "This endpoint allows you to update an existing item category in the system"
      operationId: "UpdateItemCategory"
      parameters:
        - $ref: "#/components/parameters/ItemCategoryIdParam"
      requestBody:
        description: "Item category update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateItemCategoryRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Item category updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Item category not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing item category"
      description: "This endpoint allows you to delete an existing item category in the system"
      operationId: "DeleteItemCategory"
      parameters:
        - $ref: "#/components/parameters/ItemCategoryIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Item category deleted successfully"
        "404":
          description: "Item category not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateItemCategoryRequest:
      type: object
      properties:
        name:
          type: string
          example: "Electronics"
      required:
        - name
    UpdateItemCategoryRequest:
      type: object
      properties:
        name:
          type: string
          example: "Electronics"
  parameters:
    ItemCategoryIdParam:
      name: "id"
      in: "path"
      description: "ID of the item category to be managed"
      required: true
      schema:
        type: string
