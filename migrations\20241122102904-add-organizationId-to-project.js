'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Project', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Project', 'organizationId');
  },
};
