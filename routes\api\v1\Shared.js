const express = require('express');

const router = express.Router();
const { checkSchema } = require('express-validator');

const SharedControl = require('../../../controllers/api/v1/Shared');
const AuthHandler = require('../../../models/helpers/AuthHelper');
const roles = require('../../../config/options').usersRoles;
const SharedSchema = require('../../../schema-validation/Shared');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');
const AWSHelpers = require('../../../models/helpers/AWSHelper');

const AssetController = require('@controllers/v1/Shared');
const AssetSchema = require('@schema-validation/Asset');

router.post(
  '/upload',
  AuthHandler.authenticateJWT(roles.getAllRolesAsArray()),
  AWSHelpers.postUpload.single('file'),
  SharedControl.postUploadMedia
);

router.get(
  '/signed-url',
  AuthHandler.authenticateJWT(roles.getAllRolesAsArray()),
  checkSchema(SharedSchema.generateUrl),
  ErrorHandleHelper.requestValidator,
  SharedControl.getPostSignedURL
);

router.get(
  '/pincode/:pincode',
  AuthHandler.authenticateJWT(roles.getAllRolesAsArray()),
  SharedControl.searchByPincode
);

router.post(
  '/asset',
  AuthHandler.authenticateJWT(),
  checkSchema(AssetSchema.createAsset),
  ErrorHandleHelper.requestValidator,
  AssetController.createAsset
);

router.put(
  '/asset/:id',
  AuthHandler.authenticateJWT(),
  checkSchema(AssetSchema.updateAsset),
  ErrorHandleHelper.requestValidator,
  AssetController.updateAsset
);

router.delete(
  '/asset/:id',
  AuthHandler.authenticateJWT(),
  checkSchema(AssetSchema.deleteAsset),
  ErrorHandleHelper.requestValidator,
  AssetController.deleteAsset
);

module.exports = router;
