const {
  Tasks,
  TaskTags,
  Unit,
  Project,
  User,
  SubTasks,
  TaskDependency,
  TaskTagMapping,
  TaskUnitMapping,
  TaskFollowers,
  Designation,
  TaskComments,
  TaskDocuments,
} = require('..');
const {
  successMessage,
  errorMessage,
  activityType,
} = require('@config/options');
const { Op } = require('sequelize');

exports.createTask = async (data, loggedInUser) => {
  const transaction = await Tasks.sequelize.transaction();
  try {
    if (data.projectId) {
      const project = await Project.findByPk(data.projectId);
      if (!project) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Project with id ${data.projectId}`
          ),
        };
      }
    }

    const userValidation = async (userId, label) => {
      if (userId) {
        const user = await User.findByPk(userId);
        if (!user) {
          throw new Error(
            errorMessage.DOES_NOT_EXIST(`${label} with id ${userId}`)
          );
        }
      }
    };
    await userValidation(data.assignedTo, 'Assigned To User');
    await userValidation(data.assignedBy, 'Assigned By User');

    if (data.taskTagsIds?.length) {
      const existingTags = await TaskTags.findAll({
        where: { id: { [Op.in]: data.taskTagsIds } },
      });
      if (existingTags.length !== data.taskTagsIds.length) {
        throw new Error(errorMessage.DOES_NOT_EXIST('One or more Task Tags'));
      }
    }

    if (data.unitIds?.length) {
      const existingUnits = await Unit.findAll({
        where: { id: { [Op.in]: data.unitIds }, projectId: data.projectId },
      });
      if (existingUnits.length !== data.unitIds.length) {
        throw new Error(errorMessage.DOES_NOT_EXIST('One or more Units'));
      }
    }

    if (data.followers?.length) {
      const followers = await User.findAll({
        where: { id: { [Op.in]: data.followers } },
      });
      if (followers.length !== data.followers.length) {
        throw new Error(errorMessage.DOES_NOT_EXIST('One or more Followers'));
      }
    }

    // if (data.startDate && data.endDate) {
    //   const startDate = new Date(data.startDate);
    //   const endDate = new Date(data.endDate);
    //   const now = new Date();

    //   console.log('startDate', startDate);
    //   console.log('endDate', endDate);
    //   console.log('now', now);

    //   if (startDate >= endDate) {
    //     console.log('case 1...');
    //     throw new Error('Start date must be earlier than end date.');
    //   }
    //   if (startDate < now) {
    //     console.log('case 2...');
    //     throw new Error('Start date must be in the future or today.');
    //   }
    // }

    if (data.startDate && data.endDate) {
      const startDate = new Date(data.startDate);
      const endDate = new Date(data.endDate);
      const now = new Date();

      // Set time to midnight for comparison
      startDate.setHours(0, 0, 0, 0);
      endDate.setHours(0, 0, 0, 0);
      now.setHours(0, 0, 0, 0);

      if (startDate >= endDate) {
        throw new Error('Start date must be earlier than end date.');
      }
      if (startDate < now) {
        throw new Error('Start date must be in the future or today.');
      }
    }

    if (data.dependencies?.length) {
      const dependencyTaskIds = data.dependencies.map((dep) => dep.taskId);
      const existingTasks = await Tasks.findAll({
        where: { id: { [Op.in]: dependencyTaskIds } },
      });
      if (existingTasks.length !== dependencyTaskIds.length) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST('One or more Dependency Tasks')
        );
      }
    }

    // Create Task
    const taskPayload = {
      ...data,
      organizationId: data.organizationId ?? loggedInUser.currentOrganizationId,
      createdBy: loggedInUser.id,
    };
    const task = await Tasks.create(taskPayload, { transaction });

    // Create Task Tags
    if (data.taskTagsIds?.length) {
      const taskTagMappings = data.taskTagsIds.map((tagId) => ({
        taskId: task.id,
        tagId: tagId,
      }));
      await TaskTagMapping.bulkCreate(taskTagMappings, { transaction });
    }

    // Create Task Units
    if (data.unitIds?.length) {
      const taskUnitMappings = data.unitIds.map((unitId) => ({
        taskId: task.id,
        unitId: unitId,
      }));
      await TaskUnitMapping.bulkCreate(taskUnitMappings, { transaction });
    }

    // Create SubTasks
    if (data.subTasks?.length) {
      const subTasksPayload = data.subTasks.map((subTask) => ({
        ...subTask,
        taskId: task.id,
        createdBy: loggedInUser.id,
      }));
      await SubTasks.bulkCreate(subTasksPayload, { transaction });
    }

    // Create Dependencies
    if (data.dependencies?.length) {
      const dependenciesPayload = data.dependencies.map((dependency) => ({
        ...dependency,
        dependentTaskId: dependency.taskId,
        taskId: task.id,
        createdBy: loggedInUser.id,
      }));
      await TaskDependency.bulkCreate(dependenciesPayload, { transaction });
    }

    // Create followers
    if (data.followers?.length) {
      const taskFollowersMappings = data.followers.map((followerId) => ({
        taskId: task.id,
        userId: followerId,
      }));
      await TaskFollowers.bulkCreate(taskFollowersMappings, { transaction });
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Task'),
      data: task,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the task.',
    };
  }
};

exports.updateTask = async (taskId, data, loggedInUser) => {
  const transaction = await Tasks.sequelize.transaction();
  try {
    const task = await Tasks.findByPk(taskId);
    if (!task) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Task with ID: ' + taskId),
      };
    }

    if (data.status && data.status !== task.status) {
      await task.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated status ${task.status} to ${data.status}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.assignedTo && data.assignedTo !== task.assignedTo) {
      const assignedToUser = await User.findByPk(data.assignedTo);
      if (!assignedToUser) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Assigned To User with id ${data.assignedTo}`
          )
        );
      }
      await task.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Assigned task to ${assignedToUser.firstName} ${assignedToUser.lastName}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.assignedBy && data.assignedBy !== task.assignedBy) {
      const assignedByUser = await User.findByPk(data.assignedBy);
      if (!assignedByUser) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Assigned By User with id ${data.assignedBy}`
          )
        );
      }
      await task.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Assigned by ${assignedByUser.firstName} ${assignedByUser.lastName}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (
      (data.startDate &&
        new Date(data.startDate).getTime() !==
          new Date(task.startDate).getTime()) ||
      (data.endDate &&
        new Date(data.endDate).getTime() !== new Date(task.endDate).getTime())
    ) {
      const now = new Date();
      if (data.startDate && data.endDate) {
        const startDate = new Date(data.startDate);
        const endDate = new Date(data.endDate);
        if (startDate >= endDate) {
          throw new Error('Start date must be earlier than end date.');
        }
        if (startDate <= now) {
          throw new Error('Start date must be in the future or today.');
        }
      }
    }

    task.updatedBy = loggedInUser.id;
    Object.assign(task, data);
    await task.save({ transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Task'),
      data: task,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the task.',
    };
  }
};

exports.updateTaskDetails = async (taskId, data, loggedInUser) => {
  const transaction = await Tasks.sequelize.transaction();
  try {
    const task = await Tasks.findByPk(taskId);
    if (!task) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Task with ID: ' + taskId),
      };
    }

    if (data.status && data.status !== task.status) {
      await task.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated status from ${task.status} to ${data.status}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.assignedTo && data.assignedTo !== task.assignedTo) {
      const assignedToUser = await User.findByPk(data.assignedTo);
      if (!assignedToUser) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Assigned To User with id ${data.assignedTo}`
          )
        );
      }
      await task.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Assigned task to ${assignedToUser.firstName} ${assignedToUser.lastName}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.assignedBy && data.assignedBy !== task.assignedBy) {
      const assignedByUser = await User.findByPk(data.assignedBy);
      if (!assignedByUser) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Assigned By User with id ${data.assignedBy}`
          )
        );
      }
      await task.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Assigned by ${assignedByUser.firstName} ${assignedByUser.lastName}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (
      (data.startDate &&
        new Date(data.startDate).getDate() !==
          new Date(task.startDate).getDate()) ||
      (data.endDate &&
        new Date(data.endDate).getDate() !== new Date(task.endDate).getDate())
    ) {
      const now = new Date();
      if (data.startDate && data.endDate) {
        const startDate = new Date(data.startDate);
        const endDate = new Date(data.endDate);
        if (startDate >= endDate) {
          throw new Error('Start date must be earlier than end date.');
        }
        if (startDate <= now) {
          throw new Error('Start date must be in the future or today.');
        }
      }
    }

    task.updatedBy = loggedInUser.id;
    Object.assign(task, data);
    await task.save({ transaction });

    if (data.taskTagsIds) {
      const existingTagMappings = await TaskTagMapping.findAll({
        where: { taskId },
      });
      const existingTagIds = existingTagMappings.map((tag) => tag.tagId);
      const newTagIds = data.taskTagsIds.filter(
        (id) => !existingTagIds.includes(id)
      );
      const removedTagIds = existingTagIds.filter(
        (id) => !data.taskTagsIds.includes(id)
      );

      if (newTagIds.length) {
        const newMappings = newTagIds.map((tagId) => ({ taskId, tagId }));
        await TaskTagMapping.bulkCreate(newMappings, { transaction });
      }
      if (removedTagIds.length) {
        await TaskTagMapping.destroy({
          where: { taskId, tagId: removedTagIds },
          transaction,
        });
      }
    }

    if (data.unitIds) {
      const existingUnitMappings = await TaskUnitMapping.findAll({
        where: { taskId },
      });
      const existingUnitIds = existingUnitMappings.map((unit) => unit.unitId);
      const newUnitIds = data.unitIds.filter(
        (id) => !existingUnitIds.includes(id)
      );
      const removedUnitIds = existingUnitIds.filter(
        (id) => !data.unitIds.includes(id)
      );

      if (newUnitIds.length) {
        const newMappings = newUnitIds.map((unitId) => ({ taskId, unitId }));
        await TaskUnitMapping.bulkCreate(newMappings, { transaction });
      }
      if (removedUnitIds.length) {
        await TaskUnitMapping.destroy({
          where: { taskId, unitId: removedUnitIds },
          transaction,
        });
      }
    }

    if (data.followers) {
      const existingFollowerMappings = await TaskFollowers.findAll({
        where: { taskId },
      });
      const existingFollowerIds = existingFollowerMappings.map(
        (follower) => follower.userId
      );
      const newFollowerIds = data.followers.filter(
        (id) => !existingFollowerIds.includes(id)
      );
      const removedFollowerIds = existingFollowerIds.filter(
        (id) => !data.followers.includes(id)
      );

      if (newFollowerIds.length) {
        const newMappings = newFollowerIds.map((userId) => ({
          taskId,
          userId,
        }));
        await TaskFollowers.bulkCreate(newMappings, { transaction });
      }
      if (removedFollowerIds.length) {
        await TaskFollowers.destroy({
          where: { taskId, userId: removedFollowerIds },
          transaction,
        });
      }
    }

    if (data.dependencies) {
      for (const dep of data.dependencies) {
        if (!dep.id) {
          await TaskDependency.create(
            {
              ...dep,
              taskId,
              dependentTaskId: dep.taskId,
              createdBy: loggedInUser.id,
            },
            { transaction }
          );
        } else {
          const existingDependency = await TaskDependency.findOne({
            where: { taskId, dependentTaskId: dep.taskId },
          });

          if (existingDependency) {
            existingDependency.dependencyType = dep.dependencyType;
            existingDependency.status = dep.status;
            await existingDependency.save({ transaction });
          } else {
            await TaskDependency.create(
              {
                ...dep,
                taskId,
                dependentTaskId: dep.taskId,
                createdBy: loggedInUser.id,
              },
              { transaction }
            );
          }
        }
      }
      const dependencyIds = data.dependencies.map((dep) => dep.id);
      await TaskDependency.destroy({
        where: {
          taskId,
          id: { [Op.notIn]: dependencyIds },
        },
        transaction,
      });
    }

    if (data.subTasks) {
      for (const subTask of data.subTasks) {
        if (subTask.id) {
          const existingSubTask = await SubTasks.findOne({
            where: { id: subTask.id, taskId },
          });
          if (existingSubTask) {
            Object.assign(existingSubTask, subTask);
            await existingSubTask.save({ transaction });
          }
        } else {
          await SubTasks.create(
            { ...subTask, taskId, createdBy: loggedInUser.id },
            { transaction }
          );
        }
      }
      const subTaskIds = data.subTasks.map((subTask) => subTask.id);
      await SubTasks.destroy({
        where: {
          taskId,
          id: { [Op.notIn]: subTaskIds },
        },
        transaction,
      });
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Task'),
      data: task,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the task.',
    };
  }
};

exports.getTaskDetails = async (taskId) => {
  try {
    const task = await Tasks.findOne({
      where: { id: taskId },
      attributes: {
        exclude: [
          'projectId',
          'assignedTo',
          'assignedBy',
          'createdBy',
          'updatedBy',
        ],
      },
      include: [
        {
          model: Project,
          as: 'project',
          required: false,
          attributes: ['id', 'name', 'logo'],
        },
        {
          model: User,
          as: 'assignedToUser',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name'],
            },
          ],
        },
        {
          model: User,
          as: 'assignedByUser',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name'],
            },
          ],
        },
        {
          model: TaskTags,
          as: 'taskTags',
          attributes: ['id', 'name'],
          through: { attributes: [] },
        },
        {
          model: Unit,
          as: 'units',
          attributes: ['id', 'name'],
          through: { attributes: [] },
        },
        {
          model: TaskDependency,
          as: 'dependencies',
          attributes: ['id', 'dependencyType', 'status'],
          include: [
            {
              model: Tasks,
              as: 'dependentTask',
              attributes: ['id', 'title'],
            },
          ],
        },
        {
          model: SubTasks,
          as: 'subTasks',
          attributes: ['id', 'status', 'title', 'startDate', 'dueDate'],
        },
        {
          model: User,
          as: 'followers',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          through: { attributes: [] },
        },
      ],
    });

    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Task with ID: ${taskId}`),
      };
    }

    const activities = await task.getActivities({
      attributes: {
        exclude: ['activityOn', 'recordId', 'createdBy'],
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
      ],
    });

    const taskComments = await TaskComments.findAll({
      where: { taskId },
      attributes: ['id', 'comment', 'createdAt', 'updatedAt'],
      include: [
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
      ],
    });

    const taskDocuments = await TaskDocuments.findAll({
      where: { taskId },
      attributes: [
        'id',
        'fileName',
        'fileType',
        'filePath',
        'fileSize',
        'createdAt',
        'updatedAt',
      ],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
      ],
    });

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('Task'),
      data: { task, activities, taskComments, taskDocuments },
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.message || 'An error occurred while fetching the task details.',
    };
  }
};

exports.getTaskList = async (data) => {
  try {
    const {
      status,
      startDate,
      endDate,
      projectId,
      assignedTo,
      assignedBy,
      createdBy,
      search,
      'taskTags.id': taskTagId,
      'units.name': unitName,
      organizationId,
      limit = 10,
      start = 0,
    } = data;

    const filters = {
      ...(status && {
        status: { [Op.in]: Array.isArray(status) ? status : [status] },
      }),
      ...(startDate && { startDate: { [Op.gte]: new Date(startDate) } }),
      ...(endDate && { endDate: { [Op.lte]: new Date(endDate) } }),
      ...(projectId && {
        projectId: {
          [Op.in]: Array.isArray(projectId) ? projectId : [projectId],
        },
      }),
      ...(assignedTo && {
        assignedTo: {
          [Op.in]: Array.isArray(assignedTo) ? assignedTo : [assignedTo],
        },
      }),
      ...(assignedBy && {
        assignedBy: {
          [Op.in]: Array.isArray(assignedBy) ? assignedBy : [assignedBy],
        },
      }),
      ...(createdBy && {
        createdBy: {
          [Op.in]: Array.isArray(createdBy) ? createdBy : [createdBy],
        },
      }),
    };

    const searchFilters = search
      ? { title: { [Op.iLike]: `%${search}%` } }
      : {};

    const includeConditions = [
      {
        model: TaskTags,
        as: 'taskTags',
        attributes: ['id', 'name'],
        through: { attributes: [] },
        ...(taskTagId && { where: { id: taskTagId } }),
      },
      {
        model: Unit,
        as: 'units',
        attributes: ['id', 'name'],
        through: { attributes: [] },
        ...(unitName && { where: { name: { [Op.iLike]: `%${unitName}%` } } }),
      },
      {
        model: Project,
        as: 'project',
        attributes: ['id', 'name', 'logo'],
        ...(organizationId && { where: { organizationId } }),
      },
      {
        model: User,
        as: 'assignedToUser',
        attributes: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'profilePicture',
        ],
        include: [
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
          },
        ],
      },
      {
        model: User,
        as: 'assignedByUser',
        attributes: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'profilePicture',
        ],
        include: [
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
          },
        ],
      },
      {
        model: TaskDependency,
        as: 'dependencies',
        attributes: ['id', 'dependencyType', 'status'],
        include: [
          {
            model: Tasks,
            as: 'dependentTask',
            attributes: ['id', 'title'],
          },
        ],
      },
      {
        model: SubTasks,
        as: 'subTasks',
        attributes: ['id', 'status', 'title', 'startDate', 'dueDate'],
      },
      {
        model: User,
        as: 'followers',
        attributes: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'profilePicture',
        ],
        through: { attributes: [] },
      },
    ];

    const { rows, count } = await Tasks.findAndCountAll({
      where: {
        ...filters,
        ...searchFilters,
      },
      include: includeConditions,
      limit,
      offset: start,
      distinct: true,
      order: [['createdAt', 'DESC']],
    });

    return {
      success: true,
      message: 'Tasks fetched successfully',
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'An error occurred while fetching tasks',
    };
  }
};
