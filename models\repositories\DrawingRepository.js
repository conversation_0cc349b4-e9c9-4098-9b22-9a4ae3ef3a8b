const { defaultStatus } = require('@config/options');
const { Drawing } = require('..');
const DocumentRepository = require('./DocumentRepository');

exports.createDrawings = async (
  drawings,
  entityType,
  entityId,
  loggedInUser,
  projectId
) => {
  const parentDocument = await DocumentRepository.getDocumentDefaultDocument(
    projectId,
    'Drawings'
  );

  if (drawings && drawings.length > 0) {
    const drawingPromises = drawings.map(async (drawing) => {
      const document = await DocumentRepository.createDocument(
        {
          fileName: drawing.fileName,
          fileType: drawing.fileType,
          fileSize: drawing.fileSize,
          filePath: drawing.filePath,
          projectId: projectId,
          parentFolderId: parentDocument.id,
          isFolder: false,
          status: defaultStatus.ACTIVE,
        },
        loggedInUser
      );
      return Drawing.create({
        type: entityType,
        fileName: drawing.filePath,
        [`${entityType.toLowerCase()}Id`]: entityId,
        documentId: document.id,
      });
    });
    await Promise.all(drawingPromises);
  }
};

exports.deleteDrawingsByUnitId = async (unitId) => {
  try {
    const deletedCount = await Drawing.destroy({
      where: {
        unitId: unitId,
      },
    });

    if (deletedCount > 0) {
      return {
        success: true,
        message: `${deletedCount} drawing(s) deleted successfully.`,
      };
    } else {
      return {
        success: false,
        message: 'No drawings found for the specified unit.',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Error while deleting drawings: ${error.message}`,
    };
  }
};

exports.deleteDrawingsByFloorId = async (floorId) => {
  try {
    const deletedCount = await Drawing.destroy({
      where: {
        floorId: floorId,
      },
    });

    if (deletedCount > 0) {
      return {
        success: true,
        message: `${deletedCount} drawing(s) deleted successfully.`,
      };
    } else {
      return {
        success: false,
        message: 'No drawings found for the specified floor.',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Error while deleting drawings: ${error.message}`,
    };
  }
};

exports.deleteDrawingsBySpaceId = async (spaceId) => {
  try {
    const deletedCount = await Drawing.destroy({
      where: {
        spaceId: spaceId,
      },
    });

    if (deletedCount > 0) {
      return {
        success: true,
        message: `${deletedCount} drawing(s) deleted successfully.`,
      };
    } else {
      return {
        success: false,
        message: 'No drawings found for the specified floor.',
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Error while deleting drawings: ${error.message}`,
    };
  }
};

exports.updateDrawing = async (
  data,
  entityType,
  entityId,
  loggedInUser,
  projectId
) => {
  await Drawing.destroy({
    where: {
      [`${entityType.toLowerCase()}Id`]: entityId,
    },
  });

  await this.createDrawings(
    data,
    entityType,
    entityId,
    loggedInUser,
    projectId
  );
};
