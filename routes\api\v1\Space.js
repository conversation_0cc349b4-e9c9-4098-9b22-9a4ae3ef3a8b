const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const SpaceController = require('@controllers/v1/Space');
const SpaceSchema = require('@schema-validation/Space');
const ErrorHandleHelper = require('@helpers/ErrorHandleHelper');

router.post(
  '/space-floor',
  checkSchema(SpaceSchema.createSpaceForFloor),
  ErrorHandleHelper.requestValidator,
  SpaceController.createSpaceForFloor
);

router.post(
  '/space-unit',
  checkSchema(SpaceSchema.createSpaceForUnit),
  ErrorHandleHelper.requestValidator,
  SpaceController.createSpaceForUnit
);

router.post(
  '/space',
  checkSchema(SpaceSchema.createSpace),
  ErrorHandleHelper.requestValidator,
  SpaceController.createSpace
);

router.put(
  '/space/:id',
  checkSchema(SpaceSchema.updateSpace),
  ErrorHandleHelper.requestValidator,
  SpaceController.updateSpace
);

router.delete(
  '/space/:id',
  checkSchema(SpaceSchema.validateRemoveSpace),
  ErrorHandleHelper.requestValidator,
  SpaceController.deleteSpace
);

router.post(
  '/space/:id/drawing',
  checkSchema(SpaceSchema.validateAddDrawingsToSpace),
  ErrorHandleHelper.requestValidator,
  SpaceController.addDrwaingsToSpace
);

module.exports = router;
