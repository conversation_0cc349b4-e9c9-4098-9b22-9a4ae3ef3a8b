const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const RoleController = require('@controllers/v1/settings/Role');
const RoleSchema = require('@schema-validation/settings/Role');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(RoleSchema.createRole),
  ErrorHandleHelper.requestValidator,
  RoleController.createRole
);

router.put(
  '/:id',
  checkSchema(RoleSchema.updateRole),
  ErrorHandleHelper.requestValidator,
  RoleController.updateRole
);

router.delete(
  '/:id',
  checkSchema(RoleSchema.deleteRole),
  ErrorHandleHelper.requestValidator,
  RoleController.deleteRole
);

module.exports = router;
