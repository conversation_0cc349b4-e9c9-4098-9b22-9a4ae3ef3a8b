const {
  EmployeeTemplateItem,
  EmployeeTemplate,
  TemplateMaster,
  TemplateEntity,
  sequelize,
} = require('..');
const {
  successMessage,
  calculationType,
  templateEntityType,
  errorMessage,
  templateType,
} = require('../../config/options');

exports.findOne = async (query) => await EmployeeTemplateItem.findOne(query);

exports.findAll = async (query) => await EmployeeTemplateItem.findAll(query);

exports.createEmployeeLeavePolicy = async (data, employeeId, loggedInUser) => {
  const transaction = await sequelize.transaction();
  try {
    const exitingLeaveEmployeeTemplate = await this.checkEmployeeTemplateExist(
      employeeId,
      templateType.LEAVE
    );

    if (exitingLeaveEmployeeTemplate) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Employee Template'),
      };
    }
    const exitingTemplateMaster = await TemplateMaster.findOne({
      where: {
        id: data.templateId,
      },
      transaction,
    });

    let additionalLeave;
    if (
      data?.employeeAdditionalLeave &&
      data.employeeAdditionalLeave.length > 0
    ) {
      const templateEntities = await this.addEmployeeLeaveTemplateEntity(
        data.employeeAdditionalLeave,
        employeeId,
        data.templateId,
        transaction
      );
      additionalLeave = templateEntities.data.map((entity) => ({
        entityId: entity.dataValues.id,
        name: entity.dataValues.name,
        entityType: entity.dataValues.entityType,
        calculation: entity.dataValues.calculation,
        maxAllowed: entity.dataValues.maxAllowed,
        calculationType: calculationType.FIXED,
        employeeId: employeeId,
        templateId: data.templateId,
        createdBy: loggedInUser.id,
      }));
    }

    const employeeLeaveTemplateItems = [];
    for (const leave of data.employeeLeave) {
      const templateEntity = await TemplateEntity.findOne({
        where: {
          id: leave.entityId,
        },
      });

      if (!templateEntity) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Template Entity'),
        };
      }

      employeeLeaveTemplateItems.push({
        entityId: templateEntity.id,
        name: templateEntity.name,
        entityType: templateEntity.entityType,
        calculation: templateEntity.calculation,
        maxAllowed: leave.maxAllowed,
        calculationType: calculationType.FIXED,
        employeeId: employeeId,
        templateId: templateEntity.templateId,
        createdBy: loggedInUser.id,
      });
    }

    const leavePolicyPayload = [
      ...(additionalLeave || []),
      ...(employeeLeaveTemplateItems.length > 0
        ? employeeLeaveTemplateItems
        : []),
    ];

    const employeeTemplatePayload = {
      employeeId: employeeId,
      name: exitingTemplateMaster.name,
      templateType: exitingTemplateMaster.templateType,
      templateId: exitingTemplateMaster.id,
      organizationId: loggedInUser.currentOrganizationId,
      employeeTemplateItems: [...leavePolicyPayload],
    };

    const leavePolicy = await EmployeeTemplate.create(employeeTemplatePayload, {
      include: [
        {
          model: EmployeeTemplateItem,
          as: 'employeeTemplateItems',
        },
      ],
      transaction,
    });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Employee Leave Policy'),
      data: leavePolicy,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: errorMessage.SERVER_ERROR,
    };
  }
};

exports.addEmployeeLeaveTemplateEntity = async (
  data,
  employeeId,
  templateId,
  transaction
) => {
  const leaveTemplateEntities = data.map((entity) => ({
    name: entity.name,
    maxAllowed: entity.maxAllowed,
    employeeId,
    templateId: templateId,
    calculationType: calculationType.FIXED,
    entityType: templateEntityType.LEAVE_POLICY,
  }));

  const templateEntities = await TemplateEntity.bulkCreate(
    leaveTemplateEntities,
    { transaction }
  );

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Template Entity'),
    data: templateEntities,
  };
};

exports.checkEmployeeTemplateExist = async (employeeId, templateType) => {
  const query = {
    where: {
      employeeId,
      templateType,
    },
  };
  return await EmployeeTemplate.findOne(query);
};

exports.addEmployeeSalaryPayroll = async (
  data,
  employeeId,
  loggedInUser,
  transaction,
  isUpdate = false
) => {
  try {
    if (isUpdate) {
      const checkEmployeeTemplate = await this.checkEmployeeTemplateExist(
        employeeId,
        templateType.SALARY
      );

      if (checkEmployeeTemplate) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST('Employee Template'),
        };
      }
    }

    const exitingTemplateMaster = await TemplateMaster.findOne({
      where: {
        id: data.templateId,
      },
      transaction,
    });

    const entityIds = data.employeeSalary.map((item) => item.entityId);

    const query = {
      where: {
        id: entityIds,
      },
    };

    const templateEntities = await TemplateEntity.findAll(query);

    const employeeTemplateItems = data.employeeSalary.map((entity) => {
      const templateEntity = templateEntities.find(
        (e) => e.id === entity.entityId
      );

      return {
        employeeId,
        name: templateEntity.name,
        calculationType: entity.calculationType,
        calculation: entity.calculation,
        monthlyAmount: entity.monthlyAmount,
        annualAmount: entity.annualAmount,
        createdBy: loggedInUser.id,
        entityType: templateEntity.entityType,
      };
    });

    const payload = {
      employeeId,
      name: exitingTemplateMaster.name,
      templateType: exitingTemplateMaster.templateType,
      organizationId: loggedInUser.currentOrganizationId,
      employeeTemplateItems,
    };

    const employeeSalary = await EmployeeTemplate.create(payload, {
      include: [
        {
          model: EmployeeTemplateItem,
          as: 'employeeTemplateItems',
        },
      ],
      transaction,
    });

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Salary Payroll'),
      data: employeeSalary,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.putEmployeeSalaryPayroll = async (data, employeeId, loggedInUser) => {
  const transaction = await sequelize.transaction();

  try {
    const checkEmployeeTemplate = await this.checkEmployeeTemplateExist(
      employeeId,
      templateType.SALARY
    );

    if (!checkEmployeeTemplate) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Employee Template Payroll'),
      };
    }

    const result = await this.addEmployeeSalaryPayroll(
      data,
      employeeId,
      loggedInUser,
      transaction
    );

    await checkEmployeeTemplate.destroy({ transaction });

    if (!result.success) {
      await transaction.rollback();
      return {
        success: false,
        message: result.message,
      };
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Employee Salary Payroll'),
      data: result.data,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};
