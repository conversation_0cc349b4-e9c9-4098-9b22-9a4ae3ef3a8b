const { errorMessage } = require('@config/options');

exports.checkExistence = async (model, query, select = null) => {
  try {
    const result = await model.findOne({
      where: query,
      attributes: select,
    });
    return result;
  } catch (error) {
    console.log('Error checking existence', error);
    throw error;
  }
};

exports.validateExistence = async (model, id, modelName) => {
  try {
    const query = {
      where: {
        id,
      },
    };
    const record = await model.findOne(query);
    if (!record) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`${modelName} with id: ${id}`)
      );
    }
    return record;
  } catch (error) {
    throw new Error(error.message);
  }
};
