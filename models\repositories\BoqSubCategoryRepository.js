const { Project, BOQCategory, BoqEntry } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.findOne = async (query) => await Project.findOne(query);

exports.createSubcategory = async (projectId, data, loggedInUser) => {
  try {
    const query = {
      where: {
        id: projectId,
      },
    };
    const project = await this.findOne(query);
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('project'),
      };
    }

    if (data.parentCategoryId) {
      const parentCategory = await BOQCategory.findOne({
        where: {
          id: data.parentCategoryId,
        },
      });

      if (!parentCategory) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('parent category'),
        };
      }
    }

    const costPayload = data.cost.map((costItem, index) => ({
      ...costItem,
      id: index + 1,
    }));

    const categoryPayload = {
      ...data,
      cost: costPayload,
      projectId,
      organizationId: project.organizationId,
      createdBy: loggedInUser.id,
    };

    const category = await BOQCategory.create(categoryPayload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('BOQ Subcategory'),
      data: category,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getSubCategoryDetails = async (subCategoryId) => {
  try {
    if (!subCategoryId) {
      return {
        success: false,
        message: errorMessage.REQUIRED('subCategoryId'),
      };
    }

    const subCategory = await BOQCategory.findOne({
      where: { id: subCategoryId },
      attributes: [
        'id',
        'name',
        'status',
        'projectId',
        'organizationId',
        'cost',
      ],
      include: [
        {
          model: BoqEntry,
          as: 'boqItems',
          attributes: ['id', 'total', 'status', 'cost'],
        },
      ],
    });

    if (!subCategory) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('subCategoryId'),
      };
    }

    let subCategoryCompletedQty = 0;
    let subCategoryTotalQty = 0;
    let subCategoryTotalEstimatedCost = 0;
    let subCategoryCompletedEstimatedCost = 0;

    if (subCategory.boqItems && subCategory.boqItems.length > 0) {
      subCategory.boqItems.forEach((item) => {
        const totalEstimatedCost =
          item.cost?.reduce(
            (sum, costItem) =>
              sum +
              parseFloat(costItem.estimatedRate || 0) *
                parseFloat(item.total || 0),
            0
          ) || 0;

        const completedEstimatedCost =
          item.cost?.reduce(
            (sum, costItem) =>
              sum +
              (item.status === 'completed'
                ? parseFloat(costItem.estimatedRate || 0) *
                  parseFloat(item.total || 0)
                : 0),
            0
          ) || 0;

        const completedQty =
          item.status === 'completed' ? parseFloat(item.total || 0) : 0;
        const totalQty = parseFloat(item.total || 0);

        subCategoryCompletedQty += completedQty;
        subCategoryTotalQty += totalQty;
        subCategoryTotalEstimatedCost += totalEstimatedCost;
        subCategoryCompletedEstimatedCost += completedEstimatedCost;
      });
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('subCategoryDetails'),
      data: {
        type: 'subCategory',
        id: subCategory.id,
        name: subCategory.name,
        progress:
          subCategoryTotalQty > 0
            ? (subCategoryCompletedQty / subCategoryTotalQty) * 100
            : 0,
        budgetUsed:
          subCategoryTotalEstimatedCost > 0
            ? (subCategoryCompletedEstimatedCost /
                subCategoryTotalEstimatedCost) *
              100
            : 0,
        estimatedQty: subCategoryTotalQty,
        rate:
          subCategoryTotalQty > 0
            ? subCategoryTotalEstimatedCost / subCategoryTotalQty
            : 0,
        totalEstimatedCost: subCategoryTotalEstimatedCost,
        status: subCategory.status,
        projectId: subCategory.projectId,
        organizationId: subCategory.organizationId,
        cost: subCategory.cost,
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateSubcategory = async (subCategoryId, data) => {
  try {
    const subCategory = await BOQCategory.findOne({
      where: { id: subCategoryId },
    });

    if (!subCategory) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Subcategory'),
      };
    }

    if (data.parentCategoryId) {
      const parentCategory = await BOQCategory.findOne({
        where: { id: data.parentCategoryId },
      });

      if (!parentCategory) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Parent category'),
        };
      }
    }

    let costPayload = subCategory.cost || [];
    if (data.cost) {
      costPayload = data.cost.map((costItem, index) => ({
        ...costItem,
        id: index + 1,
      }));
    }

    Object.assign(subCategory, data, {
      cost: costPayload,
    });
    await subCategory.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('BOQ Subcategory'),
      data: subCategory,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.softDeleteSubcategory = async (subCategoryId) => {
  try {
    const subCategory = await BOQCategory.findOne({
      where: { id: subCategoryId },
    });

    if (!subCategory) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Subcategory'),
      };
    }

    subCategory.status = 'deleted';

    await subCategory.save();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('BOQ Subcategory'),
      data: subCategory,
    };
  } catch (error) {
    throw new Error(error);
  }
};
