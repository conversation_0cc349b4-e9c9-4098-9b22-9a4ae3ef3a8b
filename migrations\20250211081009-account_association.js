'use strict';

const { accountAssociation } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.addColumn('AccountData', 'referenceId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        after: 'accountType',
        defaultValue: null,
      });

      await queryInterface.addColumn('AccountData', 'referenceType', {
        type: Sequelize.STRING,
        allowNull: true,
        after: 'referenceId',
        defaultValue: accountAssociation.STATIC,
      });
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.removeColumn('AccountData', 'referenceId');
      await queryInterface.removeColumn('AccountData', 'referenceType');
    });
  },
};
