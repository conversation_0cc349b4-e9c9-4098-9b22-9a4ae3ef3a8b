const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const StockAdjustmentController = require('@controllers/v1/material/StockAdjustment');
const StockAdjustmentSchema = require('@schema-validation/material/StockAdjustment');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(StockAdjustmentSchema.createStockAdjustment),
  ErrorHandleHelper.requestValidator,
  StockAdjustmentController.createStockAdjustment
);

router.put(
  '/:id',
  checkSchema(StockAdjustmentSchema.updateStockAdjustment),
  ErrorHandleHelper.requestValidator,
  StockAdjustmentController.updateStockAdjustment
);

router.patch(
  '/:id',
  checkSchema(StockAdjustmentSchema.updateStockAdjustmentStatus),
  ErrorHandleHelper.requestValidator,
  StockAdjustmentController.updateStockAdjustmentStatus
);

router.delete(
  '/:id',
  checkSchema(StockAdjustmentSchema.deleteStockAdjustment),
  ErrorHandleHelper.requestValidator,
  StockAdjustmentController.deleteStockAdjustment
);

module.exports = router;
