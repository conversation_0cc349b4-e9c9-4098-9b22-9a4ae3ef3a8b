const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const AdministrationDocController = require('@controllers/v1/settings/AdministrationDocument');
const AdministrationDocumentSchema = require('@schema-validation/settings/AdministrationDocument');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(AdministrationDocumentSchema.createAdministrationDocument),
  ErrorHandleHelper.requestValidator,
  AdministrationDocController.createAdministrationDocument
);

router.put(
  '/:roleId',
  checkSchema(AdministrationDocumentSchema.updateAdministrationDocument),
  ErrorHandleHelper.requestValidator,
  AdministrationDocController.updateAdministrationDocument
);

router.delete(
  '/:id',
  checkSchema(AdministrationDocumentSchema.deleteAdministrationDocument),
  Error<PERSON>andleHelper.requestValidator,
  AdministrationDocController.deleteAdministrationDocument
);

module.exports = router;
