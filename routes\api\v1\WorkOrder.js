const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const WorkOrderControl = require('../../../controllers/api/v1/WorkOrder');
const WorkOrderSchema = require('../../../schema-validation/WorkOrder');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/work-order/type',
  checkSchema(WorkOrderSchema.createWorkOrderType),
  ErrorHandleHelper.requestValidator,
  WorkOrderControl.createWorkOrderType
);

router.post(
  '/:id/work-order',
  checkSchema(WorkOrderSchema.createWorkOrder),
  ErrorHandleHelper.requestValidator,
  WorkOrderControl.createWorkOrder
);

router.put(
  '/work-order/:id',
  checkSchema(WorkOrderSchema.updateWorkOrder),
  ErrorHandleHelper.requestValidator,
  WorkOrderControl.updateWorkOrder
);

router.get(
  '/work-order/:id/details',
  ErrorHandleHelper.requestValidator,
  WorkOrderControl.listWorkOrderDetails
);

router.get(
  '/work-order/status-count',
  ErrorHandleHelper.requestValidator,
  WorkOrderControl.workorderStatusCount
);

router.patch(
  '/work-order/:id/allot',
  ErrorHandleHelper.requestValidator,
  WorkOrderControl.allotWorkorder
);

module.exports = router;
