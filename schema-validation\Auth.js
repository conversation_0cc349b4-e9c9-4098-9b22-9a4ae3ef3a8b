exports.sendOtp = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};

exports.verifyOtp = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  tempOtp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Temp OTP cannot be empty',
    isString: {
      errorMessage: 'Temp OTP must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};
