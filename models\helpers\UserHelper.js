const _ = require('lodash');
const sequelize = require('sequelize');
const { User, LoginHistory, DeviceSession, Employee } = require('..');
const { loginType } = require('@config/options');

const { Op } = sequelize;

const generateUsername = (proposedName) =>
  (proposedName += Math.floor(Math.random() * 100 + 1));

const generateUniqueUsername = async (proposedName) => {
  if (!proposedName) {
    proposedName = generateUsername(proposedName);
  }
  try {
    proposedName = _.replace(proposedName, /\s+/g, '');
    const userCount = await User.count({
      where: { userName: { [Op.iLike]: `%${proposedName}%` } },
    });
    if (userCount > 0) {
      return generateUniqueUsername(generateUsername(proposedName));
    }
    return _.replace(proposedName, /\s+/g, '');
  } catch (error) {
    throw new Error(error);
  }
};

exports.generateUniqueUsername = generateUniqueUsername;

exports.modifyOutputData = (existingUser) => ({
  id: existingUser.id,
  userName: existingUser.userName,
  email: existingUser.email,
  firstName: existingUser.firstName,
  lastName: existingUser.lastName,
  mobileNumber: existingUser.mobileNumber,
  countryCode: existingUser.countryCode,
  role: existingUser.role,
  status: existingUser.status,
  profilePicture: existingUser.profilePicture,
  isMobileNumberVerified: existingUser.isMobileNumberVerified,
  isEmailVerified: existingUser.isEmailVerified,
  token: existingUser.genToken(),
  lastSignInAt: existingUser.lastSignInAt,
  organizationId: existingUser.organizationId,
  organization: existingUser?.organization,
  isMfaEnabled: existingUser?.isMfaEnabled,
  designationId: existingUser?.designationId,
  designation: existingUser?.designation,
  isBusiness: existingUser?.isBusiness,
  businessName: existingUser?.businessName,
  currentOrganizationId: existingUser?.currentOrganizationId,
});

exports.saveLoginHistoryAndSession = async (
  existingUser,
  token,
  requestDetails = {}
) => {
  try {
    const { ipAddress, location, clientInfo } = requestDetails;

    // Set all old sessions to not current
    await DeviceSession.update(
      { isCurrentSession: false },
      {
        where: {
          userId: existingUser.id,
          isCurrentSession: true,
        },
      }
    );

    // Create login history
    await LoginHistory.create({
      userId: existingUser.id,
      organizationId: existingUser.organizationId || null,
      loginType: loginType.CREDENTIALS_LOGIN,
      ipAddress,
      location,
      clientInfo,
    });

    // Check for existing session
    const existingSession = await DeviceSession.findOne({
      where: {
        userId: existingUser.id,
        deviceName: clientInfo,
        ipAddress: ipAddress,
      },
    });

    if (existingSession) {
      existingSession.sessionToken = token;
      existingSession.isCurrentSession = true;
      await existingSession.save();
    } else {
      await DeviceSession.create({
        userId: existingUser.id,
        deviceName: clientInfo,
        ipAddress,
        loginType: loginType.CREDENTIALS_LOGIN,
        sessionToken: token,
        isCurrentSession: true,
      });
    }
  } catch (error) {
    throw error;
  }
};

exports.userAttributes = () => [
  'id',
  'createdAt',
  'updatedAt',
  'role',
  'countryCode',
  'mobileNumber',
  'email',
  'firstName',
  'middleName',
  'lastName',
  'status',
  'profilePicture',
  'city',
  'state',
  'pincode',
  'countryName',
  'organizationId',
  'currentOrganizationId',
  'designationId',
  'isBusiness',
  'businessName',
  'isMfaEnabled',
];

exports.createEmployeeIfNotExists = async (existingUser) => {
  const { id: userId, organizationId } = existingUser;
  try {
    const existingEmployee = await Employee.findOne({
      where: { userId },
    });

    if (!existingEmployee) {
      const employee = await Employee.create({
        userId,
        organizationId,
        employeeCode: `EMP-${userId}`,
        dateOfJoining: new Date(),
        maritalStatus: null,
      });

      return employee;
    }

    return existingEmployee;
  } catch (error) {
    throw error;
  }
};
