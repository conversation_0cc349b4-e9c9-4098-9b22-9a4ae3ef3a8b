'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('WbsActivityProjectMapping', 'floorId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Floor',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('WbsActivityProjectMapping', 'floorId');
  },
};
