const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const WarehouseController = require('@controllers/v1/material/Warehouse');
const WarehouseSchema = require('@schema-validation/material/Warehouse');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(WarehouseSchema.createWarehouse),
  ErrorHandleHelper.requestValidator,
  WarehouseController.createWarehouse
);

router.patch(
  '/:id',
  checkSchema(WarehouseSchema.updateWarehouse),
  ErrorHandleHelper.requestValidator,
  WarehouseController.updateWarehouse
);

router.delete(
  '/:id',
  checkSchema(WarehouseSchema.deleteWarehouse),
  ErrorHandleHelper.requestValidator,
  WarehouseController.deleteWarehouse
);

module.exports = router;
