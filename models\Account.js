module.exports = (sequelize, DataTypes) => {
  const Account = sequelize.define(
    'Account',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      debitAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      creditAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      currantBalanceAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Account.associate = (models) => {
    Account.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return Account;
};
