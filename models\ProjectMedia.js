module.exports = (sequelize, DataTypes) => {
  const ProjectMedia = sequelize.define(
    'ProjectMedia',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      mediaType: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      fileName: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ProjectMedia.associate = (models) => {
    ProjectMedia.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onDelete: 'SET NULL',
    });
  };

  return ProjectMedia;
};
