const { loginType } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const DeviceSession = sequelize.define(
    'DeviceSession',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      deviceName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      ipAddress: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      loginType: {
        type: DataTypes.ENUM(loginType.loginTypeArray()),
        allowNull: false,
        defaultValue: loginType.CREDENTIALS_LOGIN,
      },
      lastActivityAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      sessionToken: {
        type: DataTypes.TEXT,
        allowNull: true,
        unique: true,
      },
      isCurrentSession: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
      indexes: [
        {
          fields: ['userId'],
        },
        {
          fields: ['sessionToken'],
        },
      ],
    }
  );

  DeviceSession.associate = (models) => {
    DeviceSession.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  DeviceSession.prototype.logout = async function () {
    return this.destroy();
  };

  return DeviceSession;
};
