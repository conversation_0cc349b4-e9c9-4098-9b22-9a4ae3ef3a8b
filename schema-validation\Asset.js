const { assetType } = require('../config/options');

exports.createAsset = {
  assetType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Asset type cannot be empty',
    },
    isString: {
      errorMessage: 'Asset type must be a string',
    },
    custom: {
      options: (value) => {
        const allowedAssetType = assetType.getAssetTypeArray();
        if (value && !allowedAssetType.includes(value)) {
          throw new Error(
            `AssetType must be one of: ${allowedAssetType.join(', ')}`
          );
        }
        return true;
      },
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
};

exports.updateAsset = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Asset Id is required',
    },
    isInt: {
      errorMessage: 'Asset Id must be a valid integer',
    },
  },
  assetType: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedAssetType = assetType.getAssetTypeArray();
        if (value && !allowedAssetType.includes(value)) {
          throw new Error(
            `AssetType must be one of: ${allowedAssetType.join(', ')}`
          );
        }
        return true;
      },
    },
  },
  name: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
};

exports.deleteAsset = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Asset Id is required',
    },
    isInt: {
      errorMessage: 'Asset Id must be a valid integer',
    },
  },
};
