const { BOQCategory, Project, BoqEntry, BOQMetric } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const options = require('@config/options');

exports.findOne = async (query) => await Project.findOne(query);

exports.createCategory = async (projectId, data, loggedInUser) => {
  try {
    const query = {
      where: {
        id: projectId,
      },
    };
    const project = await this.findOne(query);
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('project'),
      };
    }

    const categoryPayload = {
      ...data,
      projectId,
      organizationId: project.organizationId,
      createdBy: loggedInUser.id,
    };

    const category = await BOQCategory.create(categoryPayload);

    const boqCategoryWithMetric = await BOQCategory.findOne({
      where: { id: category.id },
      include: [
        {
          model: BOQMetric,
          as: 'boqMetric',
          attributes: ['metricType'],
        },
      ],
    });

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('BOQ Category'),
      data: {
        ...category.toJSON(),
        metricType: boqCategoryWithMetric?.boqMetric?.metricType || null,
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateCategory = async (categoryId, data) => {
  try {
    const category = await BOQCategory.findByPk(categoryId);
    if (!category) {
      return {
        success: false,
        message: errorMessage.NO_USER('categoryId'),
      };
    }

    Object.assign(category, data);
    await category.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('BOQ Category'),
      data: category,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getBoqCategoryDetails = async (categoryId) => {
  try {
    const category = await BOQCategory.findOne({
      where: { id: categoryId, parentCategoryId: null },
      attributes: [
        'id',
        'name',
        'status',
        'projectId',
        'organizationId',
        'createdAt',
        'boqMetricId',
      ],
      include: [
        {
          model: BOQCategory,
          as: 'subCategories',
          attributes: ['id', 'name', 'status', 'projectId', 'organizationId'],
          include: [
            {
              model: BoqEntry,
              as: 'boqItems',
              attributes: [
                'id',
                'name',
                'description',
                'status',
                'total',
                'cost',
                'projectId',
                'organizationId',
              ],
            },
          ],
        },
        {
          model: BOQMetric,
          as: 'boqMetric',
          attributes: ['metricType'],
        },
      ],
    });

    if (!category) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('category'),
        data: null,
      };
    }

    let categoryCompletedQty = 0;
    let categoryTotalQty = 0;
    let categoryTotalEstimatedCost = 0;
    let categoryCompletedEstimatedCost = 0;

    category.subCategories.forEach((subCategory) => {
      let subCategoryCompletedQty = 0;
      let subCategoryTotalQty = 0;
      let subCategoryTotalEstimatedCost = 0;
      let subCategoryCompletedEstimatedCost = 0;

      subCategory.boqItems.forEach((item) => {
        const totalEstimatedCost =
          item.cost?.reduce(
            (sum, costItem) =>
              sum +
              parseFloat(costItem.estimatedRate || 0) *
                parseFloat(item.total || 0),
            0
          ) || 0;
        const completedEstimatedCost =
          item.cost?.reduce(
            (sum, costItem) =>
              sum +
              (item.status === 'completed'
                ? parseFloat(costItem.estimatedRate || 0) *
                  parseFloat(item.total || 0)
                : 0),
            0
          ) || 0;

        const completedQty =
          item.status === 'completed' ? parseFloat(item.total || 0) : 0;
        const totalQty = parseFloat(item.total || 0);

        subCategoryCompletedQty += completedQty;
        subCategoryTotalQty += totalQty;
        subCategoryTotalEstimatedCost += totalEstimatedCost;
        subCategoryCompletedEstimatedCost += completedEstimatedCost;
      });

      categoryCompletedQty += subCategoryCompletedQty;
      categoryTotalQty += subCategoryTotalQty;
      categoryTotalEstimatedCost += subCategoryTotalEstimatedCost;
      categoryCompletedEstimatedCost += subCategoryCompletedEstimatedCost;
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('BoqCategoryDetails'),
      data: {
        type: 'category',
        id: category.id,
        name: category.name,
        progress:
          categoryTotalQty > 0
            ? (categoryCompletedQty / categoryTotalQty) * 100
            : 0,
        budgetUsed:
          categoryTotalEstimatedCost > 0
            ? (categoryCompletedEstimatedCost / categoryTotalEstimatedCost) *
              100
            : 0,
        estimatedQty: categoryTotalQty,
        rate:
          categoryTotalQty > 0
            ? categoryTotalEstimatedCost / categoryTotalQty
            : 0,
        totalEstimatedCost: categoryTotalEstimatedCost,
        status: category.status,
        organizationId: category.organizationId,
        projectId: category.projectId,
        boqMetricId: category.boqMetricId,
        metricType: category.boqMetric ? category.boqMetric.metricType : null,
      },
    };
  } catch (error) {
    throw error;
  }
};

exports.softDeleteCategory = async (categoryId) => {
  try {
    const category = await BOQCategory.findOne({
      where: { id: categoryId },
    });

    if (!category) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('category'),
      };
    }

    category.status = 'deleted';

    await category.save();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('BOQ category'),
      data: category,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getBoqCategoryStatusCount = async (query) => {
  const { organizationId, projectId } = query;

  try {
    const data = {
      total: 0,
      status: {},
    };

    let statusMap = Object.fromEntries(
      [
        options.defaultStatus.UN_ASSIGNED,
        options.defaultStatus.CONTRACTED,
        options.defaultStatus.ONGOING,
        options.defaultStatus.COMPLETED,
        options.defaultStatus.DELETED,
        options.defaultStatus.QUALIFIED,
      ].map((status) => [status, 0])
    );

    let queryOptions = {
      attributes: [
        'status',
        [
          BOQCategory.sequelize.fn(
            'COUNT',
            BOQCategory.sequelize.col('status')
          ),
          'count',
        ],
      ],
      where: {},
      group: ['status'],
    };

    if (organizationId) {
      queryOptions.where.organizationId = organizationId;
    }
    if (projectId) {
      queryOptions.where.projectId = projectId;
    }

    const boqStatusCounts = await BOQCategory.findAll(queryOptions);

    boqStatusCounts.forEach((row) => {
      const status = row.dataValues.status;
      const count = parseInt(row.dataValues.count, 10);
      if (statusMap.hasOwnProperty(status)) {
        statusMap[status] = count;
      }
    });

    data.status = statusMap;
    data.total = Object.values(statusMap).reduce(
      (acc, count) => acc + count,
      0
    );

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('boq-status-count'),
      data,
    };
  } catch (error) {
    throw error;
  }
};
