exports.createCatalogue = {
  title: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Title is required',
    isString: {
      errorMessage: 'Title must be a valid string',
    },
  },
  categoryId: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Category Id is required',
    isInt: {
      errorMessage: 'Category Id must be a valid integer',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a valid string',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a valid string',
    },
  },
  backgroundImage: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Background image must be a valid string',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
};

exports.updateCatalogue = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'CatalogueId is required',
    },
    isInt: {
      errorMessage: 'CatalogueId must be a valid integer',
    },
  },
  title: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Title must be a valid string',
    },
  },
  categoryId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Category Id must be a valid integer',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a valid string',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a valid string',
    },
  },
  backgroundImage: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Background image must be a valid string',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
};

exports.deleteCatalogue = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'CatalogueId is required',
    },
    isInt: {
      errorMessage: 'CatalogueId must be a valid integer',
    },
  },
};

exports.deleteCatalogueMedia = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'CatalogueMediaId is required',
    },
    isInt: {
      errorMessage: 'CatalogueMediaId must be a valid integer',
    },
  },
};
