exports.assignModulePermission = {
  roleId: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Role ID is required',
    isInt: {
      errorMessage: 'Role ID must be a valid integer',
    },
  },
  permissions: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Permissions array is required',
    isArray: {
      errorMessage: 'Permissions must be an array',
    },
    custom: {
      options: (value) => {
        if (!Array.isArray(value) || value.length === 0) {
          throw new Error('At least one permission is required');
        }

        value.forEach((permission, index) => {
          if (!permission.moduleId) {
            throw new Error(
              `moduleId is required for permission at index ${index}`
            );
          }
          if (typeof permission.moduleId !== 'number') {
            throw new Error(
              `moduleId must be a number for permission at index ${index}`
            );
          }
          if (typeof permission.canEdit !== 'boolean') {
            throw new Error(
              `canEdit must be a boolean for permission at index ${index}`
            );
          }
          if (typeof permission.canCreate !== 'boolean') {
            throw new Error(
              `canCreate must be a boolean for permission at index ${index}`
            );
          }
          if (typeof permission.isEnabled !== 'boolean') {
            throw new Error(
              `isEnabled must be a boolean for permission at index ${index}`
            );
          }
          if (typeof permission.isAssigned !== 'boolean') {
            throw new Error(
              `isAssigned must be a boolean for permission at index ${index}`
            );
          }
        });
        return true;
      },
    },
  },
};

exports.updateModulePermission = {
  roleId: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'roleId is required',
    },
    isInt: {
      errorMessage: 'roleId must be a valid integer',
    },
  },
  permissions: {
    in: ['body'],
    optional: true,
    errorMessage: 'Permissions array is required',
    isArray: {
      errorMessage: 'Permissions must be an array',
    },
    custom: {
      options: (value) => {
        if (!Array.isArray(value) || value.length === 0) {
          throw new Error('At least one permission is required');
        }

        value.forEach((permission, index) => {
          if (!permission.id) {
            throw new Error(`id is required for permission at index ${index}`);
          }
          if (typeof permission.canEdit !== 'boolean') {
            throw new Error(
              `canEdit must be a boolean for permission at index ${index}`
            );
          }
          if (typeof permission.canCreate !== 'boolean') {
            throw new Error(
              `canCreate must be a boolean for permission at index ${index}`
            );
          }
          if (typeof permission.isEnabled !== 'boolean') {
            throw new Error(
              `isEnabled must be a boolean for permission at index ${index}`
            );
          }
          if (typeof permission.isAssigned !== 'boolean') {
            throw new Error(
              `isAssigned must be a boolean for permission at index ${index}`
            );
          }
        });
        return true;
      },
    },
  },
};

exports.removeModulePermission = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Module Id is required',
    },
    isInt: {
      errorMessage: 'Module Id must be a valid integer',
    },
  },
};
