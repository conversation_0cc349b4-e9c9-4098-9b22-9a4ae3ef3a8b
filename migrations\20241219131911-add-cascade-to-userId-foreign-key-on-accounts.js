'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('Account', 'Account_userId_fkey');

    await queryInterface.addConstraint('Account', {
      fields: ['userId'],
      type: 'foreign key',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('Account', 'Account_userId_fkey');

    await queryInterface.addConstraint('Account', {
      fields: ['userId'],
      type: 'foreign key',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  },
};
