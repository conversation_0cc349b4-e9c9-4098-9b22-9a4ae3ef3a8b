'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_WorkOrder_status" ADD VALUE IF NOT EXISTS 'on_hold';
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_WorkOrder_status" ADD VALUE IF NOT EXISTS 'allotted';
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" SET DEFAULT 'drafts';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" DROP DEFAULT;
    `);
  },
};
