paths:
  /crm/customer:
    post:
      tags:
        - "CRM"
      summary: "Create a new Customer"
      description: "This endpoint allows you to register a new customer by providing all necessary details."
      operationId: "CreateCustomer"
      requestBody:
        description: "The details of the new customer to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/customer"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Customer has been created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/customer'
        "400":
          description: "Invalid input data or customer already exists."
        "500":
          description: "Internal Server Error"

  /crm/customer/{id}:      
    put:
      tags:
        - "CRM"
      summary: "Update an existing Customer"
      description: "This endpoint allows you to update the details of an existing customer by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateCustomer"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the customer to be updated."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The updated information for the customer. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/customer-update"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Customer has been updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/customer'
        "400":
          description: "Invalid input data, customer not found, or customer already exists."
        "404":
          description: "Customer not found."
        "500":
          description: "Internal Server Error"

    patch:
      tags:
        - "CRM"
      summary: "Update Customer Status"
      description: "This endpoint allows you to update the status of an existing customer by providing its `id` in the URL path and new status in the request body."
      operationId: "UpdateCustomerStatus"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the customer whose status is to be updated."
          schema:
            type: string
            example: "123"
      requestBody:
        description: "The new status for the customer."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/customer-status-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Customer status has been updated successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Status updated successfully."
        "400":
          description: "Invalid input data or customer not found."
        "404":
          description: "Customer not found."
        "500":
          description: "Internal Server Error"

    delete:
      tags:
        - "CRM"
      summary: "Delete an existing Customer"
      description: "This endpoint allows you to delete an existing customer by providing its `id` in the URL path."
      operationId: "DeleteCustomer"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the customer to be deleted."
          schema:
            type: integer
            example: 123
      security:
        - bearerAuth: []
      responses:
        "204":
          description: "Customer has been successfully deleted."
        "400":
          description: "Invalid customer ID."
        "404":
          description: "Customer not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    customer:
      type: object
      properties:
        profilePicture:
          type: string
          description: "The profile picture of the customer, typically a URL or file path."
          example: "https://your-cloudfront-url.com/uploads/profile-pic.jpg"
        type:
          type: string
          enum:
            - "person"
            - "corporate"
            - "existing"
          description: "The type of customer (person, corporate or existing)."
          example: "person"
        businessName: 
          type: string
          description: "The name of the business or company that the customer represents."
          example: "PrimeTech Solutions"
        firstName:
          type: string
          description: "The first name of the customer."
          example: "Rahul"
        lastName:
          type: string
          description: "The last name of the customer."
          example: "Sharma"
        email:
          type: string
          description: "The email address of the customer."
          example: "<EMAIL>"
        countryCode:
          type: string
          description: "The country code for the customer's contact number."
          example: "+91"
        contactNumber:
          type: string
          description: "The contact number of the customer."
          example: "9876543210"
        sourceId:
          type: integer
          description: "The ID of the source from which the customer was referred."
          example: 1 
        subSourceId:
          type: integer
          description: "The ID of the sub-source from which the customer was referred."
          example: 2 
      required:
        - type
        - email
        - firstName
        - countryCode
        - contactNumber
      additionalProperties: false

    customer-update:
      type: object
      properties:
        profilePicture:
          type: string
          description: "The profile picture of the customer, typically a URL or file path."
          example: "https://your-cloudfront-url.com/uploads/profile-pic.jpg"
        type:
          type: string
          enum:
            - "person"
            - "corporate"
            - "existing"
          description: "The type of customer (person, corporate, or existing)."
          example: "person"
        businessName: 
          type: string
          description: "The name of the business or company that the customer represents."
          example: "PrimeTech Solutions"
        firstName:
          type: string
          description: "The first name of the customer."
          example: "Neha"
        lastName:
          type: string
          description: "The last name of the customer."
          example: "Patel"
        email:
          type: string
          description: "The email address of the customer."
          example: "<EMAIL>"
        countryCode:
          type: string
          description: "The country code for the customer's contact number."
          example: "+91"
        contactNumber:
          type: string
          description: "The contact number of the customer."
          example: "9123456789"
        sourceId:
          type: integer
          description: "The ID of the source from which the customer was referred."
          example: 2 
        subSourceId:
          type: integer
          description: "The ID of the sub-source from which the customer was referred."
          example: 3
        isArchived:
          type: boolean
          description: "Indicates whether the customer is archived."
          example: false
      required: []
      additionalProperties: false

    customer-status-update:
      type: object
      properties:
        status:
          type: string
          enum:
            - "new_lead"
            - "qualified"
            - "site_visit_completed"
            - "negotiation"
            - "on_hold"
            - "no_future_activity"
            - "lost"
            - "unqualified"
            - "archived"
          description: "The new status of the customer."
          example: "new_lead"
      required:
        - status
      additionalProperties: false