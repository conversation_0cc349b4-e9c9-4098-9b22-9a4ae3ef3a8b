'use strict';
const { itemMediaType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('ItemMedia', {
      id: {
        type: Sequelize.INTEGER,
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
      },
      mediaType: {
        type: Sequelize.ENUM(...itemMediaType.getValues()),
        allowNull: false,
        defaultValue: itemMediaType.ITEM,
      },
      fileName: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      fileType: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      filePath: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      fileSize: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      itemId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Item',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      itemVariantId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ItemVariant',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ItemMedia');
  },
};
