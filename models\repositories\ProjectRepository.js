const sequelize = require('sequelize');
const { Op } = sequelize;
const {
  Project,
  Drawing,
  ProjectMedia,
  Address,
  ProjectTeam,
  User,
  Organization,
} = require('..');
const DrawingRepository = require('./DrawingRepository');
const DocumentRepository = require('./DocumentRepository');
const { checkExistence } = require('@helpers/QueryHelper');
const AddressRepository = require('./AddressRepository');
const {
  successMessage,
  defaultStatus,
  addressType,
  errorMessage,
  drawingType,
  activityType,
} = require('@config/options');
const axios = require('axios');

exports.findOne = async (query) => await Project.findOne(query);

exports.findAll = async (query) => await Project.findAll(query);
exports.findAndCountAll = async (query) => await Project.findAndCountAll(query);

exports.checkAndGetProject = async (projectId) => {
  const query = {
    where: {
      id: projectId,
      status: { [Op.notIn]: [defaultStatus.DELETED] },
    },
  };
  const project = await Project.findOne(query);
  if (!project) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Project'),
    };
  }
  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Project'),
    data: project,
  };
};

exports.generateOrUpdateProjectCode = async (organizationId, projectId) => {
  const organization = await Organization.findOne({
    where: { id: organizationId },
  });

  const prefix = organization.name.trim().slice(0, 2).includes(' ')
    ? organization.name.trim().charAt(0).toUpperCase()
    : organization.name.trim().slice(0, 2).toUpperCase();

  return `${prefix}${organizationId}${projectId}`;
};

exports.getAllProjects = async (data, loggedInUser, organizationId) => {
  const {
    startDate,
    endDate,
    status,
    city = [],
    state = [],
    search = '',
    limit = 10,
    start = 0,
  } = data;

  try {
    const query = {
      where: {
        parentProjectId: null,
        status: data.status
          ? { [Op.in]: Array.isArray(status) ? status : [status] }
          : { [Op.not]: [defaultStatus.ARCHIVED, defaultStatus.DELETED] },
        organizationId: organizationId || loggedInUser.currentOrganizationId,
        ...(startDate && { startDate: { [Op.gte]: new Date(startDate) } }),
        ...(endDate && { endDate: { [Op.lte]: new Date(endDate) } }),
        [Op.or]: [
          { name: { [Op.iLike]: `%${search}%` } },
          { projectCode: { [Op.iLike]: `%${search}%` } },
        ],
      },
      include: [
        {
          model: Address,
          as: 'address',
          required: city || state,
          where: {
            [Op.and]: [
              ...(city.length || city > 0
                ? [{ city: { [Op.in]: Array.isArray(city) ? city : [city] } }]
                : []),
              ...(state.length || state > 0
                ? [
                    {
                      state: {
                        [Op.in]: Array.isArray(state) ? state : [state],
                      },
                    },
                  ]
                : []),
            ],
          },
          attributes: [
            'id',
            'address',
            'landmark',
            'city',
            'state',
            'pincode',
            'latitude',
            'longitude',
          ],
        },
        {
          model: ProjectMedia,
          as: 'projectMedia',
          required: false,
          where: { mediaType: 'banner' },
          attributes: [
            'id',
            'mediaType',
            'fileName',
            'fileType',
            'filePath',
            'fileSize',
          ],
        },
        {
          model: User,
          as: 'teamMembers',
          required: false,
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
        {
          model: Drawing,
          as: 'drawings',
          required: false,
          attributes: ['id', 'type', 'fileName', 'projectId'],
        },
      ],
      attributes: [
        'id',
        'name',
        'logo',
        'projectCode',
        'organizationId',
        'parentProjectId',
        'startDate',
        'endDate',
        'status',
        'createdAt',
        'updatedAt',
        [
          sequelize.literal(`(
            SELECT COUNT(*)
            FROM "WorkOrder" AS "workOrders"
            WHERE "workOrders"."projectId" = "Project"."id"
          )`),
          'totalWorkOrders',
        ],
        [
          sequelize.literal(`(
            SELECT COUNT(*)
            FROM "WorkOrder" AS "workOrders"
            WHERE "workOrders"."projectId" = "Project"."id"
            AND "workOrders"."status" = 'completed'
          )`),
          'totalCompleteWorkOrders',
        ],
        [
          sequelize.literal(`(
            SELECT 
              CASE 
                WHEN COUNT(*) = 0 THEN 0 
                ELSE ROUND((SUM(CASE WHEN "workOrders"."status" = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2) 
              END
            FROM "WorkOrder" AS "workOrders"
            WHERE "workOrders"."projectId" = "Project"."id"
          )`),
          'completionPercentage',
        ],
        [
          sequelize.literal(`(
            SELECT 
              CASE 
                WHEN COUNT(DISTINCT "units"."id") = 0 THEN 0 
                ELSE ROUND((COUNT(DISTINCT "ProjectBookedUnit"."id") * 100.0 / COUNT(DISTINCT "units"."id")), 2) 
              END
            FROM "Unit" AS "units"
            LEFT JOIN "ProjectBookedUnit" AS "ProjectBookedUnit"
              ON "ProjectBookedUnit"."unitId" = "units"."id"
              AND "ProjectBookedUnit"."status" = 'booked'
            WHERE "units"."projectId" IN (
              SELECT "id" FROM "Project" AS "subProjects" WHERE "subProjects"."parentProjectId" = "Project"."id"
            ) OR "units"."projectId" = "Project"."id"
          )`),
          'bookingPercentage',
        ],
        [
          sequelize.literal(`(
            SELECT COUNT("units"."id")
            FROM "Unit" AS "units"
            WHERE "isSaleable" = true
              AND ("units"."projectId" IN (
                SELECT "id" FROM "Project" AS "subProjects" 
                WHERE "subProjects"."parentProjectId" = "Project"."id"
              ) OR "units"."projectId" = "Project"."id")
              AND NOT EXISTS (
                SELECT 1 FROM "ProjectBookedUnit" AS "ProjectBookedUnit"
                WHERE "ProjectBookedUnit"."unitId" = "units"."id"
                  AND "ProjectBookedUnit"."status" IN ('on_hold', 'booked')
              )
          )`),
          'unitCount',
        ],
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
    };

    if (data.search) {
      query.where = {
        ...query.where,
        [Op.or]: [
          { name: { [Op.iLike]: `%${data.search}%` } },
          { projectCode: { [Op.iLike]: `%${data.search}%` } },
        ],
      };
    }

    const { rows, count } = await this.findAndCountAll(query, { logger: true });

    const rowData = await Promise.all(
      rows.map(async (row) => {
        row = await row.get({ plain: true });
        const address = row?.address;

        if (address && address.latitude && address.longitude) {
          const weather = await this.detectWeather(
            address.latitude,
            address.longitude
          );
          return { ...row, weather: weather?.data };
        }
        return { ...row, weather: null };
      })
    );

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Project'),
      data: {
        rows: rowData,
        pagination: {
          totalCount: count,
          start: data.start,
          limit: data.limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.createProjectAndSubProject = async (
  data,
  loggedInUser,
  parentProjectId = null
) => {
  try {
    data.organizationId =
      data.organizationId ?? loggedInUser.currentOrganizationId;
    let parentProject;
    if (parentProjectId) {
      const { success, data: projectData } =
        await this.checkAndGetProject(parentProjectId);
      if (!success) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Project'),
        };
      }

      const existingProject = await Project.findOne({
        where: {
          name: {
            [Op.iLike]: data.name,
          },
          parentProjectId: parentProjectId,
        },
        attributes: ['id'],
      });
      if (existingProject) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(`Project with name ${data.name}`),
        };
      }
      parentProject = projectData;
    }

    const payload = {
      ...data,
      parentProjectId: parentProjectId || null,
      createdBy: loggedInUser.id,
    };

    if (data.addressDetails) {
      const addressPayload = {
        ...data.addressDetails,
        addressType: addressType.PROJECT,
      };

      const address = await Address.create(addressPayload);
      payload['addressId'] = address.id;
    }

    const project = await Project.create(payload);

    if (!parentProjectId) {
      const projectCode = await this.generateOrUpdateProjectCode(
        project.organizationId,
        project.id
      );
      project.projectCode = projectCode;
      await project.save();
    }

    if (project) {
      await DocumentRepository.createProjectFolder(
        project,
        loggedInUser,
        parentProject
      ).then(async () => {
        if (data.drawings && data.drawings.length > 0) {
          await DrawingRepository.createDrawings(
            data.drawings,
            drawingType.PROJECT,
            project.id,
            loggedInUser,
            project.id
          );
        }
      });
    }

    await this.addCreatorToProjectTeam(project.id, loggedInUser.id);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Project'),
      data: project,
    };
  } catch (error) {
    console.log('error.....', error);
    throw new Error(error);
  }
};

exports.addCreatorToProjectTeam = async (projectId, userId) => {
  try {
    const projectTeamPayload = {
      projectId: projectId,
      userId: userId,
      invitedBy: userId,
      status: defaultStatus.ACCEPTED,
    };
    await ProjectTeam.create(projectTeamPayload);
  } catch (error) {
    throw new Error(error);
  }
};

exports.detectWeather = async (latitude, longitude) => {
  try {
    const url = `${process.env.OPEN_WEATHER_BASE_API}/weather?lat=${latitude}&lon=${longitude}&appid=${process.env.OPEN_WEATHER_API_KEY}&units=metric`;

    const response = await axios.get(url);
    if (response.status >= 200 && response.status < 300) {
      return {
        success: true,
        message: successMessage.FETCH_SUCCESS_MESSAGE('Weather'),
        data: response.data,
      };
    } else {
      return {
        success: false,
        message: errorMessage.WEATHER_API_ERROR,
      };
    }
  } catch (error) {
    throw new Error(error);
  }
};

exports.addDrwaingsToProject = async (projectId, data, loggerInUser) => {
  try {
    const project = await checkExistence(Project, { id: projectId });
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Project with Id ${projectId}`),
      };
    }

    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.PROJECT,
        project.id,
        loggerInUser,
        projectId
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Drawings'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.putProjectWithDrawing = async (projectId, data, loggedInUser) => {
  const transaction = await Project.sequelize.transaction();
  try {
    const query = {
      where: {
        id: projectId,
      },
    };

    const existingProject = await Project.findOne(query);
    if (!existingProject) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Project'),
      };
    }

    if (data.name && data.name !== existingProject.name) {
      await existingProject.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated project name from '${existingProject.name}' to '${data.name}'`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.about && data.about !== existingProject.about) {
      await existingProject.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated project about from '${existingProject.about}' to '${data.about}'`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.logo && data.logo !== existingProject.logo) {
      await existingProject.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated project logo from '${existingProject.logo}' to '${data.logo}'`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.reraNumber && data.reraNumber !== existingProject.reraNumber) {
      await existingProject.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated project Rera Number from '${existingProject.reraNumber}' to '${data.reraNumber}'`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    Object.assign(existingProject, data);
    await existingProject.save({ transaction });

    if (data.addressDetails) {
      await AddressRepository.createOrUpdateAddress(
        data.addressDetails,
        addressType.PROJECT,
        loggedInUser,
        existingProject.addressId
      );
    }

    await transaction.commit();
    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.updateDrawing(
        data.drawings,
        drawingType.PROJECT,
        existingProject.id,
        loggedInUser,
        existingProject.id
      );
    }

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Project'),
    };
  } catch (error) {
    await transaction.rollback();
    console.error('Error updating project with drawing:', error);
    return {
      success: false,
      message: error.message || 'An error occurred while updating the project.',
    };
  }
};
