const { actionType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const DocumentActivityLog = sequelize.define(
    'DocumentActivityLog',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      actionType: {
        type: DataTypes.ENUM(actionType.getActionTypeArray()),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );
  DocumentActivityLog.associate = function (models) {
    DocumentActivityLog.belongsTo(models.User, {
      as: 'performer',
      foreignKey: 'performedBy',
      allowNull: true,
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    DocumentActivityLog.belongsTo(models.User, {
      as: 'target',
      foreignKey: 'performedOn',
      allowNull: true,
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    DocumentActivityLog.belongsTo(models.Document, {
      foreignKey: 'documentId',
      as: 'document',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };
  return DocumentActivityLog;
};
