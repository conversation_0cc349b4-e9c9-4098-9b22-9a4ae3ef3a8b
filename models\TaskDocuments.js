module.exports = (sequelize, DataTypes) => {
  const TaskDocuments = sequelize.define(
    'TaskDocuments',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TaskDocuments.associate = (models) => {
    TaskDocuments.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'task',
    });

    TaskDocuments.belongsTo(models.Document, {
      foreignKey: 'documentId',
      as: 'document',
    });

    TaskDocuments.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    TaskDocuments.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
    });
  };

  return TaskDocuments;
};
