'use strict';
const { customerStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status DROP DEFAULT',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status TYPE VARCHAR(255)',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'DROP TYPE IF EXISTS enum_customer_status',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'DROP TYPE IF EXISTS "enum_Customer_status"',
        { transaction }
      );

      await queryInterface.sequelize.query(
        `CREATE TYPE "enum_Customer_status" AS ENUM ('new_lead', 'qualified', 'site_visit_completed', 'negotiation', 'on_hold', 'no_future_activity', 'lost', 'unqualified', 'archived')`,
        { transaction }
      );

      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status TYPE "enum_Customer_status" USING status::"enum_Customer_status"',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status SET DEFAULT :defaultValue, ALTER COLUMN status SET NOT NULL',
        {
          replacements: { defaultValue: customerStatus.NEW_LEAD },
          transaction,
        }
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    return queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status DROP DEFAULT',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status TYPE VARCHAR(255)',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'DROP TYPE IF EXISTS "enum_Customer_status"',
        { transaction }
      );

      await queryInterface.sequelize.query(
        `CREATE TYPE "enum_Customer_status" AS ENUM ('new_lead', 'qualified', 'site_visit_completed', 'negotiation', 'on_hold', 'no_future_activity', 'lost', 'unqualified')`,
        { transaction }
      );

      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status TYPE "enum_Customer_status" USING status::"enum_Customer_status"',
        { transaction }
      );

      await queryInterface.sequelize.query(
        'ALTER TABLE "Customer" ALTER COLUMN status SET DEFAULT :defaultValue, ALTER COLUMN status SET NOT NULL',
        {
          replacements: { defaultValue: customerStatus.NEW_LEAD },
          transaction,
        }
      );
    });
  },
};
