'use strict';

module.exports = (sequelize, DataTypes) => {
  const UnitType = sequelize.define(
    'UnitType',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  return UnitType;
};
