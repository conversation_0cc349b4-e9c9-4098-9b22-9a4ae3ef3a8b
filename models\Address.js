const { addressType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Address = sequelize.define(
    'Address',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      addressType: {
        type: DataTypes.ENUM(addressType.getAddressTypeArray()),
        allowNull: true,
      },
      city: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      state: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      pincode: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      country: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      addressLine2: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      landmark: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      latitude: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      longitude: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Address.associate = (models) => {
    Address.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      allowNull: true,
    });

    Address.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
      allowNull: true,
    });

    // Address.belongsTo(models.ContactPerson, {
    //   foreignKey: 'contactPersonId',
    //   as: 'contactPerson',
    //   onDelete: 'CASCADE',
    // });
  };

  return Address;
};
