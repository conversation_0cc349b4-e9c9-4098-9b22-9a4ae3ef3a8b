'use strict';
// const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Amenities = sequelize.define(
    'Amenities',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      logo: {
        allowNull: true,
        type: DataTypes.TEXT,
        // get() {
        //   return OPTIONS.generateCloudFrontUrl(this.getDataValue('logo'));
        // },
        // set(file) {
        //   if (file) {
        //     this.setDataValue('logo', `uploads/${file.split('uploads/')[1]}`);
        //   }
        // },
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  return Amenities;
};
