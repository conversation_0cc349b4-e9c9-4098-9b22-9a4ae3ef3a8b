const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const RequirementController = require('@controllers/v1/crm/Requirement');
const RequirementSchema = require('@schema-validation/Requirement');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(RequirementSchema.updateRequirement),
  ErrorHandleHelper.requestValidator,
  RequirementController.createRequirement
);

router.put(
  '/:id',
  checkSchema(RequirementSchema.updateRequirement),
  ErrorHandleHelper.requestValidator,
  RequirementController.updateRequirement
);

module.exports = router;
