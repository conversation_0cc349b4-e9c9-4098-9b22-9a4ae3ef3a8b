module.exports = (sequelize, DataTypes) => {
  const WbsActivityQualityControl = sequelize.define(
    'WbsActivityQualityControl',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      isCompleted: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WbsActivityQualityControl.associate = (models) => {
    WbsActivityQualityControl.belongsTo(models.WbsActivity, {
      foreignKey: 'wbsActivityId',
      as: 'wbsActivity',
    });

    WbsActivityQualityControl.belongsTo(models.TemplateMaster, {
      foreignKey: 'templateId',
      as: 'template',
    });
  };

  return WbsActivityQualityControl;
};
