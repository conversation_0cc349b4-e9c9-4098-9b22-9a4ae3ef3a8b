const { Contractor } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.findOne = async (query) => await Contractor.findOne(query);

exports.findAll = async (query) => await Contractor.findAll(query);

exports.findAndCountAll = async (query) =>
  await Contractor.findAndCountAll(query);

exports.checkAndCreate = async (data, loggedInUser) => {
  const query = {
    where: {
      email: data.email,
    },
  };
  const existingContractor = await this.findOne(query);
  if (existingContractor) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST(`Contractor`),
    };
  }

  const payload = {
    ...data,
    createdBy: loggedInUser.id,
    organizationId: loggedInUser.currentOrganizationId,
  };

  const contractor = await Contractor.create(payload);

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Contractor'),
    data: contractor,
  };
};

exports.updateContractor = async (contractorId, data, loggedInUser) => {
  const contractor = await Contractor.findOne({
    where: {
      id: contractorId,
    },
  });

  if (!contractor) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST(`Contractor`),
    };
  }

  const payload = {
    ...data,
    updatedBy: loggedInUser.id,
  };

  await Contractor.update(payload, {
    where: {
      id: contractorId,
    },
  });

  const updatedContractor = await Contractor.findOne({
    where: {
      id: contractorId,
    },
  });

  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE(`Contractor`),
    data: updatedContractor,
  };
};
