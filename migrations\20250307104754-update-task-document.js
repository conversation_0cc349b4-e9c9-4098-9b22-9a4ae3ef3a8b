module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.removeColumn('TaskDocuments', 'documentUrl', {
        transaction,
      });

      await queryInterface.addColumn(
        'TaskDocuments',
        'fileName',
        {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        'TaskDocuments',
        'fileType',
        {
          type: Sequelize.STRING(100),
          allowNull: true,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        'TaskDocuments',
        'filePath',
        {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        { transaction }
      );

      await queryInterface.addColumn(
        'TaskDocuments',
        'fileSize',
        {
          type: Sequelize.DECIMAL(10, 2),
          allowNull: true,
        },
        { transaction }
      );
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.transaction(async (transaction) => {
      await queryInterface.addColumn(
        'TaskDocuments',
        'documentUrl',
        {
          type: Sequelize.TEXT,
          allowNull: true,
        },
        { transaction }
      );

      await queryInterface.removeColumn('TaskDocuments', 'fileName', {
        transaction,
      });
      await queryInterface.removeColumn('TaskDocuments', 'fileType', {
        transaction,
      });
      await queryInterface.removeColumn('TaskDocuments', 'filePath', {
        transaction,
      });
      await queryInterface.removeColumn('TaskDocuments', 'fileSize', {
        transaction,
      });
    });
  },
};
