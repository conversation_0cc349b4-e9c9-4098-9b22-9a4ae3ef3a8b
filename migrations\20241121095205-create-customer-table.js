'use strict';
const { customerType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Customer', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      type: {
        type: Sequelize.ENUM(...customerType.getCustomerTypeArray()),
        allowNull: false,
      },
      firstName: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      lastName: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
        unique: true,
      },
      countryCode: {
        type: Sequelize.STRING(5),
        allowNull: true,
      },
      contactNumber: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      sourceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Source',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      subSourceId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Source',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Customer');
  },
};
