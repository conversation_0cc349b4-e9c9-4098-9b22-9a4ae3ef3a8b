'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('EmployeeTemplateItem', 'annualAmount', {
      type: Sequelize.DECIMAL(20, 2),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('EmployeeTemplateItem', 'annualAmount');
  },
};
