'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Address_addressType" ADD VALUE 'invited_user';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // Rolling back this change is tricky with ENUMs, as PostgreSQL does not support removing individual enum values directly
    // You will need to manually recreate the ENUM type if you want to roll this back (to remove the added value).
    // To rollback, you might recreate the enum without the added value.
    // Or leave the change as-is, because removing the ENUM value isn't straightforward in PostgreSQL.
  },
};
