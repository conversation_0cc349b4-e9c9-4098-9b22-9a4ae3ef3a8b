const sequelize = require('sequelize');
const { Op } = sequelize;
const _ = require('lodash');

const { sequelize: sequelizeTransaction } = require('../../models/index');

const { Journal, TransactionEntry, User, Designation } = require('..');
const { successMessage, errorMessage } = require('@config/options');
const {
  handleTransactionsEnteries,
  deleteTransactionEntry,
  deleteJournalTransactionEntry,
  getReferenceIdFromTransactionEntryWithAccountAndReferenceType,
} = require('./TransactionEntryRepository');
const options = require('@config/options');

exports.getJournal = async (query) => await Journal.findOne(query);

exports.findAndCountAll = async (query) => await Journal.findAndCountAll(query);

exports.createJournal = async (objParams) => {
  const transaction = await sequelizeTransaction.transaction();

  try {
    const {
      organizationId,
      date,
      journalNumber,
      notes,
      arrTransactions,
      journalState,
      createdBy,
    } = objParams;
    const query = {
      where: {
        journalNumber: {
          [Op.eq]: journalNumber,
        },
      },
    };

    const existingJournal = await this.getJournal(query);
    if (existingJournal) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Journal Number'),
      };
    }

    let totalAmount = 0;
    if (!_.isEmpty(arrTransactions)) {
      totalAmount = arrTransactions.reduce(
        (sum, transaction) => sum + transaction.creditAmount,
        0
      );
    }

    const objParamsForJournalCreate = {
      organizationId,
      date,
      journalNumber,
      notes,
      createdBy,
      totalAmount,
      journalState,
    };

    const createdJournal = await Journal.create(objParamsForJournalCreate, {
      transaction,
    });

    if (!_.isEmpty(arrTransactions)) {
      await Promise.allSettled(
        arrTransactions.map(async (objEach) => {
          const objPramsForTransactionEnteries = {
            ...objEach,
            referenceId: createdJournal.id,
            referenceType: options.transactionTypeForEntries.JOURNAL,
            createdBy,
          };
          return await handleTransactionsEnteries(
            objPramsForTransactionEnteries,
            transaction
          );
        })
      );
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Journal'),
      data: createdJournal,
    };
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Journal'),
      };
    }
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.updateJournal = async (objParams) => {
  const transaction = await sequelizeTransaction.transaction();

  try {
    const {
      date,
      journalNumber,
      notes,
      arrTransactions,
      createdBy,
      journalId,
      deletedTransactions,
    } = objParams;

    let totalAmount = 0;
    if (!_.isEmpty(arrTransactions)) {
      totalAmount = arrTransactions.reduce(
        (sum, transaction) => sum + transaction.creditAmount,
        0
      );
    }

    const objParamsForJournalCreate = {
      date,
      journalNumber,
      notes,
      createdBy,
      totalAmount,
    };

    await Journal.update(
      objParamsForJournalCreate,
      {
        where: {
          id: journalId,
        },
      },
      {
        transaction,
      }
    );

    if (!_.isEmpty(arrTransactions)) {
      await Promise.allSettled(
        arrTransactions.map(async (objEach) => {
          const objPramsForTransactionEnteries = {
            ...objEach,
            referenceId: journalId,
            referenceType: options.transactionTypeForEntries.JOURNAL,
            createdBy,
          };
          return await handleTransactionsEnteries(
            objPramsForTransactionEnteries,
            transaction
          );
        })
      );
    }

    const arrDeletedTransactionIds = deletedTransactions.split(',');
    if (!_.isEmpty(arrDeletedTransactionIds)) {
      await Promise.allSettled(
        arrDeletedTransactionIds.map(async (transactionId) => {
          await deleteTransactionEntry(transactionId, transaction);
        })
      );
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Journal'),
      data: {},
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.getAllJournalData = async (objParams) => {
  try {
    let {
      organizationId,
      search = '',
      start = 0,
      limit = 10,
      journalId = '',
      flagShowTransaction = false,
      journalState = '',
      amount,
      accountId = '',
      dateStart,
      dateEnd,
      createdBy = '',
    } = objParams;

    if (!_.isEmpty(accountId)) {
      const objParamsToFindReferenceId = {
        referenceType: options.transactionTypeForEntries.JOURNAL,
        accountIds: accountId.split(','),
      };
      const arrReferenceData =
        await getReferenceIdFromTransactionEntryWithAccountAndReferenceType(
          objParamsToFindReferenceId
        );

      journalId = arrReferenceData.map((objEachRecord) => {
        return objEachRecord.referenceId;
      });
    }

    const query = {
      where: {
        ...(organizationId && { organizationId }),
        ...(journalId && { id: journalId }),
        ...(!_.isEmpty(journalState) && {
          journalState: journalState.split(','),
        }),
        ...(!_.isEmpty(createdBy) && { createdBy: createdBy.split(',') }),
        ...(amount && { totalAmount: amount }),
        status: options.recordStatus.ACTIVE,
      },
      include: [
        {
          model: User,
          as: 'createdByUser',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name'],
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
    };

    if (dateStart && dateEnd) {
      query.where.date = {
        [Op.between]: [dateStart, dateEnd], // Filters transactions between dateStart and dateEnd
      };
    } else if (dateStart) {
      query.where.date = {
        [Op.gte]: dateStart, // Filters transactions after dateStart
      };
    } else if (dateEnd) {
      query.where.date = {
        [Op.lte]: dateEnd, // Filters transactions before dateEnd
      };
    }

    if (search) {
      query.where = {
        ...query.where,
        [Op.or]: [
          { notes: { [Op.iLike]: `%${search}%` } },
          { journalNumber: { [Op.iLike]: `%${search}%` } },
        ],
      };
    }

    if (flagShowTransaction) {
      query.include = [
        {
          model: TransactionEntry,
          as: 'transactionEntries',
          scope: options.transactionTypeForEntries.JOURNAL,
          required: false,
          where: {
            status: options.recordStatus.ACTIVE,
          },
        },
      ];
    }

    const { rows, count } = await this.findAndCountAll(query, { logger: true });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Journal'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteJournal = async (journalId) => {
  const transaction = await sequelizeTransaction.transaction();
  try {
    await Journal.update(
      {
        status: options.recordStatus.DELETED,
      },
      {
        where: {
          id: journalId,
        },
      },
      { transaction }
    );

    await deleteJournalTransactionEntry(journalId, transaction);

    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Journal'),
      data: {},
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};
