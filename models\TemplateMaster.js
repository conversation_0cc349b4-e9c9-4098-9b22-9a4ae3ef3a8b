'use strict';
const { templateType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const TemplateMaster = sequelize.define(
    'TemplateMaster',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      templateType: {
        type: DataTypes.ENUM(templateType.getTemplateTypeArray()),
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TemplateMaster.associate = function (models) {
    TemplateMaster.hasMany(models.TemplateEntity, {
      foreignKey: 'templateId',
      as: 'templateEntities',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      allowNull: true,
    });

    TemplateMaster.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
      allowNull: false,
    });
  };

  return TemplateMaster;
};
