exports.createCategory = {
  name: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
  imageUrl: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Image URL must be a valid string',
    },
  },
  parentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Parent ID must be a valid integer',
    },
  },
};

exports.updateCategory = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Category Id is required',
    },
    isInt: {
      errorMessage: 'Category Id must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
  imageUrl: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Image URL must be a valid string',
    },
  },
  parentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Parent ID must be a valid integer',
    },
  },
};

exports.deleteCategory = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Category Id is required',
    },
    isInt: {
      errorMessage: 'Category Id must be a valid integer',
    },
  },
};
