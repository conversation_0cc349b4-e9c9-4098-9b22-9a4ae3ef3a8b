const {
  requestPriority,
  requestType,
  requestStatus,
  leaveType,
} = require('@config/options');

exports.createRequestSchema = {
  title: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Title must be a string',
    },
  },
  requestedTo: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'requestedTo cannot be empty',
    },
    isInt: {
      errorMessage: 'requestedTo must be an integer',
    },
  },
  leaveForEmployeeId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'leaveForEmployeeId must be an integer',
    },
  },
  recordId: {
    in: ['body'],
    custom: {
      options: (value, { req }) => {
        const { requestType } = req.body;
        if (
          !['general_request', 'leave_request'].includes(requestType) &&
          (value === undefined || value === null)
        ) {
          throw new Error(
            'recordId cannot be empty when requestType is not general_request or leave_request'
          );
        }
        if (value !== undefined && value !== null && !Number.isInteger(value)) {
          throw new Error('recordId must be an integer');
        }
        return true;
      },
    },
  },
  requestType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'requestType cannot be empty',
    },
    isIn: {
      options: [requestType.getValues()],
      errorMessage: `Invalid request type. Must be one of: ${requestType.getValues().join(', ')}`,
    },
  },
  priority: {
    in: ['body'],
    isIn: {
      options: [requestPriority.getValues()],
      errorMessage: `Invalid priority. Must be one of: ${requestPriority.getValues().join(', ')}`,
    },
    optional: true,
  },
  dueDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'dueDate must be a valid ISO 8601 date',
    },
  },
  fromDate: {
    in: ['body'],
    custom: {
      options: (value, { req }) => {
        if (req.body.requestType === 'leave_request' && !value) {
          throw new Error('fromDate is required for leave_request');
        }
        return true;
      },
    },
    isISO8601: {
      errorMessage: 'fromDate must be a valid ISO 8601 date',
    },
    optional: true,
  },
  toDate: {
    in: ['body'],
    custom: {
      options: (value, { req }) => {
        if (req.body.requestType === 'leave_request' && !value) {
          throw new Error('toDate is required for leave_request');
        }
        return true;
      },
    },
    isISO8601: {
      errorMessage: 'toDate must be a valid ISO 8601 date',
    },
    optional: true,
  },
  leaveType: {
    in: ['body'],
    custom: {
      options: (value, { req }) => {
        if (req.body.requestType === 'leave_request' && !value) {
          throw new Error('leaveType is required for leave_request');
        }
        return true;
      },
    },
    isIn: {
      options: [leaveType.getValues()],
      errorMessage: `Invalid leaveType. Must be one of: ${leaveType.getValues()}`,
    },
    optional: true,
  },
  totalDays: {
    in: ['body'],
    custom: {
      options: (value, { req }) => {
        if (
          req.body.requestType === 'leave_request' &&
          (value === undefined || value === null)
        ) {
          throw new Error('totalDays is required for leave_request');
        }
        return true;
      },
    },
    isInt: {
      errorMessage: 'totalDays must be an integer',
    },
    optional: true,
  },
  notes: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Notes must be a string',
    },
  },
};

exports.updateRequestSchema = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Request Id is required',
    },
    isString: {
      errorMessage: 'Request Id must be a valid string',
    },
  },
  requestedTo: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'requestedTo must be an integer',
    },
  },
  priority: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [requestPriority.getValues()],
      errorMessage: `Invalid priority. Must be one of: ${requestPriority.getValues().join(', ')}`,
    },
  },
  dueDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'dueDate must be a valid ISO 8601 date',
    },
  },
  notes: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Notes must be a string',
    },
  },
  status: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [requestStatus.getValues()],
      errorMessage: 'Invalid status value',
    },
  },
};

exports.getRequestDetailsSchema = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Request Id is required',
    },
    isString: {
      errorMessage: 'Request Id must be a valid string',
    },
  },
};

exports.requestListSchema = {
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
  dueDate: {
    in: ['query'],
    optional: true,
    isISO8601: {
      errorMessage: 'Due date must be a valid ISO 8601 date',
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'Search term must be a string',
    },
  },
};
