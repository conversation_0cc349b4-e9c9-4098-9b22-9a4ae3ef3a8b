const OfficeLocationRepository = require('../../../../models/repositories/LocationRepository');
const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
  successMessage,
} = require('../../../../config/options');

exports.createLocation = async (req, res) => {
  try {
    const { success, message, data } =
      await OfficeLocationRepository.checkAndCreateOfficeLocation(req.body);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message, data }));
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getLocations = async (req, res) => {
  try {
    const { start = 0, limit = 10, search = '' } = req.query;

    const { message, data } = await OfficeLocationRepository.getOfficeLocations(
      {
        start,
        limit,
        search,
      }
    );

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getLocationById = async (req, res) => {
  try {
    const location = await OfficeLocationRepository.getLocation({
      where: { id: req.params.id },
    });
    if (!location) {
      return res
        .status(resCode.HTTP_NOT_FOUND)
        .json(
          genRes(
            resCode.HTTP_NOT_FOUND,
            errorMessage.DOES_NOT_EXIST('office location'),
            errorTypes.ENTITY_NOT_FOUND
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.DETAIL_MESSAGE('office location'),
        data: location,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.createUpdateLocation = async (req, res) => {
  try {
    const { success, message, data } =
      await OfficeLocationRepository.checkAndUpdateOfficeLocation(
        req.params.id,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
