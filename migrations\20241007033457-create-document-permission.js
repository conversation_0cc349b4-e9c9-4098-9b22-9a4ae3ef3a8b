'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('DocumentPermission', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Document',
          key: 'id',
        },
        onDelete: 'CASCADE',
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
        allowNull: false,
      },
      canView: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      canEdit: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      canDownload: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addConstraint('DocumentPermission', {
      fields: ['documentId', 'userId'],
      type: 'unique',
      name: 'unique_document_user_permission',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('DocumentPermission');
  },
};
