module.exports = (sequelize, DataTypes) => {
  const TaskFollowers = sequelize.define(
    'TaskFollowers',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TaskFollowers.associate = (models) => {
    TaskFollowers.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'task',
    });

    TaskFollowers.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return TaskFollowers;
};
