const { contactPersonCategory, contactPersonType } = require('@config/options');

exports.createContactPerson = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'customerId cannot be empty',
    },
    isInt: {
      errorMessage: 'customerId must be an integer',
    },
  },
  contactCategory: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Contact category cannot be empty',
    },
    custom: {
      options: (value) => {
        const allowedValues =
          contactPersonCategory.getContactPersonCategoryArray();
        if (!allowedValues.includes(value)) {
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid contact category provided',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Business name must be a string',
    },
  },
  firstName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'First name must be a string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Last name must be a string',
    },
  },
  countryCode: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Country code cannot be empty',
    },
  },
  contactNumber: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Contact number cannot be empty',
    },
    isNumeric: {
      errorMessage: 'Contact number must be numeric',
    },
  },
  email: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Email cannot be empty',
    },
    isEmail: {
      errorMessage: 'Email must be a valid email address',
    },
  },
  panNumber: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'PAN number must be a string',
    },
  },
  gstNumber: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'GST number must be a string',
    },
  },
  contactType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Contact type cannot be empty',
    },
    custom: {
      options: (value) => {
        const allowedValues = contactPersonType.getContactPersonTypeArray();
        if (!allowedValues.includes(value)) {
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid contact type provided',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'About must be a string',
    },
  },
  details: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Details must be a string',
    },
  },
  address: {
    in: ['body'],
    optional: true,
    isObject: {
      errorMessage: 'Address must be an object',
    },
    custom: {
      options: (value) => {
        const addressSchema = {
          city: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'City must be a string',
            },
          },
          state: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'State must be a string',
            },
          },
          pincode: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Pincode must be a string',
            },
          },
          country: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Country must be a string',
            },
          },
          address: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Address must be a string',
            },
          },
          addressLine2: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Address Line 2 must be a string',
            },
          },
          landmark: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Landmark must be a string',
            },
          },
          latitude: {
            in: ['body'],
            optional: true,
            isFloat: {
              errorMessage: 'Latitude must be a float',
            },
          },
          longitude: {
            in: ['body'],
            optional: true,
            isFloat: {
              errorMessage: 'Longitude must be a float',
            },
          },
        };

        // Validate the address object against the address schema
        for (const key in addressSchema) {
          if (addressSchema.hasOwnProperty(key)) {
            const validator = addressSchema[key];
            if (value[key] !== undefined) {
              const result = validator.customSanitizer
                ? validator.customSanitizer(value[key])
                : value[key];
              if (result !== value[key]) {
                throw new Error(validator.errorMessage);
              }
            }
          }
        }
        return true;
      },
    },
  },
};

exports.updateContactPerson = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'ContactPerson Id is required',
    },
    isInt: {
      errorMessage: 'ContactPerson Id must be a valid integer',
    },
  },
  contactCategory: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'Contact category cannot be empty',
    },
    custom: {
      options: (value) => {
        const allowedValues =
          contactPersonCategory.getContactPersonCategoryArray();
        if (!allowedValues.includes(value)) {
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid contact category provided',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Business name must be a string',
    },
  },
  firstName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'First name must be a string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Last name must be a string',
    },
  },
  countryCode: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'Country code cannot be empty',
    },
  },
  contactNumber: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'Contact number cannot be empty',
    },
    isNumeric: {
      errorMessage: 'Contact number must be numeric',
    },
  },
  email: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'Email cannot be empty',
    },
    isEmail: {
      errorMessage: 'Email must be a valid email address',
    },
  },
  panNumber: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'PAN number must be a string',
    },
  },
  gstNumber: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'GST number must be a string',
    },
  },
  contactType: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = contactPersonType.getContactPersonTypeArray();
        if (!allowedValues.includes(value)) {
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid contact type provided',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'About must be a string',
    },
  },
  details: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Details must be a string',
    },
  },
  address: {
    in: ['body'],
    optional: true,
    isObject: {
      errorMessage: 'Address must be an object',
    },
    custom: {
      options: (value) => {
        const addressSchema = {
          city: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'City must be a string',
            },
          },
          state: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'State must be a string',
            },
          },
          pincode: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Pincode must be a string',
            },
          },
          country: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Country must be a string',
            },
          },
          address: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Address must be a string',
            },
          },
          addressLine2: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Address Line 2 must be a string',
            },
          },
          landmark: {
            in: ['body'],
            optional: true,
            isString: {
              errorMessage: 'Landmark must be a string',
            },
          },
          latitude: {
            in: ['body'],
            optional: true,
            isFloat: {
              errorMessage: 'Latitude must be a float',
            },
          },
          longitude: {
            in: ['body'],
            optional: true,
            isFloat: {
              errorMessage: 'Longitude must be a float',
            },
          },
        };

        // Validate the address object against the address schema
        for (const key in addressSchema) {
          if (addressSchema.hasOwnProperty(key)) {
            const validator = addressSchema[key];
            if (value[key] !== undefined) {
              const result = validator.customSanitizer
                ? validator.customSanitizer(value[key])
                : value[key];
              if (result !== value[key]) {
                throw new Error(validator.errorMessage);
              }
            }
          }
        }
        return true;
      },
    },
  },
};

exports.removeContactPerson = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'ContactPerson Id is required',
    },
    isInt: {
      errorMessage: 'ContactPerson Id must be a valid integer',
    },
  },
};
