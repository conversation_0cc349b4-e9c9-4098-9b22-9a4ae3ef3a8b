const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ApprovalWorkflowController = require('@controllers/v1/settings/ApprovalWorkflow');
const ApprovalWorkflowSchema = require('@schema-validation/settings/ApprovalWorkflow');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/approval-workflow',
  checkSchema(ApprovalWorkflowSchema.addWorkflow),
  ErrorHandleHelper.requestValidator,
  ApprovalWorkflowController.createApprovalWorkflow
);

router.put(
  '/approval-workflow/:id',
  checkSchema(ApprovalWorkflowSchema.updateWorkflow),
  ErrorHandleHelper.requestValidator,
  ApprovalWorkflowController.updateApprovalWorkflow
);

module.exports = router;
