'use strict';

/** @type {import('sequelize-cli').Migration} */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Address_addressType" 
      ADD VALUE 'contact_person';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    // Leaving down empty because removing enum values is not supported in PostgreSQL
  },
};
