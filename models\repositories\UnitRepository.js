const sequelize = require('sequelize');
const { Op } = require('sequelize');

const {
  Unit,
  Project,
  Floor,
  Amenities,
  Drawing,
  Space,
  UnitType,
} = require('..');
const {
  errorMessage,
  successMessage,
  drawingType,
} = require('../../config/options');
const DrawingRepository = require('./DrawingRepository');
const { checkExistence, validateExistence } = require('@helpers/QueryHelper');

exports.findOne = async (query) => await Unit.findOne(query);
exports.findAll = async (query) => await Unit.findAll(query);

exports.getAllAmenities = async (query) => await Amenities.findAll(query);

exports.validateAndCreateUnit = async (projectId, data, loggedInUser) => {
  const { floorId, unitTypeId, amenities } = data;
  try {
    await Promise.all([
      validateExistence(Project, projectId, 'Project'),
      floorId && validateExistence(Floor, floorId, 'Floor'),
      unitTypeId && validateExistence(UnitType, unitTypeId, 'UnitType'),
    ]);

    if (amenities && amenities.length) {
      const existingAmenities = await this.getAllAmenities({
        where: {
          id: { [Op.in]: data.amenities },
        },
      });
      if (existingAmenities.length !== data.amenities.length) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            'One or more amenities in the list do not exist'
          ),
        };
      }
    }

    const existingUnit = await Unit.findOne({
      where: {
        name: {
          [Op.iLike]: data.name,
        },
        projectId: projectId,
      },
      attributes: ['id'],
    });
    if (existingUnit) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(`Unit with name ${data.name}`),
      };
    }

    const payload = {
      ...data,
      projectId: projectId,
      createdBy: loggedInUser.id,
    };

    const createdUnit = await Unit.create(payload);
    if (amenities && amenities.length) {
      await createdUnit.setAmenities(amenities);
    }

    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.UNIT,
        createdUnit.id,
        loggedInUser,
        projectId
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Unit'),
      data: createdUnit,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.validateAndUpdateUnit = async (unitId, data, loggedInUser) => {
  const { projectId, floorId, unitTypeId, amenities } = data;
  try {
    const unit = await validateExistence(Unit, unitId, 'Unit');
    await Promise.all([
      projectId && validateExistence(Project, projectId, 'Project'),
      floorId && validateExistence(Floor, floorId, 'Floor'),
      unitTypeId && validateExistence(UnitType, unitTypeId, 'UnitType'),
    ]);

    if (amenities && amenities.length) {
      const existingAmenities = await this.getAllAmenities({
        where: {
          id: { [Op.in]: amenities },
        },
      });
      if (existingAmenities.length !== amenities.length) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            'One or more amenities in the list do not exist'
          ),
        };
      }
    }

    if (data.name) {
      const existingUnit = await Unit.findOne({
        where: {
          name: {
            [Op.iLike]: data.name,
          },
          projectId: unit.projectId,
          id: {
            [Op.ne]: unitId,
          },
        },
        attributes: ['id'],
      });
      if (existingUnit) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(`Unit with name ${data.name}`),
        };
      }
    }

    const updatedUnit = Object.assign(unit, data);
    await unit.save();

    if (amenities && amenities.length) {
      await updatedUnit.setAmenities(amenities);
    }
    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.deleteDrawingsByUnitId(updatedUnit.id);
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.UNIT,
        updatedUnit.id,
        loggedInUser,
        unit.projectId
      );
    }

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Unit'),
      data: updatedUnit,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message,
    };
  }
};

exports.getLastAddUnit = async (subProjectId) => {
  return Unit.findOne({
    where: {
      projectId: subProjectId,
    },
    attributes: [
      [sequelize.literal('CAST(name AS INTEGER)'), 'lastUnitNumber'],
      [
        sequelize.literal(`
          LPAD(CAST(CAST(name AS INTEGER) + 1 AS TEXT), 3, '0')
        `),
        'nextUnitNumber',
      ],
    ],
    order: [[sequelize.literal('CAST(name AS INTEGER)'), 'DESC']],
  });
};

exports.createDuplicateUnit = async (unitId, projectId, loggedInUser) => {
  const query = {
    where: {
      id: unitId,
      projectId,
    },
    include: [
      {
        model: Drawing,
        as: 'drawings',
        attributes: {
          exclude: [
            'createdAt',
            'updatedAt',
            'id',
            'unitId',
            'projectId',
            'floorId',
          ],
        },
      },
      {
        model: Space,
        as: 'spaces',
        attributes: {
          exclude: ['createdAt', 'updatedAt', 'id', 'unitId', 'floorId'],
        },
      },
      {
        model: Floor,
        as: 'floorsInUnit',
        attributes: {
          exclude: ['createdAt', 'updatedAt', 'id', 'unitId'],
        },
        include: [
          {
            model: Drawing,
            as: 'drawings',
            attributes: {
              exclude: [
                'createdAt',
                'updatedAt',
                'id',
                'unitId',
                'projectId',
                'floorId',
              ],
            },
          },
          {
            model: Space,
            as: 'spaces',
            attributes: {
              exclude: ['createdAt', 'updatedAt', 'id', 'unitId', 'floorId'],
            },
          },
        ],
      },
    ],
    attributes: {
      exclude: ['createdAt', 'updatedAt', 'id'],
    },
  };

  const unit = await Unit.findOne(query);
  if (!unit) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('floor'),
    };
  }
  const payloadUnit = {
    ...(await unit.get({
      plain: true,
    })),
  };

  // if (unit.isNamingFormat) {
  //   const lastUnit = await this.getLastAddUnit(payloadUnit.projectId);
  //   payloadUnit.name = lastUnit?.get('nextUnitNumber');
  // }

  payloadUnit.createdBy = loggedInUser.id;
  if (Array.isArray(payloadUnit.spaces)) {
    payloadUnit.spaces.forEach((space) => {
      space.createdBy = loggedInUser.id;
    });
  }
  if (Array.isArray(payloadUnit.floorsInUnit)) {
    payloadUnit.floorsInUnit.forEach((floor) => {
      floor.createdBy = loggedInUser.id;
      if (Array.isArray(floor.spaces)) {
        floor.spaces.forEach((space) => {
          space.createdBy = loggedInUser.id;
        });
      }
    });
  }

  const duplicateUnit = await Unit.bulkCreate([payloadUnit], {
    include: [
      {
        model: Drawing,
        as: 'drawings',
      },
      {
        model: Space,
        as: 'spaces',
      },
      {
        model: Floor,
        as: 'floorsInUnit',

        include: [
          {
            model: Drawing,
            as: 'drawings',
          },
          {
            model: Space,
            as: 'spaces',
          },
        ],
      },
    ],
  });

  return {
    success: true,
    message: successMessage.DUPLICATE_ADD_SUCCESS_MESSAGE('Unit'),
    data: duplicateUnit,
  };
};

exports.deleteUnit = async (unitId) => {
  try {
    const unit = await Unit.findOne({
      where: { id: unitId },
      include: [
        {
          model: Floor,
          as: 'floorsInUnit',
          attributes: ['id'],
        },
      ],
    });
    if (!unit) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Unit with Id ${unitId}`),
      };
    }

    if (unit.floorsInUnit.length > 0) {
      const floorIds = unit.floorsInUnit.map((floor) => floor.id);
      await Floor.destroy({ where: { id: floorIds } });
    }

    await DrawingRepository.deleteDrawingsByUnitId(unit.id);
    await Unit.destroy({ where: { id: unitId } });
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Unit'),
    };
  } catch (error) {
    throw error;
  }
};

exports.addDrwaingsToUnit = async (unitId, data, loggedInUser) => {
  try {
    const unit = await checkExistence(Unit, { id: unitId });
    if (!unit) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Unit with Id ${unitId}`),
      };
    }

    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.UNIT,
        unit.id,
        loggedInUser,
        unit.projectId
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Drawings'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
