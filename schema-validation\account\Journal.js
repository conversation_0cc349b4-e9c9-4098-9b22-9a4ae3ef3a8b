exports.createJournal = {
  organizationId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
  },
  date: {
    in: ['body'],
    isDate: {
      options: { format: 'MM-DD-YYYY' },
      errorMessage: 'Date must be in the format MM-DD-YYYY',
    },
  },
  journalNumber: {
    in: ['body'],
    isString: true,
  },
  notes: {
    in: ['body'],
    isString: true,
    isLength: {
      options: { max: 500 },
      errorMessage: 'Notes cannot exceed 500 characters',
    },
  },
  journalState: {
    in: ['body'],
    trim: true,
    isString: true,
  },
  arrTransactions: {
    in: ['body'],
    isArray: {
      options: { min: 1 },
      errorMessage: 'Transactions must be a non-empty array',
    },
  },
  'arrTransactions.*.transactionId': {
    in: ['body'],
    isInt: {
      options: { min: 0 },
      errorMessage: 'Transaction ID must be a non-negative integer',
    },
    toInt: true,
  },
  'arrTransactions.*.accountId': {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'Account ID must be a positive integer',
    },
    toInt: true,
  },
  'arrTransactions.*.description': {
    in: ['body'],
    isString: true,
    isLength: {
      options: { max: 255 },
      errorMessage: 'Description cannot exceed 255 characters',
    },
  },
  'arrTransactions.*.creditAmount': {
    in: ['body'],
    optional: true,
    isFloat: {
      options: { min: 0 },
      errorMessage: 'Credit amount must be a non-negative number',
    },
    toFloat: true,
  },
  'arrTransactions.*.debitAmount': {
    in: ['body'],
    optional: true,
    isFloat: {
      options: { min: 0 },
      errorMessage: 'Debit amount must be a non-negative number',
    },
    toFloat: true,
  },
};

exports.getJournal = {
  organizationId: {
    in: ['query'],
    trim: true,
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'search must be a string',
    },
  },
  journalState: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'journalState must be a string',
    },
  },
  amount: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'amount must be a integer',
    },
  },
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
};

exports.deleteJournal = {
  journalId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'journalId must be an integer',
    },
  },
};

exports.getJournalById = {
  journalId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'journalId must be an integer',
    },
  },
};

exports.updateJournal = {
  date: {
    in: ['body'],
    isDate: {
      options: { format: 'MM-DD-YYYY' },
      errorMessage: 'Date must be in the format MM-DD-YYYY',
    },
  },
  journalNumber: {
    in: ['body'],
    isString: true,
  },
  notes: {
    in: ['body'],
    isString: true,
    isLength: {
      options: { max: 500 },
      errorMessage: 'Notes cannot exceed 500 characters',
    },
  },
  journalState: {
    in: ['body'],
    trim: true,
    isString: true,
  },
  arrTransactions: {
    in: ['body'],
    isArray: {
      options: { min: 1 },
      errorMessage: 'Transactions must be a non-empty array',
    },
  },
  'arrTransactions.*.transactionId': {
    in: ['body'],
    isInt: {
      options: { min: 0 },
      errorMessage: 'Transaction ID must be a non-negative integer',
    },
    toInt: true,
  },
  'arrTransactions.*.accountId': {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'Account ID must be a positive integer',
    },
    toInt: true,
  },
  'arrTransactions.*.description': {
    in: ['body'],
    isString: true,
    isLength: {
      options: { max: 255 },
      errorMessage: 'Description cannot exceed 255 characters',
    },
  },
  'arrTransactions.*.creditAmount': {
    in: ['body'],
    optional: true,
    isFloat: {
      options: { min: 0 },
      errorMessage: 'Credit amount must be a non-negative number',
    },
    toFloat: true,
  },
  'arrTransactions.*.debitAmount': {
    in: ['body'],
    optional: true,
    isFloat: {
      options: { min: 0 },
      errorMessage: 'Debit amount must be a non-negative number',
    },
    toFloat: true,
  },
};
