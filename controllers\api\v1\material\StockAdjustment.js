const { genRes, errorMessage, resCode } = require('@config/options');
const StockAdjustmentRepository = require('@repo/StockAdjustmentRepository');

exports.createStockAdjustment = async (req, res) => {
  try {
    const { success, message, data } =
      await StockAdjustmentRepository.validateAndCreateStockAdjustment(
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateStockAdjustment = async (req, res) => {
  const { id: stockAdjustmentId } = req.params;
  try {
    const { success, message, data } =
      await StockAdjustmentRepository.validateAndUpdateStockAdjustment(
        stockAdjustmentId,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateStockAdjustmentStatus = async (req, res) => {
  const { id: stockAdjustmentId } = req.params;
  try {
    const { success, message, data } =
      await StockAdjustmentRepository.validateAndUpdateStockAdjustmentStatus(
        stockAdjustmentId,
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteStockAdjustment = async (req, res) => {
  const { id: stockAdjustmentId } = req.params;
  try {
    const { success, message } =
      await StockAdjustmentRepository.validateAndDeleteStockAdjustment(
        stockAdjustmentId
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
