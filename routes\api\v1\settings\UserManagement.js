const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const AuthHandler = require('@models/helpers/AuthHelper');
const UserManagementController = require('@controllers/v1/settings/UserManagement');
const UserManagementSchema = require('@schema-validation/settings/UserManagement');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  AuthHandler.authenticateJWT(),
  checkSchema(UserManagementSchema.inviteUser),
  ErrorHandleHelper.requestValidator,
  UserManagementController.inviteUser
);

router.post(
  '/:id/resend',
  AuthHandler.authenticateJWT(),
  checkSchema(UserManagementSchema.resendInvitation),
  ErrorHandleHelper.requestValidator,
  UserManagementController.resendInvitation
);

router.patch(
  '/:id/manage',
  checkSchema(UserManagementSchema.manageInvite),
  ErrorHandleHelper.requestValidator,
  UserManagementController.manageInvite
);

router.delete(
  '/:id',
  checkSchema(UserManagementSchema.deleteInvite),
  AuthHandler.authenticateJWT(),
  UserManagementController.deleteInvite
);

module.exports = router;
