const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const OrgController = require('@controllers/v1/organization/Organization');
const OrgSchema = require('@schema-validation/organization/Organization');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.get(
  '/:inviteId',
  checkSchema(OrgSchema.getOrganizationByInviteId),
  ErrorHandleHelper.requestValidator,
  OrgController.getOrganizationByInviteId
);

module.exports = router;
