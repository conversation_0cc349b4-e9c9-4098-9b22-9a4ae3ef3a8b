const { genRes, errorMessage, resCode } = require('../../../config/options');

const OrganizationBrandRepository = require('../../../models/repositories/OrganizationBrandRepository');

exports.registerOrganizationBrand = async (req, res) => {
  try {
    const { organizationId } = req.user;
    const { success, message } =
      await OrganizationBrandRepository.checkAndCreateOrgBrandWithMedia(
        req.body,
        organizationId
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
