const { SubTasks, Tasks } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.findOne = async (query) => await Tasks.findOne(query);

exports.addSubtask = async (taskId, data) => {
  try {
    const query = {
      where: {
        id: taskId,
      },
    };
    const task = await this.findOne(query);
    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('task'),
      };
    }

    const subtaskPayload = {
      taskId,
      ...data,
    };

    const subtask = await SubTasks.create(subtaskPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('subtask'),
      data: subtask,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateSubtask = async (subTaskId, data) => {
  try {
    const subtask = await SubTasks.findByPk(subTaskId);
    if (!subtask) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('subTaskId'),
      };
    }

    Object.assign(subtask, data);
    await subtask.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('subtask'),
      data: subtask,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteSubtask = async (subTaskId) => {
  try {
    const subtask = await SubTasks.findByPk(subTaskId);
    if (!subtask) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('subtask'),
      };
    }

    await subtask.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('subtask'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
