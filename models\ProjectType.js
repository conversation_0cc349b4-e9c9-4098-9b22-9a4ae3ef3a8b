const { structureType, projectTypeCategory } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const ProjectType = sequelize.define(
    'ProjectType',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      projectCategory: {
        type: DataTypes.ENUM(
          ...projectTypeCategory.getProjectTypeCategoryArray()
        ),
        allowNull: true,
      },
      structureType: {
        type: DataTypes.ENUM(...structureType.getStructureTypeArray()),
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ProjectType.associate = (models) => {
    ProjectType.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
    });
  };

  return ProjectType;
};
