exports.createJournal = {
  organizationId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'organizationId must be a integer',
    },
  },
  name: {
    in: ['body'],
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  year: {
    in: ['body'],
    isInt: {
      errorMessage: 'year must be a integer',
    },
  },
  period: {
    in: ['body'],
    isString: true,
    isString: {
      errorMessage: 'period must be a string',
    },
  },
  income: {
    in: ['body'],
    isArray: {
      options: { min: 0 },
      errorMessage: 'income must be a non-empty array',
    },
  },
  'income.*.budgetEntryId': {
    in: ['body'],
    isInt: {
      options: { min: 0 },
      errorMessage: 'budgetEntryId must be a non-negative integer',
    },
    toInt: true,
  },
  'income.*.accountId': {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'accountId must be a positive integer',
    },
    toInt: true,
  },
  'income.*.budgetData': {
    in: ['body'],
    isObject: {
      errorMessage: 'budgetData must be an object',
    },
  },
};

exports.getBudget = {
  organizationId: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'organizationId must be a integer',
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'search must be a string',
    },
  },
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
};

exports.deleteBudget = {
  budgetId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'budgetId must be an integer',
    },
  },
};

exports.getBudgetById = {
  budgetId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'budgetId must be an integer',
    },
  },
};

exports.updateJournal = {
  budgetId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'budgetId must be an integer',
    },
  },
  name: {
    in: ['body'],
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  year: {
    in: ['body'],
    isInt: {
      errorMessage: 'year must be a integer',
    },
  },
  period: {
    in: ['body'],
    isString: true,
    isString: {
      errorMessage: 'period must be a string',
    },
  },
  income: {
    in: ['body'],
    isArray: {
      options: { min: 0 },
      errorMessage: 'income must be a non-empty array',
    },
  },
  'income.*.budgetEntryId': {
    in: ['body'],
    isInt: {
      options: { min: 0 },
      errorMessage: 'budgetEntryId must be a non-negative integer',
    },
    toInt: true,
  },
  'income.*.accountId': {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'accountId must be a positive integer',
    },
    toInt: true,
  },
  'income.*.budgetData': {
    in: ['body'],
    in: ['body'],
    isObject: {
      errorMessage: 'budgetData must be an object',
    },
  },
};
