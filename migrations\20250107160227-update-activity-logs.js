'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.renameColumn(
      'ActivityLog',
      'activityType',
      'actionType'
    );

    await queryInterface.addColumn('ActivityLog', 'activityOn', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });
    await queryInterface.addColumn('ActivityLog', 'recordId', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.renameColumn(
      'ActivityLog',
      'taskName',
      'activityDescription'
    );

    await queryInterface.changeColumn('ActivityLog', 'activityDescription', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.changeColumn('ActivityLog', 'employeeId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Employee',
        key: 'id',
      },
    });
    await queryInterface.changeColumn('ActivityLog', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Employee',
        key: 'id',
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.renameColumn(
      'ActivityLog',
      'actionType',
      'activityType'
    );
    await queryInterface.removeColumn('ActivityLog', 'activityOn');
    await queryInterface.removeColumn('ActivityLog', 'recordId');
    await queryInterface.renameColumn(
      'ActivityLog',
      'activityDescription',
      'taskName'
    );
    await queryInterface.changeColumn('ActivityLog', 'taskName', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });
    await queryInterface.changeColumn('ActivityLog', 'employeeId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Employee',
        key: 'id',
      },
    });
    await queryInterface.changeColumn('ActivityLog', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Employee',
        key: 'id',
      },
    });
  },
};
