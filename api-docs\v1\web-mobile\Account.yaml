paths:
  /account:
    post:
      summary: Creates account
      description: Create new account for organization
      operationId: createAccount
      tags:
        - Account
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAccount"
      responses:
        "201":
          description: Account created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: "Retrieves a list of accounts"
      description: "Retrieves a list of accounts based on optional query parameters such as organization ID, search string, start offset, limit, and status."
      operationId: "getAccountList"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: organizationId
          in: query
          required: true
          description: The ID of the organization to filter projects by
          schema:
            type: integer
        - name: search
          in: query
          required: false
          description: A search string to filter projects by name or description
          schema:
            type: string
        - name: start
          in: query
          required: false
          description: The start offset for pagination
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The limit of projects to return per page
          schema:
            type: integer
        - name: parentAccountId
          in: query
          required: false
          description: The parentAccountId to get list of child account ids
          schema:
            type: integer
        - name: accountCategory
          in: query
          required: false
          description: The account category of the accounts
          schema:
            type: string
        - name: accountType
          in: query
          required: false
          description: The account types of the accounts
          schema:
            type: string
      responses:
        "200":
          description: "Accoutn code verification successfully."
        "400":
          description: "Account code verification error."
        "500":
          description: "Internal Server Error"
  /account/getAccountCategoryTypeList:
    get:
      summary: "Get Account category and type list"
      description: "Get Account category and type list for dropdown"
      operationId: "getAccountCategoryTypeList"
      tags:
        - Account
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Account category type list fetched successfully."
        "500":
          description: "Internal Server Error"
  /account/getAccountEnteryTypeList:
    get:
      summary: "Get Account entries type list"
      description: "Get Account entries type list for dropdown"
      operationId: "getAccountEnteryTypeList"
      tags:
        - Account
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Account entry type list fetched successfully."
        "500":
          description: "Internal Server Error"
  /account/createDefaultAccount:
    get:
      summary: "Create default account for organization"
      description: "This apis is used to create default account for organization, when an organization is created"
      operationId: "accountCreateDefaultAccount"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: organizationId
          in: query
          required: true
          description: The ID of the organization to filter projects by
          schema:
            type: integer
      responses:
        "200":
          description: "Default account created successfully."
        "500":
          description: "Internal Server Error"
  /account/{accountId}/getTransactionEntries:
    get:
      summary: Get account transactions
      description: Get account transactions
      operationId: getAccountTransactions
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account Id to fetch
          schema:
            type: integer
        - name: start
          in: query
          required: false
          description: The start offset for pagination
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The limit of projects to return per page
          schema:
            type: integer
        - name: transactionType
          in: query
          required: false
          description: transaction type journal/expense/payment_received
          schema:
            type: string
      responses:
        "201":
          description: Account delted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /account/{accountId}:
    get:
      summary: Get account by Id
      description: Get account by Id
      operationId: getAccountById
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account Id to fetch
          schema:
            type: integer
      responses:
        "201":
          description: Account delted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    delete:
      summary: Creates account
      description: Create new account for organization
      operationId: deleteAccount
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account Id to delete
          schema:
            type: integer
      responses:
        "201":
          description: Account delted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    patch:
      summary: Update Account details
      description: Update Account details
      operationId: patchAccount
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: accountId
          in: path
          required: true
          description: Account Id to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateAccount"
      responses:
        "201":
          description: Account delted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /account/verifyAccountCode/{code}:
    get:
      summary: "Verify unique account code"
      description: "This endpoint returns if the account code entered is unique or not"
      operationId: "verifyAccountCode"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: code
          in: path
          required: true
          description: Account code
          schema:
            type: string
      responses:
        "200":
          description: "Accoutn code verification successfully."
        "400":
          description: "Account code verification error."
        "500":
          description: "Internal Server Error"
  /account/journal:
    post:
      summary: Creates Journal entry
      description: Create new journal entry
      operationId: createJournal
      tags:
        - Account
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createJournal"
      responses:
        "201":
          description: Journal created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: "Get journal account list"
      description: "Get journal account list"
      operationId: "getJournayListData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: organizationId
          in: query
          required: true
          description: The ID of the organization to filter projects by
          schema:
            type: integer
        - name: search
          in: query
          required: false
          description: A search string to filter projects by name or description
          schema:
            type: string
        - name: journalState
          in: query
          required: false
          description: Journal State draft/saved/under_approval/rejected/escalated
          example: saved
          schema:
            type: string
        - name: amount
          in: query
          required: false
          description: Journal Amount
          schema:
            type: integer
        - name: accountId
          in: query
          required: false
          description: comma seperated account ids
          schema:
            type: string
        - name: dateStart
          in: query
          required: false
          description: Start date range of expense
          schema:
            type: string
        - name: dateEnd
          in: query
          required: false
          description: End date range of expense
          schema:
            type: string
        - name: createdBy
          in: query
          required: false
          description: Id of the created by user
          schema:
            type: integer
        - name: start
          in: query
          required: false
          description: The start offset for pagination
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The limit of projects to return per page
          schema:
            type: integer
      responses:
        "200":
          description: "Journal information fetched successfully."
        "400":
          description: "Journal information fetched error."
        "500":
          description: "Internal Server Error"
  /account/journal/{journalId}:
    get:
      summary: "Get journal data by id"
      description: "Get journal data by id"
      operationId: "getJournayByIdData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: journalId
          in: path
          required: true
          description: Journal Id
          schema:
            type: integer
      responses:
        "200":
          description: "Journal information fetched successfully."
        "400":
          description: "Journal information fetched error."
        "500":
          description: "Internal Server Error"
    put:
      summary: Update Journal entry
      description: Update new journal entry
      operationId: updateJournal
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: journalId
          in: path
          required: true
          description: Journal Id
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateJournal"
      responses:
        "201":
          description: Journal updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    delete:
      get:
      summary: "Delete journal data by id"
      description: "Delete journal data by id"
      operationId: "deleteJournayData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: journalId
          in: path
          required: true
          description: Journal Id
          schema:
            type: integer
      responses:
        "200":
          description: "Journal information deleted successfully."
        "400":
          description: "Journal information deleted error."
        "500":
          description: "Internal Server Error"
  


  /account/budget:
    post:
      summary: Creates Budget
      description: Create new budget entry
      operationId: createBudget
      tags:
        - Account
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createBudget"
      responses:
        "201":
          description: Budget created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: "Get budget list"
      description: "Get budget list"
      operationId: "getBudgetListData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: organizationId
          in: query
          required: true
          description: Organization Id of which budget belongs to
          schema:
            type: string
        - name: search
          in: query
          required: false
          description: A search string to filter projects by name or description
          schema:
            type: string
        - name: start
          in: query
          required: false
          description: The start offset for pagination
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The limit of projects to return per page
          schema:
            type: integer
      responses:
        "200":
          description: "Budget information fetched successfully."
        "400":
          description: "Budget information fetched error."
        "500":
          description: "Internal Server Error"
  /account/budget/{budgetId}:
    get:
      summary: "Get budget by id"
      description: "Get budget  by id"
      operationId: "getBudgetByIdData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: budgetId
          in: path
          required: true
          description: Budget Id
          schema:
            type: integer
      responses:
        "200":
          description: "Budget information fetched successfully."
        "400":
          description: "Budget information fetched error."
        "500":
          description: "Internal Server Error"
    put:
      summary: Update Budget entry
      description: Update new Budget entry
      operationId: updateBudget
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: budgetId
          in: path
          required: true
          description: Budget Id
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateBudget"
      responses:
        "201":
          description: Budget updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    delete:
      get:
      summary: "Delete budget data by id"
      description: "Delete budget data by id"
      operationId: "deleteBudgetData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: budgetId
          in: path
          required: true
          description: Budget Id
          schema:
            type: integer
      responses:
        "200":
          description: "Budget information deleted successfully."
        "400":
          description: "Budget information deleted error."
        "500":
          description: "Internal Server Error"
  
  /account/expense:
    post:
      summary: Creates Expense
      description: Create new expense entry
      operationId: createExpense
      tags:
        - Account
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createExpense"
      responses:
        "201":
          description: Expense created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: "Get expense list"
      description: "Get expense list"
      operationId: "getExpenseListData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: organizationId
          in: query
          required: true
          description: Organization Id of which budget belongs to
          schema:
            type: string
        - name: search
          in: query
          required: false
          description: A search string to filter projects by name or description
          schema:
            type: string
        - name: start
          in: query
          required: false
          description: The start offset for pagination
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The limit of projects to return per page
          schema:
            type: integer
        - name: dateStart
          in: query
          required: false
          description: Start date range of expense
          schema:
            type: string
        - name: dateEnd
          in: query
          required: false
          description: End date range of expense
          schema:
            type: string
        - name: expenseState
          in: query
          required: false
          description: Expense state 'draft'/'under_approval'/'approved'
          schema:
            type: string
        - name: expenseAccountId
          in: query
          required: false
          description: Account Id of the expense
          schema:
            type: integer
        - name: vendorId
          in: query
          required: false
          description: vendor Id of the expense
          schema:
            type: integer
        - name: paidThroughAccountId
          in: query
          required: false
          description: Paid through account Id of the expense
          schema:
            type: integer
      responses:
        "200":
          description: "Budget information fetched successfully."
        "400":
          description: "Budget information fetched error."
        "500":
          description: "Internal Server Error"
  /account/expense/{expenseId}:
    get:
      summary: "Get expense by id"
      description: "Get expense  by id"
      operationId: "getExpenseByIdData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: expenseId
          in: path
          required: true
          description: Budget Id
          schema:
            type: integer
      responses:
        "200":
          description: "Expense information fetched successfully."
        "400":
          description: "Expense information fetched error."
        "500":
          description: "Internal Server Error"
    put:
      summary: Update Expense entry
      description: Update Expense entry
      operationId: updateExpense
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: expenseId
          in: path
          required: true
          description: Expense Id
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateExpense"
      responses:
        "201":
          description: Expense updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    delete:
      get:
      summary: "Delete expense data by id"
      description: "Delete expense data by id"
      operationId: "deleteExpenseData"
      tags:
        - Account
      security:
        - bearerAuth: []
      parameters:
        - name: expenseId
          in: path
          required: true
          description: Expense Id
          schema:
            type: integer
      responses:
        "200":
          description: "Expense information deleted successfully."
        "400":
          description: "Expense information deleted error."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    updateExpense:
      type: object
      properties:
        date:
          type: string
          example: 01-01-2025
          description: Date of the journal entry
        expenseAccountId:
          type: integer
          example: 1
          description: Account Id of the expense
        paidThroughAccountId:
          type: integer
          example: 1
          description: Account Id of the payment
        amount:
          type: integer
          example: 100
          description: Amount of the expense
        vendorId:
          type: integer
          example: null
          description: Vendor Id of the expense
        invoiceNumber:
          type: string
          example: 001
          description: Invoice number of the expense
        notes:
          type: string
          example: This is test note for expense
          description: Expense notes
      required:
        - date
        - expenseAccountId
        - paidThroughAccountId
        - amount
    createExpense:
      type: object
      properties:
        organizationId:
          type: integer
          example: 1
          description: Selected Organization Id
        date:
          type: string
          example: 01-01-2025
          description: Date of the journal entry
        expenseAccountId:
          type: integer
          example: 1
          description: Account Id of the expense
        paidThroughAccountId:
          type: integer
          example: 1
          description: Account Id of the payment
        amount:
          type: integer
          example: 100
          description: Amount of the expense
        vendorId:
          type: integer
          example: null
          description: Vendor Id of the expense
        invoiceNumber:
          type: string
          example: 001
          description: Invoice number of the expense
        notes:
          type: string
          example: This is test note for expense
          description: Expense notes
      required:
        - organizationId
        - date
        - expenseAccountId
        - paidThroughAccountId
        - amount
    updateBudget:
      type: object
      properties:
        name:
          type: string
          example: Budget name
          description: Name of the budget
        year:
          type: integer
          example: 2025
          description: Year of the budget
        period:
          type: string
          example: MONTHLY
          description: enum values of budget period MONTHLY, QUARTERLY, HALF_YEARLY,YEARLY
          enum: [MONTHLY, QUARTERLY, HALF_YEARLY,YEARLY]
        deletedBudgetEntry:
          type: string
          example: 1,2,3
          description: comma seperated deleted budget entry id
        income:
          type: array
          items:
            type: object
            properties:
              budgetEntryId:
                type: integer
                example: 0
                description: Budget id of this income entry
              accountId:
                type: integer
                example: 1
                description: Account Id for transaction
              budgetData:
                type: object
                example: {}
                description: Budget entry data in JSON as per period selected
        expense:
          type: array
          items:
            type: object
            properties:
              budgetEntryId:
                type: integer
                example: 0
                description: Budget id of this income entry
              accountId:
                type: integer
                example: 1
                description: Account Id for transaction
              budgetData:
                type: object
                example: {}
                description: Budget entry data in JSON as per period selected
    createBudget:
      type: object
      properties:
        organizationId:
          type: integer
          example: 1
          description: Selected Organization Id
        name:
          type: string
          example: Budget name
          description: Name of the budget
        year:
          type: integer
          example: 2025
          description: Year of the budget
        period:
          type: string
          example: MONTHLY
          description: enum values of budget period MONTHLY, QUARTERLY, HALF_YEARLY,YEARLY
          enum: [MONTHLY, QUARTERLY, HALF_YEARLY,YEARLY]
        income:
          type: array
          items:
            type: object
            properties:
              budgetEntryId:
                type: integer
                example: 0
                description: Budget id of this income entry
              accountId:
                type: integer
                example: 1
                description: Account Id for transaction
              budgetData:
                type: object
                example: {}
                description: Budget entry data in JSON as per period selected
        expense:
          type: array
          items:
            type: object
            properties:
              budgetEntryId:
                type: integer
                example: 0
                description: Budget id of this income entry
              accountId:
                type: integer
                example: 1
                description: Account Id for transaction
              budgetData:
                type: object
                example: {}
                description: Budget entry data in JSON as per period selected
      required:
        - organizationId
        - name
        - year
        - period
    updateJournal:
      type: object
      properties:
        date:
          type: string
          example: 01-01-2025
          description: Date of the journal entry
        journalNumber:
          type: string
          example: 001
          description: Journal number of the entry
        notes:
          type: string
          example: This is test note for journal
          description: Journal notes
        journalState:
          type: string
          example: approved
          description: State of the journal draft/under_approval/approved
        deletedTransactions:
          type: string
          example: 1,2,3
          description: comma seperated deleted transaction id
        arrTransactions:
          type: array
          items:
            type: object
            properties:
              transactionId:
                type: integer
                example: 0
                description: Account Id for transaction 0 for new transactions and transaction id for update transaction
              accountId:
                type: integer
                example: 1
                description: Account Id for transaction
              description:
                type: string
                example: This is credit entry
                description: Account Id for transaction
              creditAmount:
                type: integer
                example: 100
                description: Account Id for transaction
              debitAmount:
                type: integer
                example: 0
                description: Account Id for transaction
    createJournal:
      type: object
      properties:
        organizationId:
          type: string
          example: 001
          description: Journal number of the entry
        date:
          type: string
          example: 01-01-2025
          description: Date of the journal entry
        journalNumber:
          type: string
          example: 001
          description: Journal number of the entry
        notes:
          type: string
          example: This is test note for journal
          description: Journal notes
        journalState:
          type: string
          example: saved
          description: State of the journal draft/saved/under_approval
        arrTransactions:
          type: array
          items:
            type: object
            properties:
              transactionId:
                type: integer
                example: 0
                description: Account Id for transaction 0 for new transactions and transaction id for update transaction
              accountId:
                type: integer
                example: 1
                description: Account Id for transaction
              description:
                type: string
                example: This is credit entry
                description: Account Id for transaction
              creditAmount:
                type: integer
                example: 100
                description: Account Id for transaction
              debitAmount:
                type: integer
                example: 0
                description: Account Id for transaction
    updateAccount:
      type: object
      properties:
        accountCategory:
          type: string
          example: ASSETS
          description: 
        accountType:
          type: string
          example: OTHER_ASSETS
          description: 
        name:
          type: string
          example: Account name
          description: 
        description:
          type: string
          example: This is account description
          description: 
        bankAccountNumber:
          type: string
          example: **************
          description: 
        ifscCode:
          type: string
          example: HDFC000123
          description: 
        parentAccountId:
          type: integer
          example: 1
          description: 
      required:
        - organizationId
        - accountCategory
        - accountType
        - name
    createAccount:
      type: object
      properties:
        organizationId:
          type: integer
          example: 1
          description: Selected Organization Id
        accountCategory:
          type: string
          example: ASSETS
          description: 
        accountType:
          type: string
          example: OTHER_ASSETS
          description: 
        name:
          type: string
          example: Account name
          description: 
        code:
          type: string
          example: accountcode
          description: 
        description:
          type: string
          example: This is account description
          description: 
        bankAccountNumber:
          type: string
          example: **************
          description: 
        ifscCode:
          type: string
          example: HDFC000123
          description: 
        parentAccountId:
          type: integer
          example: 1
          description: 
        openingBalance:
          type: integer
          example: 100
          description: Opening Balance
        totalCredit:
          type: integer
          example: 100
          description: 
        totalDebit:
          type: integer
          example: 200
          description: 
      required:
        - organizationId
        - accountCategory
        - accountType
        - name