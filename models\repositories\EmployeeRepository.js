const { Op } = require('sequelize');
const _ = require('lodash');

const {
  Employee,
  Address,
  User,
  Attendance,
  EmployeeTemplate,
  EmployeeTemplateItem,
  UserOrganization,
  sequelize,
  Designation,
} = require('..');
const UserRepository = require('./UserRepository');
const {
  addressType,
  templateEntityType,
  templateType,
  inviteStatus,
} = require('../../config/options');

const {
  errorMessage,
  successMessage,
  usersRoles,
  defaultStatus,
} = require('../../config/options');
const EmployeeDocumentRepository = require('./EmployeeDocumentRepository');
const BankDetailsRepository = require('./BankDetailsRepository');
const EmployeeTemplateItemRepository = require('./EmployeeTemplateItemRepository');
const UserHelper = require('../helpers/UserHelper');
const EmailHelper = require('@helpers/EmailHelper');
const AccountRepository = require('./AccountRepository');
// const { ModulePermissionRepository } = require('./ModulePermissionRepository');
exports.findAndCountAll = async (query) =>
  await Employee.findAndCountAll(query);

exports.findOne = async (query) => await Employee.findOne(query);
exports.findAll = async (query) => await Employee.findAll(query);

exports.createEmployee = async (data, userId, loggerInUser, transaction) => {
  const payload = {
    employeeCode: data.employeeCode,
    dateOfJoining: data.dateOfJoining,
    maritalStatus: data.maritalStatus,
    reportedTo: data.reportedTo,
    organizationId: loggerInUser.currentOrganizationId,
    userId: userId,
  };

  if (data.addressDetails) {
    const addressPayload = {
      ...data.addressDetails,
      userId: userId,
      addressType: addressType.EMPLOYEE,
    };
    await Address.create(addressPayload, { transaction });
  }

  return await Employee.create(payload, { transaction });
};

exports.updateEmployee = async (data, employeeId, loggedInUser) => {
  const query = {
    where: {
      id: employeeId,
    },
    include: [
      {
        model: User,
        as: 'user',
        where: {
          status: { [Op.notIn]: [defaultStatus.DELETED] },
        },
        attributes: ['id'],
      },
    ],
  };
  const existingEmployee = await Employee.findOne(query);

  if (!existingEmployee) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Employee'),
    };
  }

  const userExist = await UserRepository.checkUser(
    data,
    existingEmployee.user.id
  );

  if (userExist.success) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('User Email and Mobile Number'),
    };
  }

  existingEmployee.employeeCode = data.employeeCode;
  existingEmployee.dateOfJoining = data.dateOfJoining;
  existingEmployee.maritalStatus = data.maritalStatus;
  existingEmployee.reportedTo = data.reportedTo;
  existingEmployee.organizationId = loggedInUser.currentOrganizationId;

  if (data.addressDetails) {
    const existingAddress = await Address.findOne({
      where: {
        userId: existingEmployee.userId,
      },
    });
    existingAddress.address = data.addressDetails.address;
    existingAddress.city = data.addressDetails.city;
    existingAddress.pincode = data.addressDetails.pincode;
    existingAddress.state = data.addressDetails.state;
    await existingAddress.save();
  }
  await UserRepository.createAndUpdateUser(data, existingEmployee.user.id);
  return await existingEmployee.save();
};

exports.addEmployeeWithDocument = async (data, loggedInUser) => {
  const transaction = await sequelize.transaction();
  try {
    const userExist = await UserRepository.checkUser(data);
    if (userExist.success) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('user email and mobile number'),
      };
    }

    const query = {
      where: {
        employeeCode: data.employeeCode,
        organizationId: loggedInUser.currentOrganizationId,
      },
    };

    const checkEmployee = await Employee.findOne(query);

    if (checkEmployee) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Employee Code'),
      };
    }
    data.role = usersRoles.EMPLOYEE;
    data.organizationId = loggedInUser.currentOrganizationId;

    const newUser = await UserRepository.createAndUpdateUser(
      data,
      null,
      transaction
    );

    // Sent Invitation Mail to Employee
    if (newUser) {
      const role = await Designation.findOne({
        where: {
          id: newUser.designationId,
        },
      });
      if (!role) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Role with Id ${newUser.designationId}`
          ),
        };
      }

      const createInvitation = await UserOrganization.create(
        {
          userId: newUser.id,
          designationId: data.designationId,
          organizationId: data.organizationId,
          inviteStatus: inviteStatus.PENDING,
          isPrimary: true,
        },
        { transaction }
      );

      const invitationLink = `${process.env.FRONTEND_URL}/invite?id=${createInvitation.id}`;
      const emailData = {
        to: newUser.email,
        variables: {
          invitation_link: invitationLink,
          recipient_email: newUser.email,
          role_title: role.name,
        },
      };
      await EmailHelper.sendEmail(emailData, 'role_invitation');
    }

    const newEmployee = await this.createEmployee(
      data,
      newUser.id,
      loggedInUser,
      transaction
    );

    const objAccountCreate = {
      organizationId: data.organizationId,
      accountCategory: 'ASSETS',
      accountType: 'OTHER_CURRENT_ASSETS',
      referenceId: newEmployee.id,
      referenceType: 'employee',
      name: `${data.firstName} ${data.lastName}`,
      code: _.kebabCase(`${newEmployee.id}_${data.firstName} ${data.lastName}`),
      description: `Employee account for ${data.firstName} ${data.lastName}`,
      bankAccountNumber: '',
      ifscCode: '',
      parentAccountId: null,
      openingBalance: 0,
      totalCredit: 0,
      totalDebit: 0,
      createdBy: loggedInUser.id,
    };

    await AccountRepository.validateAndCreateAccount(objAccountCreate);

    if (data.employeeDocument && data.employeeDocument.length > 0) {
      await EmployeeDocumentRepository.addBulkEmployeeDocument(
        data.employeeDocument,
        newUser.id,
        loggedInUser.id,
        transaction
      );
    }

    if (data.bankDetails) {
      await BankDetailsRepository.createBankDetails(
        data.bankDetails,
        newUser.id,
        loggedInUser.id,
        transaction
      );
    }

    // await ModulePermissionRepository.assignDefaultPermissionsToUser(
    //   newUser.id,
    //   user.organizationId,
    //   user.id,
    //   data.designationId
    // );

    await transaction.commit();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Employee'),
      data: {
        user: UserHelper.modifyOutputData(newUser),
        employee: newEmployee,
      },
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.putEmployeeWithDocument = async (data, employeeId, loggedInUser) => {
  const query = {
    where: {
      id: { [Op.not]: employeeId },
      employeeCode: data.employeeCode,
      organizationId: loggedInUser.currentOrganizationId,
    },
  };

  const checkEmployeeCode = await Employee.findOne(query);

  if (checkEmployeeCode) {
    return {
      success: false,
      message: errorMessage.ALREADY_EXIST('Employee Code'),
    };
  }
  const updateEmployee = await this.updateEmployee(
    data,
    employeeId,
    loggedInUser
  );

  await EmployeeDocumentRepository.updateBulkEmployeeDocument(
    data.employeeDocument,
    updateEmployee.userId,
    loggedInUser.id
  );

  await BankDetailsRepository.updateBankDetails(
    data.bankDetails,
    updateEmployee.userId,
    loggedInUser.id
  );

  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE('Employee'),
  };
};

exports.patchEmployeeStatus = async (data, employeeId, loggerInUser) => {
  const query = {
    where: {
      id: employeeId,
    },
    include: [
      {
        model: User,
        as: 'user',
      },
    ],
  };

  const existingEmployee = await Employee.findOne(query);
  const currentUserOrganization = await UserOrganization.findOne({
    where: {
      organizationId: loggerInUser.currentOrganizationId,
      userId: existingEmployee.userId,
    },
  });

  if (!currentUserOrganization) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('User Organization'),
    };
  }
  existingEmployee.user.status = data.status;
  currentUserOrganization.status = data.status;

  await currentUserOrganization.save(); //this is future use when we have one employee have multiple account.
  await existingEmployee.user.save(); // Explicitly save the user instance
  await existingEmployee.save();
  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE('Employee'),
    data: {
      existingEmployee,
      currentUserOrganization,
    },
  };
};

exports.addEmployeeLeavePolicy = async (employeeId, data, loggerInUser) => {
  const query = {
    where: {
      id: employeeId,
    },
  };

  const existingEmployee = await Employee.findOne(query);

  if (!existingEmployee) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Employee'),
    };
  }

  const { success, message } =
    await EmployeeTemplateItemRepository.createEmployeeLeavePolicy(
      data,
      employeeId,
      loggerInUser
    );

  if (!success) {
    return {
      success: false,
      message,
    };
  }

  const existingUserOrganization = await UserOrganization.findOne({
    where: {
      userId: existingEmployee.userId,
      organizationId: loggerInUser.currentOrganizationId,
    },
  });

  if (!existingUserOrganization) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('User Organization'),
    };
  }

  existingUserOrganization.attendanceMarkType =
    data?.attendance?.calculationType;
  existingUserOrganization.unpaidLeaveDeductionType =
    data?.unpaidLeave?.calculationType;
  existingUserOrganization.unpaidLeaveDeductionAmount =
    data?.unpaidLeave?.calculation;

  existingUserOrganization.save();
  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Employee Leave Policy'),
  };
};

exports.checkEmployeeExist = async (employeeId) => {
  const query = {
    where: {
      id: employeeId,
    },
    include: [
      {
        model: User,
        as: 'user',
        where: {
          status: { [Op.notIn]: [defaultStatus.DELETED] },
        },
      },
    ],
  };

  const checkEmployee = await Employee.findOne(query);

  if (!checkEmployee) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Employee'),
    };
  }

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Employee'),
    data: checkEmployee,
  };
};

exports.getEmployeeBankDetails = async (employeeId) => {
  const query = {
    where: {
      id: employeeId,
    },
    include: [
      {
        model: User,
        as: 'user',
      },
    ],
  };

  const checkEmployee = await Employee.findOne(query);

  if (!checkEmployee) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Employee'),
    };
  }

  const employeeBankDetails = await BankDetailsRepository.getBankDetails(
    checkEmployee.user.id
  );

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Bank Details'),
    data: employeeBankDetails,
  };
};

exports.addEmployeeAttendance = async (employeeId, data, user) => {
  try {
    const checkEmployee = await this.checkEmployeeExist(employeeId);

    if (!checkEmployee.success) {
      return {
        success: false,
        message: checkEmployee.message,
      };
    }

    await this.addAttendance(employeeId, data, user.id);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Employee Attendance'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
exports.addAttendance = async (employeeId, data, createdBy) => {
  const payload = {
    employeeId: employeeId,
    date: data.date,
    inTime: data.inTime,
    outTime: data.outTime,
    status: data.status,
    description: data.description,
    createdBy: createdBy,
  };

  const attendance = await Attendance.create(payload);

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Attendance'),
    data: attendance,
  };
};

exports.addEmployeeSalary = async (employeeId, data, loggerInUser) => {
  const transaction = await sequelize.transaction();

  try {
    const checkEmployee = await this.checkEmployeeExist(employeeId);

    if (!checkEmployee.success) {
      return {
        success: false,
        message: checkEmployee.message,
      };
    }

    if (data.employeeSalary && data.employeeSalary.length <= 0) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Employee Salary Entity'),
      };
    }

    const {
      success,
      message,
      data: employeeSalary,
    } = await EmployeeTemplateItemRepository.addEmployeeSalaryPayroll(
      data,
      employeeId,
      loggerInUser,
      transaction
    );

    await transaction.commit();
    return {
      success,
      message,
      data: employeeSalary,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.getEmployeeTemplate = async (employeeId, type) => {
  const query = {
    where: { employeeId, templateType: type },
    attributes: ['id', 'name', 'employeeId', 'templateType'],
  };

  return await EmployeeTemplate.findOne(query);
};

exports.getEmployeeSalaryPayroll = async (employeeId, data, user) => {
  try {
    const employeeSalaryTemplate = await this.getEmployeeTemplate(
      employeeId,
      templateType.SALARY
    );

    if (!employeeSalaryTemplate) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          'Employee Template Salary Payroll'
        ),
      };
    }
    const salaryQuery = {
      where: {
        employeeTemplateId: employeeSalaryTemplate.id,
      },
      attributes: [
        'id',
        'name',
        'entityType',
        'calculation',
        'monthlyAmount',
        'annualAmount',
        'calculationType',
      ],
      row: true,
    };

    const employeeTemplateItems =
      await EmployeeTemplateItem.findAll(salaryQuery);

    if (employeeTemplateItems && employeeTemplateItems.length < 1) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Employee Salary Payroll'),
      };
    }

    const earnings = [];
    const deductions = [];

    let totalEarnings = 0;
    let totalDeductions = 0;
    employeeTemplateItems.forEach((item) => {
      if (
        [templateEntityType.EARNING, templateEntityType.REIMBURSEMENT].includes(
          item.entityType
        )
      ) {
        earnings.push(item);
        totalEarnings += parseFloat(item.monthlyAmount) || 0;
      }
      if (
        [templateEntityType.DEDUCTION, templateEntityType.TAX].includes(
          item.entityType
        )
      ) {
        deductions.push(item);
        totalDeductions += parseFloat(item.monthlyAmount) || 0;
      }
    });

    const grossSalary = totalEarnings;
    const netSalary = grossSalary - totalDeductions;
    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Employee Salary Payroll'),
      data: {
        template: employeeSalaryTemplate,
        grossSalary,
        netSalary,
        earnings,
        deductions,
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.putEmployeeSalary = async (employeeId, data, loggerInUser) => {
  const existingEmployee = await this.checkEmployeeExist(employeeId);

  if (!existingEmployee.success) {
    return {
      success: false,
      message: checkEmployee.message,
    };
  }

  if (data.employeeSalary && data.employeeSalary.length <= 0) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Employee Salary Entity'),
    };
  }

  const {
    success,
    message,
    data: payroll,
  } = await EmployeeTemplateItemRepository.putEmployeeSalaryPayroll(
    data,
    employeeId,
    loggerInUser
  );

  return {
    success,
    message,
    data: payroll,
  };
};
