'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameColumn(
      'DocumentPermission',
      'canDownload',
      'canDelete'
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.renameColumn(
      'DocumentPermission',
      'canDelete',
      'canDownload'
    );
  },
};
