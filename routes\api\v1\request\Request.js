const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const RequestController = require('@controllers/v1/request/Request');
const RequestSchema = require('@schema-validation/request/Request');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(RequestSchema.createRequestSchema),
  ErrorHandleHelper.requestValidator,
  RequestController.createRequest
);

router.patch(
  '/:id',
  checkSchema(RequestSchema.updateRequestSchema),
  ErrorHandleHelper.requestValidator,
  RequestController.updateRequest
);

router.get(
  '/details/:id',
  checkSchema(RequestSchema.getRequestDetailsSchema),
  ErrorHandleHelper.requestValidator,
  RequestController.getRequestDetails
);

router.get(
  '/',
  checkSchema(RequestSchema.requestListSchema),
  ErrorHandleHelper.requestValidator,
  RequestController.getRequestList
);

router.get(
  '/task/status-count',
  ErrorHandleHelper.requestValidator,
  RequestController.getRequestStatusCount
);

module.exports = router;
