'use strict';
const OPTIONS = require('@config/options');
const { contactPersonCategory, contactPersonType } = OPTIONS;

module.exports = (sequelize, DataTypes) => {
  const ContactPerson = sequelize.define(
    'ContactPerson',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      contactCategory: {
        type: DataTypes.ENUM(
          contactPersonCategory.getContactPersonCategoryArray()
        ),
        allowNull: false,
      },
      logo: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return OPTIONS.generateCloudFrontUrl(this.getDataValue('logo'));
        },
        set(file) {
          if (file) {
            this.setDataValue('logo', `uploads/${file.split('uploads/')[1]}`);
          }
        },
      },
      businessName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      firstName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      lastName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      countryCode: {
        type: DataTypes.STRING(5),
        allowNull: true,
      },
      contactNumber: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      panNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      gstNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      contactType: {
        type: DataTypes.ENUM(contactPersonType.getContactPersonTypeArray()),
        allowNull: true,
      },
      about: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      details: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ContactPerson.associate = (models) => {
    ContactPerson.belongsTo(models.Customer, {
      foreignKey: 'customerId',
      as: 'customer',
      onDelete: 'CASCADE',
    });

    ContactPerson.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });

    ContactPerson.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    ContactPerson.belongsTo(models.Address, {
      foreignKey: 'addressId',
      as: 'address',
      onDelete: 'CASCADE',
    });
  };

  return ContactPerson;
};
