const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const DepartmentController = require('@controllers/v1/settings/Department');
const DepartmentSchema = require('@schema-validation/settings/Department');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(DepartmentSchema.createDepartment),
  ErrorHandleHelper.requestValidator,
  DepartmentController.createDepartment
);

router.put(
  '/:id',
  checkSchema(DepartmentSchema.updateDepartment),
  ErrorHandleHelper.requestValidator,
  DepartmentController.updateDepartment
);

router.delete(
  '/:id',
  checkSchema(DepartmentSchema.deleteDepartment),
  ErrorHandleHelper.requestValidator,
  DepartmentController.deleteDepartment
);

module.exports = router;
