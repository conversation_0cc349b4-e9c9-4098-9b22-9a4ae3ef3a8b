'use strict';
const { facing } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Unit = sequelize.define(
    'Unit',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      isSaleable: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      isNamingFormat: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      superBuiltUpArea: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      builtUpArea: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      carpetArea: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      facing: {
        type: DataTypes.ENUM(facing.getFacingArray()),
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      additionalFields: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      plotArea: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      roadTangent: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      remainingArea: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      proRataFsiFactor: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      zoning: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      basicFsi: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      permissibleFsi: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      createdAt: 'createdAt',
      updatedAt: 'updatedAt',
      freezeTableName: true,
    }
  );

  Unit.associate = function (models) {
    Unit.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Unit.hasMany(models.Floor, {
      foreignKey: 'unitId',
      as: 'floorsInUnit',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    Unit.belongsTo(models.UnitType, {
      foreignKey: 'unitTypeId',
      as: 'unitType',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Unit.belongsTo(models.Floor, {
      foreignKey: 'floorId',
      as: 'unitsInFloor',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Unit.belongsToMany(models.Amenities, {
      through: 'UnitAmenities',
      foreignKey: 'unitId',
      otherKey: 'amenityId',
      as: 'amenities',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Unit.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Unit.hasMany(models.Drawing, {
      foreignKey: 'unitId',
      as: 'drawings',
      onDelete: 'CASCADE',
    });

    Unit.hasMany(models.Space, {
      foreignKey: 'unitId',
      as: 'spaces',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  };

  return Unit;
};
