'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Attendance_status" ADD VALUE IF NOT EXISTS 'company_holiday';
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Attendance_status" ADD VALUE IF NOT EXISTS 'work_from_home';
    `);
  },

  down: async (queryInterface, Sequelize) => {},
};
