module.exports = (sequelize, DataTypes) => {
  const UserLeaveSummary = sequelize.define(
    'UserLeaveSummary',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      maxAllowed: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      used: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      remaining: {
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      leaveType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  UserLeaveSummary.associate = (models) => {
    UserLeaveSummary.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return UserLeaveSummary;
};
