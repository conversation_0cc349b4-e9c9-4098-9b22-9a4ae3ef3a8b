'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'BoqCostItems',
      'BoqCostItems_costItemId_fkey'
    );

    await queryInterface.dropTable('SubCategoryCostItem');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.createTable('SubCategoryCostItem', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      rate: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      unit: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addConstraint('BoqCostItems', {
      fields: ['costItemId'],
      type: 'foreign key',
      name: 'BoqCostItems_costItemId_fkey',
      references: {
        table: 'SubCategoryCostItem',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },
};
