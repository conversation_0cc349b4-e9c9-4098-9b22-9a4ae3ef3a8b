const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const WorkOrderBoqController = require('../../../controllers/api/v1/WorkOrderBoqMapping');
const WorkOrderBoqSchema = require('../../../schema-validation/WorkOrderBoqMapping');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/:id/workorder-boq-mapping',
  checkSchema(WorkOrderBoqSchema.addWorkOrderBOQMapping),
  ErrorHandleHelper.requestValidator,
  WorkOrderBoqController.addWorkOrderBOQMapping
);

router.get(
  '/workorder-boq-mapping-listing',
  ErrorHandleHelper.requestValidator,
  WorkOrderBoqController.listWorkOrderBoqMapping
);

router.delete(
  '/workorder-boq-mapping',
  checkSchema(WorkOrderBoqSchema.deleteWorkOrderBOQMappingValidator),
  ErrorHandleHelper.requestValidator,
  WorkOrderBoqController.deleteWorkOrderBOQMapping
);

module.exports = router;
