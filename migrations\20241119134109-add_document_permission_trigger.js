'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION create_default_document_permission()
      RETURNS TRIGGER AS $$
      BEGIN
        INSERT INTO "DocumentPermission" ("documentId", "userId", "canView", "canEdit", "canDelete")
        VALUES (NEW.id, NEW."createdBy", TRUE, TRUE, TRUE);
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await queryInterface.sequelize.query(`
      CREATE TRIGGER create_default_document_permission_trigger
      AFTER INSERT ON "Document"
      FOR EACH ROW
      EXECUTE FUNCTION create_default_document_permission();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      'DROP TRIGGER IF EXISTS create_default_document_permission_trigger ON "Document";'
    );
    await queryInterface.sequelize.query(
      'DROP FUNCTION IF EXISTS create_default_document_permission;'
    );
  },
};
