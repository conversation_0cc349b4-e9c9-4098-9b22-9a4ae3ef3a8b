'use strict';

module.exports = (sequelize, DataTypes) => {
  const FavoritesDocument = sequelize.define(
    'FavoritesDocument',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      isFavorites: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  FavoritesDocument.associate = (models) => {
    FavoritesDocument.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    FavoritesDocument.belongsTo(models.Document, {
      foreignKey: 'documentId',
      as: 'document',
      onDelete: 'CASCADE',
    });
  };

  return FavoritesDocument;
};
