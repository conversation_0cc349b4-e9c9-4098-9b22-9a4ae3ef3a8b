const { escalation } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ApprovalWorkflow', 'escalation', {
      type: Sequelize.ENUM(escalation.escalationArray()),
      allowNull: false,
      defaultValue: escalation.MEMBER,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ApprovalWorkflow', 'escalation');
  },
};
