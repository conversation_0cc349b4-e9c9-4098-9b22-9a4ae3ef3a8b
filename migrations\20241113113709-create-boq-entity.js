const { boqCalculationType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('BOQItems', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      boqCategory: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'BOQCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      boqSubCategoryId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'BOQSubCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      boqMetricId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'BOQMetric',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      length: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      breadth: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      height: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: true,
      },
      total: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      calculationType: {
        type: Sequelize.ENUM(boqCalculationType.getBoqCalculationTypeArray()),
        allowNull: false,
        defaultValue: boqCalculationType.METRIC,
      },
      labourCost: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      materialCost: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      totalCost: {
        type: Sequelize.DECIMAL(12, 2),
        allowNull: false,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('BOQItems');
  },
};
