exports.createCustomTag = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Task Tag name cannot be empty',
    },
    isString: {
      errorMessage: 'Task Tag name must be a string',
    },
  },
  type: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Type is required and cannot be empty',
    },
    isString: {
      errorMessage: 'Type must be a string',
    },
    custom: {
      options: (value) => value === 'custom_tag',
      errorMessage: 'Type must be "custom_tag"',
    },
  },
};

exports.updateCustomTag = {
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Task Tag name must be a string',
    },
    notEmpty: {
      errorMessage: 'Task Tag name cannot be empty if provided',
    },
  },
  type: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Type must be a string',
    },
    custom: {
      options: (value) => value === 'custom_tag',
      errorMessage: 'Type must be "custom_tag"',
    },
  },
};
