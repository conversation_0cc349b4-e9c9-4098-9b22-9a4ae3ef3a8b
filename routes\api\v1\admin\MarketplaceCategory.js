const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const MarketplaceCategoryController = require('@controllers/v1/admin/MarketplaceCategory');
const MarketplaceCategorySchema = require('@schema-validation/admin/MarketplaceCategory');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(MarketplaceCategorySchema.createCategory),
  ErrorHandleHelper.requestValidator,
  MarketplaceCategoryController.createCategory
);

router.put(
  '/:id',
  checkSchema(MarketplaceCategorySchema.updateCategory),
  ErrorHandleHelper.requestValidator,
  MarketplaceCategoryController.updateCategory
);

router.delete(
  '/:id',
  checkSchema(MarketplaceCategorySchema.deleteCategory),
  ErrorHandleHelper.requestValidator,
  MarketplaceCategoryController.deleteCategory
);

module.exports = router;
