const { PaymentPlan, PaymentStage } = require('..');
const { successMessage, paymentPlanType } = require('../../config/options');
const ProjectRepository = require('./ProjectRepository');
exports.findOne = async (query) => await PaymentPlan.findOne(query);
exports.findAndCountAll = async (query = {}) => {
  return await PaymentPlan.findAndCountAll(query);
};
exports.findAll = async (query = {}) => {
  return await PaymentPlan.findAll(query);
};
exports.bulkCreatePaymentStages = async (data) =>
  await PaymentStage.bulkCreate(data);

exports.createPaymentPlan = async (
  projectId,
  data,
  loggedInUser,
  planType = paymentPlanType.PAYMENT_PLAN
) => {
  const { success, message } =
    await ProjectRepository.checkAndGetProject(projectId);

  if (!success) {
    return {
      success: false,
      message,
    };
  }

  const payload = {
    name: data.name,
    description: data.description,
    additionalTerm: data.additionalTerm,
    planType,
    projectId: projectId,
    createdBy: loggedInUser.id,
    ...(data.paymentStages
      ? {
          paymentStages: data.paymentStages.map((stage) => ({
            ...stage,
            projectId: projectId,
          })),
        }
      : {}),
  };

  const paymentPlan = await PaymentPlan.create(payload, {
    include: [
      {
        model: PaymentStage,
        as: 'paymentStages',
      },
    ],
  });

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Payment Plan'),
    data: paymentPlan,
  };
};

exports.checkAndUpdatePaymentPlan = async (
  projectId,
  PaymentPlanId,
  data,
  loggedInUser
) => {
  const query = {
    where: {
      projectId,
      id: PaymentPlanId,
    },
  };

  const existingPaymentPlan = await this.findOne(query);

  existingPaymentPlan.name = data.name || existingPaymentPlan.name;
  existingPaymentPlan.description =
    data.description || existingPaymentPlan.description;
  existingPaymentPlan.additionalTerm =
    data.additionalTerm || existingPaymentPlan.additionalTerm;
  existingPaymentPlan.save();

  if (data.paymentStages) {
    await PaymentStage.destroy({
      where: {
        paymentPlanId: existingPaymentPlan.id,
      },
    });

    const paymentStages = data.paymentStages.map((stage) => ({
      ...stage,
      paymentPlanId: existingPaymentPlan.id,
      projectId: projectId,
    }));

    await this.bulkCreatePaymentStages(paymentStages);
  }
  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Payment Revision'),
  };
};
