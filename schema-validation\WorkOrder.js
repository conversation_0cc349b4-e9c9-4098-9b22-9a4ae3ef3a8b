exports.createWorkOrderType = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1, max: 50 },
      errorMessage: 'Name must be between 1 and 50 characters',
    },
  },
};

exports.createWorkOrder = {
  workOrderNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Work Order Number cannot be empty',
    },
    isString: {
      errorMessage: 'Work Order Number must be a string',
    },
    isLength: {
      options: { min: 1, max: 50 },
      errorMessage: 'Work Order Number must be between 1 and 50 characters',
    },
  },
  workOrderType: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Work Order Type cannot be empty',
    },
    isInt: {
      errorMessage: 'Work Order Type must be an integer',
    },
    toInt: true,
  },
  fromDate: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'fromDate cannot be empty',
    },
    isDate: {
      errorMessage: 'fromDate must be a valid date',
    },
  },
  toDate: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'toDate cannot be empty',
    },
    isDate: {
      errorMessage: 'toDate must be a valid date',
    },
  },
};

exports.updateWorkOrder = {
  workOrderNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Work Order Number must be a string',
    },
    isLength: {
      options: { min: 1, max: 50 },
      errorMessage: 'Work Order Number must be between 1 and 50 characters',
    },
  },
  projectId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Project ID must be an integer',
    },
    toInt: true,
  },
  workOrderType: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Work Order Type must be an integer',
    },
    toInt: true,
  },
  scheduleDate: {
    in: ['body'],
    optional: true,
    isDate: {
      errorMessage: 'Schedule Date must be a valid date',
    },
  },
};
