const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskUnitMappingController = require('@controllers/v1/task/TaskUnitMapping');
const TaskUnitSchema = require('@schema-validation/task/TaskUnitMapping');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/:id/task-unit-mapping',
  checkSchema(TaskUnitSchema.createTaskUnitMapping),
  ErrorHandleHelper.requestValidator,
  TaskUnitMappingController.createTaskUnitMapping
);

router.delete(
  '/:id/:unitId/task-unit-mapping',
  ErrorHandleHelper.requestValidator,
  TaskUnitMappingController.deleteTaskUnitMapping
);

module.exports = router;
