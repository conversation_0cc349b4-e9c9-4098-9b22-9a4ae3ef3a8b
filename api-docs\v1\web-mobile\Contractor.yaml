paths:
  /contractor:
    post:
      tags:
        - "Contractor"
      summary: "Create a contractor"
      description: "Creates a new contractor with the provided details."
      operationId: "createContractor"
      requestBody:
        description: "Contractor creation details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/create-contractor"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Contractor created successfully"
        "400":
          description: "The Contractor already exist"
        "500":
          description: "Internal Server Error"

  /contractor/{id}:
    put:
      tags:
        - "Contractor"
      summary: "Update a contractor"
      description: "Updates the details of an existing contractor."
      operationId: "updateContractor"
      parameters:
        - name: "id"
          in: "path"
          required: true
          description: "The ID of the contractor to update."
          schema:
            type: "string"
      requestBody:
        description: "Contractor update details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/update-contractor"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Contractor updated successfully"
        "400":
          description: "Invalid input or contractor not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    create-contractor:
      type: object
      required:
        - contractorType
        - email
      properties:
        contractorType:
          type: string
          example: "individual"
        logo:
          type: string
          example: "https://example.com/logo.png"
        businessName:
          type: string
          example: "Acme Construction"
        firstName:
          type: string
          example: "John"
        lastName:
          type: string
          example: "Doe"
        countryCode:
          type: string
          example: "+1"
        mobileNumber:
          type: string
          example: "************"
        email:
          type: string
          example: "<EMAIL>"
        gstNumber:
          type: string
          example: "22AAAAA0000A1Z5"
        panNumber:
          type: string
          example: "**********"
        address:
          type: string
          example: "123 Main St, Anytown, CA 12345"
        pinCode:
          type: string
          maxLength: 6
          example: "12345"
        about:
          type: string
          nullable: true
          example: "this for constructor side"

    update-contractor:
      type: "object"
      properties:
        contractorType:
          type: "string"
          description: "The type of contractor."
          example: "Individual"
        logo:
          type: "string"
          description: "URL of the contractor's logo."
          example: "https://example.com/logo.png"
        businessName:
          type: "string"
          description: "The business name of the contractor."
          example: "ABC Contractors"
        firstName:
          type: "string"
          description: "First name of the contractor."
          example: "John"
        lastName:
          type: "string"
          description: "Last name of the contractor."
          example: "Doe"
        countryCode:
          type: "string"
          description: "Country code for the contractor's mobile number."
          example: "+1"
        mobileNumber:
          type: "string"
          description: "Mobile number of the contractor."
          example: "1234567890"
        email:
          type: "string"
          description: "Email address of the contractor."
          example: "<EMAIL>"
        gstNumber:
          type: "string"
          description: "GST number of the contractor."
          example: "22**********2Z5"
        panNumber:
          type: "string"
          description: "PAN number of the contractor."
          example: "**********"
        address:
          type: "string"
          description: "Address of the contractor."
          example: "123 Main Street, City, Country"
        pinCode:
          type: "string"
          description: "PIN code of the contractor's address."
          example: "123456"
        about:
          type: "string"
          description: "Additional information about the contractor."
          example: "Experienced contractor specializing in residential projects."
