const {
  gender,
  maritalStatus,
  calculationType,
  defaultStatus,
} = require('../config/options');

exports.createOrUpdateEmployee = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Document name cannot be empty',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
  gender: {
    in: ['body'],
    isIn: {
      options: [gender.getGenderArray()],
      errorMessage: 'Gender must be male, female, or other',
    },
  },
  profilePicture: {
    in: ['body'],
    trim: true,
    isString: {
      errorMessage: 'profilePicture name must be path',
    },
  },
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'First name cannot be empty',
    },
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  middleName: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'middleName cannot be empty',
    },
    isString: {
      errorMessage: 'middleName must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'lastName cannot be empty',
    },
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'Mobile number cannot be empty',
    },
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  personalEmail: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'personalEmail cannot be empty',
    },
    isString: {
      errorMessage: 'Email must be string',
    },
  },
  locationId: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'locationId cannot be empty',
    },
    isString: {
      errorMessage: 'Location ID must be string',
    },
  },
  designationId: {
    in: ['body'],
    trim: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'designationId cannot be empty',
    },
    isString: {
      errorMessage: 'Designation ID must be string',
    },
  },

  alternateCountryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Alternate Country Code cannot be empty',
    isString: {
      errorMessage: 'Alternate Country Code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  alternateMobileNumber: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'Alternate Mobile Number cannot be empty',
    },
    isString: {
      errorMessage: 'Alternate Mobile Number must be string',
    },
  },
  dateOfBirth: {
    in: ['body'],
    trim: true,
    isDate: {
      options: { format: 'YYYY-MM-DD' },
      errorMessage: 'Invalid date format',
    },
  },
  employeeCode: {
    in: ['body'],
    trim: true,
    isString: {
      errorMessage: 'Designation ID must be string',
    },
  },
  maritalStatus: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'maritalStatus cannot be empty',
    },
    isIn: {
      options: [maritalStatus.getMaritalStatusArray()],
      errorMessage: 'maritalStatus must be male, female, or other',
    },
  },
  dateOfJoining: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDate: {
      options: { format: 'YYYY-MM-DD' },
      errorMessage: 'Invalid date format',
    },
  },
  reportedTo: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'reported ID cannot be empty',
    },
    isInt: {
      errorMessage: 'reported ID must be Int',
    },
  },
  'addressDetails.address': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'address cannot be empty',
    },
    isString: {
      errorMessage: 'address must be string',
    },
  },
  'addressDetails.city': {
    in: ['body'],
    isString: true,
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'city cannot be empty',
    },
    isString: {
      errorMessage: 'city must be string',
    },
  },
  'addressDetails.pincode': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'pincode cannot be empty',
    },
    isString: {
      errorMessage: 'pincode must be string',
    },
  },
  'addressDetails.state': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'state cannot be empty',
    },
    isString: {
      errorMessage: 'state must be string',
    },
  },
  'employeeDocument.*.id': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'id cannot be empty',
    },
    isInt: {
      errorMessage: 'id must be int',
    },
  },
  'employeeDocument.*.cardNumber': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'cardNumber cannot be empty',
    },
    isString: {
      errorMessage: 'cardNumber must be string',
    },
  },
  'employeeDocument.*.name': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'employeeDocument.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  'employeeDocument.*.documentPath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'documentPath cannot be empty',
    },
    isString: {
      errorMessage: 'documentPath must be string',
    },
  },
  'bankDetails.id': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'id cannot be empty',
    },
    isInt: {
      errorMessage: 'id ID must be Int',
    },
  },
  'bankDetails.accountName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'accountName cannot be empty',
    },
    isString: {
      errorMessage: 'accountName must be string',
    },
  },
  'bankDetails.accountNumber': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'accountNumber cannot be empty',
    },
    isString: {
      errorMessage: 'accountNumber must be string',
    },
  },
  'bankDetails.bankName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'bankName cannot be empty',
    },
    isString: {
      errorMessage: 'bankName must be string',
    },
  },
  'bankDetails.ifscCode': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'ifscCode cannot be empty',
    },
    isString: {
      errorMessage: 'ifscCode must be string',
    },
  },
};

exports.updateEmployeeStatus = {
  status: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'status cannot be empty',
    },
    isIn: {
      options: [defaultStatus.getDefaultStatusArray()],
      errorMessage: `status value must be 'active', 'inactive', 'rejected'`,
    },
  },
};
exports.createEmployeeLeavePolicy = {
  'employeeLeave.*.entityId': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'entityId cannot be empty',
    },
    isInt: {
      errorMessage: 'entityId must be int',
    },
  },
  'employeeLeave.*.maxAllowed': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'maxAllowed cannot be empty',
    },
    isInt: {
      errorMessage: 'maxAllowed must be int',
    },
  },
  'employeeAdditionalLeave.*.name': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'employeeAdditionalLeave.*.maxAllowed': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'maxAllowed cannot be empty',
    },
    isInt: {
      errorMessage: 'maxAllowed must be int',
    },
  },
  'attendance.entityId': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'attendance entityId cannot be empty',
    },
    isInt: {
      errorMessage: 'attendance entityId must be int',
    },
  },
  'attendance.calculationType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'attendance calculationType cannot be empty',
    },
    isIn: {
      options: [calculationType.getCalculationTypeArray()],
      errorMessage: 'calculationType must enum',
    },
  },
  'unpaidLeave.entityId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'entityId cannot be empty',
    },
    isInt: {
      errorMessage: 'entityId must be int',
    },
  },
  'unpaidLeave.calculationType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'calculationType cannot be empty',
    },
    isIn: {
      options: [calculationType.getCalculationTypeArray()],
      errorMessage: 'calculationType must enum',
    },
  },
  'unpaidLeave.calculation': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'maxAllowed cannot be empty',
    },
    isFloat: {
      errorMessage: 'maxAllowed must be a float',
    },
  },
  templateId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'templateId cannot be empty',
    },
    isInt: {
      errorMessage: 'templateId must be int',
    },
  },
};

exports.createEmployeeAttendance = {
  date: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'date cannot be empty',
    },
    isDate: {
      errorMessage: 'date must be a valid date',
    },
  },
  inTime: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'inTime cannot be empty',
    },
    custom: {
      options: (value) => {
        return /^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/.test(value);
      },
      errorMessage: 'inTime must be in HH:mm:ss format',
    },
  },
  outTime: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'outTime cannot be empty',
    },
    custom: {
      options: (value) => {
        return /^([01]?[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/.test(value);
      },
      errorMessage: 'inTime must be in HH:mm:ss format',
    },
  },
  status: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'status cannot be empty',
    },
    isString: {
      errorMessage: 'status must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be a string',
    },
  },
};

exports.createAndUpdateEmployeeSalary = {
  'employeeSalary.*.entityId': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'entityId cannot be empty',
    },
    isInt: {
      errorMessage: 'entityId must be int',
    },
  },
  'employeeSalary.*.calculation': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'calculation cannot be empty',
    },
    isFloat: {
      errorMessage: 'calculation must be a decimal number',
    },
  },
  'employeeSalary.*.monthlyAmount': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'calculation cannot be empty',
    },
    isFloat: {
      errorMessage: 'calculation must be a decimal number',
    },
  },
  'employeeSalary.*.annualAmount': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'calculation cannot be empty',
    },
    isFloat: {
      errorMessage: 'calculation must be a decimal number',
    },
  },
  'employeeSalary.*.calculationType': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'calculationType cannot be empty',
    },
    isIn: {
      options: [calculationType.getCalculationTypeArray()],
      errorMessage: 'calculationType must be either fixed or percentage',
    },
  },
  templateId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'templateId cannot be empty',
    },
    isInt: {
      errorMessage: 'templateId must be int',
    },
  },
};
