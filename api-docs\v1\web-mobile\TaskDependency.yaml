paths:
  /task/{id}/dependency:
    post:
      summary: Creates a new Dependency
      description: Creates a new Dependency for specific Task
      operationId: createDependency
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of Task
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createDependency"
      responses:
        "201":
          description: Dependency created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/dependency/{id}:
    patch:
      summary: Update Existing Dependency
      description: Update a Existing Dependency with Id
      operationId: updateDependency
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of Dependency
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateDependency"
      responses:
        "200":
          description: Dependency updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    
    delete:
      summary: Delete a Dependency
      description: Delete Dependency if you dont need it
      operationId: deleteDependency
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of Dependency
      responses:
        "200":
          description: Dependency deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createDependency:
      type: object
      required:
        - dependentTaskId
        - dependencyType
        - status
      properties:
        dependentTaskId:
          type: integer
          example: 1
          description: "dependent taskId based on Task"
        dependencyType:
          type: string
          example: "blocking"
          description: "The dependencyType of the dependency, which must be 'blocking','blockedBy'"
        status:
          type: string
          example: "fs"
          description: "The status of the dependency, which must be 'fs','ff','ss',''sf"

    updateDependency:
      type: object
      properties:
        dependencyType:
          type: string
          example: "blocking"
          description: "The dependencyType of the dependency, which must be 'blocking','blockedBy'"
        status:
          type: string
          example: "fs"
          description: "The status of the dependency, which must be 'fs','ff','ss','sf'"
