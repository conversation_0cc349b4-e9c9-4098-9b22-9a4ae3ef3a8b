paths:
  /project/unit-type:
    post:
      tags:
        - Project
      summary: "Create a new Unit Type"
      description: "This endpoint allows you to register a new UnitType by providing a name."
      operationId: "CreateUnitType"
      requestBody:
        description: "The name of the new UnitType to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/unit-type"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "UnitType has been created successfully."
        "400":
          description: "Invalid input data or unit type already exists."
        "500":
          description: "Internal Server Error"

  /project/unit-type/{id}:
    put:
      tags:
        - Project
      summary: "Update an existing UnitType"
      description: "This endpoint allows you to update the name of an existing UnitType by providing its id and new name."
      operationId: "UpdateUnitType"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the UnitType to be updated."
          schema:
            type: integer
            example: 1
      requestBody:
        description: "The updated information for the UnitType."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/unit-type-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "UnitType has been updated successfully."
        "400":
          description: "Invalid input data, unit type not found, or unit type already exists."
        "404":
          description: "UnitType not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    unit-type:
      type: object
      properties:
        name:
          type: string
          description: "The name of the unit type"
          example: "1BHK Apartment"
      required:
        - name       
    unit-type-update:
      type: object
      properties:
        name:
          type: string
          description: "The new name of the unit type."
          example: "2BHK Apartment"
      required:
        - name