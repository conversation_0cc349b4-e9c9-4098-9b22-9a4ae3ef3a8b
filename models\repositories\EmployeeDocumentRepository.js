const sequelize = require('sequelize');
const { Op } = sequelize;

const { EmployeeDocument } = require('..');
const { successMessage } = require('../../config/options');

exports.addBulkEmployeeDocument = async (
  data,
  userId,
  createdBy,
  transaction
) => {
  try {
    const employeeDocumentPayload = data.map((media) => ({
      ...media,
      userId,
      createdBy,
    }));

    const employeeDocuments = await EmployeeDocument.bulkCreate(
      employeeDocumentPayload,
      {
        transaction,
      }
    );

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Employee Document'),
      data: employeeDocuments.map((permission) => permission.id),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateBulkEmployeeDocument = async (documents, userId, updatedBy) => {
  try {
    if (!documents || documents.length === 0) {
      await EmployeeDocument.destroy({ where: { userId } });
      return {
        success: true,
        message: successMessage.UPDATE_SUCCESS_MESSAGE('Employee Document'),
      };
    }

    const providedIds = documents.map((doc) => doc.id);

    const existingDocuments = await EmployeeDocument.findAll({
      where: { userId },
      attributes: ['id', 'createdBy'],
    });
    const existingIds = existingDocuments.map((doc) => doc.id);

    const idsToDelete = existingIds.filter((id) => !providedIds.includes(id));

    if (idsToDelete.length > 0) {
      await EmployeeDocument.destroy({
        where: { id: { [Op.in]: idsToDelete } },
      });
    }

    const docsToUpdate = documents.map((doc) => {
      const existingDoc = existingDocuments.find((eDoc) => eDoc.id === doc.id);

      return {
        ...doc,
        userId,
        createdBy: existingDoc ? existingDoc.createdBy : updatedBy,
        updatedBy: updatedBy,
      };
    });

    await EmployeeDocument.bulkCreate(docsToUpdate, {
      updateOnDuplicate: [
        'cardNumber',
        'name',
        'description',
        'documentPath',
        'userId',
      ],
    });

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Employee Document'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
