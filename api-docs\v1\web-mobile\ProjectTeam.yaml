paths:
  /project/{id}/team/{userId}/invite:
    post:
      tags:
        - Project
      summary: "Send an invitation to a user to join a project team"
      description: "This endpoint allows a project manager or admin to send an invitation to a user to join a project team."
      operationId: "sendInvitation"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the project to which the user is being invited."
          schema:
            type: integer
            example: 1
        - name: userId
          in: path
          required: true
          description: "The ID of the user being invited to the project."
          schema:
            type: integer
            example: 123
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "The invitation has been sent successfully."
        "400":
          description: "Invalid input data, project, or user not found."
        "404":
          description: "Project or user not found."
        "409":
          description: "Conflict: The user has already been invited or is already part of the project."
        "500":
          description: "Internal Server Error"

  /project/team/invite/{id}:
    patch:
      tags:
        - Project
      summary: "Accept or reject an invitation to join a project team"
      description: "This endpoint allows a user to accept or reject an invitation to join a project team using the invitation ID."
      operationId: "updateInvitationStatus"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the invitation to be accepted or rejected."
          schema:
            type: integer
            example: 1
      requestBody:
        description: "The status of the invitation (accepted or rejected)."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/InvitationStatus" 
        required: true
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "The invitation status has been updated successfully."
        "400":
          description: "Invalid input data, invitation not found, or invalid status."
        "404":
          description: "Invitation not found."
        "409":
          description: "Conflict: Invitation has already been accepted or rejected."
        "500":
          description: "Internal Server Error"

  /project/team/{id}:
    delete:
      tags:
        - Project
      summary: "Remove a user from the project team using projectTeamId"
      description: "This endpoint allows a project manager or admin to remove a user from the project team using the projectTeamId."
      operationId: "removeTeamMemberById"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the project team association to be removed (projectTeamId)."
          schema:
            type: integer
            example: 1
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "The user has been removed from the project team successfully."
        "400":
          description: "Invalid input data or project/team association not found."
        "404":
          description: "Project or user not found."
        "409":
          description: "Conflict: The user is not part of the project or cannot be removed."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    InvitationStatus:
      type: object
      properties:
        status:
          type: string
          enum: ["accepted", "rejected"]
          description: "The status of the invitation."
          example: "accepted"
      required:
        - status
