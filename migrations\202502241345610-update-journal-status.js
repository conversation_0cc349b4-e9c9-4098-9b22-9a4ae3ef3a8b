'use strict';

const { journalState } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Journal');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Journal" ALTER COLUMN "journalState" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Journal_journalState_new') THEN
          CREATE TYPE "enum_Journal_journalState_new" AS ENUM (${journalState
            .getValues()
            .map((state) => `'${state}'`)
            .join(', ')});
        END IF;
      END $$;
    `);

    if (tableDescription.journalState) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Journal" ALTER COLUMN "journalState" TYPE "enum_Journal_journalState_new"
        USING "journalState"::text::"enum_Journal_journalState_new";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Journal_journalState') THEN
          DROP TYPE "enum_Journal_journalState";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Journal_journalState_new" RENAME TO "enum_Journal_journalState";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Journal" ALTER COLUMN "journalState" SET DEFAULT 'draft';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Journal');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Journal" ALTER COLUMN "journalState" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Journal_journalState_old') THEN
          CREATE TYPE "enum_Journal_journalState_old" AS ENUM ('draft');
        END IF;
      END $$;
    `);

    if (tableDescription.journalState) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Journal" ALTER COLUMN "journalState" TYPE "enum_Journal_journalState_old"
        USING "journalState"::text::"enum_Journal_journalState_old";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Journal_journalState') THEN
          DROP TYPE "enum_Journal_journalState";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Journal_journalState_old" RENAME TO "enum_Journal_journalState";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Journal" ALTER COLUMN "journalState" SET DEFAULT 'draft';
    `);
  },
};
