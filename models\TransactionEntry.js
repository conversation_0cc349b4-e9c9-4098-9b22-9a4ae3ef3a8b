const { recordStatus } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const TransactionEntry = sequelize.define(
    'TransactionEntry',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      referenceId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      referenceType: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      accountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      description: {
        type: DataTypes.STRING,
        default: null,
        allowNull: true,
      },
      debitAmount: {
        type: DataTypes.DECIMAL(20, 2),
        default: null,
        allowNull: false,
      },
      creditAmount: {
        type: DataTypes.DECIMAL(20, 2),
        default: null,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(recordStatus.getValues()),
        default: recordStatus.ACTIVE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TransactionEntry.associate = (models) => {
    TransactionEntry.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
      onDelete: 'NO ACTION',
    });

    TransactionEntry.belongsTo(models.Journal, {
      foreignKey: 'referenceId',
      constraints: false,
    });
  };

  return TransactionEntry;
};
