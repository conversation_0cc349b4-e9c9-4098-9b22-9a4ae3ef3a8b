const { TaskTags } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const { Op } = require('sequelize');

exports.createTaskTag = async (data, loggedInUser) => {
  try {
    const tagPayload = {
      ...data,
      organizationId: data.organizationId ?? loggedInUser.currentOrganizationId,
      createdBy: loggedInUser.id,
    };

    const tag = await TaskTags.create(tagPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('custom Tag'),
      data: tag,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateTaskTag = async (tagId, data) => {
  try {
    const tag = await TaskTags.findByPk(tagId);
    if (!tag) {
      return {
        success: false,
        message: errorMessage.NO_USER('tagId'),
      };
    }

    Object.assign(tag, data);
    await tag.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('custom Tag'),
      data: tag,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteTaskTag = async (tagId) => {
  try {
    const tag = await TaskTags.findByPk(tagId);
    if (!tag) {
      return {
        success: false,
        message: errorMessage.NO_USER('tagId'),
      };
    }

    await tag.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('custom Tag'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getTaskTags = async (query) => {
  try {
    const { type = 'all', organizationId } = query;
    let whereClause = {};

    if (organizationId) {
      whereClause.organizationId = {
        [Op.or]: [organizationId, null],
      };
    }

    if (type !== 'all') {
      whereClause.type = type;
    }

    const tags = await TaskTags.findAll({
      where: whereClause,
      attributes: ['id', 'name'],
    });

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('task tags'),
      data: tags,
    };
  } catch (error) {
    throw new Error(error);
  }
};
