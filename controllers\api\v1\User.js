const sequelize = require('sequelize');
const OPTIONS = require('../../../config/options');
const {
  Organization,
  Workspace,
  Designation,
  Employee,
} = require('../../../models');
const UserRepository = require('../../../models/repositories/UserRepository');
const OrganizationRepository = require('../../../models/repositories/OrganizationRepository');
const DocumentRequireRepository = require('../../../models/repositories/DocumentRequireRepository');
const DesignationMediaRepository = require('../../../models/repositories/DesignationMediaRepository');
const DeviceSessionRepository = require('@repo/DeviceSessionRepository');
const { generatePassword } = require('@models/helpers/UtilHelper');
const { getRequestDeviceDetails } = require('@models/helpers/UtilHelper');
const EmailHelper = require('@helpers/EmailHelper');
const ModulePermissionRepository = require('@repo/ModulePermissionRepository');

const {
  genRes,
  errorMessage,
  errorTypes,
  successMessage,
  resCode,
  defaultStatus,
  usersRoles,
} = require('../../../config/options');
const {
  userAttributes,
  modifyOutputData,
} = require('@models/helpers/UserHelper');

const { Op } = sequelize;

exports.login = async (req, res) => {
  const headers = req.headers;
  const requestDetails = await getRequestDeviceDetails(headers);
  try {
    const { email, mobileNumber, password } = req.body;
    let responseUser;
    if (email && password) {
      responseUser = await UserRepository.checkAndLoginWithPassword(
        req.body,
        true,
        requestDetails
      );
      if (!responseUser.success) {
        return res
          .status(resCode.HTTP_BAD_REQUEST)
          .json(genRes(resCode.HTTP_BAD_REQUEST, responseUser.message));
      }
    } else if (mobileNumber) {
      responseUser = await UserRepository.validateUserAndSendOtp(
        req.body,
        false,
        requestDetails
      );
      if (!responseUser.success) {
        return res
          .status(resCode.HTTP_BAD_REQUEST)
          .json(genRes(resCode.HTTP_BAD_REQUEST, responseUser.message));
      }
    }

    delete responseUser.success;
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: responseUser.message,
        data: responseUser.data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.signup = async (req, res) => {
  try {
    req.body.role = usersRoles.USER;
    const { success, data, message } = await UserRepository.checkAndCreate(
      req.body
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { data, message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.sendOtp = async (req, res) => {
  try {
    const { success, message } = await UserRepository.checkUserAndLoginWithOtp(
      req.body
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyOtp = async (req, res) => {
  try {
    const headers = req.headers;
    const requestDetails = await getRequestDeviceDetails(headers);
    const isEmail = req.body.type === 'email';
    const { success, message, data } = await UserRepository.checkAndVerifyOtp(
      req.body,
      isEmail,
      requestDetails
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, message, errorTypes.INPUT_VALIDATION)
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putUserProfile = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
      },
      attributes: {
        exclude: ['tempOtp', 'tempOtpExpiresAt', 'password', 'role'],
      },
    };

    const payload = {
      firstName: req.body.firstName || existingUser.firstName,
      lastName: req.body.lastName || existingUser.lastName,
      profilePicture: req.body.profilePicture || existingUser.profilePicture,
      countryCode: req.body.countryCode || existingUser.countryCode,
      locationId: req.body.locationId || existingUser.locationId,
      mobileNumber: req.body.mobileNumber || existingUser.mobileNumber,
      designationId: req.body.designationId || existingUser.designationId,
    };

    const { success, message, data } = await UserRepository.updateUser(
      query,
      payload
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getUserProfile = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
        status: [
          OPTIONS.defaultStatus.ACTIVE,
          OPTIONS.defaultStatus.ON_BOARDED,
          OPTIONS.defaultStatus.PENDING,
        ],
      },
      include: [
        {
          model: Organization,
          as: 'organization',
          required: false,
          attributes: ['id', 'name', 'logo'],
          include: [
            {
              model: Workspace,
              as: 'workspace',
              required: false,
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
        {
          model: Organization,
          as: 'currentOrganization',
          required: false,
          attributes: ['id', 'name', 'logo'],
          include: [
            {
              model: Workspace,
              as: 'workspace',
              required: false,
              attributes: ['id', 'name', 'description'],
            },
          ],
        },
        {
          model: Designation,
          as: 'designation',
          required: false,
          attributes: ['id', 'name'],
        },
      ],
      attributes: userAttributes(),
    };
    const existingUser = await UserRepository.getUser(query);

    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, errorMessage.DOES_NOT_EXIST('User'))
        );
    }
    const permissions =
      await ModulePermissionRepository.findPermissionsByRoleId(
        existingUser.designationId
      );
    const data = modifyOutputData(existingUser);
    data.permissions = permissions;
    const employeeDetails = await Employee.findOne({
      where: { userId: existingUser.id },
      attributes: ['id'],
    });

    data.employee = employeeDetails;
    data.organizationId = existingUser.currentOrganizationId;
    data.organization = existingUser.currentOrganization;
    data['oldOrganization'] = existingUser.organization;

    return res.status(resCode.HTTP_OK).json(genRes(resCode.HTTP_OK, { data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createPassword = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
        status: { [Op.notIn]: [defaultStatus.DELETED] },
      },
      attributes: { exclude: ['tempOtpExpiresAt', 'tempOtp', 'referralCode'] },
    };

    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            OPTIONS.errorMessage.DOES_NOT_EXIST('User')
          )
        );
    }
    existingUser.password = await generatePassword(req.body.password);
    await existingUser.save();
    const data = modifyOutputData(existingUser);

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        data,
        message: successMessage.SAVED_SUCCESS_MESSAGE('password'),
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          OPTIONS.errorMessage.SERVER_ERROR,
          OPTIONS.errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.changePassword = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
        status: { [Op.notIn]: [defaultStatus.DELETED] },
      },
      attributes: ['id', 'email', 'password'],
    };
    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            OPTIONS.errorMessage.DOES_NOT_EXIST('User')
          )
        );
    }
    if (!existingUser.validPassword(req.body.currentPassword)) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            OPTIONS.errorMessage.INCORRECT_DATA('current password')
          )
        );
    }
    existingUser.password = await UserRepository.generatePassword(
      req.body.newPassword
    );
    await existingUser.save();

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.SAVED_SUCCESS_MESSAGE('Password'),
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteUserAccount = async (req, res) => {
  try {
    const { id } = req.user;
    const user = await UserRepository.getUser({
      where: { id },
    });
    if (!user) {
      return res
        .status(resCode.HTTP_NOT_FOUND)
        .json(
          genRes(resCode.HTTP_NOT_FOUND, errorMessage.DOES_NOT_EXIST('User'))
        );
    }
    const { success, message } = await UserRepository.patchUpdateStatus(
      user,
      defaultStatus.DELETED,
      true
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res
      .status(resCode.HTTP_OK)
      .json(
        genRes(resCode.HTTP_OK, { message: 'Account deleted successfully' })
      );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyEmail = async (req, res) => {
  req.body.role = usersRoles.USER;
  req.body.status = defaultStatus.PENDING;
  try {
    const { success, message, data } = await UserRepository.checkAndCreate(
      req.body
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    await UserRepository.sendOtp(data, true);

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.OTP_SEND('email'),
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.register = async (req, res) => {
  try {
    const { id } = req.user;

    const query = {
      where: { id },
      attributes: {
        exclude: ['tempOtp', 'tempOtpExpiresAt'],
      },
    };

    // const documentCheckResult =
    //   await DocumentRequireRepository.checkRequiredDocuments(
    //     designationId,
    //     req.body
    //   );

    // if (!documentCheckResult.success) {
    //   return res
    //     .status(resCode.HTTP_BAD_REQUEST)
    //     .json(genRes(resCode.HTTP_BAD_REQUEST, documentCheckResult.message));
    // }

    if (req.body.organization) {
      const {
        success: orgSuccess,
        message: orgMessage,
        data: organization,
      } = await OrganizationRepository.checkAndCreateOrganization(
        req.body.organization,
        req.user,
        req.body.countryCode,
        req.body.mobileNumber
      );

      if (!orgSuccess) {
        return res
          .status(resCode.HTTP_BAD_REQUEST)
          .json(genRes(resCode.HTTP_BAD_REQUEST, orgMessage));
      }

      const emailData = {
        to: process.env.MCUBE_EMAIL || '<EMAIL>',
        variables: {
          name: req.body.organization.name,
          email: req.user.email,
          mobile_number: `${req.body.countryCode} ${req.body.mobileNumber}`,
        },
      };
      await EmailHelper.sendEmail(emailData, 'mcube_information_submission');

      req.body.organizationId = organization
        ? organization.id
        : req.user.organizationId;
    }

    const { success, message } = await UserRepository.updateUserWithWorkspace(
      query,
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    if (req.body.organizationMedia) {
      await DesignationMediaRepository.createUploadedDocuments(id, req.body);
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.ADD_SUCCESS_MESSAGE('User and Organization'),
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getRequiredDocuments = async (req, res) => {
  try {
    const { designationId } = req.user;
    const { message, data } =
      await DocumentRequireRepository.getRequiredDocuments(designationId);
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.removeDeviceSessionById = async (req, res) => {
  try {
    const { id } = req.params;
    const loggedInUser = req.user;
    const { success, message } =
      await DeviceSessionRepository.removeDeviceSessionById(id, loggedInUser);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getDeviceSessions = async (req, res) => {
  try {
    const start = parseInt(req.query.start) || 0;
    const limit = parseInt(req.query.limit) || 10;
    const loggedInUser = req.user;

    const { message, data } = await DeviceSessionRepository.getDeviceSessions({
      start,
      limit,
      loggedInUser,
    });

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

exports.getLoginHistory = async (req, res) => {
  try {
    const start = parseInt(req.query.start) || 0;
    const limit = parseInt(req.query.limit) || 10;
    const loggedInUser = req.user;

    const { message, data } = await DeviceSessionRepository.getLoginHistory({
      start,
      limit,
      loggedInUser,
    });

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (error) {
    console.error(error);
    return res.status(500).json({ error: 'Internal Server Error' });
  }
};

exports.testEmail = async (req, res) => {
  try {
    const { email, name } = req.body;
    if (!email) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, 'Email is required'));
    }

    const emailData = {
      to: email,
      name: name || '',
      subject: 'Test Email from Bricko',
      variables: {
        otp: '112233',
        userName: name || 'User',
      },
    };

    await EmailHelper.sendEmail(emailData);

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: 'Test email sent successfully',
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.onboardInvitedUser = async (req, res) => {
  try {
    const { success, message, data } = await UserRepository.onboardInvitedUser(
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.toggleMfa = async (req, res) => {
  try {
    const { success, message } = await UserRepository.toggleMfa(
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyMfa = async (req, res) => {
  try {
    const { success, message } = await UserRepository.verifyMfaOtp(
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.switchOrganization = async (req, res) => {
  const { organizationId } = req.params;
  try {
    const { success, message } = await UserRepository.switchOrganization(
      organizationId,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;
    const { success, message } = await UserRepository.forgotPassword(email);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.resetPassword = async (req, res) => {
  try {
    const { success, message } = await UserRepository.resetPassword(req.body);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getBankDetailsForUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { success, message, data } = await UserRepository.getBankDetails(id);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.registerBrand = async (req, res) => {
  try {
    const { success, message } = await UserRepository.registerBrand(
      req.body,
      req.user
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
