const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskTagMappingController = require('@controllers/v1/task/TaskTagMapping');
const TaskTagSchema = require('@schema-validation/task/TaskTagMapping');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/:id/task-tag-mapping',
  checkSchema(TaskTagSchema.createTaskTagMapping),
  ErrorHandleHelper.requestValidator,
  TaskTagMappingController.createTaskTag
);

router.delete(
  '/:id/:tagId/task-tag-mapping',
  ErrorHandleHelper.requestValidator,
  TaskTagMappingController.deleteTaskTag
);

module.exports = router;
