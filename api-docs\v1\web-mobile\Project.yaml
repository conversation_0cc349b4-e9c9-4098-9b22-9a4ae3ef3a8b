paths:
  /project:
    post:
      summary: Creates a new project
      description: Creates a new project with the provided details including name, type, dates, and address information
      operationId: createProject
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateProject"
      responses:
        "201":
          description: Project created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: Retrieves a list of projects
      description: Retrieves a list of projects based on optional query parameters such as organization ID, search string, start offset, limit, and status.
      operationId: getProjects
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: organizationId
          in: query
          required: false
          description: The ID of the organization to filter projects by
          schema:
            type: integer
        - name: search
          in: query
          required: false
          description: A search string to filter projects by name or description
          schema:
            type: string
        - name: start
          in: query
          required: false
          description: The start offset for pagination
          schema:
            type: integer
        - name: limit
          in: query
          required: false
          description: The limit of projects to return per page
          schema:
            type: integer
        - name: startDate
          in: query
          required: false
          description: The start date for filtering projects
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          required: false
          description: The end date for filtering projects
          schema:
            type: string
            format: date
        - name: city
          in: query
          required: false
          description: An array of cities to filter projects by
          schema:
            type: array
            items:
              type: string
        - name: state
          in: query
          required: false
          description: An array of states to filter projects by
          schema:
            type: array
            items:
              type: string
        - name: status
          in: query
          required: false
          description: An array of active to filter projects by
          schema:
            type: array
            items:
              type: string
      responses:
        "200":
          description: List of projects retrieved successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /project/{id}:
    put:
      summary: Updates a project
      description: Updates an existing project with the provided details including name, type, dates, and address information
      operationId: updateProject
      tags:
        - Project
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the project under which the sub-project is created
          schema:
            type: integer
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createOrUpdateProject"
      responses:
        "200":
          description: Project updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /project/{id}/sub-project:
    post:
      summary: Creates a new sub-project
      description: Creates a new sub-project under a specific project. Requires project ID as a path parameter and sub-project details in the request body.
      operationId: createSubProject
      tags:
        - Project
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the project under which the sub-project is created
          schema:
            type: integer
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSubProject"
      responses:
        "201":
          description: Sub-project created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /project/detect-weather:
    get:
      summary: Get weather details for a given latitude and longitude
      description: Retrieves weather data like temperature, wind speed, and pressure based on the provided latitude and longitude
      operationId: detectWeather
      tags:
        - Project
      parameters:
        - name: latitude
          in: query
          required: true
          description: Latitude of the location
          schema:
            type: string
            example: "32.1054"
        - name: longitude
          in: query
          required: true
          description: Longitude of the location
          schema:
            type: string
            example: "76.3789"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: Weather data fetched successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  temperature:
                    type: number
                    example: 15.2
                  windSpeed:
                    type: number
                    example: 3.2
                  pressure:
                    type: number
                    example: 1012.5
        "400":
          description: Invalid request (missing latitude or longitude)
        "500":
          description: Internal Server Error
  /project/{id}/drawing:
    post:
      summary: Adds drawings to a project
      description: Upload drawings for a specific project using the provided project ID. The drawings should be sent as an array of filenames.
      operationId: addDrawingsToProject
      tags:
        - Project
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the project to which the drawings belong
          schema:
            type: integer
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addDrawingsToProject"
      responses:
        "201":
          description: Drawings uploaded successfully
        "400":
          description: Invalid request or invalid drawing files
        "500":
          description: Internal Server Error

components:
  schemas:
    createOrUpdateProject:
      type: object
      properties:
        name:
          type: string
          example: "Green Valley Heights"
        projectTypeId:
          type: integer
          example: 1
        organizationId: 
          type: integer
          example: 1
        logo:
          type: string
          example: "https://example.com/project-logo.png"
        reraNumber:
          type: string
          maxLength: 50
          example: "RERA2023ABC123"
        startDate:
          type: string
          format: date
          example: "2023-01-01"
        endDate:
          type: string
          example: "2025-12-31"
        status:
          type: string
          example: "new_project"
          description: "The current status of the project"
        about:
          type: string
          example: "A premium residential project with modern amenities"
        addressDetails:
          type: object
          properties:
            address:
              type: string
              example: "123 Main Street"
            city:
              type: string
              example: "Mumbai"
            pincode:
              type: string
              example: "400001"
            landmark:
              type: string
              example: "Near Central Park"
            latitude:
              type: string
              example: "19.0760"
            longitude:
              type: string
              example: "72.8777"
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "https://example.com/drawings/drawing1.png"
        additionalFields:
          type: array
          items:
            type: object
            properties:
              fieldName:
                type: string
                description: "The title of the other detail."
                example: "Parking Availability"
              fieldValue:
                type: string
                description: "A description of the other detail."
                example: "Covered parking space available."
      required:
        - name
        - projectTypeId
    createSubProject:
      type: object
      properties:
        name:
          type: string
          example: "Green Valley Heights"
        projectTypeId:
          type: integer
          example: 1
        organizationId: 
          type: integer
          example: 1
        startDate:
          type: string
          format: date
          example: "2023-01-01"
        endDate:
          type: string
          example: "2025-12-31"
        about:
          type: string
          example: "A premium residential project with modern amenities"
        addressDetails:
          type: object
          properties:
            address:
              type: string
              example: "123 Main Street"
            city:
              type: string
              example: "Mumbai"
            pincode:
              type: string
              example: "400001"
            landmark:
              type: string
              example: "Near Central Park"
            latitude:
              type: string
              example: "19.0760"
            longitude:
              type: string
              example: "72.8777"
        drawings:
          type: array
          items:
            type: string
          example: ["drawing1.png", "drawing2.png", "drawing3.png"]
        additionalFields:
          type: array
          items:
            type: object
            properties:
              fieldName:
                type: string
                description: "The title of the other detail."
                example: "Parking Availability"
              fieldValue:
                type: string
                description: "A description of the other detail."
                example: "Covered parking space available."
      required:
        - name
        - projectTypeId
    addDrawingsToProject:
      type: object
      properties:
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
