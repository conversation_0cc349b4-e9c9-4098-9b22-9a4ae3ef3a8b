paths:
  /attendance/checkin:
    post:
      tags:
        - "Attendance"
      summary: "Check-in for the user"
      description: "This endpoint allows a user to check in for the day. If a check-in record already exists for the same date and user, it will return an error."
      operationId: "CheckIn"
      requestBody:
        description: "The details required for checking in."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CheckIn"
        required: false
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Successfully checked in."
        "400":
          description: "Invalid input data or already checked in."
        "500":
          description: "Internal Server Error"

  /attendance/checkout:
    post:
      tags:
        - "Attendance"
      summary: "Check-out for the user"
      description: "This endpoint allows a user to check out for the day. If a check-in record already exists for the same date then only you can checkout"
      operationId: "CheckOut"
      requestBody:
        description: "The details required for checking out."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CheckOut"
        required: false
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Successfully checked out."
        "400":
          description: "checkin first before checkout"
        "500":
          description: "Internal Server Error"

  /attendance/list:
    get:
      tags:
        - "Attendance"
      summary: "List attendance records"
      description: "This endpoint retrieves a list of attendance records for the logged-in user, with optional filtering based on the date range and pagination."
      operationId: "ListAttendance"
      parameters:
        - name: "start"
          in: "query"
          description: "The starting index for pagination. Default is 0."
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "The number of records to retrieve. Default is 10."
          required: false
          schema:
            type: integer
            default: 10
        - name: "fromDate"
          in: "query"
          description: "The start date to filter attendance records."
          required: false
          schema:
            type: string
            format: date
        - name: "toDate"
          in: "query"
          description: "The end date to filter attendance records."
          required: false
          schema:
            type: string
            format: date
        - name: "status"
          in: "query"
          required: false
          description: "Filter attendance by multiple statuses. Pass as a JSON array."
          schema:
            type: array
            items:
              type: string
            example: ["approved_leave", "full_day","half_day"]
        - name: "inTime"
          in: "query"
          description: "Filter attendance based on inTime"
          required: false
          schema:
            type: string
            example: 10
        - name: "outTime"
          in: "query"
          description: "Filter attendance based on outTime"
          required: false
          schema:
            type: string
            example: 18
        - name: "totalDuration"
          in: "query"
          description: "Filter attendance based on outTime"
          required: false
          schema:
            type: string
            example: 0
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully fetched attendance records."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /attendance/user-list:
    get:
      tags:
        - "Attendance"
      summary: "List attendance records for employee"
      description: "This endpoint retrieves a list of attendance  for employee records for the logged-in user, with optional filtering based on the date range and pagination."
      operationId: "ListAttendanceEmployee"
      parameters:
        - name: "start"
          in: "query"
          description: "The starting index for pagination. Default is 0."
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "The number of records to retrieve. Default is 10."
          required: false
          schema:
            type: integer
            default: 10
        - name: "fromDate"
          in: "query"
          description: "The start date to filter attendance records."
          required: false
          schema:
            type: string
            format: date
        - name: "toDate"
          in: "query"
          description: "The end date to filter attendance records."
          required: false
          schema:
            type: string
            format: date
        - name: "role"
          in: "query"
          description: "The role of User"
          required: false
          schema:
            type: string
            example: EMPLOYEE
        - name: "employeeId"
          in: "query"
          description: "The id of Employee"
          required: true
          schema:
            type: integer
            example: 1
        - name: "status"
          in: "query"
          required: false
          description: "Filter attendance by multiple statuses. Pass as a JSON array."
          schema:
            type: array
            items:
              type: string
            example: ["approved_leave", "full_day","half_day"]
        - name: "inTime"
          in: "query"
          description: "Filter attendance based on inTime"
          required: false
          schema:
            type: string
            example: 10
        - name: "outTime"
          in: "query"
          description: "Filter attendance based on outTime"
          required: false
          schema:
            type: string
            example: 18
        - name: "totalDuration"
          in: "query"
          description: "Filter attendance based on outTime"
          required: false
          schema:
            type: string
            example: 0
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully fetched attendance records."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /attendance/list-by-date:
    get:
      tags:
        - "Attendance"
      summary: "Attendance data by date"
      description: "This endpoint retrieves attendance data based on date"
      operationId: "ListAttendanceByDate"
      parameters:
        - name: "date"
          in: "query"
          description: "The date to filter attendance records."
          required: true
          schema:
            type: string
            format: date
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully fetched attendance records based on date."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /attendance/update:
    patch:
      tags:
        - "Attendance"
      summary: "Attendance Update with Ids"
      description: "This endpoint allows updating attendance records for multiple IDs."
      operationId: "UpdateAttendanceByIds"
      requestBody:
        description: "The details required to update"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/AttendanceUpdate"
        required: false
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully updated attendance records."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /attendance/status-count:
    get:
      tags:
        - "Attendance"
      summary: "Get Attendance Status Count"
      description: "This endpoint returns the count of attendance records grouped by their status."
      operationId: "GetAttendanceStatusCount"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully fetched the attendance status count."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /attendance/status-dropdown:
    get:
      tags:
        - "Attendance"
      summary: "Get Attendance Status Dropdown"
      description: "This endpoint returns of attendance status."
      operationId: "GetAttendanceStatusDropdown"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Successfully fetched the attendance status dropdown"
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

components:
  schemas:

    CheckIn:
      type: "object"
      properties:
        latitude:
          type: "number"
          format: "double"
          example: 28.6139  
        longitude:
          type: "number"
          format: "double"
          example: 77.2090  

    CheckOut:
      type: "object"
      properties:
        latitude:
          type: "number"
          format: "double"
          example: 28.6139  
        longitude:
          type: "number"
          format: "double"
          example: 77.2090  

    AttendanceUpdate:
      type: object
      properties:
        attendanceIds:
          type: array
          items:
            type: integer
          example: [1, 2, 3]
        updateData:
          type: object
          properties:
            date:
              type: string
              format: date
              example: "2025-02-05"
            inTime:
              type: string
              format: time
              example: "09:00:00"
            outTime:
              type: string
              format: time
              example: "18:00:00"
            status:
              type: string
              example: "half_day"
            description:
              type: string
              example: "updated-attendance data"

    