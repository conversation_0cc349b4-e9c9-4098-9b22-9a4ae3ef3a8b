'use strict';

const { taskTagType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('TaskTags', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM(taskTagType.getValues()),
        allowNull: false,
        defaultValue: taskTagType.TASK_TAG,
      },
      organizationId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Organization',
          key: 'id',
        },
        allowNull: true,
        onDelete: 'CASCADE',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    const taskTags = [
      'Site Visit',
      'Documentation',
      'Meeting',
      'Vendor Coordination',
      'Inspection',
      'Milestone',
      'Audit',
      'Short-Term',
      'Long-Term',
      'Time-Sensitive',
      'Recurring',
    ];

    for (const tag of taskTags) {
      await queryInterface.bulkInsert('TaskTags', [
        {
          name: tag,
          type: taskTagType.TASK_TAG,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]);
    }
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('TaskTags');
  },
};
