'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const StockAdjustment = sequelize.define(
    'StockAdjustment',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      adjustmentNumber: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      adjustmentDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.stockAdjustmentStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.stockAdjustmentStatus.DRAFT,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  StockAdjustment.associate = (models) => {
    StockAdjustment.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    StockAdjustment.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    StockAdjustment.belongsTo(models.Warehouse, {
      foreignKey: 'warehouseId',
      as: 'warehouse',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    StockAdjustment.hasMany(models.StockAdjustmentItem, {
      foreignKey: 'stockAdjustmentId',
      as: 'stockAdjustmentItem',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return StockAdjustment;
};
