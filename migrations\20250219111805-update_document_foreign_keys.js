'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'Document',
      'Document_createdBy_fkey'
    );
    await queryInterface.removeConstraint(
      'Document',
      'Document_updatedBy_fkey'
    );

    await queryInterface.addConstraint('Document', {
      fields: ['createdBy'],
      type: 'foreign key',
      name: 'Document_createdBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
    });

    await queryInterface.addConstraint('Document', {
      fields: ['updatedBy'],
      type: 'foreign key',
      name: 'Document_updatedBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'Document',
      'Document_createdBy_fkey'
    );
    await queryInterface.removeConstraint(
      'Document',
      'Document_updatedBy_fkey'
    );

    await queryInterface.addConstraint('Document', {
      fields: ['createdBy'],
      type: 'foreign key',
      name: 'Document_createdBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
    });

    await queryInterface.addConstraint('Document', {
      fields: ['updatedBy'],
      type: 'foreign key',
      name: 'Document_updatedBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
    });
  },
};
