const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const ItemVariant = sequelize.define(
    'ItemVariant',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      attribute: {
        type: DataTypes.ENUM(OPTIONS.itemVariantType.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.itemVariantType.COLOUR,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      sku: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      reOrderPoint: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      reOrderPointScope: {
        type: DataTypes.ENUM(OPTIONS.reOrderPointScope.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.reOrderPointScope.ALL_WAREHOUSES,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ItemVariant.associate = (models) => {
    ItemVariant.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    ItemVariant.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    ItemVariant.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    ItemVariant.hasMany(models.ItemMedia, {
      foreignKey: 'itemVariantId',
      as: 'itemVariantMedia',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return ItemVariant;
};
