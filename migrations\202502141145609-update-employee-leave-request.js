'use strict';
const { leaveType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription =
      await queryInterface.describeTable('EmployeeLeave');

    if (!tableDescription.fromDate) {
      await queryInterface.addColumn('EmployeeLeave', 'fromDate', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    } else if (!tableDescription.fromDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'fromDate', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    }

    if (!tableDescription.toDate) {
      await queryInterface.addColumn('EmployeeLeave', 'toDate', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    } else if (!tableDescription.toDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'toDate', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    }

    if (!tableDescription.leaveType) {
      await queryInterface.addColumn('EmployeeLeave', 'leaveType', {
        type: Sequelize.ENUM(...leaveType.getValues()),
        allowNull: true,
      });
    } else if (!tableDescription.leaveType.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'leaveType', {
        type: Sequelize.ENUM(...leaveType.getValues()),
        allowNull: true,
      });
    }

    if (!tableDescription.totalDays) {
      await queryInterface.addColumn('EmployeeLeave', 'totalDays', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    } else if (!tableDescription.totalDays.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'totalDays', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }

    if (!tableDescription.entityId) {
      await queryInterface.addColumn('EmployeeLeave', 'entityId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    } else if (!tableDescription.entityId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'entityId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }

    if (!tableDescription.employeeId) {
      await queryInterface.addColumn('EmployeeLeave', 'employeeId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    } else if (!tableDescription.employeeId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'employeeId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }

    if (!tableDescription.userId) {
      await queryInterface.addColumn('EmployeeLeave', 'userId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    } else if (!tableDescription.userId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'userId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription =
      await queryInterface.describeTable('EmployeeLeave');

    if (tableDescription.fromDate && tableDescription.fromDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'fromDate', {
        type: Sequelize.DATE,
        allowNull: false,
      });
    }

    if (tableDescription.toDate && tableDescription.toDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'toDate', {
        type: Sequelize.DATE,
        allowNull: false,
      });
    }

    if (tableDescription.leaveType && tableDescription.leaveType.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'leaveType', {
        type: Sequelize.ENUM(...leaveType.getValues()),
        allowNull: false,
      });
    }

    if (tableDescription.totalDays && tableDescription.totalDays.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'totalDays', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }

    if (tableDescription.entityId && tableDescription.entityId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'entityId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }

    if (tableDescription.employeeId && tableDescription.employeeId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'employeeId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }

    if (tableDescription.userId && tableDescription.userId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'userId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }
  },
};
