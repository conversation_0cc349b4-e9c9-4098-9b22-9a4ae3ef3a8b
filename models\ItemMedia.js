const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const ItemMedia = sequelize.define(
    'ItemMedia',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      mediaType: {
        type: DataTypes.ENUM(OPTIONS.itemMediaType.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.itemMediaType.ITEM,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ItemMedia.associate = (models) => {
    ItemMedia.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    ItemMedia.belongsTo(models.ItemVariant, {
      foreignKey: 'itemVariantId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return ItemMedia;
};
