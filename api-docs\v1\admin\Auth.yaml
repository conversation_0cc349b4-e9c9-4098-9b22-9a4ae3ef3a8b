paths:
  /admin/auth/login:
    post:
      tags:
        - "Authentication"
      summary: "User Login"
      description: "Logs in a user with their email and password, and returns an access token."
      operationId: "login"
      requestBody:
        description: User credentials
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/loginRequest"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "Login successful, returns access token and user information"
        "400":
          description: "Invalid email or password"
        "500":
          description: "Internal server error"
  /admin/auth/forgot-password:
    post:
      tags:
        - Authentication
      summary: Request Password Reset
      description: Sends a password reset email to the user
      operationId: resetPasswordRequest
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/forgotPassword" 
      produces:
        - "application/json"
      responses:
        '200':
          description: An OTP has been send to your email
        '400':
          description: User does not exists with this email
        '500':
          description: Oops! something went wrong.
  /admin/auth/verify-forget-password:
      post:
        tags:
          - Authentication
        summary: Verify Password Reset OTP
        description: Verifies the OTP sent to the user's email for password reset
        operationId: verifyResetPassword
        requestBody:
          required: true
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/verifyResetPassword"
        responses:
          '200':
            description: OTP has been verified
          '400':
            description: Invalid Otp
          '500':
            description: Oops! something went wrong.
  /admin/auth/reset-password:
    post:
      tags:
        - Authentication
      summary: Reset Password
      description: Resets the user's password using OTP verification
      operationId: resetPassword
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/resetPassword"
      responses:
        '200':
          description: reset password successfully
        '400':
          description: Invalid Otp
        '500':
          description: Oops! something went wrong.
components:
  schemas:
    forgotPassword:
      type: object
      properties:
        email:
          type: string
          format: email
          description: The email address of the user
          example: <EMAIL>
    resetPassword:
      type: object
      properties:
        email:
          type: string
          format: email
          description: The email address of the user
          example: <EMAIL>
        newPassword:
          type: string
          description: The new password to set
          example: 'NewPassword123!'
        confirmPassword:
          type: string
          description: Confirmation of the new password
          example: 'NewPassword123!'
        tempOtp:
          type: string
          description: The OTP sent to the user's email
          example: '555555'
    verifyResetPassword:
      type: object
      properties:
        tempOtp:
          type: string
          description: The OTP sent to the user's email
          example: '555555'
        email:
          type: string
          format: email
          description: The email address of the user
          example: <EMAIL>
    loginRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: The email address of the user
          example: <EMAIL>
        password:
          type: string
          description: The password of the user
          example: 'Password123!'
      required:
        - email
        - password