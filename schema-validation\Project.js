const { projectStatus } = require('@config/options');

exports.getProjects = {
  organizationId: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'organizationId ID must be an integer',
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'search must be a string',
    },
  },
  start: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    toInt: true,
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    toInt: true,
    default: 10,
  },
  status: {
    in: ['query'],
    optional: true,
  },
  startDate: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'startDate must be a string',
    },
    custom: {
      options: (value) => {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        return dateRegex.test(value);
      },
      errorMessage: 'startDate must be in YYYY-MM-DD format',
    },
  },
  endDate: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'endDate must be a string',
    },
    custom: {
      options: (value) => {
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        return dateRegex.test(value);
      },
      errorMessage: 'endDate must be in YYYY-MM-DD format',
    },
  },
  city: {
    in: ['query'],
    optional: true,
    custom: {
      options: (value, { req }) => {
        if (!Array.isArray(value)) {
          return [value];
        }
        return value;
      },
    },
  },
  state: {
    in: ['query'],
    optional: true,
    custom: {
      options: (value, { req }) => {
        if (!Array.isArray(value)) {
          return [value];
        }
        return value;
      },
    },
  },
  search: {
    in: ['query'],
    optional: true,
    isString: {
      errorMessage: 'search must be a string',
    },
  },
};

exports.createOrUpdateProjectAndSubProject = {
  name: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'name is required',
    },
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  organizationId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'organizationId ID must be an integer',
    },
  },
  projectTypeId: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'projectTypeId is required',
    },
    isInt: {
      errorMessage: 'Project type must be an integer',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
  status: {
    in: ['query'],
    optional: true,
    isIn: {
      options: [projectStatus.getProjectStatusArray()],
      errorMessage: 'projectStatus must enum',
    },
  },
  reraNumber: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'reraNumber is  is not empty',
    },
    isString: {
      errorMessage: 'RERA number must be a string',
    },
  },
  startDate: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'startDate is not empty',
    },
    isISO8601: {
      errorMessage: 'Start date must be a valid date',
    },
  },
  endDate: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'endDate is not empty',
    },
    isISO8601: {
      errorMessage: 'End date must be a valid date',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'about must be a string',
    },
  },
  'addressDetails.address': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'address 1  is not empty',
    },
    isString: {
      errorMessage: 'address must be a string',
    },
  },
  'addressDetails.city': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'city is not empty',
    },
    isString: {
      errorMessage: 'city must be a string',
    },
  },
  'addressDetails.pincode': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'pincode  is not empty',
    },
    isString: {
      errorMessage: 'pincode must be a string',
    },
  },
  'addressDetails.landmark': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'landmark  is not empty',
    },
    isString: {
      errorMessage: 'landmark must be a string',
    },
  },
  'addressDetails.latitude': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'latitude  is not empty',
    },
    isString: {
      errorMessage: 'latitude must be a string',
    },
  },
  'addressDetails.longitude': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'longitude  is not empty',
    },
    isString: {
      errorMessage: 'longitude must be a string',
    },
  },
  'additionalFields.*.fieldName': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'additionalFields  is not empty',
    },
    isString: {
      errorMessage: 'additionalFields must be a string',
    },
  },
  'additionalFields.*.fieldValue': {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'fieldValue  is not empty',
    },
    isString: {
      errorMessage: 'fieldValue must be a string',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};

exports.validateAddDrawingsToProject = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Project Id is required',
    },
    isInt: {
      errorMessage: 'Project Id must be a valid integer',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};
