'use strict';
const {
  paymentStageCalculationType,
  paymentTriggerType,
  paymentPlanType,
} = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('QuotationPaymentPlan', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      quotationId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Quotation',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      planType: {
        type: Sequelize.ENUM(...paymentPlanType.paymentPlanTypeArray()),
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      calculationType: {
        type: Sequelize.ENUM(
          ...paymentStageCalculationType.paymentStageCalculationTypeArray()
        ),
        allowNull: true,
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      triggerType: {
        type: Sequelize.ENUM(...paymentTriggerType.paymentTriggerTypeArray()),
        allowNull: true,
      },
      dueOn: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      netPayableAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('QuotationPaymentPlan');
  },
};
