const {
  Request,
  User,
  Designation,
  RequestComment,
  RequestDocument,
  Tasks,
  WorkOrder,
  Project,
  Contractor,
  Quotation,
  Unit,
  Organization,
  Customer,
  WbsActivity,
  Floor,
  EmployeeLeave,
  Employee,
  Journal,
  TransactionEntry,
  Indent,
  ApprovalWorkflow,
  Warehouse,
  StockAdjustment,
  StockAdjustmentItem,
} = require('..');
const {
  successMessage,
  errorMessage,
  requestStatus,
  activityType,
  requestType,
  requestTypeActivityMapping,
  requestTypeTitle,
} = require('@config/options');
const { Op } = require('sequelize');
const moment = require('moment');
const options = require('@config/options');

exports.createRequest = async (data, loggedInUser) => {
  const transaction = await Request.sequelize.transaction();
  try {
    const requestedTo = await User.findByPk(data.requestedTo);
    if (!requestedTo) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`requestedTo with id ${data.requestedTo}`)
      );
    }

    // remove the existing request fro now
    // const existingRequest = await Request.findOne({
    //   where: {
    //     requestType: data.requestType,
    //     recordId: data.recordId,
    //     status: { [Op.ne]: requestStatus.REJECTED },
    //   },
    // });

    // if (existingRequest) {
    //   throw new Error(errorMessage.ALREADY_EXIST(`Request`));
    // }

    if (data.requestType === requestType.WORK_ORDER_REQUEST) {
      const workOrder = await WorkOrder.findByPk(data.recordId, {
        include: [
          {
            model: Project,
            as: 'project',
            required: false,
            attributes: ['id', 'name', 'logo'],
            include: [
              {
                model: Project,
                as: 'parentProject',
                required: false,
                attributes: ['id', 'name', 'logo'],
              },
            ],
          },
        ],
      });
      if (!workOrder) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Work order with id ${data.recordId}`
          ),
        };
      }
      workOrder.status = options.workOrderStatus.UNDER_APPROVAL;
      workOrder.save({ transaction });

      data.title = requestTypeTitle.WORK_ORDER_REQUEST;
      data.shortDescription = workOrder.workOrderNumber || '';
      const parentProject = workOrder?.project?.parentProject
        ? {
            id: workOrder.project.parentProject.id,
            type: 'parentProject',
            name: workOrder.project.parentProject.name,
          }
        : null;
      data.reference = [
        parentProject,
        {
          id: workOrder?.project?.id,
          type: 'project',
          name: workOrder?.project?.name,
        },
      ].filter(Boolean);
    }

    if (data.requestType === requestType.QUOTATION_REQUEST) {
      const quotation = await Quotation.findByPk(data.recordId, {
        include: [
          {
            model: Project,
            as: 'project',
            required: false,
            attributes: ['id', 'name', 'logo'],
            include: [
              {
                model: Project,
                as: 'parentProject',
                required: false,
                attributes: ['id', 'name', 'logo'],
              },
            ],
          },
          {
            model: Unit,
            as: 'unit',
            required: false,
            attributes: ['id', 'name'],
            include: [
              {
                model: Floor,
                as: 'unitsInFloor',
                required: false,
                attributes: ['id', 'name'],
              },
            ],
          },
        ],
      });
      if (!quotation) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Quotation with id ${data.recordId}`
          ),
        };
      }
      quotation.status = options.quotationStatus.UNDER_APPROVAL;
      quotation.save({ transaction });

      data.title = requestTypeTitle.QUOTATION_REQUEST;
      data.shortDescription = quotation.quotationCode || '';
      const parentProject = quotation?.project?.parentProject
        ? {
            id: quotation.project.parentProject.id,
            type: 'parentProject',
            name: quotation.project.parentProject.name,
          }
        : null;
      const floor = quotation?.unit?.unitsInFloor
        ? {
            id: quotation?.unit?.unitsInFloor?.id,
            type: 'floor',
            name: quotation?.unit?.unitsInFloor?.name,
          }
        : null;
      data.reference = [
        parentProject,
        {
          id: quotation?.project?.id,
          type: 'project',
          name: quotation?.project?.name,
        },
        {
          id: quotation?.unit?.id,
          type: 'unit',
          name: quotation?.unit?.name,
        },
        floor,
      ].filter(Boolean);
    }

    if (data.requestType === requestType.WBS_REQUEST) {
      const wbs = await WbsActivity.findByPk(data.recordId, {
        include: [
          {
            model: Project,
            as: 'project',
            required: false,
            attributes: ['id', 'name', 'logo'],
            include: [
              {
                model: Project,
                as: 'parentProject',
                required: false,
                attributes: ['id', 'name', 'logo'],
              },
            ],
          },
          {
            model: Organization,
            as: 'organization',
            required: false,
            attributes: ['id', 'name'],
          },
        ],
      });
      if (!wbs) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`WBS with id ${data.recordId}`),
        };
      }
      wbs.status = options.defaultStatus.PENDING;
      wbs.save({ transaction });

      const parentProject = wbs?.project?.parentProject
        ? {
            id: wbs.project.parentProject.id,
            type: 'parentProject',
            name: wbs.project.parentProject.name,
          }
        : null;
      data.title = requestTypeTitle.WBS_REQUEST;
      data.shortDescription = `${wbs.shortCode} ${wbs.wbsCode}` || '';
      data.reference = [
        parentProject,
        {
          id: wbs?.project?.id,
          type: 'project',
          name: wbs?.project?.name,
        },
      ];
    }

    if (data.requestType === requestType.LEAVE_REQUEST) {
      const employee = await Employee.findOne({
        where: data.leaveForEmployeeId
          ? { id: data.leaveForEmployeeId }
          : { userId: loggedInUser.id },
      });
      if (!employee) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Employee`),
        };
      }
      const leaveRequestPayload = {
        ...data,
        userId: loggedInUser.id,
        employeeId: employee.id,
        createdBy: loggedInUser.id,
      };
      const leave = await EmployeeLeave.create(leaveRequestPayload, {
        transaction,
      });

      data.recordId = leave.id;
      data.title = requestTypeTitle.LEAVE_REQUEST;
      data.shortDescription = data.leaveType || '';
      data.reference = [];
    }

    if (data.requestType === requestType.JOURNAL_REQUEST) {
      const journal = await Journal.findByPk(data.recordId);
      if (!journal) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Journal with id ${data.recordId}`
          ),
        };
      }
      journal.journalState = options.journalState.UNDER_APPROVAL;
      journal.save({ transaction });
      data.title = requestTypeTitle.JOURNAL_REQUEST;
      data.shortDescription = journal.journalNumber || '';
    }

    if (data.requestType === requestType.INDENT_MR_REQUEST) {
      const indent = await Indent.findByPk(data.recordId, {
        include: [
          {
            model: WorkOrder,
            as: 'workOrder',
            required: false,
            attributes: ['id', 'workOrderNumber'],
          },
        ],
      });
      if (!indent) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Indent with id ${data.recordId}`
          ),
        };
      }
      indent.status = options.indentStatus.UNDER_APPROVAL;
      data.title = requestTypeTitle.INDENT_MR_REQUEST;
      data.shortDescription = indent.workOrder?.workOrderNumber || '';
      indent.save({ transaction });
    }

    if (data.requestType === requestType.STOCK_ADJUSTMENT_REQUEST) {
      const stockAdjustment = await StockAdjustment.findByPk(data.recordId, {
        include: [
          {
            model: Warehouse,
            as: 'warehouse',
            required: false,
            attributes: ['id', 'name'],
          },
        ],
      });
      if (!stockAdjustment) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Stock Adjustment with id ${data.recordId}`
          ),
        };
      }
      stockAdjustment.status = options.stockAdjustmentStatus.UNDER_APPROVAL;
      data.title = requestTypeTitle.STOCK_ADJUSTMENT_REQUEST;
      data.shortDescription = stockAdjustment.warehouse?.name || '';
      stockAdjustment.save({ transaction });
    }

    const requestPayload = {
      ...data,
      requestedTo: requestedTo.id,
      requestedBy: loggedInUser.id,
      organizationId: loggedInUser.currentOrganizationId,
    };
    const request = await Request.create(requestPayload, { transaction });

    if (data.documents && Array.isArray(data.documents)) {
      const documentRecords = data.documents.map((doc) => ({
        fileName: doc.fileName,
        fileType: doc.fileType,
        filePath: doc.filePath,
        fileSize: doc.fileSize,
        requestId: request.id,
        createdBy: loggedInUser.id,
      }));

      await RequestDocument.bulkCreate(documentRecords, { transaction });
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Request'),
      data: request,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the Request.',
    };
  }
};

exports.updateRequest = async (requestId, data, loggedInUser) => {
  const transaction = await Request.sequelize.transaction();
  try {
    const request = await Request.findByPk(requestId);
    if (!request) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Request with ID: ' + requestId),
      };
    }

    if (data.status && data.status !== request.status) {
      if (data.status === requestStatus.APPROVED) {
        request.approvedBy = loggedInUser.id;
        if (request.requestType === requestType.LEAVE_REQUEST) {
          const leaveData = await EmployeeLeave.findByPk(request.recordId);
          if (leaveData) {
            leaveData.status = options.leaveStatus.APPROVED;
            await leaveData.save({ transaction });
          }
        }
        if (request.requestType === requestType.QUOTATION_REQUEST) {
          const existingQuotation = await Quotation.findByPk(request.recordId);
          if (existingQuotation) {
            existingQuotation.status = options.quotationStatus.APPROVED;
            await existingQuotation.save({ transaction });
          }
        }
        if (request.requestType === requestType.WORK_ORDER_REQUEST) {
          const existingWorkOrder = await WorkOrder.findByPk(request.recordId);
          if (existingWorkOrder) {
            existingWorkOrder.status = options.workOrderStatus.APPROVED;
            await existingWorkOrder.save({ transaction });
          }
        }
        if (request.requestType === requestType.INDENT_MR_REQUEST) {
          const indent = await Indent.findByPk(request.recordId);
          if (indent) {
            indent.status = options.indentStatus.APPROVED;
            await indent.save({ transaction });
          }
        }
        if (request.requestType === requestType.STOCK_ADJUSTMENT_REQUEST) {
          const stockAdjustment = await StockAdjustment.findByPk(
            request.recordId
          );
          if (stockAdjustment) {
            stockAdjustment.status = options.stockAdjustmentStatus.APPROVED;
            await stockAdjustment.save({ transaction });
          }
        }
      } else {
        request.approvedBy = null;
      }
      await request.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated status ${request.status} to ${data.status}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.priority && data.priority !== request.priority) {
      await request.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Updated priority to ${data.priority}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (data.requestedTo && data.requestedTo !== request.requestedTo) {
      const requestedToUser = await User.findByPk(data.requestedTo);
      if (!requestedToUser) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Assigned To User with id ${data.requestedTo}`
          )
        );
      }
      await request.createActivity(
        {
          actionType: activityType.EDITED,
          activityDescription: `Assigned request to ${requestedToUser.firstName} ${requestedToUser.lastName}`,
          createdBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    if (
      data.dueDate &&
      new Date(data.dueDate).getTime() !== new Date(request.dueDate).getTime()
    ) {
      const now = new Date();
      if (data.dueDate) {
        const dueDate = new Date(data.dueDate);
        if (dueDate <= now) {
          throw new Error('Due date must be in the future or today.');
        }
      }
    }

    request.updatedBy = loggedInUser.id;
    Object.assign(request, data);
    await request.save({ transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Request'),
      data: request,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the Request.',
    };
  }
};

exports.getRequestDetails = async (requestId) => {
  try {
    const request = await Request.findOne({
      where: { id: requestId },
      attributes: {
        exclude: ['requestedBy', 'requestedTo', 'approvedBy'],
      },
      include: [
        {
          model: User,
          as: 'receiver',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name'],
            },
          ],
        },
        {
          model: User,
          as: 'requester',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name'],
            },
          ],
        },
        {
          model: User,
          as: 'approver',
          attributes: [
            'id',
            'firstName',
            'middleName',
            'lastName',
            'profilePicture',
          ],
          include: [
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name'],
            },
          ],
        },
      ],
    });

    if (!request) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Request with ID: ${requestId}`),
      };
    }

    const activities = await request.getActivities({
      attributes: {
        exclude: ['activityOn', 'recordId', 'createdBy'],
      },
      include: [
        {
          model: User,
          as: 'creator',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
      ],
    });

    const requestComments = await RequestComment.findAll({
      where: { requestId },
      attributes: ['id', 'comment', 'createdAt', 'updatedAt'],
      include: [
        {
          model: User,
          as: 'user',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
      ],
    });

    const requestDocuments = await RequestDocument.findAll({
      where: { requestId },
      attributes: [
        'id',
        'fileName',
        'fileType',
        'fileSize',
        'filePath',
        'createdAt',
        'updatedAt',
      ],
      include: [
        {
          model: User,
          as: 'creator',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
        },
      ],
    });

    let workOrder;
    if (request.requestType === requestType.WORK_ORDER_REQUEST) {
      workOrder = await WorkOrder.findByPk(request.recordId, {
        attributes: {
          exclude: [
            'customDetails',
            'termsAndCondition',
            'activities',
            'projectId',
            'createdBy',
            'organizationId',
            'contractorId',
          ],
        },
        include: [
          {
            model: Project,
            as: 'project',
            required: false,
            attributes: ['id', 'name', 'logo'],
            include: [
              {
                model: Organization,
                as: 'organization',
                required: false,
                attributes: ['id', 'name', 'type'],
              },
            ],
          },
          {
            model: Contractor,
            as: 'contractor',
            required: false,
            attributes: ['id', 'logo', 'businessName', 'firstName', 'lastName'],
          },
        ],
      });
    }

    let quotation;
    if (request.requestType === requestType.QUOTATION_REQUEST) {
      quotation = await Quotation.findByPk(request.recordId, {
        attributes: {
          exclude: [
            'status',
            'saleAgentId',
            'brokerAdditionTerm',
            'termsAndCondition',
            'createdAt',
            'updatedAt',
            'unitId',
            'customerId',
            'projectId',
            'createdBy',
          ],
        },
        include: [
          {
            model: Project,
            as: 'project',
            required: false,
            attributes: ['id', 'name', 'logo'],
            include: [
              {
                model: Organization,
                as: 'organization',
                required: false,
                attributes: ['id', 'name', 'type'],
              },
            ],
          },
          {
            model: Unit,
            as: 'unit',
            required: false,
            attributes: ['id', 'name', 'description'],
          },
          {
            model: Customer,
            as: 'user',
            required: false,
            attributes: ['id', 'profilePicture', 'firstName', 'lastName'],
          },
        ],
      });
    }

    let wbs;
    if (request.requestType === requestType.WBS_REQUEST) {
      wbs = await WbsActivity.findByPk(request.recordId, {
        attributes: {
          exclude: [
            'status',
            'createdAt',
            'updatedAt',
            'parentWbsActivityId',
            'wbsCategoryId',
            'organizationId',
            'projectId',
            'accountId',
          ],
        },
        include: [
          {
            model: Project,
            as: 'project',
            required: false,
            attributes: ['id', 'name', 'logo'],
            include: [
              {
                model: Organization,
                as: 'organization',
                required: false,
                attributes: ['id', 'name', 'type'],
              },
            ],
          },
          {
            model: WbsActivity,
            as: 'parentWbsActivity',
            required: false,
            attributes: ['id', 'name', 'wbsCode', 'shortCode'],
          },
          {
            model: WbsActivity,
            as: 'wbsCategory',
            required: false,
            attributes: ['id', 'name', 'wbsCode', 'shortCode'],
          },
        ],
      });
    }

    let leave;
    if (request.requestType === requestType.LEAVE_REQUEST) {
      leave = await EmployeeLeave.findByPk(request.recordId);
    }

    let journal;
    if (request.requestType === requestType.JOURNAL_REQUEST) {
      journal = await Journal.findByPk(request.recordId, {
        attributes: {
          exclude: ['organizationId', 'createdBy'],
        },
        include: [
          {
            model: TransactionEntry,
            as: 'transactionEntries',
            required: false,
            attributes: {
              exclude: [
                'referenceId',
                'journalState',
                'status',
                'referenceType',
                'accountId',
                'createdBy',
              ],
            },
          },
        ],
      });
    }

    let indent;
    if (request.requestType === requestType.INDENT_MR_REQUEST) {
      journal = await Indent.findByPk(request.recordId, {
        attributes: {
          exclude: ['organizationId', 'createdBy'],
        },
        include: [
          {
            model: Warehouse,
            as: 'warehouse',
            required: false,
          },
        ],
      });
    }

    let stockAdjustment;
    if (request.requestType === requestType.STOCK_ADJUSTMENT_REQUEST) {
      stockAdjustment = await StockAdjustment.findByPk(request.recordId, {
        include: [
          {
            model: Warehouse,
            as: 'warehouse',
            required: false,
            attributes: ['id', 'name'],
          },
          {
            model: StockAdjustmentItem,
            as: 'stockAdjustmentItem',
            required: false,
          },
        ],
      });
    }

    const approvalWorkflowDetails =
      requestTypeActivityMapping[String(request.requestType).toUpperCase()];
    const existingApprovalWorkflow = await ApprovalWorkflow.findOne({
      where: {
        moduleName: approvalWorkflowDetails.moduleName,
        activityName: approvalWorkflowDetails.activityName,
        organizationId: request.organizationId,
      },
    });

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('Request'),
      data: {
        request,
        activities,
        requestComments,
        requestDocuments,
        workOrder,
        quotation,
        wbs,
        leave,
        journal,
        indent,
        stockAdjustment,
        approvalWorkflow: existingApprovalWorkflow,
      },
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while fetching the request details.',
    };
  }
};

exports.getRequestList = async (data, loggedInUser) => {
  try {
    const {
      status,
      priority,
      dueDate,
      requestedTo,
      requestedBy,
      search,
      limit = 10,
      start = 0,
    } = data;

    const filters = {
      ...(status && {
        status: { [Op.in]: Array.isArray(status) ? status : [status] },
      }),
      ...(priority && {
        priority: { [Op.in]: Array.isArray(priority) ? priority : [priority] },
      }),
      ...(requestedTo && {
        requestedTo: {
          [Op.in]: Array.isArray(requestedTo) ? requestedTo : [requestedTo],
        },
      }),
      ...(requestedBy && {
        requestedBy: {
          [Op.in]: Array.isArray(requestedBy) ? requestedBy : [requestedBy],
        },
      }),
    };

    if (dueDate) {
      const startOfDay = moment(dueDate).startOf('day').toDate();
      const endOfDay = moment(dueDate).endOf('day').toDate();

      filters.dueDate = {
        [Op.and]: {
          [Op.gte]: startOfDay,
          [Op.lt]: endOfDay,
        },
      };
    }

    const searchFilters = search
      ? { title: { [Op.iLike]: `%${search}%` } }
      : {};

    const includeConditions = [
      {
        model: User,
        as: 'receiver',
        attributes: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'profilePicture',
        ],
        include: [
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
          },
        ],
      },
      {
        model: User,
        as: 'approver',
        attributes: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'profilePicture',
        ],
        include: [
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
          },
        ],
      },
      {
        model: User,
        as: 'requester',
        attributes: [
          'id',
          'firstName',
          'middleName',
          'lastName',
          'profilePicture',
        ],
        include: [
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name'],
          },
        ],
      },
    ];

    const { rows, count } = await Request.findAndCountAll({
      attributes: {
        exclude: ['requestedBy', 'requestedTo', 'approvedBy'],
      },
      where: {
        ...filters,
        ...searchFilters,
        organizationId: loggedInUser.currentOrganizationId,
      },
      include: includeConditions,
      limit,
      offset: start,
      distinct: true,
      order: [['createdAt', 'DESC']],
    });

    return {
      success: true,
      message: 'Requests fetched successfully',
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'An error occurred while fetching requests',
    };
  }
};

exports.getRequestStatusCount = async (queryParams, loggedInUser) => {
  try {
    const { type = 'request', userId, organizationId } = queryParams;

    const data = {
      total: 0,
      status: {},
    };

    let queryOptions = {
      attributes: [
        ['status', 'status'],
        [
          Request.sequelize.fn('COUNT', Request.sequelize.col('Tasks.status')),
          'count',
        ],
      ],
      group: ['Tasks.status'],
    };

    if (type === 'task') {
      const statusMap = Object.fromEntries(
        options.taskStatus.getValues().map((status) => [status, 0])
      );

      queryOptions = {
        ...queryOptions,
        where: {},
        include: [
          {
            model: User,
            as: 'creator',
            attributes: [],
          },
        ],
      };

      if (userId) queryOptions.where.createdBy = userId;
      if (organizationId)
        queryOptions.where['$creator.organizationId$'] = organizationId;

      const taskStatusCounts = await Tasks.findAll(queryOptions);

      taskStatusCounts.forEach(({ dataValues }) => {
        statusMap[dataValues.status] = parseInt(dataValues.count, 10) || 0;
      });

      data.status = statusMap;
      data.total = Object.values(statusMap).reduce(
        (acc, count) => acc + count,
        0
      );
    } else if (type === 'request') {
      const statusMap = Object.fromEntries(
        options.requestStatus.getValues().map((status) => [status, 0])
      );

      queryOptions.attributes = [
        ['status', 'status'],
        [
          Request.sequelize.fn(
            'COUNT',
            Request.sequelize.col('Request.status')
          ),
          'count',
        ],
      ];
      queryOptions.group = ['Request.status'];
      queryOptions.where = {
        organizationId: loggedInUser.currentOrganizationId,
      };

      const requestStatusCounts = await Request.findAll(queryOptions);

      requestStatusCounts.forEach(({ dataValues }) => {
        statusMap[dataValues.status] = parseInt(dataValues.count, 10) || 0;
      });

      data.status = statusMap;
      data.total = Object.values(statusMap).reduce(
        (acc, count) => acc + count,
        0
      );
    } else {
      return {
        success: false,
        message: errorMessage.INVALID_REQUEST,
      };
    }

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('status-count'),
      data,
    };
  } catch (error) {
    throw error;
  }
};
