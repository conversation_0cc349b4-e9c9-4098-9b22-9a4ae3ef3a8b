'use strict';
const { templateType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('EmployeeTemplate', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    await queryInterface.addColumn('EmployeeTemplate', 'name', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeTemplate', 'templateType', {
      type: Sequelize.ENUM(templateType.getTemplateTypeArray()),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('EmployeeTemplate', 'organizationId');
    await queryInterface.removeColumn('EmployeeTemplate', 'name');
    await queryInterface.removeColumn('EmployeeTemplate', 'templateType');
  },
};
