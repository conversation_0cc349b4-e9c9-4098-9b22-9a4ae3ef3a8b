const formatTime = (time) => {
  if (typeof time === 'number' || /^\d+$/.test(time)) {
    const hours = String(time).padStart(2, '0');
    return `${hours}:00:00`;
  }
  return time;
};

const formatTotalDuration = (time) => {
  if (typeof time === 'number' || /^\d+(\.\d+)?$/.test(time)) {
    return parseFloat(time).toFixed(2);
  }
  return time;
};

module.exports = { formatTime, formatTotalDuration };
