'use strict';
const {
  contactPersonCategory,
  contactPersonType,
} = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ContactPerson', 'role');
    await queryInterface.removeColumn('ContactPerson', 'name');
    await queryInterface.removeColumn('ContactPerson', 'profilePicture');
    await queryInterface.removeColumn('ContactPerson', 'tags');

    await queryInterface.addColumn('ContactPerson', 'contactCategory', {
      type: Sequelize.ENUM(
        ...contactPersonCategory.getContactPersonCategoryArray()
      ),
      allowNull: false,
    });

    await queryInterface.addColumn('ContactPerson', 'logo', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'businessName', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'firstName', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'lastName', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'panNumber', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'gstNumber', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'contactType', {
      type: Sequelize.ENUM(...contactPersonType.getContactPersonTypeArray()),
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'about', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'details', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Address', 'contactPersonId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'ContactPerson',
        key: 'id',
      },
      onDelete: 'CASCADE',
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ContactPerson', 'contactCategory');
    await queryInterface.removeColumn('ContactPerson', 'logo');
    await queryInterface.removeColumn('ContactPerson', 'businessName');
    await queryInterface.removeColumn('ContactPerson', 'firstName');
    await queryInterface.removeColumn('ContactPerson', 'lastName');
    await queryInterface.removeColumn('ContactPerson', 'panNumber');
    await queryInterface.removeColumn('ContactPerson', 'gstNumber');
    await queryInterface.removeColumn('ContactPerson', 'contactType');
    await queryInterface.removeColumn('ContactPerson', 'about');
    await queryInterface.removeColumn('ContactPerson', 'details');

    await queryInterface.removeConstraint(
      'Address',
      'Address_contactPersonId_fkey'
    );
    await queryInterface.removeColumn('Address', 'contactPersonId');

    await queryInterface.addColumn('ContactPerson', 'role', {
      type: Sequelize.ENUM('primary', 'secondary'),
      allowNull: false,
    });

    await queryInterface.addColumn('ContactPerson', 'name', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'profilePicture', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('ContactPerson', 'tags', {
      type: Sequelize.ARRAY(Sequelize.STRING(255)),
      allowNull: true,
      defaultValue: [],
    });
  },
};
