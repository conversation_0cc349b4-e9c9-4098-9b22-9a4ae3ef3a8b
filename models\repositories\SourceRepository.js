const { Op } = require('sequelize');
const { Source, User, Customer } = require('..');
const { errorMessage, successMessage } = require('@config/options');

exports.checkExistence = async (model, query) => {
  try {
    return await model.findOne({ where: query });
  } catch (error) {
    throw new Error('Error checking existence');
  }
};

exports.validateAndCreateSource = async (data) => {
  const { name, parentSourceId, userId } = data;
  try {
    if (name) {
      const existingSource = await this.checkExistence(Source, {
        name: {
          [Op.iLike]: name,
        },
      });
      if (existingSource) {
        return {
          success: false,
          message: errorMessage.EXISTING_SOURCE(`name: ${name}`),
        };
      }
    }

    if (parentSourceId) {
      const parentSource = await this.checkExistence(Source, {
        id: parentSourceId,
      });
      if (!parentSource) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Parent Source'),
        };
      }
    }

    if (userId) {
      const user = await this.checkExistence(User, { id: userId });
      if (!user) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('User'),
        };
      }
    }

    const createdSource = await Source.create(data);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Source'),
      data: createdSource,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndUpdateSource = async (sourceId, data) => {
  const { name, parentSourceId, userId } = data;
  try {
    const source = await this.checkExistence(Source, { id: sourceId });
    if (!source) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Source with Id: ${sourceId}`),
      };
    }

    if (name) {
      const existingSource = await this.checkExistence(Source, {
        name: {
          [Op.iLike]: name,
        },
        id: { [Op.ne]: sourceId },
      });
      if (existingSource) {
        return {
          success: false,
          message: errorMessage.EXISTING_SOURCE(`name: ${data.name}`),
        };
      }
    }

    if (parentSourceId) {
      const parentSource = await this.checkExistence(Source, {
        id: parentSourceId,
      });
      if (!parentSource) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Parent Source'),
        };
      }
    }

    if (userId) {
      const user = await this.checkExistence(User, { id: userId });
      if (!user) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('User'),
        };
      }
    }

    Object.assign(source, data);
    const updatedSource = await source.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Source'),
      data: updatedSource,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndUpdateLeadSource = async (customerId, data) => {
  const { sourceId, subSourceId } = data;
  try {
    const customer = await this.checkExistence(Customer, { id: customerId });
    if (!customer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Customer with the given ID'),
      };
    }

    if (sourceId) {
      const source = await this.checkExistence(Source, { id: sourceId });
      if (!source) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Source with Id: ${sourceId}`),
        };
      }
    }

    if (subSourceId) {
      const source = await this.checkExistence(Source, { id: subSourceId });
      if (!source) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `Sub Source with Id: ${subSourceId}`
          ),
        };
      }
    }

    Object.assign(customer, data);
    const updatedCustomer = await customer.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Source'),
      data: updatedCustomer,
    };
  } catch (error) {
    throw new Error(error);
  }
};
