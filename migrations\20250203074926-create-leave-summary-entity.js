module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('UserLeaveSummary', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      maxAllowed: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      used: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      remaining: {
        type: Sequelize.INTEGER,
        allowNull: false,
        defaultValue: 0,
      },
      leaveType: {
        type: Sequelize.STRING,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('UserLeaveSummary');
  },
};
