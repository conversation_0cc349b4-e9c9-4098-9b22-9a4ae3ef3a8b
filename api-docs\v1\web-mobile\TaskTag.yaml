paths:
  /task/tag/custom-tag:
    post:
      summary: Creates a new Custom Tag for Task
      description: Creates a new Custom Tag for Task with their customname
      operationId: createCustomTag
      tags:
        - Task
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createCustomTag"
      responses:
        "201":
          description: Custom Tag created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/tag/custom-tag/{id}:
    put:
      summary: Update Existing Custom Tag
      description: Update a Existing Custom Tag With name and Type
      operationId: updateCustomTag
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of custom Tag
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateCustomTag"
      responses:
        "201":
          description: Custom Tag updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    
    delete:
      summary: Delete a Custom Tag
      description: Delete Custom Tag if you dont need it
      operationId: deleteCustomTag
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of custom Tag
      responses:
        "201":
          description: Custom Tag deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/tag:
    get:
      summary: Get Task Tags
      description: Get Task Tags based on type
      operationId: getTaskTag
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          required: false
          schema:
            type: string
          description: type of tag based on priority_tag,custom_tag,department_tag
        - name: organizationId
          in: query
          required: false
          schema:
            type: integer
          description: id of organization
      responses:
        "201":
          description: Get All Tags
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createCustomTag:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
          example: "Priority Tag"
          description: "The name of the custom tag for the task"
        type:
          type: string
          example: "custom_tag"
          description: "The type of the custom tag, which must be 'custom_tag'"
        organizationId:
          type: integer
          example: 1
          description: "The organization Id from organization Table"

    updateCustomTag:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
          example: "Priority Tag"
          description: "The name of the custom tag for the task"
        type:
          type: string
          example: "custom_tag"
          description: "The type of the custom tag, which must be 'custom_tag'"