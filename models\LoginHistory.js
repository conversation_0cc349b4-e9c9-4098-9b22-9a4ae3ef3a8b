const { loginType } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const LoginHistory = sequelize.define(
    'LoginHistory',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      loginType: {
        type: DataTypes.ENUM(loginType.loginTypeArray()),
        allowNull: false,
        defaultValue: loginType.CREDENTIALS_LOGIN,
      },
      ipAddress: {
        type: DataTypes.STRING(45),
        allowNull: true,
      },
      location: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      clientInfo: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  LoginHistory.associate = (models) => {
    LoginHistory.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    LoginHistory.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });
  };

  return LoginHistory;
};
