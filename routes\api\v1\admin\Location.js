const express = require('express');
const router = express.Router();
const LocationControl = require('../../../../controllers/api/v1/admin/Location');
const LocationSchema = require('../../../../schema-validation/admin/Location');
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.get('/:id', LocationControl.getLocationById);

router.get('/', LocationControl.getLocations);

router.post(
  '/',
  checkSchema(LocationSchema.createUpdateLocation),
  ErrorHandleHelper.requestValidator,
  LocationControl.createLocation
);

router.put(
  '/:id',
  checkSchema(LocationSchema.createUpdateLocation),
  ErrorHandleHelper.requestValidator,
  LocationControl.createUpdateLocation
);

module.exports = router;
