'use strict';

module.exports = (sequelize, DataTypes) => {
  const StockAdjustmentItem = sequelize.define(
    'StockAdjustmentItem',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      adjustedStock: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      adjustment: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      value: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  StockAdjustmentItem.associate = (models) => {
    StockAdjustmentItem.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    StockAdjustmentItem.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    StockAdjustmentItem.belongsTo(models.StockAdjustment, {
      foreignKey: 'stockAdjustmentId',
      as: 'stockAdjustment',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    StockAdjustmentItem.belongsTo(models.ItemVariant, {
      foreignKey: 'itemVariantId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return StockAdjustmentItem;
};
