const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const EmployeeControl = require('../../../controllers/api/v1/Employee');
const EmployeeSchema = require('../../../schema-validation/Employee');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(EmployeeSchema.createOrUpdateEmployee),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.addEmployee
);

router.put(
  '/:id',
  checkSchema(EmployeeSchema.createOrUpdateEmployee),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.putEmployee
);

router.patch(
  '/:id/status',
  checkSchema(EmployeeSchema.updateEmployeeStatus),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.changeEmployeeStatus
);

router.get('/:id/salary', EmployeeControl.getEmployeeSalary);

router.post(
  '/:id/salary',
  checkSchema(EmployeeSchema.createAndUpdateEmployeeSalary),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.addEmployeeSalary
);

router.put(
  '/:id/salary',
  checkSchema(EmployeeSchema.createAndUpdateEmployeeSalary),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.putEmployeeSalary
);

router.post(
  '/:id/leave',
  checkSchema(EmployeeSchema.createEmployeeLeavePolicy),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.addEmployeeLeavePolicy
);

router.post(
  '/:id/attendance',
  checkSchema(EmployeeSchema.createEmployeeAttendance),
  ErrorHandleHelper.requestValidator,
  EmployeeControl.addEmployeeAttendance
);

router.get('/:id/bank-details', EmployeeControl.getEmployeeBankDetails);

module.exports = router;
