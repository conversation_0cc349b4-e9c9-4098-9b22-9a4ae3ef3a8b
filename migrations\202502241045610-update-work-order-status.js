'use strict';
const { workOrderStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('WorkOrder');

    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_WorkOrder_status_new" AS ENUM (${workOrderStatus
        .getValues()
        .map((status) => `'${status}'`)
        .join(', ')});
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "WorkOrder" ALTER COLUMN "status" TYPE "enum_WorkOrder_status_new"
        USING status::text::"enum_WorkOrder_status_new";
      `);
    }

    await queryInterface.sequelize.query(`
      DROP TYPE "enum_WorkOrder_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_WorkOrder_status_new" RENAME TO "enum_WorkOrder_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" SET DEFAULT '${workOrderStatus.DRAFT}';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('WorkOrder');

    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      CREATE TYPE "enum_WorkOrder_status_old" AS ENUM ('draft');
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "WorkOrder" ALTER COLUMN "status" TYPE "enum_WorkOrder_status_old"
        USING status::text::"enum_WorkOrder_status_old";
      `);
    }

    await queryInterface.sequelize.query(`
      DROP TYPE "enum_WorkOrder_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_WorkOrder_status_old" RENAME TO "enum_WorkOrder_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "WorkOrder" ALTER COLUMN "status" SET DEFAULT 'draft';
    `);
  },
};
