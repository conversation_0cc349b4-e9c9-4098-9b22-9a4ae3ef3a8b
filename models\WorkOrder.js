const { workOrderStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const WorkOrder = sequelize.define(
    'WorkOrder',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      workOrderNumber: {
        type: DataTypes.STRING(50),
        allowNull: false,
        unique: true,
      },
      fromDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      toDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(workOrderStatus.getValues()),
        allowNull: false,
        defaultValue: workOrderStatus.DRAFT,
      },
      customDetails: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: null,
      },
      termsAndCondition: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: null,
      },
      activities: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: null,
      },
      workOrderValue: {
        type: DataTypes.FLOAT,
        allowNull: true,
        defaultValue: 0,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WorkOrder.associate = (models) => {
    WorkOrder.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    WorkOrder.belongsTo(models.WorkOrderType, {
      foreignKey: 'workOrderType',
      as: 'type',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    WorkOrder.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
    WorkOrder.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    WorkOrder.belongsTo(models.Contractor, {
      foreignKey: 'contractorId',
      as: 'contractor',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
    WorkOrder.hasMany(models.WorkOrderBOQMapping, {
      foreignKey: 'workOrderId',
      as: 'workOrderBOQMappings',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    WorkOrder.hasOne(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        requestType: 'work_order_request',
      },
      as: 'request',
    });
    WorkOrder.hasMany(models.ActivityLog, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        activityOn: 'WorkOrder',
      },
      as: 'activityLogs',
    });
  };

  return WorkOrder;
};
