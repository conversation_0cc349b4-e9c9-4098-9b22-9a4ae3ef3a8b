const OPTIONS = require('@config/options');

exports.createProfile = {
  countryCode: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Country code must be a string',
    },
  },
  mobileNumber: {
    in: ['body'],
    optional: true,
    isMobilePhone: {
      errorMessage: 'Invalid mobile number format',
    },
  },
  personalEmail: {
    in: ['body'],
    optional: true,
    isEmail: {
      errorMessage: 'Invalid personal email format',
    },
  },
  firstName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'First name must be a string',
    },
  },
  middleName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Middle name must be a string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Last name must be a string',
    },
  },
  gender: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [OPTIONS.gender.getGenderArray()],
      errorMessage: `Gender must be one of the following: ${OPTIONS.gender.getGenderArray().join(', ')}`,
    },
  },
  designationId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Designation ID must be an integer',
    },
  },
  dateOfBirth: {
    in: ['body'],
    optional: true,
    isDate: {
      errorMessage: 'Invalid date of birth format',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'About must be a string',
    },
  },
};

exports.updateProfile = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'User ID is required',
    },
    isInt: {
      errorMessage: 'User ID must be an integer',
    },
  },
  ...exports.createProfile,
};
