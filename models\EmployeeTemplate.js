'use strict';
const { templateType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const EmployeeTemplate = sequelize.define(
    'EmployeeTemplate',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      templateType: {
        type: DataTypes.ENUM(templateType.getTemplateTypeArray()),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  EmployeeTemplate.associate = function (models) {
    EmployeeTemplate.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });
    EmployeeTemplate.belongsTo(models.Employee, {
      foreignKey: 'employeeId',
      as: 'employee',
    });

    EmployeeTemplate.hasMany(models.EmployeeTemplateItem, {
      foreignKey: 'employeeTemplateId',
      as: 'employeeTemplateItems',
      onDelete: 'CASCADE',
    });
  };

  return EmployeeTemplate;
};
