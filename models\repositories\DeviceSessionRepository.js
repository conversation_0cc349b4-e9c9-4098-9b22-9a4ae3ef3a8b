const { DeviceSession, LoginHistory } = require('..');
const { errorMessage, successMessage } = require('@config/options');

exports.removeDeviceSessionById = async (id, loggedInUser) => {
  try {
    const session = await DeviceSession.findOne({
      where: { id, userId: loggedInUser.id },
    });

    if (!session) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Device session'),
      };
    }

    await session.destroy();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Device session'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getDeviceSessions = async ({ start, limit, loggedInUser }) => {
  try {
    const { count, rows } = await DeviceSession.findAndCountAll({
      attributes: {
        exclude: ['userId', 'sessionToken'],
      },
      where: { userId: loggedInUser.id },
      offset: start,
      limit: limit,
      order: [['createdAt', 'DESC']],
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Device sessions'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getLoginHistory = async ({ start, limit, loggedInUser }) => {
  try {
    const { count, rows } = await LoginHistory.findAndCountAll({
      attributes: {
        exclude: ['userId', 'organizationId'],
      },
      where: { userId: loggedInUser.id },
      offset: start,
      limit: limit,
      order: [['createdAt', 'DESC']],
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Login History'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};
