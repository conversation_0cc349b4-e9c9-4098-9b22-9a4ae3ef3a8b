exports.createModule = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Module name cannot be empty',
    isString: {
      errorMessage: 'Module name must be a string',
    },
    isLength: {
      options: { max: 255 },
      errorMessage: 'Module name must be at most 255 characters long',
    },
  },
  parentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'ParentId must be a valid integer',
    },
  },
};

exports.updateModule = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Module Id is required',
    },
    isInt: {
      errorMessage: 'Module Id must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    notEmpty: {
      errorMessage: 'Module name cannot be empty',
    },
    isString: {
      errorMessage: 'Module name must be a string',
    },
    isLength: {
      options: { max: 255 },
      errorMessage: 'Module name must be at most 255 characters long',
    },
  },
  parentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'ParentId must be a valid integer',
    },
  },
};

exports.deleteModule = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Module Id is required',
    },
    isInt: {
      errorMessage: 'Module Id must be a valid integer',
    },
  },
};

exports.getModules = {
  organizationId: {
    in: ['query'],
    notEmpty: {
      errorMessage: 'Organization Id is required',
    },
    isInt: {
      errorMessage: 'Organization Id must be a valid integer',
    },
  },
  offset: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'Offset must be a valid integer',
    },
  },
  limit: {
    in: ['query'],
    optional: true,
    isInt: {
      errorMessage: 'Limit must be a valid integer',
    },
  },
};
