const { DesignationMedia } = require('..');
const { successMessage } = require('../../config/options');

exports.createUploadedDocuments = async (userId, body) => {
  try {
    const documents = body.organizationMedia.map((doc) => ({
      ...doc,
      userId,
    }));

    await DesignationMedia.bulkCreate(documents);
    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('DesignationMedia'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
