exports.createAmenity = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
};

exports.updateAmenity = {
  name: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
  },
};
