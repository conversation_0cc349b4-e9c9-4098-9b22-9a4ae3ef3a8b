'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('CatalogueMedia', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      mediaType: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fileName: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fileType: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: true,
      },
      catalogueId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Catalogue',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('CatalogueMedia');
  },
};
