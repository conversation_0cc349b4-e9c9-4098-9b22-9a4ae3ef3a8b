'use strict';

const { defaultStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Quotation', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      unitId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Unit',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      customerId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Customer',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      projectId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Project',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      expireDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      pricingRevision: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      pricingRevisionTotalAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      paymentPlan: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      brokerPaymentPlan: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      paymentPlanTotalAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      paymentPlanAgreementAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      paymentPlanBalance: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      homeLoanRequired: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      totalBrokerageAmount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM(...defaultStatus.getDefaultStatusArray()),
        allowNull: false,
      },
      saleAgentId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      brokerAdditionTerm: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      termsAndCondition: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      holdPropertyUntilExpiry: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Quotation');
  },
};
