const { TaskDocuments, Tasks } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.addTaskAttachment = async (data, loggedInUser) => {
  try {
    const task = await Tasks.findByPk(data.taskId);
    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Task with ID: ' + data.taskId),
      };
    }

    const taskDocumentPayload = data.document.map((document) => ({
      taskId: data.taskId,
      createdBy: loggedInUser.id,
      ...document,
    }));

    const document = await TaskDocuments.bulkCreate(taskDocumentPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Attachment'),
      data: document,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteAttachment = async (documentId) => {
  try {
    const document = await TaskDocuments.findByPk(documentId);
    if (!document) {
      return {
        success: false,
        message: errorMessage.NO_USER('documentId'),
      };
    }

    await document.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Attachment'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
