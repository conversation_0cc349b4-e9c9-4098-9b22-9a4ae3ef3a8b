const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');
const HasuraControl = require('../../../controllers/api/v1/<PERSON>ura');
const QuotationControl = require('../../../controllers/api/v1/crm/Quotation');
const HasuraSchema = require('../../../schema-validation/Hasura');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');
const AuthHandler = require('../../../models/helpers/AuthHelper');

router.post(
  '/graphql',
  AuthHandler.authenticateJWT(),
  checkSchema(HasuraSchema.hasuraAuth),
  ErrorHandleHelper.requestValidator,
  HasuraControl.hasuraAuth
);

router.post('/quotation/handler', QuotationControl.checkAndMarkQuotationExpire);

module.exports = router;
