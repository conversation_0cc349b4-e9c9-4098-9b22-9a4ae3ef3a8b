// const sequelize = require('sequelize');
const {
  Document,
  Project,
  DocumentPermission,
  User,
  FavoritesDocument,
  sequelize,
} = require('..');
const db = require('..');
const {
  errorMessage,
  successMessage,
  defaultStatus,
  actionType,
} = require('../../config/options');
const DocumentPermissionRepository = require('./DocumentPermissionRepository');
const DocumentActivityLogRepository = require('./DocumentActivityLogRepository');
const FavoritesDocumentRepository = require('./FavoritesDocumentRepository');
const { predefinedProjectFolders } = require('../../config/defaultData');
const UserRepository = require('./UserRepository');
const { Op } = require('sequelize');
const archiver = require('archiver');
const { getS3ReadStream } = require('@models/helpers/AWSHelper');

exports.findOne = async (query) => await Document.findOne(query);

exports.findAll = async (query) => await Document.findAll(query);

exports.findAndCountAll = async (query) =>
  await Document.findAndCountAll(query);

exports.getBasePath = async (parentFolderId) => {
  let folderBasePath = null;

  if (parentFolderId) {
    const parentFolder = await Document.findOne({
      where: { id: parentFolderId, isFolder: true },
    });

    if (!parentFolder) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Parent Document'),
      };
    }
    folderBasePath = `${parentFolder.basePath ? parentFolder.basePath : ''}/${parentFolderId}`;
  }

  return {
    success: true,
    data: folderBasePath,
  };
};

exports.createDocument = async (data, loggedInUser) => {
  let { success, data: basePath } = await this.getBasePath(data.parentFolderId);

  if (!success) {
    return {
      success: false,
      message,
    };
  }

  const payload = {
    name: data.name ? data.name : data.fileName,
    description: data.description,
    projectId: data.projectId,
    parentFolderId: data.parentFolderId,
    isFolder: data.isFolder,
    basePath: basePath,
    status: defaultStatus.ACTIVE,
    createdBy: loggedInUser.id,
    organizationId: loggedInUser.currentOrganizationId,
    ...(data.isFolder === false && {
      fileType: data.fileType,
      fileSize: data.fileSize,
      filePath: data.filePath,
      fileName: data.fileName,
    }),
  };

  const document = await Document.create(payload);

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Document'),
    data: document,
  };
};

exports.storeDocument = async (data, loggedInUser) => {
  if (data.permission && data.permissions.length > 0) {
    const userIds = data?.permissions.map((permission) => permission.userId);

    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        id: userIds,
      },
      attributes: ['id', 'status'],
    };

    const existingUsers = await UserRepository.findAll(query);
    const existingUserIds = existingUsers.map((user) => user.id);

    for (const permission of data.permissions) {
      if (!existingUserIds.includes(permission.userId)) {
        return {
          success: false,
          message: errorMessage.NO_USER('ID ' + permission.userId),
        };
      }
    }
  }

  for (const doc of data.document) {
    const {
      success,
      message,
      data: document,
    } = await this.createDocument(doc, loggedInUser);

    if (!success) {
      return {
        success: false,
        message,
      };
    }

    if (data.permissions && data.permissions.length > 0) {
      await DocumentPermissionRepository.addBulkPermission(
        data.permissions,
        document.id,
        loggedInUser
      );
      await DocumentActivityLogRepository.addBulkDocumentActivityLogs(
        data.permissions,
        document.id,
        loggedInUser.id
      );
    }
  }
  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Document'),
  };
};

exports.getMyDocuments = async (data, loggerInUser) => {
  const {
    search = null,
    status = null,
    limit = 10,
    peopleIds = [],
    userId = data.userId ? data.userId : loggerInUser.id,
    projectIds = [],
    start = 0,
    isFavorites = null,
    isFolder = null,
    order = ['createdAt', 'DESC'],
  } = data;

  const query = {
    where: {
      organizationId: loggerInUser.currentOrganizationId,
      status: status
        ? { [Op.in]: Array.isArray(status) ? status : [status] }
        : { [Op.not]: [defaultStatus.ARCHIVED, defaultStatus.DELETED] },
      ...(search
        ? { [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }] }
        : { parentFolderId: data.parentFolderId ?? null }),
      ...(projectIds.length
        ? {
            projectId: {
              [Op.in]: Array.isArray(projectIds) ? projectIds : [projectIds],
            },
          }
        : {}),
      ...(isFolder ? { isFolder: isFolder } : {}),
    },
    include: [
      {
        model: DocumentPermission,
        as: 'permission',
        required: true,
        where: {
          userId: userId,
          [Op.or]: [{ canView: true }],
        },
        attributes: ['userId', 'canView', 'canEdit', 'canDelete'],
      },
      {
        model: DocumentPermission,
        as: 'permissionUser',
        required: true,
        where:
          peopleIds.length > 0
            ? {
                userId: {
                  [Op.in]: Array.isArray(peopleIds) ? peopleIds : [peopleIds],
                },
                canView: true,
              }
            : {},
        attributes: ['userId', 'canView', 'canEdit', 'canDelete'],
      },
      {
        model: User,
        as: 'creator',
        required: false,
        attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
      },
      {
        model: User,
        as: 'updater',
        required: false,
        attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
      },
      {
        model: FavoritesDocument,
        as: 'favorites',
        required: isFavorites,
        where: {
          userId: userId,
          ...(isFavorites ? { isFavorites: true } : {}),
        },
        attributes: ['isFavorites'],
      },
      {
        model: Project,
        as: 'project',
        required: false,
        attributes: ['id', 'name', 'organizationId'],
      },
    ],
    attributes: [
      'id',
      'name',
      'description',
      'parentFolderId',
      'organizationId',
      'isFolder',
      'basePath',
      'status',
      'fileName',
      'fileSize',
      'filePath',
      'fileType',
      'createdAt',
      'isDefault',
      'updatedAt',
      [
        sequelize.literal(`(
          SELECT json_agg(json_build_object(
            'id', u.id,
            'firstName', u."firstName",
            'lastName', u."lastName",
            'profilePicture', u."profilePicture"
          ))
          FROM "DocumentPermission" AS dp
          LEFT JOIN "User" AS u ON u.id = dp."userId"
          WHERE dp."documentId" = "Document"."id"
          AND dp."canView" = true
        )`),
        'followers',
      ],
      [
        sequelize.literal(`(
          SELECT COUNT(*) FROM "Document" as "sub"
          WHERE "sub"."parentFolderId" = "Document"."id"
        )`),
        'subDocumentCount',
      ],
    ],
    order: [order],
    limit,
    offset: start,
  };

  const { rows, count } = await this.findAndCountAll(query);

  for (const document of rows) {
    if (document.isFolder) {
      document.dataValues.folderSize = await this.calculateFolderSize(
        document.dataValues.id
      );
    }
  }

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Document'),
    data: {
      rows,
      pagination: {
        totalCount: count,
        start,
        limit,
      },
    },
  };
};

exports.updateDocument = async (id, data, loggedInUser) => {
  const query = { where: { id: id } };

  const existingDocument = await this.findOne(query);

  if (!existingDocument) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Document'),
    };
  }

  existingDocument.name = data.name || existingDocument.name;
  existingDocument.description = data.description;
  existingDocument.updatedBy = loggedInUser.id;

  await existingDocument.save();

  if (data.isFavorites !== undefined) {
    await FavoritesDocumentRepository.upsertFavoritesDocument(
      loggedInUser.id,
      existingDocument.id,
      data.isFavorites
    );
  }

  if (data.permissions && data.permissions.length > 0) {
    await this.updateBulkPermission(
      existingDocument.id,
      data.permissions,
      loggedInUser
    );
  }

  await DocumentActivityLogRepository.addActivityLog(
    existingDocument.isFolder ? actionType.EDIT_FOLDER : actionType.EDIT_FILE,
    existingDocument.id,
    loggedInUser.id
  );

  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE('Document'),
    data: existingDocument,
  };
};

exports.updateBulkPermission = async (
  documentId,
  permissions,
  loggedInUser
) => {
  const existingPermissions = await DocumentPermissionRepository.findAll({
    where: { documentId },
    attributes: ['id', 'userId', 'canView', 'canEdit', 'canDelete'],
    raw: true,
  });

  const existingPermissionsMap = new Map(
    existingPermissions.map((perm) => [perm.userId, perm])
  );

  const newPermissionsMap = new Map(
    permissions.map((perm) => [perm.userId, perm])
  );

  const permissionsToInsert = [];
  const permissionsToUpdate = [];
  const permissionsToRemove = [];

  for (const newPerm of permissions) {
    const existingPerm = existingPermissionsMap.get(newPerm.userId);
    if (!existingPerm) {
      permissionsToInsert.push(newPerm);
    } else if (
      existingPerm.canView !== newPerm.canView ||
      existingPerm.canEdit !== newPerm.canEdit ||
      existingPerm.canDelete !== newPerm.canDelete
    ) {
      permissionsToUpdate.push({
        id: existingPerm.id,
        userId: newPerm.userId,
        canView: newPerm.canView,
        canEdit: newPerm.canEdit,
        canDelete: newPerm.canDelete,
        updatedBy: loggedInUser.id,
      });
    }
  }

  for (const existingPerm of existingPermissions) {
    if (!newPermissionsMap.has(existingPerm.userId)) {
      permissionsToRemove.push(existingPerm.userId);
    }
  }

  if (permissionsToInsert.length > 0) {
    for (const perm of permissionsToInsert) {
      await DocumentPermissionRepository.addUserInDocumentPermission(
        documentId,
        perm,
        loggedInUser
      );
    }
  }

  if (permissionsToUpdate.length > 0) {
    for (const permission of permissionsToUpdate) {
      await DocumentPermissionRepository.updateUserDocumentPermission(
        documentId,
        permission,
        loggedInUser.id
      );
    }
  }

  if (permissionsToRemove.length > 0) {
    for (const userId of permissionsToRemove) {
      await DocumentPermissionRepository.removeUserFromDocumentPermission(
        documentId,
        userId,
        loggedInUser
      );
    }
  }

  return {
    success: true,
    message: 'Permissions updated successfully',
  };
};

exports.checkDocumentExist = async (documentId) => {
  const query = {
    where: { id: documentId },
  };

  const document = await this.findOne(query);

  if (!document) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Document'),
    };
  }

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Document'),
    data: document,
  };
};

exports.getPermissionByDocument = async (id) => {
  const { success: isDocumentExist, message: documentMessage } =
    await this.checkDocumentExist(id);

  if (!isDocumentExist) {
    return {
      success,
      documentMessage,
    };
  }

  const {
    success,
    message,
    data: permission,
  } = await DocumentPermissionRepository.getPermissionByDocument(id);

  return {
    success,
    message,
    data: permission,
  };
};

exports.addUserDocument = async (documentId, data, user) => {
  try {
    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        id: data.userId,
      },
      attributes: ['id', 'status'],
    };

    const existingUsers = await UserRepository.getUser(query);

    if (!existingUsers) {
      return {
        success: false,
        message: errorMessage.NO_USER('ID ' + data.userId),
      };
    }

    const { success: isDocumentExist, message: documentMessage } =
      await this.checkDocumentExist(documentId);

    if (!isDocumentExist) {
      return {
        success,
        documentMessage,
      };
    }

    return await DocumentPermissionRepository.addUserInDocumentPermission(
      documentId,
      data,
      user
    );
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateUserDocument = async (documentId, data, user) => {
  try {
    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        id: data.userId,
      },
      attributes: ['id', 'status'],
    };

    const existingUsers = await UserRepository.getUser(query);

    if (!existingUsers) {
      return {
        success: false,
        message: errorMessage.NO_USER('ID ' + data.userId),
      };
    }

    const { success: isDocumentExist, message: documentMessage } =
      await this.checkDocumentExist(documentId);

    if (!isDocumentExist) {
      return {
        success,
        documentMessage,
      };
    }

    return await DocumentPermissionRepository.updateUserDocumentPermission(
      documentId,
      data,
      user.id
    );
  } catch (error) {
    throw new Error(error);
  }
};

exports.removeUserFromDocument = async (documentId, userId, user) => {
  try {
    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        id: userId,
      },
      attributes: ['id', 'status'],
    };

    const existingUsers = await UserRepository.getUser(query);

    if (!existingUsers) {
      return {
        success: false,
        message: errorMessage.NO_USER('ID ' + data.userId),
      };
    }

    const { success: isDocumentExist, message: documentMessage } =
      await this.checkDocumentExist(documentId);

    if (!isDocumentExist) {
      return {
        success,
        documentMessage,
      };
    }

    return await DocumentPermissionRepository.removeUserFromDocumentPermission(
      documentId,
      userId,
      user
    );
  } catch (error) {
    throw new Error(error);
  }
};

exports.getDocumentActivity = async (documentId, data) => {
  try {
    const { limit = 10, start = 0 } = data;

    const { success: isDocumentExist, message: documentMessage } =
      await this.checkDocumentExist(documentId);

    if (!isDocumentExist) {
      return {
        success,
        documentMessage,
      };
    }
    const query = {
      where: {
        documentId: documentId,
      },
      attributes: [
        'id',
        'documentId',
        'actionType',
        'performedBy',
        'performedOn',
        'createdAt',
        'updatedAt',
      ],
      include: [
        {
          model: Document,
          as: 'document',
          required: false,
          attributes: [
            'id',
            'name',
            'fileName',
            'fileType',
            'fileSize',
            'filePath',
          ],
        },
        {
          model: User,
          as: 'performer',
          required: false,
          attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
        },
        {
          model: User,
          as: 'target',
          required: false,
          attributes: ['id', 'firstName', 'lastName', 'profilePicture'],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit,
      offset: start,
    };

    const { rows, count } =
      await DocumentActivityLogRepository.findAndCountAll(query);

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Document Activity'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.calculateFolderSize = async (documentId) => {
  const [results] = await db.sequelize.query(
    `
    WITH RECURSIVE folder_structure AS (
      SELECT id, "parentFolderId", "fileSize", "isFolder"
      FROM "Document"
      WHERE id = :documentId

      UNION ALL

      SELECT d.id, d."parentFolderId", d."fileSize", d."isFolder"
      FROM "Document" d
      JOIN folder_structure fs ON d."parentFolderId" = fs.id
    )
    SELECT COALESCE(SUM(fs."fileSize"), 0) AS size
    FROM folder_structure fs
    WHERE fs."isFolder" = false
  `,
    {
      replacements: { documentId: documentId },
      type: sequelize.QueryTypes.SELECT,
    }
  );
  return results.size;
};

exports.createProjectFolder = async (project, loggedInUser, parentProject) => {
  let parentProjectDocument = 10;
  if (parentProject) {
    parentProjectDocument = await Document.findOne({
      where: {
        projectId: parentProject.id,
        parentFolderId: null,
        isFolder: true,
      },
    });
  }

  const payload = await predefinedProjectFolders(
    project.id,
    project.name,
    loggedInUser.id,
    loggedInUser.currentOrganizationId,
    parentProjectDocument?.id
  );
  const depth = await calculateMaxDepth(payload);
  const include = await recursiveIncludeDocument(depth);

  return await Document.bulkCreate(payload, {
    include: include,
  });
};

function recursiveIncludeDocument(depth) {
  if (depth <= 0) {
    return [];
  }

  const includeStructure = {
    model: Document,
    as: 'subDocuments',
    include: [],
  };
  if (depth > 1) {
    includeStructure.include = recursiveIncludeDocument(depth - 1);
  }

  if (depth === 1) {
    delete includeStructure.include;
  }
  return [includeStructure];
}
function calculateMaxDepth(documents) {
  if (!documents || documents.length === 0) {
    return 0;
  }
  let maxDepth = 1;

  documents.forEach((doc) => {
    if (doc.subDocuments && doc.subDocuments.length > 0) {
      maxDepth = Math.max(maxDepth, 1 + calculateMaxDepth(doc.subDocuments));
    }
  });

  return maxDepth;
}

exports.downloadFolderDocument = async (req, res) => {
  try {
    const { documentId } = req.query;

    const documentDetails = await Document.findOne({
      where: { id: documentId },
      attributes: ['id', 'name', 'basePath', 'isFolder'],
    });

    if (!documentDetails) {
      return {
        success: false,
        message: 'Document not found',
        data: null,
      };
    }

    const files = await getAllFilesInFolder(documentId);

    // if (!files || files.length === 0) {
    //   return {
    //     success: false,
    //     message: 'No files found in the folder',
    //     data: null,
    //   };
    // }

    const zipFileName = `folder_${documentDetails.name}.zip`;

    res.setHeader('Content-Disposition', `attachment; filename=${zipFileName}`);
    res.setHeader('Content-Type', 'application/zip');

    const archive = archiver('zip', { zlib: { level: 9 } });
    archive.pipe(res);

    for (const file of files) {
      try {
        const s3Stream = await getS3ReadStream(file.filePath);
        const filePathInZip = file.folderPath + '/' + file.name;
        archive.append(s3Stream, { name: filePathInZip });
      } catch (fileError) {
        console.error(`Error reading file ${file.filePath}:`, fileError);
      }
    }

    archive.finalize();

    archive.on('end', () => console.log('ZIP file created successfully'));

    archive.on('error', (err) => {
      console.error('ZIP Archive Error:', err);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: errorMessage.DOWNLOAD_PROCESS_FAILED('ZIP creation'),
        });
      }
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Download Folder'),
      data: { zipFileName },
    };
  } catch (error) {
    console.log(error);
    throw new Error(error);
  }
};
const getAllFilesInFolder = async (folderId) => {
  const query = `
    WITH RECURSIVE "folder_hierarchy" AS (
        SELECT "id", "name", "isFolder", "parentFolderId", "filePath", "fileSize", "basePath",
              "name"::TEXT AS "folderPath"  
        FROM "Document"
        WHERE "id" = :folderId

        UNION ALL

        SELECT d."id", d."name", d."isFolder", d."parentFolderId", d."filePath", d."fileSize", d."basePath",
              (fh."folderPath" || '/' || d."name")::TEXT AS "folderPath" 
        FROM "Document" d
        INNER JOIN "folder_hierarchy" fh ON d."parentFolderId" = fh."id"
    )
    SELECT * FROM "folder_hierarchy" WHERE "isFolder" = FALSE;
`;

  const files = await sequelize.query(query, {
    replacements: { folderId },
    type: sequelize.QueryTypes.SELECT,
  });

  console.log(files);
  return files;
};

exports.getDocumentDefaultDocument = async (projectId, folderName) => {
  const document = await Document.findOne({
    where: {
      projectId: projectId,
      name: folderName,
      isDefault: true,
    },
    attributes: ['id', 'name', 'fileName', 'fileType', 'fileSize', 'filePath'],
  });

  if (!document) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Document'),
    };
  }

  return document;
};
