'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('IndentItem', 'itemVariantId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'ItemVariant',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('IndentItem', 'itemVariantId');
  },
};
