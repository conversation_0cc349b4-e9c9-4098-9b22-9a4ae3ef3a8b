const { FavoritesDocument } = require('..');

const { successMessage } = require('../../config/options');
exports.findAll = async (query) => await FavoritesDocument.findAll(query);
exports.findOne = async (query) => await FavoritesDocument.findOne(query);

exports.upsertFavoritesDocument = async (userId, documentId, isFavorites) => {
  const query = {
    where: {
      userId: userId,
      documentId: documentId,
    },
  };
  const existingFavorite = await this.findOne(query);

  if (existingFavorite) {
    existingFavorite.isFavorites = isFavorites;
    await existingFavorite.save();
  } else {
    const data = {
      userId: userId,
      documentId: documentId,
      isFavorites: isFavorites,
    };
    await FavoritesDocument.create(data);
  }

  return {
    success: true,
    message: successMessage.UPDATE_SUCCESS_MESSAGE('Favorite Document'),
  };
};
