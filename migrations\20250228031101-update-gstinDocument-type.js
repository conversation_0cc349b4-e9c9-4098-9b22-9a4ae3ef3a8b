'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('Organization', 'gstinDocument', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('Organization', 'gstinDocument', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });
  },
};
