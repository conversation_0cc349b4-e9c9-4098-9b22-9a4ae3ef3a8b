paths:
  /project/boq/metric:
    post:
      summary: Creates a new BOQ Metric
      description: Creates a new BOQ Metric with the provided metric name
      operationId: createBOQMetric
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createBOQMetric"
      responses:
        "201":
          description: BOQ Metric created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/{id}/boq/category:
    post:
      summary: Creates a new BOQ category with associated subcategories
      description: Creates a new BOQ category along with the provided subcategories for BOQ items.
      operationId: createBOQCategory
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of Project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createBOQCategory"
      responses:
        "201":
          description: BOQ Category created successfully along with subcategories
        "400":
          description: Invalid request or validation failed
        "500":
          description: Internal Server Error

  /project/boq/category/{id}:
    put:
      summary: Updates an existing BOQ category
      description: Updates the details of a BOQ category, including the name, description, associated metric ID, and subcategories.
      operationId: updateBOQCategory
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the BOQ category to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateBOQCategory"
      responses:
        "200":
          description: BOQ Category updated successfully
        "400":
          description: Invalid request
        "404":
          description: BOQ Category not found
        "500":
          description: Internal Server Error

  /project/{id}/boq/item:
    post:
      summary: Adds a new BOQ Item
      description: Adds a new BOQ Item with details including name, category, subcategory, metric, cost, and calculations.
      operationId: addBOQ
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of Project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createBOQItem"
      responses:
        "201":
          description: BOQ Item added successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/boq/item/{id}:
    put:
      summary: Updates an existing BOQ item
      description: Updates the details of an existing BOQ item with the provided fields such as name, category, subcategory, dimensions, and costs.
      operationId: updateBOQItem
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the BOQ item to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateBOQItem"
      responses:
        "200":
          description: BOQ item updated successfully
        "400":
          description: Invalid request
        "404":
          description: BOQ item not found
        "500":
          description: Internal Server Error

    delete:
      summary: Deletes an existing BOQ item
      description: Deletes the BOQ item with the specified ID if it exists and the user is authorized to delete it.
      operationId: deleteBOQItem
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the BOQ item to delete
      responses:
        "200":
          description: BOQ item deleted successfully
        "400":
          description: Invalid request
        "404":
          description: BOQ item not found
        "403":
          description: Unauthorized access
        "500":
          description: Internal Server Error

  /project/boq/items:
    get:
      summary: Lists BOQ Items for a specific project
      description: Fetches all BOQ Items associated with the specified project, organized by categories and subcategories.
      operationId: listBOQItems
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          required: false
          description: start for the page
          schema:
            type: integer
            example: 1
        - name: limit
          in: query
          required: false
          description: limit for the page
          schema:
            type: integer
            example: 1
        - name: projectId
          in: query
          required: false
          description: ID of the project to fetch BOQ Items for
          schema:
            type: integer
            example: 1
        - name: organizationId
          in: query
          required: false
          description: ID of the Organization to fetch BOQ Items for
          schema:
            type: integer
            example: 1
        - name: status
          in: query
          required: false
          description: Filter BOQ Items by their status (e.g., un_assigned)
          schema:
            type: string
            example: un_assigned
        - name: categoryId
          in: query
          required: false
          description: Filter Categories
          schema:
            type: integer
            example: 1
        - name: subCategoryId
          in: query
          required: false
          description: Filter subCategories
          schema:
            type: integer
            example: 1
        - name: categoryName
          in: query
          required: false
          description: search based on categoryname
          schema:
            type: string
        - name: subCategoryName
          in: query
          required: false
          description: search based on subCategoryName
          schema:
            type: string
        - name: boqName
          in: query
          required: false
          description: search based on boqName
          schema:
            type: string
        - name: boqDescription
          in: query
          required: false
          description: search based on boqDescription
          schema:
            type: string
        - name: listType
          in: query
          required: false
          description: based on listType
          schema:
            type: string
      responses:
        "200":
          description: BOQ Items fetched successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/{id}/boq/sub-category:
    post:
      summary: Creates a new BOQ sub-category with associated costitems
      description: Creates a new BOQ sub-category along with the provided subcategories for BOQ items.
      operationId: createBOQSubCategory
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of Project
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createBOQSubCategory"
      responses:
        "201":
          description: BOQ Category created successfully along with subcategories
        "400":
          description: Invalid request or validation failed
        "500":
          description: Internal Server Error

  /project/boq/item/details/{id}:
    get:
      summary: Get Boq Item based on Id
      description: get Boq Item Based on Id with all Calculations
      operationId: getBoqItemById
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of Boq
      responses:
        "201":
          description: Get Boq Item Details successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/boq/item/{id}/soft-delete:
    delete:
      summary: Deletes an existing BOQ item(Soft Delete)
      description: Deletes the BOQ item with the specified ID and update it to deleted.
      operationId: deleteBOQItemSoftDelete
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the BOQ item to delete
      responses:
        "200":
          description: BOQ item deleted successfully
        "400":
          description: Invalid request
        "404":
          description: BOQ item not found
        "403":
          description: Unauthorized access
        "500":
          description: Internal Server Error

  /project/boq/category/details/{id}:
    get:
      summary: Get Boq Category based on Id
      description: get Boq Category Based on Id with all Calculations
      operationId: getBoqCategoryById
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of Category
      responses:
        "201":
          description: Get Boq Category Details successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/boq/sub-category/details/{id}:
    get:
      summary: Get Boq SubCategory based on Id
      description: get Boq SubCategory Based on Id with all Calculations
      operationId: getBoqSubCategoryById
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of SubCategory
      responses:
        "201":
          description: Get Boq SubCategory Details successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/boq/sub-category/{id}:
    patch:
      summary: Update existing BOQ sub-category with associated costitems
      description: Update existing BOQ sub-category along with the provided subcategories for BOQ items.
      operationId: updateBOQSubCategory
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of BoqSubCategory
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateBOQSubCategory"
      responses:
        "201":
          description: BOQ Category updated successfully along with cost details
        "400":
          description: Invalid request or validation failed
        "500":
          description: Internal Server Error

  /project/boq/category/{id}/soft-delete:
    delete:
      summary: Deletes an existing BOQ Category(Soft Delete)
      description: Deletes the BOQ Category with the specified ID and update it to deleted.
      operationId: deleteBOQCategorySoftDelete
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the BOQ Category to delete
      responses:
        "200":
          description: BOQ category deleted successfully
        "400":
          description: Invalid request
        "404":
          description: BOQ category not found
        "403":
          description: Unauthorized access
        "500":
          description: Internal Server Error

  /project/boq/sub-category/{id}/soft-delete:
    delete:
      summary: Deletes an existing BOQ SubCategory(Soft Delete)
      description: Deletes the BOQ SubCategory with the specified ID and update it to deleted.
      operationId: deleteBOQSubCategorySoftDelete
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the BOQ Sub Category to delete
      responses:
        "200":
          description: BOQ sub category deleted successfully
        "400":
          description: Invalid request
        "404":
          description: BOQ sub category not found
        "403":
          description: Unauthorized access
        "500":
          description: Internal Server Error

  /project/boq/status-count:
    get:
      tags:
        - "Project"
      summary: "Get Boq Status Count"
      description: "This endpoint returns the count of boq records grouped by their status."
      operationId: "GetBoqStatusCount"
      security:
        - bearerAuth: []
      parameters:
        - name: projectId
          in: query
          required: false
          description: ID of the project to fetch status count
          schema:
            type: integer
            example: 1
        - name: organizationId
          in: query
          required: false
          description: ID of the Organization to fetch status count
          schema:
            type: integer
            example: 1
      responses:
        "200":
          description: "Successfully fetched the boq status count."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /project/boq/category/status-count:
    get:
      tags:
        - "Project"
      summary: "Get Boq Category Status Count"
      description: "This endpoint returns the count of boq category records grouped by their status."
      operationId: "GetBoqCategoryStatusCount"
      security:
        - bearerAuth: []
      parameters:
        - name: projectId
          in: query
          required: false
          description: ID of the project to fetch status count
          schema:
            type: integer
            example: 1
        - name: organizationId
          in: query
          required: false
          description: ID of the Organization to fetch status count
          schema:
            type: integer
            example: 1
      responses:
        "200":
          description: "Successfully fetched the boq category status count."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /project/boq/item/{id}/status-update:
    patch:
      tags:
        - "Project"
      summary: "Update Boq Status"
      description: "This endpoint update boq status."
      operationId: "UpdateBoqStatus"
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the BOQ item to update status
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateBOQStatus"
      responses:
        "200":
          description: "Successfully Updated Boq Status"
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

components:
  schemas:
    createBOQMetric:
      type: object
      properties:
        metricValue:
          type: string
          example: m2
          description: The short value representation of the unit (e.g., m2 for square meters, m3 for cubic meters)
        label:
          type: string
          example: Square Meter
          description: The full label for the BOQ Metric (e.g., Square Meter, Cubic Meter)
        metricType:
          type: string
          example: volume
          description: The full type for the BOQ Metric (e.g., volume , area , length)
      required:
        - value
        - label
    createBOQCategory:
      type: object
      required:
        - name
        - description
        - boqMetricId
      properties:
        name:
          type: string
          description: The name of the BOQ category (e.g., Raft Footing, Brickwork)
          example: "Brickwork"
        description:
          type: string
          description: A brief description of the BOQ category
          example: "Category for brickwork-related items"
        boqMetricId:
          type: integer
          description: The ID of the related BOQ metric
          example: 1
    updateBOQCategory:
      type: object
      properties:
        name:
          type: string
          description: The name of the BOQ category.
          example: "Brickwork"
        description:
          type: string
          description: A brief description of the BOQ category.
          example: "Category for brickwork-related items"
        boqMetricId:
          type: integer
          description: The ID of the associated BOQ metric.
          example: 1
    createBOQItem:
      type: object
      properties:
        name:
          type: string
          description: The name of the BOQ Item
          example: "Brickwork Construction"
        description:
          type: string
          description: The description of the BOQ Item
          example: "Brickwork Construction description" 
        boqCategoryId:
          type: integer
          description: The ID of the associated BOQ category
          example: 1
        boqSubCategoryId:
          type: integer
          description: The ID of the associated BOQ subcategory
          example: 5
        boqMetricId:
          type: integer
          description: The ID of the associated BOQ metric
          example: 3
        length:
          type: number
          format: float
          description: The length of the BOQ item
          example: 10.5
        breadth:
          type: number
          format: float
          description: The breadth of the BOQ item
          example: 5.0
        quantity:
          type: integer
          description: The quantity of boq item
          example: 5
        height:
          type: number
          format: float
          description: The height of the BOQ item
          example: 3.0
        total:
          type: number
          format: float
          description: The total amount calculated for the BOQ item
          example: 15750.25
        categoryRate:
          type: boolean
          description: take the value as true or false
          example: false
        progressPercentage:
          type: number
          description: The progress percentage of boq Item
          example: 20
        cost:
          type: array
          items:
            type: object
            required:
              - name
              - type
              - rate  
            properties:
              name:
                type: string
                description: The name of the cost item
                example: "Construction Cost"
              type:
                type: string
                description: The type of cost (e.g., 'Material', 'Labor')
                example: "Material"
              rate:
                type: integer
                description: The price for the cost
                example: 20
              unit:
                type: string
                description: The unit of cost (e.g., 'm', 'kg')
                example: "m"
              estimatedCost:
                type: integer
                description: The cost
                example: 56
    updateBOQItem:
      type: object
      properties:
        name:
          type: string
          description: Name of the BOQ item
          example: "Reinforced Concrete Work"
        description:
          type: string
          description: Description of the BOQ item
          nullable: true
          example: "Updated details for reinforced concrete work"
        boqCategory:
          type: integer
          description: ID of the BOQ category
          example: 2
        boqSubCategoryId:
          type: integer
          description: ID of the BOQ subcategory
          example: 6
        boqMetricId:
          type: integer
          description: ID of the BOQ metric
          example: 4
        length:
          type: number
          format: float
          description: Length of the BOQ item (nullable)
          nullable: true
          example: 12.5
        breadth:
          type: number
          format: float
          description: Breadth of the BOQ item (nullable)
          nullable: true
          example: 12.5
        height:
          type: number
          format: float
          description: Height of the BOQ item (nullable)
          nullable: true
          example: 4.0
        quantity:
          type: integer
          description: The quantity of boq item
          example: 5
        total:
          type: number
          format: float
          description: Total calculated quantity or area
          example: 20000.00
        calculationType:
          type: string
          enum:
            - metric
            - percentage
          description: Calculation type (either 'metric' or 'percentage')
          example: "percentage"
        cost:
          type: array
          items:
            type: object
            required:
              - name
              - type
              - rate  
            properties:
              name:
                type: string
                description: The name of the cost item
                example: "Construction Cost"
              type:
                type: string
                description: The type of cost (e.g., 'Material', 'Labor')
                example: "Material"
              rate:
                type: integer
                description: The price for the cost
                example: 20
              unit:
                type: string
                description: The unit of cost (e.g., 'm', 'kg')
                example: "m"

    createBOQSubCategory:
      type: object
      required:
        - name
        - parentCategoryId
      properties:
        name:
          type: string
          description: The name of the BOQ sub-category
          example: "Brickwork"
        parentCategoryId:
          type: integer
          description: The id of Category
          example: 1
        cost:
          type: array
          items:
            type: object
            required:
              - name
              - type
              - rate  
            properties:
              name:
                type: string
                description: The name of the cost item
                example: "Construction Cost"
              type:
                type: string
                description: The type of cost (e.g., 'Material', 'Labor')
                example: "Material"
              rate:
                type: integer
                description: The price for the cost
                example: 20
              unit:
                type: string
                description: The unit of cost (e.g., 'm', 'kg')
                example: "m"

    updateBOQSubCategory:
      type: object
      required:
        - name
        - parentCategoryId
      properties:
        name:
          type: string
          description: The name of the BOQ sub-category
          example: "Brickwork"
        parentCategoryId:
          type: integer
          description: The id of Category
          example: 1
        cost:
          type: array
          items:
            type: object
            required:
              - name
              - type
              - rate  
            properties:
              name:
                type: string
                description: The name of the cost item
                example: "Construction Cost"
              type:
                type: string
                description: The type of cost (e.g., 'Material', 'Labor')
                example: "Material"
              rate:
                type: integer
                description: The price for the cost
                example: 20
              unit:
                type: string
                description: The unit of cost (e.g., 'm', 'kg')
                example: "m"

    updateBOQStatus:
      type: object
      properties:
        status:
          type: string
          description: The status of Boq
          enum:
            - un_assigned
            - contracted
            - qualified
            - ongoing
            - completed

    