const jwt = require('jsonwebtoken');
const sequelize = require('sequelize');
const {
  User,
  <PERSON><PERSON><PERSON>ession,
  Module,
  DesignationModulePermission,
  UserOrganization,
} = require('..');
const {
  usersRoles,
  resCode,
  genRes,
  errorTypes,
  defaultStatus,
  errorMessage,
  successMessage,
} = require('../../config/options');

const { Op } = sequelize;

const hasRole = (user, roles) => {
  if (roles && roles.length) {
    return [usersRoles.SUPER_ADMIN].includes(user.role)
      ? true
      : roles.indexOf(user.role) > -1;
  }
  return false;
};

const verifyJwt = async (token, roles, force, req) => {
  const secretOrKey = process.env.JWT_SECRET_KEY;
  return await jwt.verify(token, secretOrKey, async (err, jwtPayload) => {
    if (err) {
      return {
        status: resCode.HTTP_UNAUTHORIZED,
        errorMessage: errorMessage.UNAUTHORIZED_ACCESS,
        errorType: errorTypes.UNAUTHORIZED_ACCESS,
      };
    }

    const userId =
      jwtPayload['https://hasura.io/jwt/claims']['x-hasura-user-id'];

    if (jwtPayload && userId) {
      const existingUser = await User.findOne({
        where: {
          id: userId,
          status: { [Op.notIn]: [defaultStatus.DELETED] },
        },
        attributes: {
          exclude: [
            'tempOtp',
            'tempOtpExpiresAt',
            'lastSignInAt',
            'currentSignInIpAddress',
            'referralCode',
            'createdAt',
            'updatedAt',
          ],
        },
      });

      if (
        existingUser &&
        ![
          defaultStatus.ACTIVE,
          defaultStatus.PENDING,
          defaultStatus.REJECTED,
        ].includes(existingUser.status)
      ) {
        return {
          status: resCode.HTTP_UNAUTHORIZED,
          errorMessage: errorMessage.USER_ACCOUNT_BLOCKED,
          errorType: errorTypes.ACCOUNT_BLOCKED,
        };
      }

      if (existingUser && hasRole(existingUser, roles)) {
        const { role } = existingUser;
        if (role === usersRoles.SUPER_ADMIN || role === usersRoles.ADMIN) {
          return { status: resCode.HTTP_OK, user: existingUser };
        }

        const orgCheckResult = await checkUserOrganizationPermission(
          existingUser,
          req
        );

        if (orgCheckResult.status !== resCode.HTTP_OK) {
          return orgCheckResult;
        }

        const deviceSession = await DeviceSession.findOne({
          where: { sessionToken: token },
        });

        if (!deviceSession) {
          return {
            status: resCode.HTTP_UNAUTHORIZED,
            errorMessage: errorMessage.UNAUTHORIZED_ACCESS,
            errorType: errorTypes.UNAUTHORIZED_ACCESS,
          };
        }

        return { status: resCode.HTTP_OK, user: existingUser };
      }

      return {
        status: resCode.HTTP_UNAUTHORIZED,
        errorMessage: errorMessage.UNAUTHORIZED_ACCESS,
        errorType: errorTypes.UNAUTHORIZED_ACCESS,
      };
    }

    if (!force) {
      return { status: resCode.HTTP_OK };
    }

    return {
      status: resCode.HTTP_FORBIDDEN,
      errorMessage: errorMessage.FORBIDDEN,
      errorType: errorTypes.FORBIDDEN,
    };
  });
};

const checkUserOrganizationPermission = async (existingUser, req) => {
  const currentOrganizationId = existingUser.currentOrganizationId;
  if (existingUser.role === usersRoles.BUILDER) {
    return { status: resCode.HTTP_OK };
  }

  if (currentOrganizationId) {
    const userOrganization = await UserOrganization.findOne({
      where: {
        userId: existingUser.id,
        organizationId: currentOrganizationId,
      },
    });

    if (!userOrganization) {
      return {
        status: resCode.HTTP_UNAUTHORIZED,
        errorMessage: errorMessage.PERMISSION_DENIED,
        errorType: errorTypes.ACCESS_DENIED_EXCEPTION,
      };
    }

    const modulePermissions = await DesignationModulePermission.findAll({
      where: {
        designationId: userOrganization.designationId,
        organizationId: currentOrganizationId,
      },
      include: [
        {
          model: Module,
          as: 'module',
          attributes: ['id', 'name'],
        },
      ],
      order: [['id', 'ASC']],
    });

    if (!modulePermissions) {
      return {
        status: resCode.HTTP_UNAUTHORIZED,
        errorMessage: errorMessage.PERMISSION_DENIED,
        errorType: errorTypes.ACCESS_DENIED_EXCEPTION,
      };
    }

    const modules = modulePermissions.map((permission) => ({
      id: permission.id,
      canEdit: permission.canEdit,
      canCreate: permission.canCreate,
      isEnabled: permission.isEnabled,
      isAssigned: permission.isAssigned,
      module: {
        id: permission.module.id,
        name: permission.module.name,
      },
    }));

    const validatePermissions = await checkPermissions(modules, req);
    if (validatePermissions != true) {
      return {
        status: resCode.HTTP_UNAUTHORIZED,
        errorMessage: errorMessage.PERMISSION_DENIED,
        errorType: errorTypes.ACCESS_DENIED_EXCEPTION,
      };
    }
  }

  return { status: resCode.HTTP_OK };
};

const checkPermissions = async (modules, req) => {
  const routePath = req.originalUrl.split('?')[0].split('/');
  const paths = routePath.filter((path) => path !== '');
  const { method } = req;
  try {
    const excludedPaths = [
      'user',
      'graphql',
      'checkin',
      'checkout',
      'attendance',
      'activitylog',
      'status-count',
      'shared',
      'workspace',
      'brand',
    ];
    if (excludedPaths.some((path) => paths.includes(path))) {
      return true;
    }

    const mergedPermissions = [...modules];
    const hasCommonPermissions = paths.some((path) =>
      mergedPermissions.some((permission) => {
        let moduleName = permission.module.name.toLowerCase().replace(/s$/, '');
        const routeName = path.toLowerCase().replace(/s$/, '');

        if (moduleName === 'file') {
          moduleName = 'document';
        }

        if (moduleName === routeName && permission.isEnabled) {
          if (method === 'POST' && !permission.canCreate) {
            return false;
          }
          if (
            ['PUT', 'PATCH', 'DELETE'].includes(method) &&
            !permission.canEdit
          ) {
            return false;
          }
          if (method === 'GET') {
            return true;
          }
          return true;
        }
        return false;
      })
    );

    // if (routePath.includes('graphql')) {
    //   console.log('case graphql');
    //   const graphqlOperation = headers['x-graphql-operation'];
    //   const graphqlEndpoint = headers['x-graphql-endpoint']
    //     .toLowerCase()
    //     .replace(/s$/, '');
    //   const graphqlPermission = mergedPermissions.find(
    //     (permission) =>
    //       permission.module.name.toLowerCase().replace(/s$/, '') ===
    //         graphqlEndpoint && permission.isEnabled
    //   );
    //   if (graphqlPermission) {
    //     if (graphqlOperation === 'create' && !graphqlPermission.canCreate) {
    //       return false;
    //     }
    //     if (
    //       ['update', 'delete'].includes(graphqlOperation) &&
    //       !graphqlPermission.canEdit
    //     ) {
    //       return false;
    //     }
    //     if (graphqlOperation === 'get') {
    //       return true;
    //     }
    //     return true;
    //   }
    // }

    return hasCommonPermissions;
  } catch (err) {
    throw err;
  }
};

exports.verifyJwt = verifyJwt;

exports.authenticateJWT = function (
  roles = usersRoles.getUserRolesAsArray(),
  force = true
) {
  return function (req, res, next) {
    const authHeader = req.headers.authorization
      ? req.headers.authorization
      : `Bearer ${req.query.token}`;
    if (authHeader) {
      const token = authHeader.split(' ')[1];
      return verifyJwt(token, roles, force, req).then((checkAuth) => {
        if (checkAuth.status === resCode.HTTP_OK) {
          req.authenticated = true;
          req.user = checkAuth.user;
          next();
        } else {
          return res
            .status(checkAuth.status)
            .json(
              genRes(
                checkAuth.status,
                checkAuth.errorMessage,
                checkAuth.errorTypes
              )
            );
        }
      });
    }
    return res
      .status(resCode.HTTP_UNAUTHORIZED)
      .json(
        genRes(
          resCode.HTTP_UNAUTHORIZED,
          errorMessage.UNAUTHORIZED_ACCESS,
          errorTypes.UNAUTHORIZED_ACCESS
        )
      );
  };
};

exports.authenticateJWTAndLogout = function (
  roles = usersRoles.getUserRolesAsArray(),
  force = true
) {
  return async function (req, res, next) {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      return res
        .status(resCode.HTTP_UNAUTHORIZED)
        .json(
          genRes(
            resCode.HTTP_UNAUTHORIZED,
            errorMessage.UNAUTHORIZED_ACCESS,
            errorTypes.UNAUTHORIZED_ACCESS
          )
        );
    }

    const token = authHeader.split(' ')[1];
    try {
      const checkAuth = await verifyJwt(token, roles, force, req);
      if (checkAuth.status !== resCode.HTTP_OK) {
        return res
          .status(checkAuth.status)
          .json(
            genRes(
              checkAuth.status,
              checkAuth.errorMessage,
              checkAuth.errorTypes
            )
          );
      }

      const deviceSession = await DeviceSession.findOne({
        where: { sessionToken: token },
      });

      if (!deviceSession) {
        return res
          .status(resCode.HTTP_UNAUTHORIZED)
          .json(
            genRes(
              resCode.HTTP_UNAUTHORIZED,
              errorMessage.UNAUTHORIZED_ACCESS,
              errorTypes.UNAUTHORIZED_ACCESS
            )
          );
      }

      await deviceSession.logout();

      return res.status(resCode.HTTP_OK).json(
        genRes(resCode.HTTP_OK, {
          message: successMessage.LOGOUT_SUCCESS_MESSAGE(),
        })
      );
    } catch (err) {
      return res
        .status(resCode.HTTP_INTERNAL_ERROR)
        .json(
          genRes(resCode.HTTP_INTERNAL_ERROR, errorMessage.SERVER_ERROR, [
            err.message,
          ])
        );
    }
  };
};

exports.skillVerification = function () {
  const secretOrKey = process.env.JWT_SECRET_KEY;
  return function (req, res, next) {
    const { code } = req.query;
    return jwt.verify(code, secretOrKey, async (err, jwtPayload) => {
      if (err) {
        return res
          .status(resCode.HTTP_UNAUTHORIZED)
          .json(
            genRes(
              resCode.HTTP_UNAUTHORIZED,
              errorMessage.UNAUTHORIZED_ACCESS,
              errorTypes.UNAUTHORIZED_ACCESS
            )
          );
      }
      if (jwtPayload && jwtPayload.id) {
        req.body.skill = jwtPayload;
        next();
      } else {
        return res
          .status(resCode.HTTP_UNAUTHORIZED)
          .json(
            genRes(
              resCode.HTTP_UNAUTHORIZED,
              errorMessage.UNAUTHORIZED_ACCESS,
              errorTypes.UNAUTHORIZED_ACCESS
            )
          );
      }
    });
  };
};

const verifyInvite = async (token) => {
  const secretOrKey = process.env.JWT_SECRET_KEY;
  try {
    const jwtPayload = jwt.verify(token, secretOrKey);
    const userId =
      jwtPayload['https://hasura.io/jwt/claims']['x-hasura-user-id'];
    const existingUser = await User.findOne({
      where: { id: userId },
      attributes: ['id', 'status', 'email', 'designationId'],
    });
    return { status: resCode.HTTP_OK, user: existingUser };
    // if (existingUser.status === defaultStatus.ACTIVE) {
    //   return {
    //     status: resCode.HTTP_CONFLICT,
    //     errorMessage: errorMessage.INVITE_ALREADY_USED,
    //     errorType: errorTypes.INVITE_ALREADY_USED,
    //   };
    // } else if (existingUser.status === defaultStatus.PENDING) {
    //   return { status: resCode.HTTP_OK, user: existingUser };
    // } else {
    //   return {
    //     status: resCode.HTTP_UNAUTHORIZED,
    //     errorMessage: errorMessage.INVALID_INVITE_LINK,
    //     errorType: errorTypes.INVALID_INVITE_LINK,
    //   };
    // }
  } catch (err) {
    return err instanceof jwt.TokenExpiredError
      ? {
          status: resCode.HTTP_UNAUTHORIZED,
          errorMessage: errorMessage.INVALID_INVITE_LINK,
          errorType: errorTypes.INVALID_INVITE_LINK,
        }
      : {
          status: resCode.HTTP_UNAUTHORIZED,
          errorMessage: errorMessage.UNAUTHORIZED_ACCESS,
          errorType: errorTypes.UNAUTHORIZED_ACCESS,
        };
  }
};
exports.verifyInvite = verifyInvite;

exports.verifyInvitationToken = function (req, res, next) {
  const authHeader = req.headers.authorization;

  if (authHeader) {
    const token = authHeader.split(' ')[1];
    return verifyInvite(token).then((checkAuth) => {
      if (checkAuth.status === resCode.HTTP_OK) {
        const { email, designationId, id } = checkAuth.user;
        req.authenticated = true;
        req.user = { email, designationId, id };
        next();
      } else {
        return res
          .status(checkAuth.status)
          .json(
            genRes(
              checkAuth.status,
              checkAuth.errorMessage,
              checkAuth.errorType
            )
          );
      }
    });
  } else {
    return res
      .status(resCode.HTTP_UNAUTHORIZED)
      .json(
        genRes(
          resCode.HTTP_UNAUTHORIZED,
          errorMessage.UNAUTHORIZED_ACCESS,
          errorTypes.UNAUTHORIZED_ACCESS
        )
      );
  }
};
