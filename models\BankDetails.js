const { bankDetailStatus } = require('../config/options.js');

module.exports = (sequelize, DataTypes) => {
  const BankDetail = sequelize.define(
    'BankDetail',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      accountName: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      accountNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      ifscCode: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      bankName: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(...bankDetailStatus.contractorTypeArray()),
        defaultValue: bankDetailStatus.ACTIVE,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  BankDetail.associate = (models) => {
    BankDetail.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
      allowNull: true,
    });

    BankDetail.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
      allowNull: true,
    });

    BankDetail.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
      allowNull: true,
    });

    BankDetail.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
      allowNull: true,
    });
  };

  // BankDetail.associate = (models) => {
  //   BankDetail.belongsTo(models.User, {
  //     foreignKey: 'userId',
  //     as: 'user',
  //     onUpdate: 'CASCADE',
  //     onDelete: 'SET NULL',
  //     allowNull: true,
  //   });

  //   BankDetail.belongsTo(models.Organization, {
  //     foreignKey: 'organizationId',
  //     as: 'organization',
  //     onUpdate: 'CASCADE',
  //     onDelete: 'SET NULL',
  //     allowNull: true,
  //   });

  //   BankDetail.belongsTo(models.User, {
  //     foreignKey: 'createdBy',
  //     as: 'creator',
  //     onUpdate: 'CASCADE',
  //     onDelete: 'SET NULL',
  //   });

  //   BankDetail.belongsTo(models.User, {
  //     foreignKey: 'updatedBy',
  //     as: 'updater',
  //     onUpdate: 'CASCADE',
  //     onDelete: 'SET NULL',
  //     allowNull: true,
  //   });
  // };

  return BankDetail;
};
