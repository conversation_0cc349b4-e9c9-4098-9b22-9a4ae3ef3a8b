const { requestType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const values = requestType.getValues();
    for (const value of values) {
      await queryInterface.sequelize.query(`
        DO $$ 
        BEGIN
          IF EXISTS (
            SELECT 1
            FROM pg_type
            WHERE typname = 'enum_Request_requestType'
          ) AND NOT EXISTS (
            SELECT 1
            FROM pg_enum
            WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'enum_Request_requestType')
            AND enumlabel = '${value}'
          ) THEN
            -- Add the new enum value
            ALTER TYPE "enum_Request_requestType" ADD VALUE '${value}';
          END IF;
        END $$;
      `);
    }

    await queryInterface.changeColumn('Request', 'requestType', {
      type: Sequelize.ENUM(requestType.getValues()),
      allowNull: true,
      defaultValue: requestType.GENERAL_REQUEST,
    });

    await queryInterface.addColumn('Request', 'recordId', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.addIndex('Request', ['requestType', 'recordId'], {
      name: 'request_polymorphic_index',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex('Request', 'request_polymorphic_index');

    await queryInterface.changeColumn('Request', 'requestType', {
      type: Sequelize.ENUM([
        'general_request',
        'leave_request',
        'payment_request',
      ]),
      allowNull: true,
      defaultValue: 'general_request',
    });

    await queryInterface.removeColumn('Request', 'recordId');
  },
};
