const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskCommentsController = require('@controllers/v1/task/TaskComments');
const TaskCommentSchema = require('@schema-validation/task/TaskComments');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(TaskCommentSchema.createTaskCommentValidation),
  ErrorHandleHelper.requestValidator,
  TaskCommentsController.addComment
);

router.patch(
  '/:id',
  checkSchema(TaskCommentSchema.updateTaskCommentValidation),
  ErrorHandleHelper.requestValidator,
  TaskCommentsController.updateTaskComment
);

router.delete(
  '/:id',
  checkSchema(TaskCommentSchema.deleteTaskCommentValidation),
  ErrorHandleHelper.requestValidator,
  TaskCommentsController.deleteTaskComment
);

module.exports = router;
