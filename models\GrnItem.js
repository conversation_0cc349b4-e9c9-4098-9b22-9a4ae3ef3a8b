'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const GrnItem = sequelize.define(
    'GrnItem',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.grnItemStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.grnItemStatus.PENDING,
      },
      totalQuantity: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      receivedQuantity: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      reason: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );
  GrnItem.associate = (models) => {
    GrnItem.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    GrnItem.belongsTo(models.Grn, {
      foreignKey: 'grnId',
      as: 'grn',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    GrnItem.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    GrnItem.belongsTo(models.ItemVariant, {
      foreignKey: 'itemVariantId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    GrnItem.hasMany(models.GrnMedia, {
      foreignKey: 'grnItemId',
      as: 'grnMedia',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return GrnItem;
};
