'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Warehouse', 'about', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('Warehouse', 'addressId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Address',
        key: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Warehouse', 'about');
    await queryInterface.removeColumn('Warehouse', 'addressId');
  },
};
