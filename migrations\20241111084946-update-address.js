'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Address', 'addressType', {
      type: Sequelize.ENUM('employee', 'project'),
      allowNull: true,
    });

    await queryInterface.addColumn('Address', 'addressLine2', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Address', 'landmark', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Address', 'latitude', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });

    await queryInterface.addColumn('Address', 'longitude', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Address', 'addressType');
    await queryInterface.removeColumn('Address', 'addressLine2');
    await queryInterface.removeColumn('Address', 'landmark');
    await queryInterface.removeColumn('Address', 'latitude');
    await queryInterface.removeColumn('Address', 'longitude');
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Address_addressType";'
    );
  },
};
