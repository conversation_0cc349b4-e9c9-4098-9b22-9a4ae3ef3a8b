paths:
  /material/stock-adjustment:
    post:
      tags:
        - "Material"
      summary: "Create a new stock adjustment"
      description: "This endpoint allows you to create a new stock adjustment in the system"
      operationId: "CreateStockAdjustment"
      requestBody:
        description: "Stock adjustment creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateStockAdjustmentRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Stock adjustment created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/stock-adjustment/{id}:
    put:
      tags:
        - "Material"
      summary: "Update an existing stock adjustment"
      description: "This endpoint allows you to update an existing stock adjustment in the system"
      operationId: "UpdateStockAdjustment"
      parameters:
        - $ref: "#/components/parameters/StockAdjustmentIdParam"
      requestBody:
        description: "Stock adjustment update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateStockAdjustmentRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Stock adjustment updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Stock adjustment not found"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing stock adjustment"
      description: "This endpoint allows you to update the status of an existing stock adjustment in the system"
      operationId: "UpdateStockAdjustmentStatus"
      parameters:
        - $ref: "#/components/parameters/StockAdjustmentIdParam"
      requestBody:
        description: "Stock adjustment status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/StockAdjustmentStatusUpdateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Stock adjustment status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Stock adjustment not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing stock adjustment"
      description: "This endpoint allows you to delete an existing stock adjustment in the system"
      operationId: "DeleteStockAdjustment"
      parameters:
        - $ref: "#/components/parameters/StockAdjustmentIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Stock adjustment deleted successfully"
        "404":
          description: "Stock adjustment not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateStockAdjustmentRequest:
      type: object
      properties:
        warehouseId:
          type: integer
          example: 1
        adjustmentNumber:
          type: integer
          example: 1001
        adjustmentDate:
          type: string
          format: date
          example: "2025-03-30"
        note:
          type: string
          example: "This is a note"
        items:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
              # itemVariantId:
              #   type: integer
              #   example: 1
              adjustedStock:
                type: integer
                example: 100
              adjustment:
                type: integer
                example: -50
              value:
                type: integer
                example: 5000
      required:
        - warehouseId
        - adjustmentNumber
        - adjustmentDate
        - items

    UpdateStockAdjustmentRequest:
      type: object
      properties:
        warehouseId:
          type: integer
          example: 1
        adjustmentNumber:
          type: integer
          example: 1001
        adjustmentDate:
          type: string
          format: date
          example: "2025-03-30"
        note:
          type: string
          example: "This is a note"
        items:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
              # itemVariantId:
              #   type: integer
              #   example: 1
              adjustedStock:
                type: integer
                example: 100
              adjustment:
                type: integer
                example: -50
              value:
                type: integer
                example: 5000

    StockAdjustmentStatusUpdateRequest:
      type: object
      properties:
        status:
          type: string
          enum: ["pending_approval", "approved", "rejected", "cancelled", "escalated"]
          example: "approved"
      required:
        - status

  parameters:
    StockAdjustmentIdParam:
      name: "id"
      in: "path"
      description: "ID of the stock adjustment to be managed"
      required: true
      schema:
        type: integer