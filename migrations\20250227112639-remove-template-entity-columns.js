'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('EmployeeTemplateItem', 'templateId');
    await queryInterface.removeColumn('EmployeeTemplateItem', 'entityId');
    await queryInterface.removeColumn('EmployeeTemplate', 'templateId');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('EmployeeTemplateItem', 'templateId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'TemplateMaster',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('EmployeeTemplateItem', 'entityId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'TemplateEntity',
        key: 'id',
      },
      onUpdate: 'RESTRICT',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('EmployeeTemplate', 'templateId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'TemplateMasters',
        key: 'id',
      },
    });
  },
};
