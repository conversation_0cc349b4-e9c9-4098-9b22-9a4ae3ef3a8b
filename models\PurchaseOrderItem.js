'use strict';

module.exports = (sequelize, DataTypes) => {
  const PurchaseOrderItem = sequelize.define(
    'PurchaseOrderItem',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      subTotal: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      // sgst: {
      //   type: DataTypes.DECIMAL(10, 2),
      //   allowNull: false,
      //   defaultValue: 0.0,
      // },
      // cgst: {
      //   type: DataTypes.DECIMAL(10, 2),
      //   allowNull: false,
      //   defaultValue: 0.0,
      // },
      taxRates: {
        type: DataTypes.JSONB,
        allowNull: true,
        defaultValue: {},
      },
      discount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      total: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  PurchaseOrderItem.associate = (models) => {
    PurchaseOrderItem.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrderItem.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrderItem.belongsTo(models.PurchaseOrder, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrderItem.belongsTo(models.ItemVariant, {
      foreignKey: 'itemVariantId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return PurchaseOrderItem;
};
