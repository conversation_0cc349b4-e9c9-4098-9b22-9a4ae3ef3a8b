paths:
  /settings/invite-user:
    post:
      tags:
        - "Settings"
      summary: "Invite a new user"
      description: "This endpoint allows you to invite a new user to the system"
      operationId: "InviteUser"
      requestBody:
        description: "User invitation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/InviteUserRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "User invitation sent successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /settings/invite-user/{id}/resend:
    post:
      tags:
        - "Settings"
      summary: "Resend an invitation"
      description: "This endpoint allows you to resend an invitation"
      operationId: "ResendInvite"
      parameters:
        - $ref: "#/components/parameters/InviteIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Invite resent successfully"
        "400":
          description: "Invalid request data"
        "404":
          description: "Invite not found"
        "500":
          description: "Internal Server Error"

  /settings/invite-user/{id}/manage:
    patch:
      tags:
        - "Settings"
      summary: "Accept or reject an invitation"
      description: "This endpoint allows you to accept or reject a user invitation"
      operationId: "ManageInvite"
      parameters:
        - $ref: "#/components/parameters/InviteIdParam"
      requestBody:
        description: "Action to accept or reject the invite"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/ManageInviteRequest"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "Invite managed successfully"
        "400":
          description: "Invalid action or request data"
        "404":
          description: "Invite not found"
        "500":
          description: "Internal Server Error"

  /settings/invite-user/{id}:
    delete:
      tags:
        - "Settings"
      summary: "Delete a user invitation"
      description: "This endpoint allows you to delete a pending user invitation"
      operationId: "DeleteInvite"
      parameters:
        - $ref: "#/components/parameters/InviteIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Invitation deleted successfully"
        "404":
          description: "Invite not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    InviteUserRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        roleId:
          type: integer
          example: 1
        projectId:
          type: array
          items:
            type: integer
            example: 1
      required:
        - email
        - roleId

    ManageInviteRequest:
      type: object
      properties:
        action:
          type: string
          enum: ["accept", "reject"]
          description: "Action to be performed on the invite"
          example: "accept"
      required:
        - action

  parameters:
    InviteIdParam:
      name: "id"
      in: "path"
      description: "ID of the invite to be managed"
      required: true
      schema:
        type: string