const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const JournalController = require('../../../../controllers/api/v1/accounts/Journal');
const JournalSchema = require('../../../../schema-validation/account/Journal');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(JournalSchema.createJournal),
  ErrorHandleHelper.requestValidator,
  JournalController.createJournal
);

router.get(
  '/',
  checkSchema(JournalSchema.getJournal),
  ErrorHandleHelper.requestValidator,
  JournalController.getJournalList
);

router.get(
  '/:journalId',
  checkSchema(JournalSchema.getJournalById),
  ErrorHandleHelper.requestValidator,
  JournalController.getJournalByIdList
);

router.delete(
  '/:journalId',
  checkSchema(JournalSchema.deleteJournal),
  ErrorHandleHelper.requestValidator,
  JournalController.deleteJournal
);

router.put(
  '/:journalId',
  checkSchema(JournalSchema.updateJournal),
  ErrorHandleHelper.requestValidator,
  JournalController.updateJournalDetails
);

module.exports = router;
