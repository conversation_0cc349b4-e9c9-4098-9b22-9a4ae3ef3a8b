const db = require('..');
const { QueryTypes } = require('sequelize');
const { DocumentPermission, User } = require('..');
const {
  successMessage,
  errorMessage,
  actionType,
} = require('../../config/options');
const DocumentActivityLogRepository = require('./DocumentActivityLogRepository');

exports.findOne = async (query) => await DocumentPermission.findOne(query);

exports.findAll = async (query) => await DocumentPermission.findAll(query);

exports.bulkCreate = async (data) => await DocumentPermission.bulkCreate(data);

exports.addBulkPermission = async (data, documentId, user) => {
  try {
    const documentPermissionPayload = await data.map((media) => ({
      ...media,
      documentId: documentId,
    }));

    const documentPermissions = await this.bulkCreate(
      documentPermissionPayload
    );

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Document Permission'),
      data: documentPermissions.map((permission) => permission.id),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getPermissionByDocument = async (documentId) => {
  try {
    const query = {
      where: {
        documentId,
      },
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['firstName', 'lastName', 'email', 'profilePicture'],
        },
      ],
    };

    const documentPermission = await this.findAll(query);
    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Document Permission'),
      data: documentPermission,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.addUserInDocumentPermission = async (
  documentId,
  data,
  loggedInUser
) => {
  try {
    const nestedDocuments = await this.getNestedDocuments(documentId);
    const permissionsForDocuments = nestedDocuments.map((doc) => ({
      documentId: doc.id,
      ...data,
    }));

    const documentPermission = await this.bulkCreate(permissionsForDocuments);

    const documentPermissionPayload = permissionsForDocuments.map((doc) => ({
      documentId: doc.documentId,
      actionType: doc.canEdit
        ? actionType.ADD_USER_WITH_EDIT
        : actionType.ADD_USER_WITH_READ,
      performedBy: loggedInUser.id,
      performedOn: doc.userId,
    }));

    await DocumentActivityLogRepository.bulkCreate(documentPermissionPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Document Permission'),
      data: documentPermission,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkUserDocumentPermission = async (documentId, userId) => {
  try {
    const query = {
      where: {
        documentId,
        userId,
      },
    };

    const documentPermission = await this.findOne(query);

    if (!documentPermission) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Document Permission'),
      };
    }

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Document Permission'),
      data: documentPermission,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateUserDocumentPermission = async (
  documentId,
  data,
  loggerInUserId
) => {
  try {
    const query = {
      where: {
        documentId,
        userId: data.userId,
      },
    };

    let existingDocumentPermission = await this.findOne(query);

    if (!existingDocumentPermission) {
      return {
        success: true,
        message: errorMessage.DOES_NOT_EXIST('Document Permission'),
        data: existingDocumentPermission,
      };
    }
    existingDocumentPermission.canEdit = data.canEdit;
    existingDocumentPermission.canDelete = data.canDelete;
    existingDocumentPermission.canView = data.canView;

    await existingDocumentPermission.save();

    await DocumentActivityLogRepository.addActivityLog(
      existingDocumentPermission.canEdit
        ? actionType.UPDATE_USER_WITH_EDIT
        : actionType.UPDATE_USER_WITH_READ,
      documentId,
      loggerInUserId,
      existingDocumentPermission.userId
    );

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Document Permission'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.removeUserFromDocumentPermission = async (
  documentId,
  userId,
  loggerInUser
) => {
  try {
    const nestedDocuments = await this.getNestedDocuments(documentId);

    const documentIdsToRemove = nestedDocuments.map((doc) => doc.id);

    await DocumentPermission.destroy({
      where: {
        documentId: documentIdsToRemove,
        userId: userId,
      },
    });

    const documentPermissionPayload = nestedDocuments.map((doc) => ({
      documentId: doc.id,
      actionType: actionType.REMOVE_USER,
      performedBy: loggerInUser.id,
      performedOn: userId,
    }));

    await DocumentActivityLogRepository.bulkCreate(documentPermissionPayload);
    return {
      success: true,
      message: successMessage.REMOVED_SUCCESS_MESSAGE('Document Permission'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getNestedDocuments = async (documentId) => {
  return await db.sequelize.query(
    `
    WITH RECURSIVE document_hierarchy AS (
            SELECT id, name, "isFolder", "basePath", "parentFolderId", status
            FROM public."Document"  
            WHERE id = :documentId
            UNION ALL
            SELECT d.id, d.name, d."isFolder", d."basePath", d."parentFolderId", d.status
            FROM public."Document" d
            JOIN document_hierarchy dh ON dh.id = d."parentFolderId"
          )
          SELECT * FROM document_hierarchy;
  `,
    {
      replacements: { documentId: documentId },
      type: QueryTypes.SELECT,
    }
  );
};
