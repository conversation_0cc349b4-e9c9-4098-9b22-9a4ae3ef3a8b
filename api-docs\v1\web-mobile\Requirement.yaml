paths:
  /crm/requirement:
    post:
      tags:
        - "CRM"
      summary: "Create a new Requirement"
      description: "This endpoint allows you to register a new requirement by providing all necessary details."
      operationId: "CreateRequirement"
      requestBody:
        description: "The details of the new requirement to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/requirement"
          "application/x-www-form-urlencoded":
            schema:
              $ref: "#/components/schemas/requirement"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Requirement has been created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/requirement'
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error"

  /crm/requirement/{id}:      
    put:
      tags:
        - "CRM"
      summary: "Update an existing Requirement"
      description: "This endpoint allows you to update the details of an existing requirement by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateRequirement"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the requirement to be updated."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The updated information for the requirement. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/requirement-update"
          "application/x-www-form-urlencoded":
            schema:
              $ref: "#/components/schemas/requirement-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Requirement has been updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/requirement'
        "400":
          description: "Invalid input data, requirement not found."
        "404":
          description: "Requirement not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    requirement:
      type: object
      properties:
        customerId:
          type: integer
          description: "The customer ID associated with the requirement."
          example: 123
        purpose:
          type: string
          enum:
            - "investment"
            - "end_use"
            - "rental"
            - "resale"
          description: "The purpose of the requirement."
          example: "investment"
        possessionBy:
          type: string
          enum:
            - "immediate"
            - "one_month"
            - "two_months"
            - "six_months"
            - "one_year"
            - "two_years"
            - "three_years"
            - "four_years"
            - "five_years"
            - "seven_years"
            - "ten_years"
            - "ten_plus_years"
          description: "When the possession of the property is expected."
          example: "one_year"
        budgetRange:
          type: string
          description: "The budget range for the requirement."
          example: "50-60 lakhs"
        fundingType:
          type: string
          enum:
            - "home_loan"
            - "mixed"
            - "personal"
            - "barter"
          description: "The funding type for the requirement."
          example: "home_loan"
        propertyType:
          type: string
          enum:
            - "villa"
            - "row_house"
            - "duplex"
            - "penthouse"
            - "semi_detached"
            - "apartment"
            - "plotted_development"
            - "commercial"
          description: "The type of property needed for the requirement."
          example: "apartment"
        configuration:
          type: string
          enum:
            - "residential"
            - "commercial"
            - "plotted_development"
          description: "The configuration of the property."
          example: "residential"
        configurationType:
          type: string
          enum:
            - "studio"
            - "1_bhk"
            - "2_bhk"
            - "3_bhk"
            - "4_bhk"
            - "5_bhk"
            - "5_plus_bhk"
            - "shop"
            - "office"
            - "residential"
            - "commercial"
            - "others"
          description: "The configuration type of the property."
          example: "2_bhk"
        areaRange:
          type: string
          description: "The area range for the requirement (e.g., '1000-1500 sq.ft')."
          example: "1200-1500 sq.ft"
        locationPreference:
          type: string
          description: "Location preference for the property."
          example: "South Delhi"
        projectId:
          type: integer
          description: "The project ID for the property."
          example: 456
        subProjectId:
          type: integer
          description: "The sub-project ID within the project."
          example: 789
        furnishingType:
          type: string
          enum:
            - "unfurnished"
            - "semi_furnished"
            - "fully_furnished"
          description: "The furnishing type of the property."
          example: "semi_furnished"
        directionPreference:
          type: string
          enum:
            - "any"
            - "north"
            - "east"
            - "south"
            - "west"
            - "north_east"
            - "south_east"
            - "south_west"
            - "north_west"
          description: "Direction preference for the property."
          example: "north_east"
        otherPreferences:
          type: string
          description: "Any other preferences regarding the requirement."
          example: "Near metro station"
      required:
        - customerId
      additionalProperties: false

    requirement-update:
      type: object
      properties:
        customerId:
          type: integer
          description: "The customer ID associated with the requirement."
          example: 123
        purpose:
          type: string
          enum:
            - "investment"
            - "end_use"
            - "rental"
            - "resale"
          description: "The purpose of the requirement."
          example: "investment"
        possessionBy:
          type: string
          enum:
            - "immediate"
            - "one_month"
            - "two_months"
            - "six_months"
            - "one_year"
            - "two_years"
            - "three_years"
            - "four_years"
            - "five_years"
            - "seven_years"
            - "ten_years"
            - "ten_plus_years"
          description: "When the possession of the property is expected."
          example: "one_year"
        budgetRange:
          type: string
          description: "The budget range for the requirement."
          example: "50-60 lakhs"
        fundingType:
          type: string
          enum:
            - "home_loan"
            - "mixed"
            - "personal"
            - "barter"
          description: "The funding type for the requirement."
          example: "home_loan"
        propertyType:
          type: string
          enum:
            - "villa"
            - "row_house"
            - "duplex"
            - "penthouse"
            - "semi_detached"
            - "apartment"
            - "plotted_development"
            - "commercial"
          description: "The type of property needed for the requirement."
          example: "apartment"
        configuration:
          type: string
          enum:
            - "residential"
            - "commercial"
            - "plotted_development"
          description: "The configuration of the property."
          example: "residential"
        configurationType:
          type: string
          enum:
            - "studio"
            - "1_bhk"
            - "2_bhk"
            - "3_bhk"
            - "4_bhk"
            - "5_bhk"
            - "5_plus_bhk"
            - "shop"
            - "office"
            - "residential"
            - "commercial"
            - "others"
          description: "The configuration type of the property."
          example: "2_bhk"
        areaRange:
          type: string
          description: "The area range for the requirement (e.g., '1000-1500 sq.ft')."
          example: "1200-1500 sq.ft"
        locationPreference:
          type: string
          description: "Location preference for the property."
          example: "South Delhi"
        projectId:
          type: integer
          description: "The project ID for the property."
          example: 456
        subProjectId:
          type: integer
          description: "The sub-project ID within the project."
          example: 789
        furnishingType:
          type: string
          enum:
            - "unfurnished"
            - "semi_furnished"
            - "fully_furnished"
          description: "The furnishing type of the property."
          example: "semi_furnished"
        directionPreference:
          type: string
          enum:
            - "any"
            - "north"
            - "east"
            - "south"
            - "west"
            - "north_east"
            - "south_east"
            - "south_west"
            - "north_west"
          description: "Direction preference for the property."
          example: "north_east"
        otherPreferences:
          type: string
          description: "Any other preferences regarding the requirement."
          example: "Near metro station"
      additionalProperties: false
