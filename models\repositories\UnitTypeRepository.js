const sequelize = require('sequelize');
const { Op } = sequelize;
const { UnitType } = require('..');
const { errorMessage, successMessage } = require('../../config/options');

exports.getUnitType = async (query) => await UnitType.findOne(query);

exports.validateAndCreateUnitType = async (data) => {
  const { name } = data;
  try {
    const existingUnitType = await this.getUnitType({
      where: {
        name: {
          [Op.iLike]: name,
        },
      },
    });

    if (existingUnitType) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('UnitType'),
      };
    }

    const createdUnitType = await UnitType.create(data);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('UnitType'),
      data: createdUnitType,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndUpdateUnitType = async (unitTypeId, data) => {
  const { name } = data;
  try {
    const existingUnitType = await this.getUnitType({
      where: { id: unitTypeId },
    });

    if (!existingUnitType) {
      return {
        success: false,
        message: errorMessage.DATA_NOT_FOUND,
      };
    }

    const duplicateUnitType = await this.getUnitType({
      where: {
        name: {
          [Op.iLike]: name,
        },
        id: {
          [Op.ne]: unitTypeId,
        },
      },
    });

    if (duplicateUnitType) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('UnitType'),
      };
    }

    existingUnitType.name = name;
    const updatedUnitType = await existingUnitType.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('UnitType'),
      data: updatedUnitType,
    };
  } catch (error) {
    throw new Error(error);
  }
};
