'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('TaskTags', [
      {
        name: 'high',
        type: 'priority_tag',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'medium',
        type: 'priority_tag',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'low',
        type: 'priority_tag',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'urgent',
        type: 'priority_tag',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'critical',
        type: 'priority_tag',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('TaskTags', { type: 'priority_tag' }, {});
  },
};
