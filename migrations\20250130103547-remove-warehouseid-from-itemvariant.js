'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'ItemVariant',
      'ItemVariant_warehouseId_fkey'
    );

    await queryInterface.removeColumn('ItemVariant', 'warehouseId');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('ItemVariant', 'warehouseId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Warehouse',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },
};
