'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    // Step 1: Remove NOT NULL constraints first using raw SQL
    await queryInterface.sequelize.query(`
      ALTER TABLE "EmployeeDocument" 
      ALTER COLUMN "userId" DROP NOT NULL,
      ALTER COLUMN "createdBy" DROP NOT NULL,
      ALTER COLUMN "updatedBy" DROP NOT NULL,
      ALTER COLUMN "organizationId" DROP NOT NULL;
    `);

    // Step 2: Remove existing foreign key constraints
    await queryInterface
      .removeConstraint('EmployeeDocument', 'EmployeeDocument_userId_fkey')
      .catch(() => {});

    await queryInterface
      .removeConstraint('EmployeeDocument', 'EmployeeDocument_createdBy_fkey')
      .catch(() => {});

    await queryInterface
      .removeConstraint('EmployeeDocument', 'EmployeeDocument_updatedBy_fkey')
      .catch(() => {});

    await queryInterface
      .removeConstraint(
        'EmployeeDocument',
        'EmployeeDocument_organizationId_fkey'
      )
      .catch(() => {});

    // Step 3: Add back the foreign key references with NULL allowed
    await queryInterface.changeColumn('EmployeeDocument', 'userId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('EmployeeDocument', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('EmployeeDocument', 'updatedBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('EmployeeDocument', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    // First remove any NULL values
    await queryInterface.sequelize.query(`
      UPDATE "EmployeeDocument"
      SET "userId" = 1,
          "createdBy" = 1,
          "updatedBy" = 1,
          "organizationId" = 1
      WHERE "userId" IS NULL 
         OR "createdBy" IS NULL 
         OR "updatedBy" IS NULL 
         OR "organizationId" IS NULL;
    `);

    // Remove existing constraints
    await queryInterface
      .removeConstraint('EmployeeDocument', 'EmployeeDocument_userId_fkey')
      .catch(() => {});

    await queryInterface
      .removeConstraint('EmployeeDocument', 'EmployeeDocument_createdBy_fkey')
      .catch(() => {});

    await queryInterface
      .removeConstraint('EmployeeDocument', 'EmployeeDocument_updatedBy_fkey')
      .catch(() => {});

    await queryInterface
      .removeConstraint(
        'EmployeeDocument',
        'EmployeeDocument_organizationId_fkey'
      )
      .catch(() => {});

    // Add NOT NULL constraints back
    await queryInterface.sequelize.query(`
      ALTER TABLE "EmployeeDocument" 
      ALTER COLUMN "userId" SET NOT NULL,
      ALTER COLUMN "createdBy" SET NOT NULL,
      ALTER COLUMN "updatedBy" SET NOT NULL,
      ALTER COLUMN "organizationId" SET NOT NULL;
    `);

    // Restore the columns to their original state
    await queryInterface.changeColumn('EmployeeDocument', 'userId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('EmployeeDocument', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('EmployeeDocument', 'updatedBy', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('EmployeeDocument', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },
};
