const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const AuthHandler = require('../../../models/helpers/AuthHelper');
const UserControl = require('../../../controllers/api/v1/User');
const UserSchema = require('../../../schema-validation/User');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/login',
  checkSchema(UserSchema.passwordLogin),
  ErrorHandleHelper.requestValidator,
  UserControl.login
);

router.post(
  '/sign-up',
  checkSchema(UserSchema.signUp),
  ErrorHandleHelper.requestValidator,
  UserControl.signup
);

router.post(
  '/register',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.register),
  ErrorHandleHelper.requestValidator,
  UserControl.register
);

router.post(
  '/verify-email',
  checkSchema(UserSchema.verifyEmail),
  ErrorHandleHelper.requestValidator,
  UserControl.verifyEmail
);

router.post(
  '/send-otp',
  checkSchema(UserSchema.sendOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.sendOtp
);

router.patch(
  '/verify-otp',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.verifyOtp),
  ErrorHandleHelper.requestValidator,
  UserControl.verifyOtp
);

router.put(
  '/',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.updateInfo),
  ErrorHandleHelper.requestValidator,
  UserControl.putUserProfile
);

router.get('/', AuthHandler.authenticateJWT(), UserControl.getUserProfile);

router.put(
  '/generate-password',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.generatePassword),
  ErrorHandleHelper.requestValidator,
  UserControl.createPassword
);

router.patch(
  '/change-password',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.changePassword),
  ErrorHandleHelper.requestValidator,
  UserControl.changePassword
);

router.patch(
  '/close-account',
  AuthHandler.authenticateJWT(),
  UserControl.deleteUserAccount
);

router.get(
  '/required-documents',
  AuthHandler.verifyInvitationToken,
  UserControl.getRequiredDocuments
);

router.get(
  '/login-history',
  AuthHandler.authenticateJWT(),
  UserControl.getLoginHistory
);

router.get(
  '/device-session',
  AuthHandler.authenticateJWT(),
  UserControl.getDeviceSessions
);

router.get(
  '/:id/bank-details',
  AuthHandler.authenticateJWT(),
  UserControl.getBankDetailsForUser
);

router.delete(
  '/:id/logout',
  AuthHandler.authenticateJWT(),
  UserControl.removeDeviceSessionById
);

router.put(
  '/onboard-user',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.onboardInvitedUser),
  ErrorHandleHelper.requestValidator,
  UserControl.onboardInvitedUser
);

router.delete('/logout', AuthHandler.authenticateJWTAndLogout());

router.post('/test-email', UserControl.testEmail);

router.patch(
  '/toggle-mfa',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.toggleMfa),
  ErrorHandleHelper.requestValidator,
  UserControl.toggleMfa
);

router.patch(
  '/verify-mfa',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.verifyMfa),
  ErrorHandleHelper.requestValidator,
  UserControl.verifyMfa
);

router.patch(
  '/switch-organization/:organizationId',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.switchOrganization),
  ErrorHandleHelper.requestValidator,
  UserControl.switchOrganization
);

router.post(
  '/forgot-password',
  checkSchema(UserSchema.forgotPassword),
  ErrorHandleHelper.requestValidator,
  UserControl.forgotPassword
);

router.patch(
  '/reset-password',
  checkSchema(UserSchema.resetPassword),
  ErrorHandleHelper.requestValidator,
  UserControl.resetPassword
);

router.post(
  '/register-brand',
  AuthHandler.authenticateJWT(),
  checkSchema(UserSchema.registerBrand),
  ErrorHandleHelper.requestValidator,
  UserControl.registerBrand
);

module.exports = router;
