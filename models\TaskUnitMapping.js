module.exports = (sequelize, DataTypes) => {
  const TaskUnitMapping = sequelize.define(
    'TaskUnitMapping',
    {
      taskId: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        references: {
          model: 'Tasks',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      unitId: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        references: {
          model: 'Unit',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
    },
    {
      timestamps: false,
      tableName: 'TaskUnitMapping',
      freezeTableName: true,
    }
  );

  TaskUnitMapping.associate = (models) => {
    TaskUnitMapping.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      onDelete: 'CASCADE',
    });
    TaskUnitMapping.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      onDelete: 'CASCADE',
    });
  };

  return TaskUnitMapping;
};
