const TaskUnitMappingRepository = require('@repo/TaskUnitMappingRepository');
const { genRes, errorMessage, resCode } = require('@config/options');

exports.createTaskUnitMapping = async (req, res) => {
  const { id: taskId } = req.params;
  try {
    const { success, message, data } =
      await TaskUnitMappingRepository.addTaskUnitMapping(taskId, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteTaskUnitMapping = async (req, res) => {
  const { id: taskId, unitId } = req.params;
  try {
    const { success, message, data } =
      await TaskUnitMappingRepository.deleteTaskUnitMapping(taskId, unitId);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
