const { Customer, Contact<PERSON><PERSON>, User, sequelize, Address } = require('..');
const { Op } = require('sequelize');
const {
  errorMessage,
  successMessage,
  usersRoles,
  addressType,
  contactPersonType,
} = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

// Helper function to check existence of email or contact number for both ContactPerson and User
const checkExistenceForConditions = async (
  model,
  conditions,
  excludeId = null
) => {
  if (excludeId) {
    conditions.id = { [Op.ne]: excludeId };
  }
  const selectFields = ['id'];
  return await checkExistence(model, conditions, selectFields);
};

const validateContactPersonData = async (
  data,
  contactPersonId = null,
  userId = null
) => {
  try {
    const { email, countryCode, contactNumber } = data;
    const normalizedEmail = email ? email.toLowerCase() : null;

    if (normalizedEmail) {
      const emailConditions = { email: normalizedEmail };
      const [existingEmail<PERSON>ontact<PERSON>erson, existingEmailUser] = await Promise.all(
        [
          checkExistenceForConditions(
            ContactPerson,
            emailConditions,
            contactPersonId
          ),
          checkExistenceForConditions(User, emailConditions, userId),
        ]
      );

      if (existingEmailContactPerson || existingEmailUser) {
        return {
          success: false,
          message: errorMessage.EXISTS_USER(`email: ${normalizedEmail}`),
        };
      }
    }

    if (countryCode && contactNumber) {
      const contactConditionsContactPerson = { countryCode, contactNumber };
      const contactConditionsUser = {
        countryCode,
        mobileNumber: contactNumber,
      };

      const [existingContactNumberContactPerson, existingContactNumberUser] =
        await Promise.all([
          checkExistenceForConditions(
            ContactPerson,
            contactConditionsContactPerson,
            contactPersonId
          ),
          checkExistenceForConditions(User, contactConditionsUser, userId),
        ]);

      if (existingContactNumberContactPerson || existingContactNumberUser) {
        return {
          success: false,
          message: errorMessage.EXISTS_USER(`contact number: ${contactNumber}`),
        };
      }
    }

    return { success: true };
  } catch (error) {
    throw error;
  }
};

exports.validateAndCreateContactPerson = async (
  customerId,
  data,
  loggedInUser
) => {
  const { email, contactNumber, address, contactType } = data;
  const normalizedEmail = email.toLowerCase();
  const transaction = await sequelize.transaction();
  try {
    const existingCustomer = await checkExistence(Customer, { id: customerId });
    if (!existingCustomer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Customer with Id ${customerId}`),
      };
    }

    // Check if primary contact person already exists for this customer
    if (contactType === contactPersonType.PRIMARY) {
      const existingPrimaryContact = await ContactPerson.findOne({
        where: {
          customerId,
          contactType: contactPersonType.PRIMARY,
        },
        transaction,
      });

      if (existingPrimaryContact) {
        return {
          success: false,
          message: errorMessage.PRIMARY_CONTACT_PERSON,
        };
      }
    }

    const validationResponse = await validateContactPersonData(data);
    if (!validationResponse.success) {
      return validationResponse;
    }

    data.email = normalizedEmail;
    data.createdBy = loggedInUser.id;
    data.organizationId = loggedInUser.currentOrganizationId;

    let addressId = null;
    if (address) {
      address.addressType = addressType.CONTACT_PERSON;
      const createdAddress = await Address.create(address, { transaction });
      addressId = createdAddress.id;
    }

    const createdUser = await User.create(
      { ...data, mobileNumber: contactNumber, role: usersRoles.CONTACT_PERSON },
      { transaction }
    );
    data.userId = createdUser.id;
    data.customerId = customerId;
    data.addressId = addressId;

    const createdContactPerson = await ContactPerson.create(data, {
      transaction,
    });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Contact Person'),
      data: createdContactPerson,
    };
  } catch (error) {
    console.log('validateAndCreateContactPerson error....', error);
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.validateAndUpdateContactPerson = async (contactPersonId, data) => {
  const transaction = await sequelize.transaction();
  try {
    const contactPerson = await ContactPerson.findOne({
      where: { id: contactPersonId },
      include: [
        { model: User, as: 'user' },
        { model: Address, as: 'address' },
        { model: Customer, as: 'customer' },
      ],
      transaction,
    });
    if (!contactPerson) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          'Contact Person with the given ID'
        ),
      };
    }

    // Check if changing to primary when one already exists
    if (
      data.contactType === contactPersonType.PRIMARY &&
      contactPerson.contactType !== contactPersonType.PRIMARY
    ) {
      const existingPrimaryContact = await ContactPerson.findOne({
        where: {
          customerId: contactPerson.customerId,
          contactType: contactPersonType.PRIMARY,
          id: { [Op.ne]: contactPersonId },
        },
        transaction,
      });

      if (existingPrimaryContact) {
        return {
          success: false,
          message: errorMessage.PRIMARY_CONTACT_PERSON,
        };
      }
    }

    const validationResponse = await validateContactPersonData(
      data,
      contactPersonId,
      contactPerson.user?.id
    );
    if (!validationResponse.success) {
      return validationResponse;
    }

    if (contactPerson.user) {
      const user = contactPerson.user;
      if (data.email) user.email = data.email.toLowerCase();
      if (data.contactNumber) user.mobileNumber = data.contactNumber;
      Object.assign(user, data);
      await user.save({ transaction });
    }

    if (data.address) {
      if (contactPerson.address) {
        Object.assign(contactPerson.address, data.address);
        await contactPerson.address.save({ transaction });
      } else {
        const createdAddress = await Address.create(data.address, {
          transaction,
        });
        contactPerson.addressId = createdAddress.id;
      }
    }

    Object.assign(contactPerson, data);
    const updatedContactPerson = await contactPerson.save({ transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Contact Person'),
      data: updatedContactPerson,
    };
  } catch (error) {
    console.log('validateAndUpdateContactPerson error....', error);
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.validateAndRemoveContactPerson = async (contactPersonId) => {
  const transaction = await sequelize.transaction();
  try {
    const contactPerson = await ContactPerson.findByPk(contactPersonId, {
      transaction,
      include: [
        {
          model: User,
          as: 'user',
        },
        {
          model: Address,
          as: 'address',
        },
      ],
    });
    if (!contactPerson) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Contact Person with id : ${contactPersonId}`
        ),
      };
    }

    if (contactPerson.user) {
      await contactPerson.user.destroy({ transaction });
    }

    if (contactPerson.address) {
      await contactPerson.address.destroy({ transaction });
    }

    await contactPerson.destroy({ transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.REMOVED_SUCCESS_MESSAGE('Contact Person'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};
