const { TaskComments, Tasks } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.findOne = async (query) => await TaskComments.findOne(query);

exports.addComment = async (loggedInUser, data) => {
  try {
    const task = await checkExistence(Tasks, {
      id: data.taskId,
    });
    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Task with ID: ' + data.taskId),
      };
    }

    const taskCommentPayload = {
      userId: loggedInUser.id,
      ...data,
    };

    const comment = await TaskComments.create(taskCommentPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('comment'),
      data: comment,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateTaskComment = async (commentId, loggedInUser, data) => {
  try {
    const comment = await TaskComments.findOne({
      where: {
        id: commentId,
        userId: loggedInUser.id,
      },
    });
    if (!comment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Comment with ID: ' + commentId),
      };
    }

    Object.assign(comment, data);
    await comment.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('comment'),
      data: comment,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteTaskComment = async (loggedInUser, commentId) => {
  try {
    const comment = await TaskComments.findOne({
      where: {
        id: commentId,
        userId: loggedInUser.id,
      },
    });
    if (!comment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Comment with ID: ' + commentId),
      };
    }

    await comment.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('comment'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
