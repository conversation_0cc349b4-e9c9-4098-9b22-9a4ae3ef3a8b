'use strict';

const { structureType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('ProjectType', 'structureType', {
      type: Sequelize.ENUM(...structureType.getStructureTypeArray()),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('ProjectType', 'structureType');

    // Drop the ENUM type created for structureType
    await queryInterface.sequelize.query(
      'DROP TYPE "enum_ProjectType_structureType";'
    );
  },
};
