'use strict';
const { metricType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_BOQMetric_metricType" ADD VALUE IF NOT EXISTS 'number';
    `);
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_BOQMetric_metricType" ADD VALUE IF NOT EXISTS 'weight';
    `);
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_BOQMetric_metricType" ADD VALUE IF NOT EXISTS 'time';
    `);

    await queryInterface.changeColumn('BOQMetric', 'metricType', {
      type: Sequelize.ENUM(...metricType.getMetricTypeArray()),
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('BOQMetric', 'metricType', {
      type: Sequelize.ENUM('volume', 'area', 'length'),
      allowNull: false,
    });
  },
};
