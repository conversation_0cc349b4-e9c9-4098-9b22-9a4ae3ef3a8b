paths:
  /document/signed-url:
    get:
      summary: "Generate Signed URL for Document Upload - mobile & web"
      description: Get a signed URL to upload a document to the specified folder in the project.
      operationId: getPostDocumentSignedUrl
      tags:
        - Document
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - in: query
          name: fileName
          required: true
          schema:
            type: string
            example: "example.pdf"
        - in: query
          name: parentFolderId
          schema:
            type: integer
            example: 1
      security:
        - bearerAuth: []
      responses:
        '200':
          description: "Successfully generated signed URL for document upload."
        '400':
          description: "Invalid Request"
        '500':
          description: "Internal server error."
  /document:
    get:
      summary: "Get Documents - mobile & web"
      description: Fetch a list of documents(file/folder).
      operationId: "getDocument"
      tags:
        - Document
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - in: query
          name: projectIds
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
          description: project specific documents
        - in: query
          name: userId
          schema:
            type: integer
            example: 1
          description: Employee User Id
        - in: query
          name: peopleIds
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
          description: User Ids
        - in: query
          name: parentFolderId
          schema:
            type: integer
            example: 1
          description: The parent Folder Id
        - in: "query"
          name: "status"
          schema:
            type: array
            items:
              type: string
            example: ["active", "archived", "deleted"]
        - in: "query"
          name: "search"
          schema:
            type: string
        - in: query
          name: start
          schema:
            type: integer
            example: 0
          description: The page number for pagination.
        - in: query
          name: limit
          schema:
            type: integer
            example: 10
          description: The number of documents to return per page.
        - in: "query"
          name: "isFolder"
          schema:
            type: boolean
            example: true
        - in: query
          name: isFavorites
          schema:
            type: boolean
      security:
        - bearerAuth: []
      responses:
        '200':
          description: "Fetched Document details successfully"
        '400':
          description: "The Document does not exist"
        '500':
          description: "Internal server error"
    post:
      tags:
        - "Document"
      summary: "Create a document - mobile & web"
      description: "Creates a new document with the provided details."
      operationId: "createDocument"
      parameters: []
      requestBody:
        description: "Document creation details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/document-creation"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Document created successfully"
        "400":
          description: "The Parent Document does not exist"
        "500":
          description: "Internal Server Error"
  /document/download:
    get: 
      tags:
        - "Document"
      summary: "Download document - mobile & web"
      description: "Downloads a document based on the provided document ID."
      operationId: "downloadDocument"
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: "documentId"
          in: "query"
          required: true
          description: "ID of the document to be downloaded"
          schema:
            type: "integer"
      requestBody:
        description: "Document download request"
        required: false
        content: {}
      produces:
        - "application/octet-stream"
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Document downloaded successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /document/{id}:
    put: 
      tags:
        - "Document"
      summary: "Update document - mobile & web"
      description: "Updates an existing document with the provided details."
      operationId: "updateDocument"
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: ID of the document id
          schema:
            type: integer
      requestBody:
        description: "Document creation details"
        required: true
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/document-update"
      produces:
        - "application/json"
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Document updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /document/{id}/permission:
    get:
      summary: "Get document permissions - mobile & web"
      description: Retrieve the permissions of a document based on the provided document ID.
      tags:
        - "Document"
      operationId: "FetchDocumentPermission"
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: ID of the document whose permissions are to be retrieved
          schema:
            type: integer
      responses:
        "200":
          description: "Fetched Document details successfully"
        "400":
          description: "The Document does not exist"
        "500":
          description: "Internal Server Error"
    post:
      summary: Add User In document permissions
      description: Add User In Document permissions (view, edit, remove) for a user on a document.
      operationId: addUserDocumentPermission
      tags:
        - Document
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the document
          schema:
            type: integer
            example: 1
      requestBody:
        description: Document permission details
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/add-user-document-permission"
      security:
        - bearerAuth: []
      responses:
        '200':
          description: "Successfully added document permissions"
        '400':
          description: "The User does not have permission for Edit"
        '500':
          description: "Internal server error"
    put:
      summary: update User document permissions
      description: update in user permissions (view, edit, remove) for a user on a document.
      operationId: updateUserDocumentPermission
      tags:
        - Document
      parameters:
        - name: id
          in: path
          required: true
          description: ID of the document
          schema:
            type: integer
            example: 1
      requestBody:
        description: Document permission details
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/update-user-document-permission"
      security:
        - bearerAuth: []
      responses:
        '200':
          description: "Successfully added document permissions"
        '400':
          description: "The User does not have permission for Edit"
        '500':
          description: "Internal server error"
  /document/{id}/permission/{userId}:
    delete:
      tags:
        - "Document"
      summary: "Remove User Document Permission - mobile & web"
      description: "Deletes User the permission for a specified user on a specific document."
      operationId: "deleteUserDocumentPermission"
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: "ID of the document for which the permission will be deleted."
          schema:
            type: integer
        - name: userId
          in: path
          required: true
          description: "ID of the user whose permission will be deleted."
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        '200':
          description: "Document Permission removed successfully"
        '404':
          description: "The User does not have permission for Delete"
        '500':
          description: "Internal server error"
  /document/{id}/activities:
    get:
      summary: Get Document Activities
      description: Fetch a list of document Activities.
      operationId: "getDocumentActivities"
      tags:
        - Document
      parameters:
        - name: id
          in: path
          required: true
          description: Document ID
          schema:
            type: string
        - in: query
          name: start
          schema:
            type: integer
            example: 0
          description: The page number for pagination.
        - in: query
          name: limit
          schema:
            type: integer
            example: 10
          description: The number of activities to return per page.
      security:
        - bearerAuth: []
      responses:
        '200':
          description: "Fetched Document activity successfully"
        '400':
          description: "The Document does not exist"
        '500':
          description: "Internal server error"
components:
  schemas:
    document-base:
      type: "object"
      properties:
        parentFolderId:
          type: "integer"
        name:
          type: "string"
        description:
          type: "string"
        projectId:
          type: "integer"
        isFolder:
          type: "boolean"
        fileType:
          type: "string"
        fileSize:
          type: "integer"
        filePath:
          type: "string"
        fileName:
          type: "string"
        isFavorites:
          type: boolean
    document-permission:
      type: object
      properties:
        userId:
          type: integer
          description: ID of the user
        canView:
          type: boolean
          description: Permission to view the document
        canEdit:
          type: boolean
          description: Permission to edit the document
        canDelete:
          type: boolean
          description: Permission to remove the document
      required:
        - userId
        - canView
        - canEdit
        - canDelete
    document-creation:
      type: "object"
      properties:
        document:
          type: "array"
          items:
            $ref: "#/components/schemas/document-base"
        permissions:
          type: "array"
          items:
            $ref: "#/components/schemas/document-permission"
      required:
        - document
    document-update:
      allOf:
        - $ref: "#/components/schemas/document-base"
      properties:
        permissions:
          type: "array"
          items:
            $ref: "#/components/schemas/document-permission"
    add-user-document-permission:
      allOf:
        - $ref: "#/components/schemas/document-permission"
      required:
        - userId
        - canView
        - canEdit
        - canDelete
    update-user-document-permission:
      allOf:
        - $ref: "#/components/schemas/document-permission"
      required:
        - canView
        - canEdit
        - canDelete
