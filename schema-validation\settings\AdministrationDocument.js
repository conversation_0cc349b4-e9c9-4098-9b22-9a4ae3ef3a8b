exports.createAdministrationDocument = {
  roleId: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Role ID is required',
    isInt: {
      errorMessage: 'Role ID must be a valid integer',
    },
  },
  documents: {
    in: ['body'],
    isArray: {
      errorMessage: 'Documents must be an array',
    },
    custom: {
      options: (documents) => {
        return documents.every((doc) => {
          const { name, isRequiredField, isUploadRequired } = doc;
          if (typeof name !== 'string' || name.trim() === '') {
            throw new Error('Each document must have a valid name');
          }
          if (typeof isRequiredField !== 'boolean') {
            throw new Error(
              'Each document must have a valid isRequiredField boolean'
            );
          }
          if (typeof isUploadRequired !== 'boolean') {
            throw new Error(
              'Each document must have a valid isUploadRequired boolean'
            );
          }
          return true;
        });
      },
    },
  },
};

exports.updateAdministrationDocument = {
  roleId: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'roleId is required',
    },
    isInt: {
      errorMessage: 'roleId must be a valid integer',
    },
  },
  documents: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Documents must be an array',
    },
    custom: {
      options: (documents) => {
        return documents.every((doc) => {
          const { name, isRequiredField, isUploadRequired } = doc;
          if (typeof name !== 'string' || name.trim() === '') {
            throw new Error('Each document must have a valid name');
          }
          if (typeof isRequiredField !== 'boolean') {
            throw new Error(
              'Each document must have a valid isRequiredField boolean'
            );
          }
          if (typeof isUploadRequired !== 'boolean') {
            throw new Error(
              'Each document must have a valid isUploadRequired boolean'
            );
          }
          return true;
        });
      },
    },
  },
};

exports.deleteAdministrationDocument = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Document Id is required',
    },
    isInt: {
      errorMessage: 'Document Id must be a valid integer',
    },
  },
};
