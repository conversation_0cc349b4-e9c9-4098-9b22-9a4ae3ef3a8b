'use strict';

const { requirement } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Requirement = sequelize.define(
    'Requirement',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      purpose: {
        type: DataTypes.ENUM(requirement.purpose.getPurposeArray()),
        allowNull: true,
      },
      possessionBy: {
        type: DataTypes.ENUM(requirement.possessionBy.getPossessionByArray()),
        allowNull: true,
      },
      budgetRange: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      fundingType: {
        type: DataTypes.ENUM(requirement.fundingType.getFundingTypeArray()),
        allowNull: true,
      },
      propertyType: {
        type: DataTypes.ENUM(requirement.propertyType.getPropertyTypeArray()),
        allowNull: true,
      },
      configuration: {
        type: DataTypes.ENUM(requirement.configuration.getConfigurationArray()),
        allowNull: true,
      },
      configurationType: {
        type: DataTypes.ENUM(
          requirement.configurationType.getConfigurationTypeArray()
        ),
        allowNull: true,
      },
      areaRange: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      locationPreference: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      furnishingType: {
        type: DataTypes.ENUM(
          requirement.furnishingType.getFurnishingTypeArray()
        ),
        allowNull: true,
      },
      directionPreference: {
        type: DataTypes.ENUM(
          requirement.directionPreference.getDirectionPreferenceArray()
        ),
        allowNull: true,
      },
      otherPreferences: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Requirement.associate = (models) => {
    Requirement.belongsTo(models.Customer, {
      foreignKey: 'customerId',
      as: 'customer',
      onDelete: 'SET NULL',
    });

    Requirement.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onDelete: 'SET NULL',
    });

    Requirement.belongsTo(models.Project, {
      foreignKey: 'subProjectId',
      as: 'subProject',
      onDelete: 'SET NULL',
    });
  };

  return Requirement;
};
