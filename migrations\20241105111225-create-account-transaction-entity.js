'use strict';

const { defaultStatus, transactionType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('AccountTransaction', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      accountId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Account',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      transactionType: {
        type: Sequelize.ENUM(transactionType.getTransactionTypeArray()),
        allowNull: false,
        defaultValue: transactionType.CREDIT,
      },
      amount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: false,
      },
      depositAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: false,
      },
      withdrawalAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: false,
      },
      balanceAmount: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: defaultStatus.PENDING,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      invoiceId: {
        type: Sequelize.BIGINT,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.NOW,
        onUpdate: Sequelize.NOW,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('AccountTransaction');
  },
};
