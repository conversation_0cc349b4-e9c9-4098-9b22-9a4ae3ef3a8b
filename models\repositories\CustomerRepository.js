const {
  Customer,
  Source,
  User,
  ContactPerson,
  Address,
  sequelize,
} = require('..');
const { Op } = require('sequelize');
const {
  errorMessage,
  successMessage,
  usersRoles,
  contactPersonType,
  contactPersonCategory,
} = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.findOne = async (query) => await Customer.findOne(query);
exports.findAll = async (query) => await Customer.findAll(query);

const checkExistenceForEntity = async (
  entity,
  conditions,
  excludeId = null,
  selectFields
) => {
  if (excludeId) {
    conditions.id = { [Op.ne]: excludeId };
  }
  return await checkExistence(entity, conditions, selectFields);
};

const validateCustomerData = async (data, customerId = null, userId = null) => {
  const { email, countryCode, contactNumber, sourceId, subSourceId } = data;
  const normalizedEmail = email ? email.toLowerCase() : null;
  const selectFields = ['id'];

  if (normalizedEmail) {
    const emailConditions = { email: normalizedEmail };
    const emailCheck = await Promise.all([
      checkExistenceForEntity(
        Customer,
        emailConditions,
        customerId,
        selectFields
      ),
      checkExistenceForEntity(User, emailConditions, userId, selectFields),
      checkExistenceForEntity(
        ContactPerson,
        emailConditions,
        null,
        selectFields
      ),
    ]);

    if (emailCheck[0] || emailCheck[1] || emailCheck[2]) {
      return {
        success: false,
        message: errorMessage.EXISTS_USER(`email: ${normalizedEmail}`),
      };
    }
  }

  if (countryCode && contactNumber) {
    const contactConditionsCustomer = { countryCode, contactNumber };
    const contactConditionsUser = { countryCode, mobileNumber: contactNumber };
    const contactConditionsContactPerson = { countryCode, contactNumber };

    const contactCheck = await Promise.all([
      checkExistenceForEntity(
        Customer,
        contactConditionsCustomer,
        customerId,
        selectFields
      ),
      checkExistenceForEntity(
        User,
        contactConditionsUser,
        userId,
        selectFields
      ),
      checkExistenceForEntity(
        ContactPerson,
        contactConditionsContactPerson,
        null,
        selectFields
      ),
    ]);

    if (contactCheck[0] || contactCheck[1] || contactCheck[2]) {
      return {
        success: false,
        message: errorMessage.EXISTS_USER(`contact number: ${contactNumber}`),
      };
    }
  }

  if (sourceId) {
    const existingSource = await checkExistence(
      Source,
      { id: sourceId },
      selectFields
    );
    if (!existingSource) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Source with Id ${sourceId}`),
      };
    }
  }

  if (subSourceId) {
    const existingSubSource = await checkExistence(
      Source,
      { id: subSourceId },
      selectFields
    );
    if (!existingSubSource) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `SubSource with Id ${subSourceId}`
        ),
      };
    }
  }

  return { success: true };
};

const createPrimaryContactPerson = async (
  customer,
  userId,
  createdBy,
  data,
  transaction
) => {
  try {
    const contactPerson = await ContactPerson.create(
      {
        customerId: customer.id,
        userId,
        createdBy,
        contactCategory: contactPersonCategory.INDIVIDUAL,
        contactType: contactPersonType.PRIMARY,
        firstName: customer.firstName,
        lastName: customer.lastName,
        email: data.email,
        contactNumber: data.contactNumber,
        countryCode: data.countryCode,
      },
      { transaction }
    );

    return contactPerson;
  } catch (error) {
    throw error;
  }
};

exports.validateAndCreateCustomer = async (data, loggedInUser) => {
  const { email } = data;
  const normalizedEmail = email.toLowerCase();
  const transaction = await sequelize.transaction();
  try {
    const validationResponse = await validateCustomerData(data);
    if (!validationResponse.success) {
      return validationResponse;
    }

    data.email = normalizedEmail;
    data.createdBy = loggedInUser.id;
    data.organizationId = loggedInUser.currentOrganizationId;

    const createdUser = await User.create(
      {
        ...data,
        role: usersRoles.CUSTOMER,
        mobileNumber: data.contactNumber,
      },
      { transaction }
    );

    data.userId = createdUser.id;
    const createdCustomer = await Customer.create(data, { transaction });

    // Create primary contact person
    await createPrimaryContactPerson(
      createdCustomer,
      createdUser.id,
      loggedInUser.id,
      data,
      transaction
    );

    await transaction.commit();

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Customer'),
      data: createdCustomer,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.validateAndUpdateCustomer = async (customerId, data) => {
  const transaction = await sequelize.transaction();
  try {
    const customer = await Customer.findOne({
      where: { id: customerId },
      include: [
        { model: User, as: 'user' },
        { model: ContactPerson, as: 'contactPerson' },
      ],
      transaction,
    });
    if (!customer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Customer with the given ID ${customerId}`
        ),
      };
    }

    const validationResponse = await validateCustomerData(
      data,
      customerId,
      customer.user?.id
    );
    if (!validationResponse.success) {
      return validationResponse;
    }

    if (customer.user) {
      const user = customer.user;
      if (data.email) user.email = data.email.toLowerCase();
      if (data.contactNumber) user.mobileNumber = data.contactNumber;
      Object.assign(user, data);
      await user.save({ transaction });
    }

    // Update contact person details
    if (customer.contactPerson) {
      const contactPerson = customer.contactPerson;
      if (data.email) contactPerson.email = data.email;
      if (data.contactNumber) contactPerson.contactNumber = data.contactNumber;
      if (data.countryCode) contactPerson.countryCode = data.countryCode;
      if (data.firstName) contactPerson.firstName = data.firstName;
      if (data.lastName) contactPerson.lastName = data.lastName;
      await contactPerson.save({ transaction });
    }

    Object.assign(customer, data);
    const updatedCustomer = await customer.save({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Customer'),
      data: updatedCustomer,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.updateCustomerStatus = async (customerId, data) => {
  const { status } = data;
  try {
    const customer = await checkExistence(Customer, { id: customerId }, ['id']);
    if (!customer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Customer with the given ID ${customerId}`
        ),
      };
    }

    customer.status = status;
    await customer.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Customer status'),
      data: customer,
    };
  } catch (error) {
    console.error(error);
    return {
      success: false,
      message: 'Error updating customer status',
    };
  }
};

exports.validateAndDeleteCustomer = async (contactPersonId) => {
  const transaction = await sequelize.transaction();
  try {
    const customer = await Customer.findByPk(contactPersonId, {
      transaction,
      include: [
        {
          model: User,
          as: 'user',
        },
        {
          model: ContactPerson,
          as: 'contactPersons',
          include: [
            {
              model: Address,
              as: 'address',
            },
            {
              model: User,
              as: 'user',
            },
          ],
        },
      ],
    });

    if (!customer) {
      await transaction.rollback();
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Customer with id : ${contactPersonId}`
        ),
      };
    }

    if (customer.contactPersons) {
      for (let contactPerson of customer.contactPersons) {
        if (contactPerson.address) {
          await contactPerson.address.destroy({ transaction });
        }
        if (contactPerson.user) {
          await contactPerson.user.destroy({ transaction });
        }
        await contactPerson.destroy({ transaction });
      }
    }

    if (customer.user) {
      await customer.user.destroy({ transaction });
    }
    await customer.destroy({ transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.REMOVED_SUCCESS_MESSAGE('Customer'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};
