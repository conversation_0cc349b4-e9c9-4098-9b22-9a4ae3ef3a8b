'use strict';

/** @type {import('sequelize-cli').Migration} */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'DocumentRequire',
      'DocumentRequire_designationId_fkey'
    );
    await queryInterface.addConstraint('DocumentRequire', {
      fields: ['designationId'],
      type: 'foreign key',
      name: 'DocumentRequire_designationId_fkey',
      references: {
        table: 'Designation',
        field: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'DocumentRequire',
      'DocumentRequire_designationId_fkey'
    );
    await queryInterface.addConstraint('DocumentRequire', {
      fields: ['designationId'],
      type: 'foreign key',
      name: 'DocumentRequire_designationId_fkey',
      references: {
        table: 'Designation',
        field: 'id',
      },
      onDelete: 'NO ACTION',
    });
  },
};
