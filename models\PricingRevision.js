module.exports = (sequelize, DataTypes) => {
  const PricingRevision = sequelize.define(
    'PricingRevision',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      startDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      endDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  PricingRevision.associate = (models) => {
    PricingRevision.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    PricingRevision.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });

    PricingRevision.hasMany(models.PricingCharge, {
      foreignKey: 'pricingRevisionId',
      as: 'pricingCharges',
    });
  };

  return PricingRevision;
};
