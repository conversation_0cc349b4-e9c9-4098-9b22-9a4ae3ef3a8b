const sequelize = require('sequelize');
const UserRepository = require('./UserRepository');
const options = require('../../config/options');
const UserHelper = require('../helpers/UserHelper');
const { AccessManagement } = require('..');
const { errorMessage, successMessage } = require('../../config/options');

const { Op } = sequelize;

exports.postAccessManagement = async (body, userId) => {
  try {
    const accessManagement = body.accessManagement.map((element) =>
      Object.assign(element, { userId })
    );
    return await AccessManagement.bulkCreate(accessManagement);
  } catch (error) {
    throw new Error(error);
  }
};

exports.putUpdateAccessManagement = async (body, userId) => {
  try {
    if (body.accessManagement && body.accessManagement.length > 0) {
      for (let i = 0; i < body.accessManagement.length; i += 1) {
        const iterator = body.accessManagement[i];
        if (iterator.id) {
          await AccessManagement.update(
            {
              view: iterator.view,
              add: iterator.add,
              edit: iterator.edit,
              remove: iterator.remove,
            },
            { where: { id: iterator.id } }
          );
        } else {
          iterator.userId = userId;
          await AccessManagement.create(iterator);
        }
      }
    }
  } catch (error) {
    throw new Error(error);
  }
};
exports.createAdmin = async (body) => {
  body.role = options.usersRoles.ADMIN;
  body.status = options.defaultStatus.ACTIVE;
  const newAdmin = await UserRepository.checkAndCreate(body);
  if (!newAdmin.success) {
    return {
      success: false,
      message: newAdmin.message,
    };
  }
  const message = options.successMessage.ADD_SUCCESS_MESSAGE('Staff user');
  return {
    success: true,
    message,
  };
};

exports.updateAdmin = async (body, query) => {
  const existingAdmin = await UserRepository.getUser(query);
  if (!existingAdmin) {
    return {
      success: false,
      message: options.errorMessage.DOES_NOT_EXIST('Admin'),
    };
  }

  if (body.mobileNumber) {
    const duplicateUser = await UserRepository.checkDuplicateUserByMobileNumber(
      existingAdmin,
      body
    );

    if (!duplicateUser.success) {
      return duplicateUser;
    }
  }

  const adminFieldsToUpdate = {
    firstName: body.firstName,
    lastName: body.lastName,
    profilePicture: body.profilePicture,
    countryCode: body.countryCode,
    locationId: body.locationId,
    mobileNumber: body.mobileNumber,
    designationId: body.designationId,
  };

  const { success, data, message } = await UserRepository.updateUser(
    query,
    adminFieldsToUpdate
  );

  if (!success) {
    return {
      success,
      message,
    };
  }
  return {
    success,
    data,
    message,
  };
};

exports.changePassword = async (body, query) => {
  const existingUser = await UserRepository.getUser(query);
  if (!existingUser) {
    return {
      success: false,
      message: options.errorMessage.DOES_NOT_EXIST('Admin'),
    };
  }
  existingUser.password = await UserHelper.generatePassword(body.password);
  await existingUser.save();
  return {
    success: true,
    message: successMessage.SAVED_SUCCESS_MESSAGE('Password'),
  };
};
exports.updateProfile = async (existingData, body) => {
  try {
    existingData.firstName = body.firstName;
    existingData.lastName = body.lastName;
    existingData.profilePicture = body.profilePicture;
    await existingData.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Profile'),
      data: existingData,
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.verifyOtp = async (body, user, isEmail = false) => {
  try {
    const query = {
      where: {
        tempOtp: body.tempOtp,
        tempOtpExpiresAt: { [Op.gte]: new Date() },
        email: user.email,
      },
    };
    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }

    existingUser.tempOtp = null;
    existingUser.lastSignInAt = new Date();
    existingUser.tempOtpExpiresAt = null;
    if (isEmail) {
      existingUser.isEmailVerified = true;
    }

    await existingUser.save();
    return {
      success: true,
      message: successMessage.OTP_VERIFIED(),
    };
  } catch (e) {
    throw new Error(e);
  }
};
