module.exports = (sequelize, DataTypes) => {
  const OrganizationBrandMedia = sequelize.define(
    'OrganizationBrandMedia',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileName: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      tableName: 'OrganizationBrandMedia',
      timestamps: true,
    }
  );

  OrganizationBrandMedia.associate = function (models) {
    OrganizationBrandMedia.belongsTo(models.OrganizationBrand, {
      foreignKey: 'organizationBrandId',
      as: 'organizationBrand',
    });
  };

  return OrganizationBrandMedia;
};
