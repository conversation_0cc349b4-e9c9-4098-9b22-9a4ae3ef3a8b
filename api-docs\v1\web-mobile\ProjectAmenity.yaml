
paths:
  /project/amenity:
    post:
      tags:
        - Project
      summary: "Create a new Amenity"
      description: "This endpoint allows you to register a new Amenity by providing a name."
      operationId: "CreateAmenity"
      requestBody:
        description: "The name of the new Amenity to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/amenity-create"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Amenity has been created successfully."
        "400":
          description: "Invalid input data or amenity already exists."
        "500":
          description: "Internal Server Error"

  /project/amenity/{id}:
    put:
      tags:
        - Project
      summary: "Update an existing Amenity"
      description: "This endpoint allows you to update the name of an existing Amenity by providing its id and new name."
      operationId: "UpdateAmenity"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the Amenity to be updated."
          schema:
            type: integer
            example: 1 
      requestBody:
        description: "The updated information for the Amenity."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/amenity-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Amenity has been updated successfully."
        "400":
          description: "Invalid input data, amenity not found, or amenity already exists."
        "404":
          description: "Amenity not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    amenity-create:
      type: object
      properties:
        name:
          type: string
          description: "The name of the amenity"
          example: "Balconies"
        logo:
          type: string
          description: "The logo URL or file path for the amenity (optional)."
          example: "https://example.com/logo.png"
      required:
        - name        
    amenity-update:
      type: object
      properties:
        name:
          type: string
          description: "The new name of the amenity."
          example: "Jacuzzi"
        logo:
          type: string
          description: "The logo URL or file path for the amenity (optional)."
          example: "https://example.com/logo.png"

