paths:
  /task/follower:
    post:
      summary: Add a follower for Task
      description: Add a follower for a existing task
      operationId: addFollower
      tags:
        - Task
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addFollower"
      responses:
        "201":
          description: Follower created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/{id}/{userId}/follower:
    delete:
      summary: Delete a Follower
      description: Delete Follower if you dont need it
      operationId: deleteFollower
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of task
        - name: userId
          in: path
          required: true
          schema:
            type: integer
          description: ID of User
      responses:
        "201":
          description: Follower deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    addFollower:
      type: object
      required:
        - taskId
        - userId
      properties:
        taskId:
          type: integer
          example: 1
          description: "The id of task for the follower"
        userId:
          type: integer
          example: 1
          description: "The id of user for the follower"
        

  