'use strict';

const OPTIONS = require('../config/options');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_GrnItem_status" ADD VALUE IF NOT EXISTS 'pending';
    `);

    await queryInterface.sequelize.query(`
      UPDATE "GrnItem" SET status = 'pending' WHERE status IS NULL;
    `);

    await queryInterface.changeColumn('GrnItem', 'status', {
      type: Sequelize.ENUM(OPTIONS.grnItemStatus.getValues()),
      allowNull: false,
      defaultValue: OPTIONS.grnItemStatus.PENDING,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('GrnItem', 'status', {
      type: Sequelize.ENUM(OPTIONS.grnItemStatus.getValues()),
      allowNull: true,
    });
  },
};
