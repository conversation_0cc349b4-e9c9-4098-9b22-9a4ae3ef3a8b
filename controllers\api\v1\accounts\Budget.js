const { resCode, genRes, errorMessage } = require('@config/options');
const BudgetRepository = require('@models/repositories/BudgetRepository');

exports.createBudget = async (req, res) => {
  try {
    const { user } = req;

    const { success, message, data } = await BudgetRepository.CreateBudget({
      ...req.body,
      createdBy: user.id,
    });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getBudgetList = async (req, res) => {
  try {
    const {
      params: { organizationId, search, start, limit },
      user,
    } = req;

    const objParamsForGetBudgetList = {
      organizationId,
      search,
      start,
      limit,
      createdBy: user.id,
    };

    const { success, message, data } = await BudgetRepository.getBudgetList(
      objParamsForGetBudgetList
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getBudgetById = async (req, res) => {
  try {
    const {
      params: { budgetId },
    } = req;

    const objParamsForGetBudgetById = {
      budgetId,
    };

    const { success, message, data } = await BudgetRepository.getBudgetList(
      objParamsForGetBudgetById
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteBudget = async (req, res) => {
  try {
    const {
      params: { budgetId },
    } = req;

    const { success, message, data } =
      await BudgetRepository.deleteBudget(budgetId);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateBudgetDetails = async (req, res) => {
  try {
    const {
      user,
      params: { budgetId },
    } = req;

    const { success, message, data } = await BudgetRepository.updateBudget({
      ...req.body,
      budgetId,
      createdBy: user.id,
    });

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
