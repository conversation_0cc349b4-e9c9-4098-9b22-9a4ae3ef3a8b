paths:
  /material/item:
    post:
      tags:
        - "Material"
      summary: "Create a new item"
      description: "This endpoint allows you to create a new item in the system"
      operationId: "CreateItem"
      requestBody:
        description: "Item creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateItemRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Item created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/item/{id}/clone:
    post:
      tags:
        - "Material"
      summary: "Clone an existing item"
      description: "This endpoint allows you to clone an existing item in the system"
      operationId: "CloneItem"
      parameters:
        - $ref: "#/components/parameters/ItemIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Item cloned successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Item not found"
        "500":
          description: "Internal Server Error"

  /material/item/{id}:
    put:
      tags:
        - "Material"
      summary: "Update an existing item"
      description: "This endpoint allows you to update an existing item in the system"
      operationId: "UpdateItem"
      parameters:
        - $ref: "#/components/parameters/ItemIdParam"
      requestBody:
        description: "Item update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateItemRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Item updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Item not found"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing item"
      description: "This endpoint allows you to update the status of an existing item in the system"
      operationId: "UpdateItemStatus"
      parameters:
        - $ref: "#/components/parameters/ItemIdParam"
      requestBody:
        description: "Item status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateItemStatusRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Item status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Item not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing item"
      description: "This endpoint allows you to delete an existing item in the system"
      operationId: "DeleteItem"
      parameters:
        - $ref: "#/components/parameters/ItemIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Item deleted successfully"
        "404":
          description: "Item not found"
        "500":
          description: "Internal Server Error"

  /material/item/media/{id}:
    delete:
      tags:
        - "Material"
      summary: "Delete item media"
      description: "This endpoint allows you to delete media associated with an existing item in the system"
      operationId: "DeleteItemMedia"
      parameters:
        - $ref: "#/components/parameters/ItemMediaIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Media deleted successfully"
        "404":
          description: "Media not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateItemRequest:
      type: object
      properties:
        name:
          type: string
          example: "Laptop"
        categoryId:
          type: integer
          example: 10
        sku:
          type: string
          example: "LAP123"
        unitOfMeasurement:
          type: string
          enum:
            - box
            - cm
            - dz
            - ft
            - g
            - in
            - kg
            - km
            - lb
            - mg
            - ml
            - m
            - pcs
          example: "box"
        hsn:
          type: string
          example: "1234"
        # taxRate:
        #   type: string
        #   example: "18%"
        taxIds:
          type: array
          items:
            type: integer
          description: "List of tax IDs associated with the item"
          example: [1, 2]
        reOrderPoint:
          type: integer
          example: 10
        reOrderPointScope:
          type: string
          enum:
            - all_warehouses
            - each_warehouse
          example: "all_warehouses"
        description:
          type: string
          example: "High-end gaming laptop"
        media:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5
        # variant:
        #   type: array
        #   items:
        #     type: object
        #     properties:
        #       attribute:
        #         type: string
        #         enum:
        #           - colour
        #           - size
        #           - dimension
        #           - material
        #           - grade
        #           - weight
        #           - finish
        #           - usage
        #           - packaging
        #           - type
        #           - wattage
        #           - pattern
        #           - design
        #         example: "colour"
        #       name:
        #         type: string
        #         example: "Red"
        #       sku:
        #         type: string
        #         example: "LAP123-RED"
        #       reOrderPoint:
        #         type: integer
        #         example: 5
        #       reOrderPointScope:
        #         type: string
        #         enum:
        #           - all_warehouses
        #           - each_warehouse
        #         example: "each_warehouse"
        #       media:
        #         type: array
        #         items:
        #           type: object
        #           properties:
        #             fileName:
        #               type: string
        #               example: "image.jpg"
        #             fileType:
        #               type: string
        #               example: "image/jpeg"
        #             filePath:
        #               type: string
        #               example: "/uploads/image.jpg"
        #             fileSize:
        #               type: number
        #               example: 1024.5
      required:
        - name
        - categoryId
        - unitOfMeasurement
        - taxRate

    UpdateItemRequest:
      type: object
      properties:
        name:
          type: string
          example: "Laptop"
        categoryId:
          type: integer
          example: 10
        sku:
          type: string
          example: "LAP123"
        unitOfMeasurement:
          type: string
          enum:
            - box
            - cm
            - dz
            - ft
            - g
            - in
            - kg
            - km
            - lb
            - mg
            - ml
            - m
            - pcs
          example: "box"
        hsn:
          type: string
          example: "1234"
        taxRate:
          # type: string
          # example: "18%"
        taxIds:
          type: array
          items:
            type: integer
          description: "List of tax IDs associated with the item"
          example: [1, 2]
        reOrderPoint:
          type: integer
          example: 10
        reOrderPointScope:
          type: string
          enum:
            - all_warehouses
            - each_warehouse
          example: "all_warehouses"
        description:
          type: string
          example: "High-end gaming laptop"
        media:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5
        # variant:
        #   type: array
        #   items:
        #     type: object
        #     properties:
        #       attribute:
        #         type: string
        #         enum:
        #           - colour
        #           - size
        #           - dimension
        #           - material
        #           - grade
        #           - weight
        #           - finish
        #           - usage
        #           - packaging
        #           - type
        #           - wattage
        #           - pattern
        #           - design
        #         example: "colour"
        #       name:
        #         type: string
        #         example: "Red"
        #       sku:
        #         type: string
        #         example: "LAP123-RED"
        #       reOrderPoint:
        #         type: integer
        #         example: 5
        #       reOrderPointScope:
        #         type: string
        #         enum:
        #           - all_warehouses
        #           - each_warehouse
        #         example: "each_warehouse"
        #       media:
        #         type: array
        #         items:
        #           type: object
        #           properties:
        #             fileName:
        #               type: string
        #               example: "image.jpg"
        #             fileType:
        #               type: string
        #               example: "image/jpeg"
        #             filePath:
        #               type: string
        #               example: "/uploads/image.jpg"
        #             fileSize:
        #               type: number
        #               example: 1024.5

    UpdateItemStatusRequest:
      type: object
      properties:
        status:
          type: string
          enum:
            - active
            - in_active
            - draft
            - available
            - out_of_stock
            - low_inventory
            - archived
            - order_placed
          example: "active"
      required:
        - status

  parameters:
    ItemIdParam:
      name: "id"
      in: "path"
      description: "ID of the item to be managed"
      required: true
      schema:
        type: string

    ItemMediaIdParam:
      name: "id"
      in: "path"
      description: "ID of the item media to be deleted"
      required: true
      schema:
        type: integer