const { subTaskStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const SubTasks = sequelize.define(
    'SubTasks',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(subTaskStatus.getValues()),
        allowNull: false,
        defaultValue: subTaskStatus.PENDING,
      },
      startDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      dueDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  SubTasks.associate = (models) => {
    SubTasks.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'task',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  };

  return SubTasks;
};
