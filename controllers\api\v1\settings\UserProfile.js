const { genRes, errorMessage, resCode } = require('@config/options');
const SettingProfileRepository = require('@models/repositories/SettingProfileRepository');

exports.updateUserProfile = async (req, res) => {
  const { id: userId } = req.params;
  try {
    const { success, message, data } =
      await SettingProfileRepository.updateUserProfile(userId, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
