'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // First, modify the column to remove enum constraint temporarily
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status TYPE VARCHAR(255) USING status::VARCHAR;'
    );

    // Now we can safely drop the old enums
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_indent_status";'
    );
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Indent_status";'
    );

    // Create new enum type
    await queryInterface.sequelize
      .query(`CREATE TYPE "enum_Indent_status" as enum (
      'draft',
      'pending_approval',
      'escalated',
      'approved',
      'rejected',
      'in_progress',
      'delayed',
      'fulfilled',
      'cancelled'
    );`);

    // Update existing data to match new enum values
    await queryInterface.sequelize.query(`
      UPDATE "Indent"
      SET status = CASE
        WHEN status = 'pending' THEN 'pending_approval'
        WHEN status = 'due_soon' THEN 'in_progress'
        WHEN status = 'ongoing' THEN 'in_progress'
        WHEN status = 'draft' THEN 'draft'
        WHEN status = 'delayed' THEN 'delayed'
        ELSE 'draft'
      END;
    `);

    // Change column back to enum type with new default
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status TYPE "enum_Indent_status" USING status::"enum_Indent_status";'
    );
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status SET DEFAULT \'pending_approval\';'
    );
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status SET NOT NULL;'
    );
  },

  async down(queryInterface, Sequelize) {
    // First, modify the column to remove enum constraint
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status TYPE VARCHAR(255) USING status::VARCHAR;'
    );

    // Update existing data to match old enum values
    await queryInterface.sequelize.query(`
      UPDATE "Indent"
      SET status = CASE
        WHEN status = 'pending_approval' THEN 'pending'
        WHEN status = 'in_progress' THEN 'ongoing'
        WHEN status = 'draft' THEN 'draft'
        WHEN status = 'delayed' THEN 'delayed'
        ELSE 'draft'
      END;
    `);

    // Drop the enum type
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Indent_status";'
    );

    // Create the old enum type
    await queryInterface.sequelize
      .query(`CREATE TYPE "enum_Indent_status" as enum (
      'draft',
      'pending',
      'ongoing',
      'delayed',
      'due_soon'
    );`);

    // Change column back to enum type
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status TYPE "enum_Indent_status" USING status::"enum_Indent_status";'
    );

    // Set the default and not null constraint
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status SET DEFAULT \'pending\';'
    );
    await queryInterface.sequelize.query(
      'ALTER TABLE "Indent" ALTER COLUMN status SET NOT NULL;'
    );
  },
};
