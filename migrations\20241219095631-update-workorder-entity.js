module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('WorkOrder', 'contractorId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Contractor',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addColumn('WorkOrder', 'customDetails', {
      type: Sequelize.JSON,
      allowNull: true,
      defaultValue: null,
    });

    await queryInterface.addColumn('WorkOrder', 'termsAndCondition', {
      type: Sequelize.TEXT,
      allowNull: true,
      defaultValue: null,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('WorkOrder', 'contractorId');
    await queryInterface.removeColumn('WorkOrder', 'customDetails');
    await queryInterface.removeColumn('WorkOrder', 'termsAndCondition');
  },
};
