'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('WarehouseItem', 'itemVariantId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'ItemVariant',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('WarehouseItem', 'itemVariantId');
  },
};
