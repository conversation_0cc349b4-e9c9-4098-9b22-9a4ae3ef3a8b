# Database Architecture - Bricko Core Service

## Overview

The Bricko Core Service is a comprehensive real estate and construction management system with the following main modules:

- User Management & Authentication
- Organization Management
- Project Management
- Task Management
- CRM (Customer Relationship Management)
- Inventory & Material Management
- Financial Management
- Document Management
- Work Order Management
- Employee Management
- BOQ (Bill of Quantities) Management

## Database Schema

### 1. Core User & Organization Management

#### Users Table

```sql
Users (
  id (PK),
  role,
  countryCode,
  mobileNumber,
  email,
  personalEmail,
  firstName,
  middleName,
  lastName,
  password,
  tempOtp,
  tempOtpExpiresAt,
  isMobileNumberVerified,
  isEmailVerified,
  lastSignInAt,
  currentSignInIpAddress,
  registrationPlatform,
  status,
  profilePicture,
  isFromAdmin,
  designationId (FK -> Designations),
  dateOfBirth,
  gender,
  alternateCountryCode,
  alternateMobileNumber,
  about,
  maritalStatus,
  currentOrganizationId (FK -> Organizations),
  panNumber,
  isMfaEnabled,
  isBusiness,
  businessName,
  resetPasswordToken,
  resetPasswordTokenExpiry,
  createdAt,
  updatedAt
)
```

#### Organizations Table

```sql
Organizations (
  id (PK),
  name,
  type,
  countryCode,
  mobile,
  country,
  about,
  website,
  panNumber,
  tanNumber,
  dateFormat,
  measurementUnits,
  gstinNumber,
  gstinDocument,
  address,
  state,
  city,
  pincode,
  timeFormat,
  timeZone,
  numberFormat,
  currency,
  isPrimary,
  logo,
  workspaceId (FK -> Workspaces),
  createdAt,
  updatedAt
)
```

#### UserOrganizations Table (Junction)

```sql
UserOrganizations (
  id (PK),
  userId (FK -> Users),
  organizationId (FK -> Organizations),
  role,
  status,
  createdAt,
  updatedAt
)
```

#### Departments Table

```sql
Departments (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### Designations Table

```sql
Designations (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### Addresses Table

```sql
Addresses (
  id (PK),
  addressType,
  city,
  state,
  pincode,
  country,
  address,
  addressLine2,
  landmark,
  latitude,
  longitude,
  userId (FK -> Users),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

### 2. Project Management

#### Projects Table

```sql
Projects (
  id (PK),
  name,
  logo,
  projectCode,
  reraNumber,
  startDate,
  endDate,
  createdBy (FK -> Users),
  about,
  status,
  description,
  featuresAndSpecifications,
  additionalFields (JSONB),
  parentProjectId (FK -> Projects),
  projectTypeId (FK -> ProjectTypes),
  organizationId (FK -> Organizations),
  addressId (FK -> Addresses),
  createdAt,
  updatedAt
)
```

#### ProjectTypes Table

```sql
ProjectTypes (
  id (PK),
  name,
  description,
  structureType,
  projectCategory,
  createdAt,
  updatedAt
)
```

#### ProjectTeams Table (Junction)

```sql
ProjectTeams (
  id (PK),
  projectId (FK -> Projects),
  userId (FK -> Users),
  role,
  createdAt,
  updatedAt
)
```

#### ProjectMedia Table

```sql
ProjectMedia (
  id (PK),
  projectId (FK -> Projects),
  mediaType,
  mediaUrl,
  createdAt,
  updatedAt
)
```

#### Floors Table

```sql
Floors (
  id (PK),
  name,
  floorNumber,
  floorType,
  orderIndex,
  projectId (FK -> Projects),
  unitId (FK -> Units),
  createdAt,
  updatedAt
)
```

#### Units Table

```sql
Units (
  id (PK),
  isSaleable,
  isNamingFormat,
  name,
  superBuiltUpArea,
  builtUpArea,
  carpetArea,
  facing,
  description,
  additionalFields (JSONB),
  plotArea,
  roadTangent,
  remainingArea,
  proRataFsiFactor,
  zoning,
  basicFsi,
  permissibleFsi,
  projectId (FK -> Projects),
  unitTypeId (FK -> UnitTypes),
  floorId (FK -> Floors),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### UnitTypes Table

```sql
UnitTypes (
  id (PK),
  name,
  description,
  createdAt,
  updatedAt
)
```

#### Amenities Table

```sql
Amenities (
  id (PK),
  name,
  description,
  logo,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### ProjectAmenities Table (Junction)

```sql
ProjectAmenities (
  id (PK),
  projectId (FK -> Projects),
  amenityId (FK -> Amenities),
  createdAt,
  updatedAt
)
```

#### UnitAmenities Table (Junction)

```sql
UnitAmenities (
  id (PK),
  unitId (FK -> Units),
  amenityId (FK -> Amenities),
  createdAt,
  updatedAt
)
```

### 3. Task Management

#### Tasks Table

```sql
Tasks (
  id (PK),
  title,
  description,
  status,
  startDate,
  endDate,
  projectId (FK -> Projects),
  assignedTo (FK -> Users),
  assignedBy (FK -> Users),
  createdBy (FK -> Users),
  updatedBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### SubTasks Table

```sql
SubTasks (
  id (PK),
  title,
  description,
  status,
  taskId (FK -> Tasks),
  createdAt,
  updatedAt
)
```

#### TaskTags Table

```sql
TaskTags (
  id (PK),
  name,
  color,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### TaskTagMapping Table (Junction)

```sql
TaskTagMapping (
  id (PK),
  taskId (FK -> Tasks),
  tagId (FK -> TaskTags),
  createdAt,
  updatedAt
)
```

#### TaskUnitMapping Table (Junction)

```sql
TaskUnitMapping (
  id (PK),
  taskId (FK -> Tasks),
  unitId (FK -> Units),
  createdAt,
  updatedAt
)
```

#### TaskFollowers Table (Junction)

```sql
TaskFollowers (
  id (PK),
  taskId (FK -> Tasks),
  userId (FK -> Users),
  createdAt,
  updatedAt
)
```

#### TaskDependency Table

```sql
TaskDependency (
  id (PK),
  taskId (FK -> Tasks),
  dependentTaskId (FK -> Tasks),
  dependencyType,
  createdAt,
  updatedAt
)
```

#### TaskComments Table

```sql
TaskComments (
  id (PK),
  comment,
  taskId (FK -> Tasks),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### TaskDocuments Table

```sql
TaskDocuments (
  id (PK),
  taskId (FK -> Tasks),
  documentId (FK -> Documents),
  createdAt,
  updatedAt
)
```

### 4. CRM (Customer Relationship Management)

#### Customers Table

```sql
Customers (
  id (PK),
  type,
  firstName,
  lastName,
  email,
  countryCode,
  contactNumber,
  profilePicture,
  businessName,
  status,
  isArchived,
  sourceId (FK -> Sources),
  subSourceId (FK -> Sources),
  createdBy (FK -> Users),
  organizationId (FK -> Organizations),
  userId (FK -> Users),
  createdAt,
  updatedAt
)
```

#### Sources Table

```sql
Sources (
  id (PK),
  name,
  description,
  userId (FK -> Users),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### ContactPersons Table

```sql
ContactPersons (
  id (PK),
  firstName,
  lastName,
  email,
  phone,
  designation,
  customerId (FK -> Customers),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### Requirements Table

```sql
Requirements (
  id (PK),
  title,
  description,
  status,
  budget,
  timeline,
  customerId (FK -> Customers),
  projectId (FK -> Projects),
  subProjectId (FK -> Projects),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### TeamAssignments Table

```sql
TeamAssignments (
  id (PK),
  customerId (FK -> Customers),
  userId (FK -> Users),
  role,
  assignedBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### Notes Table

```sql
Notes (
  id (PK),
  title,
  content,
  customerId (FK -> Customers),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

### 5. Inventory & Material Management

#### Items Table

```sql
Items (
  id (PK),
  name,
  sku,
  unitOfMeasurement,
  hsn,
  taxRate,
  reOrderPoint,
  reOrderPointScope,
  description,
  status,
  createdBy (FK -> Users),
  organizationId (FK -> Organizations),
  categoryId (FK -> ItemCategories),
  createdAt,
  updatedAt
)
```

#### ItemCategories Table

```sql
ItemCategories (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### ItemVariants Table

```sql
ItemVariants (
  id (PK),
  itemId (FK -> Items),
  variantName,
  sku,
  price,
  cost,
  stock,
  createdAt,
  updatedAt
)
```

#### ItemMedia Table

```sql
ItemMedia (
  id (PK),
  itemId (FK -> Items),
  mediaType,
  mediaUrl,
  createdAt,
  updatedAt
)
```

#### Warehouses Table

```sql
Warehouses (
  id (PK),
  name,
  address,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### WarehouseItems Table

```sql
WarehouseItems (
  id (PK),
  warehouseId (FK -> Warehouses),
  itemId (FK -> Items),
  quantity,
  createdAt,
  updatedAt
)
```

#### Indents Table

```sql
Indents (
  id (PK),
  indentNumber,
  status,
  requestedBy (FK -> Users),
  approvedBy (FK -> Users),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### IndentItems Table

```sql
IndentItems (
  id (PK),
  indentId (FK -> Indents),
  itemId (FK -> Items),
  quantity,
  description,
  createdAt,
  updatedAt
)
```

#### GRNs Table (Goods Received Notes)

```sql
GRNs (
  id (PK),
  grnNumber,
  status,
  receivedBy (FK -> Users),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### GrnItems Table

```sql
GrnItems (
  id (PK),
  grnId (FK -> GRNs),
  itemId (FK -> Items),
  quantity,
  price,
  createdAt,
  updatedAt
)
```

#### GrnMedia Table

```sql
GrnMedia (
  id (PK),
  grnId (FK -> GRNs),
  mediaType,
  mediaUrl,
  createdAt,
  updatedAt
)
```

#### StockAdjustments Table

```sql
StockAdjustments (
  id (PK),
  adjustmentNumber,
  type,
  reason,
  organizationId (FK -> Organizations),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### StockAdjustmentItems Table

```sql
StockAdjustmentItems (
  id (PK),
  stockAdjustmentId (FK -> StockAdjustments),
  itemId (FK -> Items),
  quantity,
  createdAt,
  updatedAt
)
```

### 6. Financial Management

#### Accounts Table

```sql
Accounts (
  id (PK),
  debitAmount,
  creditAmount,
  currantBalanceAmount,
  userId (FK -> Users),
  createdAt,
  updatedAt
)
```

#### Budgets Table

```sql
Budgets (
  id (PK),
  name,
  amount,
  startDate,
  endDate,
  organizationId (FK -> Organizations),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### BudgetEntries Table

```sql
BudgetEntries (
  id (PK),
  budgetId (FK -> Budgets),
  amount,
  type,
  description,
  createdAt,
  updatedAt
)
```

#### Expenses Table

```sql
Expenses (
  id (PK),
  title,
  amount,
  date,
  category,
  description,
  organizationId (FK -> Organizations),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### Journals Table

```sql
Journals (
  id (PK),
  journalNumber,
  date,
  description,
  organizationId (FK -> Organizations),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### TransactionEntries Table

```sql
TransactionEntries (
  id (PK),
  journalId (FK -> Journals),
  accountId (FK -> Accounts),
  debitAmount,
  creditAmount,
  description,
  createdAt,
  updatedAt
)
```

#### Quotations Table

```sql
Quotations (
  id (PK),
  quotationCode,
  expireDate,
  pricingRevision,
  pricingRevisionTotalAmount,
  paymentPlan,
  brokerPaymentPlan,
  paymentPlanTotalAmount,
  paymentPlanAgreementAmount,
  paymentPlanBalance,
  homeLoanRequired,
  totalBrokerageAmount,
  status,
  saleAgentId (FK -> Users),
  brokerAdditionTerm,
  termsAndCondition,
  holdPropertyUntilExpiry,
  unitId (FK -> Units),
  customerId (FK -> Customers),
  projectId (FK -> Projects),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### QuotationUnitCosts Table

```sql
QuotationUnitCosts (
  id (PK),
  quotationId (FK -> Quotations),
  costType,
  amount,
  description,
  createdAt,
  updatedAt
)
```

#### QuotationPaymentPlans Table

```sql
QuotationPaymentPlans (
  id (PK),
  quotationId (FK -> Quotations),
  stage,
  percentage,
  amount,
  dueDate,
  createdAt,
  updatedAt
)
```

#### ProjectBookedUnits Table

```sql
ProjectBookedUnits (
  id (PK),
  unitId (FK -> Units),
  quotationId (FK -> Quotations),
  bookingDate,
  status,
  createdAt,
  updatedAt
)
```

### 7. Document Management

#### Documents Table

```sql
Documents (
  id (PK),
  name,
  description,
  isFolder,
  basePath,
  fileType,
  fileSize,
  filePath,
  fileName,
  status,
  isDefault,
  projectId (FK -> Projects),
  parentFolderId (FK -> Documents),
  createdBy (FK -> Users),
  updatedBy (FK -> Users),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### DocumentPermissions Table

```sql
DocumentPermissions (
  id (PK),
  documentId (FK -> Documents),
  userId (FK -> Users),
  permission,
  createdAt,
  updatedAt
)
```

#### DocumentActivityLogs Table

```sql
DocumentActivityLogs (
  id (PK),
  documentId (FK -> Documents),
  action,
  description,
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### FavoritesDocuments Table

```sql
FavoritesDocuments (
  id (PK),
  documentId (FK -> Documents),
  userId (FK -> Users),
  createdAt,
  updatedAt
)
```

#### Drawings Table

```sql
Drawings (
  id (PK),
  name,
  drawingNumber,
  revision,
  filePath,
  projectId (FK -> Projects),
  unitId (FK -> Units),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

### 8. Work Order Management

#### WorkOrders Table

```sql
WorkOrders (
  id (PK),
  workOrderNumber,
  fromDate,
  toDate,
  status,
  customDetails (JSON),
  termsAndCondition,
  activities (JSON),
  workOrderValue,
  projectId (FK -> Projects),
  workOrderType (FK -> WorkOrderTypes),
  createdBy (FK -> Users),
  organizationId (FK -> Organizations),
  contractorId (FK -> Contractors),
  createdAt,
  updatedAt
)
```

#### WorkOrderTypes Table

```sql
WorkOrderTypes (
  id (PK),
  name,
  description,
  createdAt,
  updatedAt
)
```

#### WorkOrderBOQMappings Table

```sql
WorkOrderBOQMappings (
  id (PK),
  workOrderId (FK -> WorkOrders),
  boqEntryId (FK -> BoqEntries),
  quantity,
  rate,
  amount,
  createdAt,
  updatedAt
)
```

### 9. Employee Management

#### Employees Table

```sql
Employees (
  id (PK),
  employeeCode,
  dateOfJoining,
  maritalStatus,
  userId (FK -> Users),
  organizationId (FK -> Organizations),
  reportedTo (FK -> Users),
  createdAt,
  updatedAt
)
```

#### EmployeeTemplates Table

```sql
EmployeeTemplates (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### EmployeeTemplateItems Table

```sql
EmployeeTemplateItems (
  id (PK),
  employeeTemplateId (FK -> EmployeeTemplates),
  itemName,
  itemType,
  monthlyAmount,
  calculationType,
  createdAt,
  updatedAt
)
```

#### EmployeeLeaves Table

```sql
EmployeeLeaves (
  id (PK),
  userId (FK -> Users),
  leaveType,
  startDate,
  endDate,
  reason,
  status,
  approvedBy (FK -> Users),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### EmployeeDocuments Table

```sql
EmployeeDocuments (
  id (PK),
  userId (FK -> Users),
  documentType,
  documentNumber,
  issueDate,
  expiryDate,
  filePath,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### Attendances Table

```sql
Attendances (
  id (PK),
  date,
  inTime,
  outTime,
  status,
  totalDuration,
  description,
  createdBy (FK -> Users),
  updatedBy (FK -> Users),
  createdAt,
  updatedAt
)
```

### 10. BOQ (Bill of Quantities) Management

#### BoqCategories Table

```sql
BoqCategories (
  id (PK),
  name,
  description,
  parentCategoryId (FK -> BoqCategories),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### BoqMetrics Table

```sql
BoqMetrics (
  id (PK),
  name,
  unit,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### BoqEntries Table

```sql
BoqEntries (
  id (PK),
  name,
  description,
  length,
  breadth,
  height,
  total,
  progressPercentage,
  status,
  categoryRate,
  cost (JSON),
  quantity,
  boqCategoryId (FK -> BoqCategories),
  boqSubCategoryId (FK -> BoqCategories),
  boqMetricId (FK -> BoqMetrics),
  createdBy (FK -> Users),
  projectId (FK -> Projects),
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

### 11. Request & Approval Management

#### Requests Table

```sql
Requests (
  id (PK),
  title,
  requestedBy (FK -> Users),
  requestedTo (FK -> Users),
  requestType,
  priority,
  status,
  approvedBy (FK -> Users),
  dueDate,
  notes,
  shortDescription,
  reference (JSONB),
  recordId,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### RequestComments Table

```sql
RequestComments (
  id (PK),
  requestId (FK -> Requests),
  comment,
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### RequestDocuments Table

```sql
RequestDocuments (
  id (PK),
  requestId (FK -> Requests),
  documentId (FK -> Documents),
  createdAt,
  updatedAt
)
```

### 12. Activity & Audit Management

#### ActivityLogs Table

```sql
ActivityLogs (
  id (PK),
  actionType,
  activityDescription,
  activityOn,
  recordId,
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### LoginHistories Table

```sql
LoginHistories (
  id (PK),
  userId (FK -> Users),
  loginTime,
  logoutTime,
  ipAddress,
  userAgent,
  createdAt,
  updatedAt
)
```

#### DeviceSessions Table

```sql
DeviceSessions (
  id (PK),
  userId (FK -> Users),
  deviceId,
  token,
  isActive,
  lastActivity,
  createdAt,
  updatedAt
)
```

### 13. Additional Supporting Tables

#### Workspaces Table

```sql
Workspaces (
  id (PK),
  name,
  description,
  createdAt,
  updatedAt
)
```

#### Modules Table

```sql
Modules (
  id (PK),
  name,
  description,
  status,
  createdAt,
  updatedAt
)
```

#### DesignationModulePermissions Table

```sql
DesignationModulePermissions (
  id (PK),
  designationId (FK -> Designations),
  moduleId (FK -> Modules),
  permissions (JSON),
  createdAt,
  updatedAt
)
```

#### DesignationMedia Table

```sql
DesignationMedia (
  id (PK),
  designationId (FK -> Designations),
  mediaType,
  mediaUrl,
  createdAt,
  updatedAt
)
```

#### Taxes Table

```sql
Taxes (
  id (PK),
  name,
  rate,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### ItemTaxes Table (Junction)

```sql
ItemTaxes (
  id (PK),
  itemId (FK -> Items),
  taxId (FK -> Taxes),
  createdAt,
  updatedAt
)
```

#### Spaces Table

```sql
Spaces (
  id (PK),
  name,
  description,
  area,
  unitId (FK -> Units),
  createdAt,
  updatedAt
)
```

#### OrganizationBrands Table

```sql
OrganizationBrands (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### OrganizationBrandMedia Table

```sql
OrganizationBrandMedia (
  id (PK),
  organizationBrandId (FK -> OrganizationBrands),
  mediaType,
  mediaUrl,
  createdAt,
  updatedAt
)
```

#### Catalogues Table

```sql
Catalogues (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### MarketplaceCategories Table

```sql
MarketplaceCategories (
  id (PK),
  name,
  description,
  createdAt,
  updatedAt
)
```

#### MasterDocuments Table

```sql
MasterDocuments (
  id (PK),
  name,
  description,
  documentType,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### DocumentRequires Table

```sql
DocumentRequires (
  id (PK),
  documentType,
  isRequired,
  inputRequired,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### Templates Table

```sql
Templates (
  id (PK),
  name,
  description,
  templateType,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### TemplateEntities Table

```sql
TemplateEntities (
  id (PK),
  templateId (FK -> Templates),
  entityName,
  entityType,
  isRequired,
  createdAt,
  updatedAt
)
```

#### PaymentPlans Table

```sql
PaymentPlans (
  id (PK),
  name,
  description,
  organizationId (FK -> Organizations),
  createdAt,
  updatedAt
)
```

#### PaymentStages Table

```sql
PaymentStages (
  id (PK),
  paymentPlanId (FK -> PaymentPlans),
  stageName,
  percentage,
  dueDays,
  createdAt,
  updatedAt
)
```

#### PricingRevisions Table

```sql
PricingRevisions (
  id (PK),
  revisionNumber,
  effectiveDate,
  organizationId (FK -> Organizations),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### PricingCharges Table

```sql
PricingCharges (
  id (PK),
  pricingRevisionId (FK -> PricingRevisions),
  chargeName,
  chargeType,
  amount,
  percentage,
  createdAt,
  updatedAt
)
```

#### PurchaseOrders Table

```sql
PurchaseOrders (
  id (PK),
  poNumber,
  supplierId (FK -> Suppliers),
  orderDate,
  deliveryDate,
  status,
  totalAmount,
  organizationId (FK -> Organizations),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### PurchaseOrderItems Table

```sql
PurchaseOrderItems (
  id (PK),
  purchaseOrderId (FK -> PurchaseOrders),
  itemId (FK -> Items),
  quantity,
  unitPrice,
  totalPrice,
  createdAt,
  updatedAt
)
```

#### WbsActivities Table (Work Breakdown Structure)

```sql
WbsActivities (
  id (PK),
  name,
  description,
  startDate,
  endDate,
  duration,
  progress,
  status,
  projectId (FK -> Projects),
  parentActivityId (FK -> WbsActivities),
  assignedTo (FK -> Users),
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### WbsActivityDependencies Table

```sql
WbsActivityDependencies (
  id (PK),
  activityId (FK -> WbsActivities),
  dependentActivityId (FK -> WbsActivities),
  dependencyType,
  lag,
  createdAt,
  updatedAt
)
```

#### WbsActivityProjectMappings Table

```sql
WbsActivityProjectMappings (
  id (PK),
  wbsActivityId (FK -> WbsActivities),
  projectId (FK -> Projects),
  createdAt,
  updatedAt
)
```

#### WbsActivityQualityControls Table

```sql
WbsActivityQualityControls (
  id (PK),
  wbsActivityId (FK -> WbsActivities),
  qualityCheck,
  status,
  checkedBy (FK -> Users),
  checkedAt,
  createdAt,
  updatedAt
)
```

#### WbsComments Table

```sql
WbsComments (
  id (PK),
  wbsActivityId (FK -> WbsActivities),
  comment,
  createdBy (FK -> Users),
  createdAt,
  updatedAt
)
```

#### WbsDocuments Table

```sql
WbsDocuments (
  id (PK),
  wbsActivityId (FK -> WbsActivities),
  documentId (FK -> Documents),
  createdAt,
  updatedAt
)
```

## Key Relationships

### One-to-Many Relationships

- Organization → Users (through UserOrganizations)
- Organization → Projects
- Organization → Departments
- Organization → Items
- Project → Units
- Project → Tasks
- Project → WorkOrders
- User → Tasks (assigned)
- User → Activities
- Customer → Requirements
- Customer → ContactPersons

### Many-to-Many Relationships

- Users ↔ Projects (through ProjectTeams)
- Users ↔ Organizations (through UserOrganizations)
- Tasks ↔ TaskTags (through TaskTagMapping)
- Tasks ↔ Units (through TaskUnitMapping)
- Items ↔ Taxes (through ItemTaxes)
- Projects ↔ Amenities (through ProjectAmenities)
- Units ↔ Amenities (through UnitAmenities)

### Self-Referencing Relationships

- Projects → Projects (parent-child)
- Documents → Documents (folder structure)
- BoqCategories → BoqCategories (parent-child)
- WbsActivities → WbsActivities (parent-child)

## Database Features

### 1. Multi-Tenancy

- Organizations table serves as the main tenant
- Most tables have organizationId for data isolation
- UserOrganizations junction table for user-organization relationships

### 2. Audit Trail

- ActivityLogs table tracks all major actions
- Timestamps on all tables
- User tracking for create/update operations

### 3. Document Management

- Hierarchical document structure (folders)
- Document permissions system
- File storage with cloud integration

### 4. Workflow Management

- Request-approval system
- Task dependencies
- Status tracking across modules

### 5. Financial Management

- Double-entry accounting system
- Budget tracking
- Expense management
- Quotation and payment plans

### 6. Real Estate Features

- Project and unit management
- BOQ (Bill of Quantities)
- Work orders and contractors
- Customer relationship management

### 7. Inventory Management

- Item catalog with variants
- Warehouse management
- Stock adjustments
- Purchase orders and GRNs

This database architecture supports a comprehensive real estate and construction management system with robust user management, project tracking, financial controls, and operational workflows.
