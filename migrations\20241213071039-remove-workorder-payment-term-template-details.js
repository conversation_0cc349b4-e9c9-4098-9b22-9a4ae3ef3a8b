const { paymentTermTemplateDetailsType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('PaymentTermTemplateDetails');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('PaymentTermTemplateDetails', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      templateId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'PaymentTermTemplate',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM(
          paymentTermTemplateDetailsType.paymentTermTemplateDetailsTypeArray()
        ),
        allowNull: false,
        defaultValue: paymentTermTemplateDetailsType.ON_DATE,
      },
      schedule: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      defaultAgreementValue: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
      },
      tax: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },
};
