const {
  unitOfMeasurement,
  reOrderPointScope,
  itemStatus,
} = require('@config/options');

exports.createItem = {
  name: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
  categoryId: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Category ID is required',
    isInt: {
      errorMessage: 'Category ID must be a valid integer',
    },
  },
  sku: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'SKU must be a valid string',
    },
  },
  unitOfMeasurement: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Unit of Measurement is required',
    custom: {
      options: (value) => {
        const allowedValues = unitOfMeasurement.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Unit of Measurement: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Unit of Measurement value provided',
    },
  },
  hsn: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'HSN must be a valid string',
    },
  },
  taxIds: {
    in: ['body'],
    optional: true,
    errorMessage: 'Tax IDs are required',
    isArray: {
      errorMessage: 'Tax IDs must be an array of numbers',
    },
  },
  reOrderPoint: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Reorder Point must be a valid integer',
    },
  },
  reOrderPointScope: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = reOrderPointScope.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid ReOrder Point Of Scope: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid ReOrder Point Of Scope value provided',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a valid string',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
  variant: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Variant must be an array of objects',
    },
  },
};

exports.cloneItem = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Item ID is required',
    },
    isInt: {
      errorMessage: 'Item ID must be a valid integer',
    },
  },
};

exports.updateItem = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Item ID is required',
    },
    isInt: {
      errorMessage: 'Item ID must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
  categoryId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Category ID must be a valid integer',
    },
  },
  sku: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'SKU must be a valid string',
    },
  },
  unitOfMeasurement: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = unitOfMeasurement.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Unit of Measurement: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Unit of Measurement value provided',
    },
  },
  hsn: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'HSN must be a valid string',
    },
  },
  taxIds: {
    in: ['body'],
    optional: true,
    errorMessage: 'Tax IDs are required',
    isArray: {
      errorMessage: 'Tax IDs must be an array of numbers',
    },
  },
  reOrderPoint: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Reorder Point must be a valid integer',
    },
  },
  reOrderPointScope: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = reOrderPointScope.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid ReOrder Point Of Scope: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid ReOrder Point Of Scope value provided',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a valid string',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
  variant: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Variant must be an array of objects',
    },
  },
};

exports.updateItemStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Item ID is required',
    },
    isInt: {
      errorMessage: 'Item ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Status is required',
    custom: {
      options: (value) => {
        const allowedValues = itemStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid status provided',
    },
  },
};

exports.deleteItem = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Item ID is required',
    },
    isInt: {
      errorMessage: 'Item ID must be a valid integer',
    },
  },
};

exports.deleteItemMedia = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'ItemMedia ID is required',
    },
    isInt: {
      errorMessage: 'ItemMedia ID must be a valid integer',
    },
  },
};
