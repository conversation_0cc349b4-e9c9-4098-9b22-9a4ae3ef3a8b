paths:
  /admin/designation:
    post:
      summary: Create a new Designation
      tags:
        - Designation
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createUpdateDesignation"
      produces:
        - application/json
      responses:
        '201':
          description: Designation created successfully
        '400':
          description: Invalid Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
          
    get:
      summary: List designations
      description: Fetches a list of all designations within the organization with pagination and search functionality.
      tags:
        - Designation
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
        - in: query
          name: limit
          schema:
            type: integer
        - in: query
          name: search
          schema:
            type: string
      responses:
        '200':
          description: Successful response
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error

  /admin/designation/{id}:
    put:
      summary: Update an existing designation
      tags:
        - Designation
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createUpdateDesignation"
      responses:
        '200':
          description: Designation updated successfully
        '400':
          description: Invalid Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    get:
      summary: Get a designation by ID
      tags:
        - Designation
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
      responses:
        "200":
          description: "Fetched designation details successfully"
        "400":
          description: "Invalid Request"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"


components:
  schemas:
    createUpdateDesignation:
      type: object
      properties:
        name:
          type: string
          description: The name of the designation
        departmentId:
          type: integer
          description: The ID of the department
        description:
          type: string
          description: The description of the designation