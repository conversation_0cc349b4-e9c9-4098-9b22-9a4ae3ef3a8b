const { requirement } = require('@config/options');

// exports.createRequirement = {
//   customerId: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Customer ID is required',
//     },
//     isInt: {
//       errorMessage: 'Customer ID must be an integer',
//     },
//   },
//   purpose: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Requirement purpose is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues = requirement.purpose.getPurposeArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid requirement purpose: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid purpose provided',
//     },
//   },
//   possessionBy: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Possession by field is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues = requirement.possessionBy.getPossessionByArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid possession type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid possession type provided',
//     },
//   },
//   budgetRange: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Budget range is required',
//     },
//     isString: {
//       errorMessage: 'Budget range must be a string',
//     },
//   },
//   fundingType: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Funding type is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues = requirement.fundingType.getFundingTypeArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid funding type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid funding type provided',
//     },
//   },
//   propertyType: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Property type is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues = requirement.propertyType.getPropertyTypeArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid property type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid property type provided',
//     },
//   },
//   configuration: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Configuration is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues = requirement.configuration.getConfigurationArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid configuration: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid configuration provided',
//     },
//   },
//   configurationType: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Configuration type is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues =
//           requirement.configurationType.getConfigurationTypeArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid configuration type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid configuration type provided',
//     },
//   },
//   areaRange: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Area range is required',
//     },
//     isString: {
//       errorMessage: 'Area range must be a string',
//     },
//   },
//   locationPreference: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Location preference is required',
//     },
//     isString: {
//       errorMessage: 'Location preference must be a string',
//     },
//   },
//   projectId: {
//     in: ['body'],
//     optional: true,
//     isInt: {
//       errorMessage: 'Project ID must be an integer',
//     },
//   },
//   subProjectId: {
//     in: ['body'],
//     optional: true,
//     isInt: {
//       errorMessage: 'Sub-Project ID must be an integer',
//     },
//   },
//   furnishingType: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Furnishing type is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues =
//           requirement.furnishingType.getFurnishingTypeArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid furnishing type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid furnishing type provided',
//     },
//   },
//   directionPreference: {
//     in: ['body'],
//     notEmpty: {
//       errorMessage: 'Direction preference is required',
//     },
//     custom: {
//       options: (value) => {
//         const allowedValues =
//           requirement.directionPreference.getDirectionPreferenceArray();
//         if (!allowedValues.includes(value)) {
//           console.log(
//             `Invalid direction preference: ${value}. Allowed values are: ${allowedValues.join(', ')}`
//           );
//           return false;
//         }
//         return true;
//       },
//       errorMessage: 'Invalid direction preference provided',
//     },
//   },
//   otherPreferences: {
//     in: ['body'],
//     optional: true,
//     isString: {
//       errorMessage: 'Other preferences must be a string',
//     },
//   },
// };

exports.updateRequirement = {
  customerId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Customer ID must be an integer',
    },
  },
  purpose: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = requirement.purpose.getPurposeArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid requirement purpose: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid purpose provided',
    },
  },
  possessionBy: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = requirement.possessionBy.getPossessionByArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid possession type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid possession type provided',
    },
  },
  budgetRange: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Budget range must be a string',
    },
  },
  fundingType: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = requirement.fundingType.getFundingTypeArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid funding type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid funding type provided',
    },
  },
  propertyType: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = requirement.propertyType.getPropertyTypeArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid property type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid property type provided',
    },
  },
  configuration: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = requirement.configuration.getConfigurationArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid configuration: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid configuration provided',
    },
  },
  configurationType: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues =
          requirement.configurationType.getConfigurationTypeArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid configuration type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid configuration type provided',
    },
  },
  areaRange: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Area range must be a string',
    },
  },
  locationPreference: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Location preference must be a string',
    },
  },
  projectId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Project ID must be an integer',
    },
  },
  subProjectId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Sub-Project ID must be an integer',
    },
  },
  furnishingType: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues =
          requirement.furnishingType.getFurnishingTypeArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid furnishing type: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid furnishing type provided',
    },
  },
  directionPreference: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues =
          requirement.directionPreference.getDirectionPreferenceArray();
        if (value && !allowedValues.includes(value)) {
          console.log(
            `Invalid direction preference: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid direction preference provided',
    },
  },
  otherPreferences: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Other preferences must be a string',
    },
  },
};
