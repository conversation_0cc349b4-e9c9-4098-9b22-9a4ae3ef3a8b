const swaggerJSDoc = require('swagger-jsdoc');

const swaggerDocs = {
  mobileSpec: {
    openapi: '3.0.0',
    info: {
      title: 'Bricko Mobile API Development',
      version: '1.0.0',
      description: 'This is a REST API application made with Express.',
      contact: {
        name: '<PERSON><PERSON>',
        email: '<EMAIL>',
      },
    },
    persistAuthorization: true,
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    servers: [
      {
        url: `${process.env.BACKEND_URL}/api/v1`,
        description: 'Development server',
      },
    ],
  },
  webSpec: {
    openapi: '3.0.0',
    info: {
      title: 'Bricko API Development',
      version: '1.0.0',
      description:
        'This is a REST API application made with Express. It retrieves data from JSONPlaceholder.',
      contact: {
        name: '<PERSON><PERSON>',
        url: '<EMAIL>',
      },
    },
    basePath: '/api/v1',
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    servers: [
      {
        url: `${process.env.BACKEND_URL}/api/v1`,
        description: 'Development server',
      },
      {
        url: `${process.env.BACKEND_URL}/api/v1/admin`,
        description: 'Development Admin server',
      },
    ],
  },
};
const adminOptions = {
  swaggerDefinition: swaggerDocs.mobileSpec,
  apis: ['api-docs/v1/admin/*.yaml'],
};
const webMobileOptions = {
  swaggerDefinition: swaggerDocs.webSpec,
  apis: ['api-docs/v1/web-mobile/*.yaml'],
};
module.exports.adminSetup = swaggerJSDoc(adminOptions);
module.exports.webMobileSetup = swaggerJSDoc(webMobileOptions);

// const swaggerJSDoc = require('swagger-jsdoc');
// const commonHeaderParameters = [
//   {
//     name: 'organization_id',
//     in: 'header',
//     required: false,
//     description: 'The ID of the organization',
//     schema: {
//       type: 'integer',
//       example: 123,
//     },
//   },
//   {
//     name: 'permissions',
//     in: 'header',
//     required: false,
//     description:
//       "A value representing the user's permission: 'create' = can create, 'view' = can view, 'edit' = can edit.",
//     schema: {
//       type: 'string',
//       enum: ['create', 'view', 'edit'],
//       example: 'create',
//     },
//   },
// ];

// function injectCommonParameters(spec, isWeb = false) {
//   // Only inject parameters if it's a web spec
//   if (isWeb && spec.paths) {
//     Object.keys(spec.paths).forEach((path) => {
//       Object.keys(spec.paths[path]).forEach((method) => {
//         if (!spec.paths[path][method].parameters) {
//           spec.paths[path][method].parameters = [];
//         }
//         spec.paths[path][method].parameters.push(...commonHeaderParameters);
//       });
//     });
//   }
//   return spec;
// }

// const swaggerDocs = {
//   mobileSpec: {
//     openapi: '3.0.0',
//     info: {
//       title: 'Bricko Mobile API Development',
//       version: '1.0.0',
//       description: 'This is a REST API application made with Express.',
//       contact: {
//         name: 'Sunil Kumar',
//         email: '<EMAIL>',
//       },
//     },
//     persistAuthorization: true,
//     components: {
//       securitySchemes: {
//         bearerAuth: {
//           type: 'http',
//           scheme: 'bearer',
//           bearerFormat: 'JWT',
//         },
//       },
//     },
//     servers: [
//       {
//         url: `${process.env.BACKEND_URL}/api/v1`,
//         description: 'Development server',
//       },
//     ],
//   },
//   webSpec: {
//     openapi: '3.0.0',
//     info: {
//       title: 'Bricko API Development',
//       version: '1.0.0',
//       description: 'This is a REST API application made with Express.',
//       contact: {
//         name: 'Sunil Kumar',
//         url: '<EMAIL>',
//       },
//     },
//     basePath: '/api/v1',
//     components: {
//       securitySchemes: {
//         bearerAuth: {
//           type: 'http',
//           scheme: 'bearer',
//           bearerFormat: 'JWT',
//         },
//       },
//     },
//     servers: [
//       {
//         url: `${process.env.BACKEND_URL}/api/v1`,
//         description: 'Development server',
//       },
//       {
//         url: `${process.env.BACKEND_URL}/api/v1/admin`,
//         description: 'Development Admin server',
//       },
//     ],
//   },
// };

// const adminOptions = {
//   swaggerDefinition: swaggerDocs.mobileSpec,
//   apis: ['api-docs/v1/admin/*.yaml'],
// };

// const webMobileOptions = {
//   swaggerDefinition: swaggerDocs.webSpec,
//   apis: ['api-docs/v1/web-mobile/*.yaml'],
// };

// const adminSpec = injectCommonParameters(swaggerJSDoc(adminOptions), false);
// const webMobileSpec = injectCommonParameters(
//   swaggerJSDoc(webMobileOptions),
//   true
// );

// module.exports.adminSetup = adminSpec;
// module.exports.webMobileSetup = webMobileSpec;
