module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableName = 'ActivityLog';
    const constraintName = 'ActivityLog_createdBy_fkey1';

    const constraints = await queryInterface.sequelize.query(
      `SELECT constraint_name 
       FROM information_schema.table_constraints 
       WHERE table_name = '${tableName}' AND constraint_name = '${constraintName}'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (constraints.length > 0) {
      await queryInterface.removeConstraint(tableName, constraintName);
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableName = 'ActivityLog';
    const constraintName = 'ActivityLog_createdBy_fkey1';

    const constraints = await queryInterface.sequelize.query(
      `SELECT constraint_name 
       FROM information_schema.table_constraints 
       WHERE table_name = '${tableName}' AND constraint_name = '${constraintName}'`,
      { type: Sequelize.QueryTypes.SELECT }
    );

    if (constraints.length === 0) {
      await queryInterface.addConstraint(tableName, {
        fields: ['createdBy'],
        type: 'foreign key',
        name: constraintName,
        references: {
          table: 'Employee',
          field: 'id',
        },
      });
    }
  },
};
