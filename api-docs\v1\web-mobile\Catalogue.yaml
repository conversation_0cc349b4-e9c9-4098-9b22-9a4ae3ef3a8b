paths:
  /brand/catalogue:
    post:
      tags:
        - "Brand"
      summary: "Create a new catalogue"
      description: "This endpoint allows you to create a new catalogue in the system"
      operationId: "CreateCatalogue"
      requestBody:
        description: "Catalogue creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateCatalogueRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Catalogue created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /brand/catalogue/{id}:
    put:
      tags:
        - "Brand"
      summary: "Update an existing catalogue"
      description: "This endpoint allows you to update an existing catalogue in the system"
      operationId: "UpdateCatalogue"
      parameters:
        - $ref: "#/components/parameters/CatalogueIdParam"
      requestBody:
        description: "Catalogue update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateCatalogueRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Catalogue updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Catalogue not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Brand"
      summary: "Delete an existing catalogue"
      description: "This endpoint allows you to delete an existing catalogue in the system"
      operationId: "DeleteCatalogue"
      parameters:
        - $ref: "#/components/parameters/CatalogueIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Catalogue deleted successfully"
        "404":
          description: "Catalogue not found"
        "500":
          description: "Internal Server Error"

  /brand/catalogue/media/{id}:
    delete:
      tags:
        - "Brand"
      summary: "Delete an existing catalogue media"
      description: "This endpoint allows you to delete an existing catalogue media in the system"
      operationId: "DeleteCatalogueMedia"
      parameters:
        - $ref: "#/components/parameters/CatalogueIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Catalogue media deleted successfully"
        "404":
          description: "Catalogue media not found"
        "500":
          description: "Internal Server Error"



components:
  schemas:
    CreateCatalogueRequest:
      type: object
      properties:
        title:
          type: string
          example: "New Catalogue"
        categoryId:
          type: integer
          example: 1
        description:
          type: string
          example: "Description of the catalogue"
        logo:
          type: string
          example: "logo_url"
        backgroundImage:
          type: string
          example: "background_image_url"
        media:
          type: array
          items:
            type: object
            properties:
              mediaType:
                type: string
                example: "image"
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5
      required:
        - title
    UpdateCatalogueRequest:
      type: object
      properties:
        title:
          type: string
          example: "Updated Catalogue"
        categoryId:
          type: integer
          example: 1
        description:
          type: string
          example: "Updated description of the catalogue"
        logo:
          type: string
          example: "updated_logo_url"
        backgroundImage:
          type: string
          example: "updated_background_image_url"
        media:
          type: array
          items:
            type: object
            properties:
              mediaType:
                type: string
                example: "image"
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5
  parameters:
    CatalogueIdParam:
      name: "id"
      in: "path"
      description: "ID of the catalogue to be managed"
      required: true
      schema:
        type: string
