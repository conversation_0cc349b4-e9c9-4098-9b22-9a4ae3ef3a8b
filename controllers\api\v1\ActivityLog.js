const { genRes, errorMessage, resCode } = require('@config/options');
const ActivityLogRepository = require('@models/repositories/ActivityLogRepository');

exports.listActivityLog = async (req, res) => {
  try {
    const { success, message, data } =
      await ActivityLogRepository.getActivities(req.query);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
