'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'Warehouse',
      'Warehouse_projectId_fkey'
    );
    await queryInterface.removeColumn('Warehouse', 'projectId');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Warehouse', 'projectId', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.addConstraint('Warehouse', {
      fields: ['projectId'],
      type: 'foreign key',
      name: 'Warehouse_projectId_fkey',
      references: {
        table: 'Project',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },
};
