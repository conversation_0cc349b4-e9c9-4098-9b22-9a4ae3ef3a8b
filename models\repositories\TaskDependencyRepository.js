const { TaskDependency, Tasks } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.findOne = async (query) => await Tasks.findOne(query);

exports.addDependency = async (taskId, data) => {
  try {
    const query = {
      where: {
        id: taskId,
      },
    };
    const task = await this.findOne(query);
    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('task'),
      };
    }

    const dependentTaskQuery = {
      where: {
        id: data.dependentTaskId,
      },
    };
    const dependentTask = await this.findOne(dependentTaskQuery);
    if (!dependentTask) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('dependent task'),
      };
    }

    const dependencyPayload = {
      taskId,
      ...data,
    };

    const dependency = await TaskDependency.create(dependencyPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('dependency'),
      data: dependency,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateDependency = async (dependencyId, data) => {
  try {
    const dependency = await TaskDependency.findByPk(dependencyId);
    if (!dependency) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('dependencyId'),
      };
    }

    Object.assign(dependency, data);
    await dependency.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('dependency'),
      data: dependency,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteDependency = async (dependencyId) => {
  try {
    const dependency = await TaskDependency.findByPk(dependencyId);
    if (!dependency) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('dependency'),
      };
    }

    await dependency.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('dependency'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
