const {
  requestPriority,
  requestStatus,
  requestType,
} = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Request = sequelize.define(
    'Request',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      requestedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
      },
      requestedTo: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
      },
      requestType: {
        type: DataTypes.ENUM(requestType.getValues()),
        allowNull: true,
        defaultValue: requestType.GENERAL_REQUEST,
      },
      priority: {
        type: DataTypes.ENUM(requestPriority.getValues()),
        allowNull: true,
        defaultValue: requestPriority.MEDIUM,
      },
      status: {
        type: DataTypes.ENUM(requestStatus.getValues()),
        allowNull: true,
        defaultValue: requestStatus.PENDING,
      },
      approvedBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
      },
      dueDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      shortDescription: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      reference: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
      recordId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Request.associate = (models) => {
    Request.belongsTo(models.User, {
      foreignKey: 'requestedBy',
      as: 'requester',
      onDelete: 'CASCADE',
    });
    Request.belongsTo(models.User, {
      foreignKey: 'requestedTo',
      as: 'receiver',
      onDelete: 'SET NULL',
    });
    Request.belongsTo(models.User, {
      foreignKey: 'approvedBy',
      as: 'approver',
      onDelete: 'SET NULL',
    });
    Request.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    Request.hasMany(models.RequestComment, {
      foreignKey: 'requestId',
      as: 'comments',
    });
    Request.hasMany(models.RequestDocument, {
      foreignKey: 'requestId',
      as: 'documents',
    });
    Request.hasMany(models.ActivityLog, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        activityOn: 'Request',
      },
      as: 'activities',
    });
    Request.belongsTo(models.WorkOrder, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    Request.belongsTo(models.Quotation, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    Request.belongsTo(models.WbsActivity, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    Request.belongsTo(models.EmployeeLeave, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    Request.belongsTo(models.Journal, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    Request.belongsTo(models.Indent, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
  };

  return Request;
};
