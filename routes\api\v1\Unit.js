const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const UnitController = require('../../../controllers/api/v1/Unit');
const UnitSchema = require('../../../schema-validation/Unit');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/:id/unit',
  checkSchema(UnitSchema.createUnit),
  ErrorHandleHelper.requestValidator,
  UnitController.createUnit
);

router.put(
  '/unit/:id',
  checkSchema(UnitSchema.updateUnit),
  ErrorHandleHelper.requestValidator,
  UnitController.updateUnit
);

router.post('/:id/unit/:unitId/duplicate', UnitController.createDuplicateUnit);

router.delete(
  '/unit/:id',
  checkSchema(UnitSchema.validateRemoveUnit),
  ErrorHandleHelper.requestValidator,
  UnitController.deleteUnit
);

router.post(
  '/unit/:id/drawing',
  checkSchema(UnitSchema.validateAddDrawingsToUnit),
  ErrorHandleHelper.requestValidator,
  UnitController.addDrwaingsToUnit
);

module.exports = router;
