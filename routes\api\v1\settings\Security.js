const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const SecurityController = require('@controllers/v1/settings/Security');
const SecuritySchema = require('@schema-validation/settings/Security');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.patch(
  '/security/change-password',
  checkSchema(SecuritySchema.changePassword),
  ErrorHandleHelper.requestValidator,
  SecurityController.changePassword
);

module.exports = router;
