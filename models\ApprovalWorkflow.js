'use strict';
const { approver, escalation } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const ApprovalWorkflow = sequelize.define(
    'ApprovalWorkflow',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      approver: {
        type: DataTypes.ENUM(approver.approverArray()),
        allowNull: false,
        defaultValue: approver.MEMBER,
      },
      escalation: {
        type: DataTypes.ENUM(escalation.escalationArray()),
        allowNull: false,
        defaultValue: escalation.MEMBER,
      },
      isApprovalRequired: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      isEscalationRequired: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      activityName: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      moduleName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ApprovalWorkflow.associate = (models) => {
    ApprovalWorkflow.belongsTo(models.User, {
      foreignKey: 'approverUserId',
      as: 'approverUser',
    });

    ApprovalWorkflow.belongsTo(models.ProjectTeam, {
      foreignKey: 'approverMemberId',
      as: 'approverMember',
    });

    ApprovalWorkflow.belongsTo(models.User, {
      foreignKey: 'escalationUserId',
      as: 'escalationUser',
    });

    ApprovalWorkflow.belongsTo(models.Designation, {
      foreignKey: 'escalationDesignationId',
      as: 'escalationDesignation',
    });

    ApprovalWorkflow.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    ApprovalWorkflow.belongsTo(models.Designation, {
      foreignKey: 'approverDesignationId',
      as: 'approverDesignation',
    });
  };

  return ApprovalWorkflow;
};
