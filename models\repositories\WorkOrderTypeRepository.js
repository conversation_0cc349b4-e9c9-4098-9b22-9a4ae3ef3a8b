const { WorkOrderType } = require('..');
const { successMessage, errorMessage } = require('../../config/options');
const { Sequelize } = require('sequelize');

exports.addWorkOrderType = async (data) => {
  try {
    const payload = {
      name: data.name,
    };

    const type = await WorkOrderType.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('WorkOrderType'),
      data: type,
    };
  } catch (error) {
    if (error instanceof Sequelize.UniqueConstraintError) {
      return {
        success: false,
        message: errorMessage.EXISTS_USER('WorkOrderType'),
      };
    }
    throw error;
  }
};
