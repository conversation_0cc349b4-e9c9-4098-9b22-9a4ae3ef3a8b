'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('DesignationModulePermission', 'canView');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('DesignationModulePermission', 'canView', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    });
  },
};
