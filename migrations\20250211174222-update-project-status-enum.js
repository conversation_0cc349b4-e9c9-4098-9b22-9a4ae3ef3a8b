'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `
      ALTER TYPE "enum_TemplateMaster_templateType" ADD VALUE IF NOT EXISTS 'attendance';
      ALTER TYPE "enum_TemplateMaster_templateType" ADD VALUE IF NOT EXISTS 'unpaid_leave_deduction';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'new_project';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'design_planning';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'sanction';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'under_construction';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'delayed';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'on_hold';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'near_completion';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'completed';
      ALTER TYPE "enum_Project_status" ADD VALUE IF NOT EXISTS 'archived';
    `
    );
  },

  async down(queryInterface, Sequelize) {},
};
