const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const ChargeTypeControl = require('../../../controllers/api/v1/ChargeType');
const ChargeTypeSchema = require('../../../schema-validation/ChargeType');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/:id/charge-type',
  checkSchema(ChargeTypeSchema.createChargeType),
  ErrorHandleHelper.requestValidator,
  ChargeTypeControl.createChargeType
);

router.put(
  '/charge-type/:id',
  checkSchema(ChargeTypeSchema.updateChargeType),
  ErrorHandleHelper.requestValidator,
  ChargeTypeControl.updateChargeType
);

router.delete(
  '/charge-type/:id',
  ErrorHandleHelper.requestValidator,
  ChargeTypeControl.deleteChargeType
);

module.exports = router;
