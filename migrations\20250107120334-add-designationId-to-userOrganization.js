'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('UserOrganization', 'designationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Designation',
        key: 'id',
      },
      onDelete: 'SET NULL',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('UserOrganization', 'designationId');
  },
};
