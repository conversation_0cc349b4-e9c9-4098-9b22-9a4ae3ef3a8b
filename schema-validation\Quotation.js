const {
  paymentStageCalculationType,
  paymentTriggerType,
  priceChargeCalculationType,
} = require('@config/options');

exports.createOrUpdateQuotation = {
  unitId: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'unitId cannot be empty',
    },
    isInt: {
      errorMessage: 'unitId must be integer',
    },
  },
  customerId: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'customerId cannot be empty',
    },
    isInt: {
      errorMessage: 'customerId must be integer',
    },
  },
  termsAndCondition: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'termsAndCondition must be string',
    },
  },
  expireDate: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDate: {
      errorMessage: 'expireDate must be a date',
    },
  },
  holdPropertyUntilExpiry: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isBoolean: {
      errorMessage: 'holdPropertyUntilExpiry must be boolean',
    },
  },
  pricingRevision: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'pricingRevision cannot be empty',
    },
    isString: {
      errorMessage: 'pricingRevision must be string',
    },
  },
  pricingRevisionTotalAmount: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'pricingRevisionTotalAmount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'pricingRevisionTotalAmount must be decimal',
    },
  },
  'unitCosts.*.name': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'unitCosts.*.chargeType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [priceChargeCalculationType.priceChargeCalculationTypeArray()],
      errorMessage: `chargeType must be agreement_value, percentage_of_agreement_value ,fixed_amount, super_built_up_area, carpet_area, tax, or fixed_amount`,
    },
    isString: {
      errorMessage: 'chargeType must be string',
    },
  },
  'unitCosts.*.rate': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'rate must be string',
    },
  },
  'unitCosts.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'rate must be string',
    },
  },
  'unitCosts.*.isTaxable': {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isTaxable must be boolean',
    },
  },
  'unitCosts.*.taxRate': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDecimal: {
      errorMessage: 'taxRate must be decimal',
    },
  },
  'unitCosts.*.hsnCode': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'hsnCode must be string',
    },
  },
  'unitCosts.*.taxAmount': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDecimal: {
      errorMessage: 'taxAmount must be decimal',
    },
  },
  'unitCosts.*.amount': {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'amount must be decimal',
    },
  },
};

exports.createQuotationPaymentPlan = {
  paymentPlan: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'paymentPlan cannot be empty',
    },
    isString: {
      errorMessage: 'paymentPlan must be string',
    },
  },
  brokerPaymentPlan: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'brokerPaymentPlan cannot be empty',
    },
    isString: {
      errorMessage: 'brokerPaymentPlan must be string',
    },
  },
  brokerAdditionTerm: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'brokerAdditionTerm must be string',
    },
  },
  totalBrokerageAmount: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'totalBrokerageAmount  cannot be empty',
    },
    isDecimal: {
      errorMessage: 'brokerPaymentPlan must be isDecimal',
    },
  },
  homeLoanRequired: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'homeLoanRequired cannot be empty',
    },
    isBoolean: {
      errorMessage: 'homeLoanRequired must be boolean',
    },
  },
  paymentPlanTotalAmount: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'paymentPlanTotalAmount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'paymentPlanTotalAmount must be isDecimal',
    },
  },
  paymentPlanAgreementAmount: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'paymentPlanAgreementAmount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'paymentPlanAgreementAmount must be Decimal',
    },
  },
  paymentPlanBalance: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'paymentPlanBalance cannot be empty',
    },
    isDecimal: {
      errorMessage: 'paymentPlanBalance must be decimal',
    },
  },
  'paymentPlans.*.name': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'paymentPlans.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'chargeType must be string',
    },
  },
  'paymentPlans.*calculationType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [
        ...paymentStageCalculationType.paymentStageCalculationTypeArray(),
      ],
      errorMessage:
        'calculationType must be percentage_of_agreement_value, percentage_of_total_sale_value, or fixed_amount',
    },
  },
  'paymentPlans.*.triggerType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [paymentTriggerType.paymentTriggerTypeArray()],
      errorMessage: 'Invalid triggerType',
    },
  },
  'paymentPlans.*.dueOn': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'dueOn cannot be empty',
    },
    isString: {
      errorMessage: 'dueOn must be string',
    },
  },
  'paymentPlans.*.amount': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDecimal: {
      errorMessage: 'amount must be decimal',
    },
  },
  'paymentPlans.*.netPayableAmount': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'netPayableAmount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'netPayableAmount must be decimal',
    },
  },
};

exports.createUnitBooking = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'id cannot be empty',
    },
  },
};
