const {
  PurchaseOrder,
  PurchaseOrderItem,
  Item,
  ItemVariant,
  Indent,
  WorkOrder,
  Task,
  User,
  Warehouse,
  sequelize,
} = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.validateAndCreatePurchaseOrder = async (data, loggedInUser) => {
  const {
    items,
    vendorId,
    indentId,
    workOrderId,
    taskId,
    warehouseId,
    ...purchaseOrderData
  } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    if (indentId) {
      const indent = await Indent.findOne({
        where: { id: indentId, organizationId },
      });
      if (!indent) throw new Error(errorMessage.INVALID_ID('Indent'));
    }

    if (workOrderId) {
      const workOrder = await WorkOrder.findOne({
        where: { id: workOrderId, organizationId },
      });
      if (!workOrder) throw new Error(errorMessage.INVALID_ID('Work Order'));
    }

    if (taskId) {
      const task = await Task.findOne({
        where: { id: taskId },
      });
      if (!task) throw new Error(errorMessage.INVALID_ID('Task'));
    }

    if (vendorId) {
      const vendor = await User.findOne({
        where: { id: vendorId },
      });
      if (!vendor) throw new Error(errorMessage.INVALID_ID('Vendor'));
    }

    if (warehouseId) {
      const warehouse = await Warehouse.findOne({
        where: { id: warehouseId },
      });
      if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));
    }

    const purchaseOrder = await PurchaseOrder.create(
      {
        ...purchaseOrderData,
        vendorId,
        indentId,
        workOrderId,
        taskId,
        warehouseId,
        createdBy: loggedInUser.id,
        organizationId: organizationId,
      },
      { transaction }
    );

    if (items && items.length > 0) {
      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) throw new Error(errorMessage.INVALID_ID('Item'));

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant)
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
        }

        await PurchaseOrderItem.create(
          {
            ...item,
            purchaseOrderId: purchaseOrder.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Purchase Order'),
      data: purchaseOrder,
    };
  } catch (error) {
    console.log('error', error);
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while creating the purchase order',
    };
  }
};

exports.validateAndUpdatePurchaseOrder = async (
  purchaseOrderId,
  data,
  loggedInUser
) => {
  const {
    items,
    vendorId,
    indentId,
    workOrderId,
    taskId,
    warehouseId,
    ...purchaseOrderData
  } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const purchaseOrder = await PurchaseOrder.findOne({
      where: {
        id: purchaseOrderId,
        organizationId: organizationId,
      },
    });

    if (!purchaseOrder) {
      throw new Error(errorMessage.INVALID_ID('Purchase Order'));
    }

    if (indentId) {
      const indent = await Indent.findOne({
        where: { id: indentId, organizationId },
      });
      if (!indent) throw new Error(errorMessage.INVALID_ID('Indent'));
    }

    if (workOrderId) {
      const workOrder = await WorkOrder.findOne({
        where: { id: workOrderId, organizationId },
      });
      if (!workOrder) throw new Error(errorMessage.INVALID_ID('Work Order'));
    }

    if (taskId) {
      const task = await Task.findOne({
        where: { id: taskId },
      });
      if (!task) throw new Error(errorMessage.INVALID_ID('Task'));
    }

    if (vendorId) {
      const vendor = await User.findOne({
        where: { id: vendorId },
      });
      if (!vendor) throw new Error(errorMessage.INVALID_ID('Vendor'));
    }

    if (warehouseId) {
      const warehouse = await Warehouse.findOne({
        where: { id: warehouseId },
      });
      if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));
    }

    await purchaseOrder.update(
      {
        ...purchaseOrderData,
        vendorId,
        indentId,
        workOrderId,
        taskId,
        warehouseId,
      },
      { transaction }
    );

    if (items && items.length > 0) {
      await PurchaseOrderItem.destroy({
        where: { purchaseOrderId: purchaseOrder.id },
        transaction,
      });

      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) throw new Error(errorMessage.INVALID_ID('Item'));

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant)
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
        }

        await PurchaseOrderItem.create(
          {
            ...item,
            purchaseOrderId: purchaseOrder.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Purchase Order'),
      data: purchaseOrder,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the purchase order',
    };
  }
};

exports.validateAndUpdatePurchaseOrderStatus = async (
  purchaseOrderId,
  data,
  loggedInUser
) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const purchaseOrder = await PurchaseOrder.findOne({
      where: {
        id: purchaseOrderId,
        organizationId: organizationId,
      },
    });

    if (!purchaseOrder) {
      throw new Error(errorMessage.INVALID_ID('Purchase Order'));
    }

    data.updatedBy = loggedInUser.id;
    await purchaseOrder.update(data, { transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Purchase Order Status'),
      data: purchaseOrder,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating the purchase order status',
    };
  }
};

exports.validateAndDeletePurchaseOrder = async (purchaseOrderId) => {
  const transaction = await sequelize.transaction();
  try {
    const purchaseOrder = await PurchaseOrder.findOne({
      where: {
        id: purchaseOrderId,
      },
    });

    if (!purchaseOrder) {
      throw new Error(errorMessage.INVALID_ID('Purchase Order'));
    }

    await PurchaseOrderItem.destroy({
      where: { purchaseOrderId: purchaseOrder.id },
      transaction,
    });

    await purchaseOrder.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Purchase Order'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while deleting the purchase order',
    };
  }
};
