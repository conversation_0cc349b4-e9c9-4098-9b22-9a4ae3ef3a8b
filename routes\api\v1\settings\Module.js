const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ModuleController = require('@controllers/v1/settings/Module');
const ModuleSchema = require('@schema-validation/settings/Module');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(ModuleSchema.createModule),
  ErrorHandleHelper.requestValidator,
  ModuleController.createModule
);

router.get(
  '/',
  checkSchema(ModuleSchema.getModules),
  ErrorHandleHelper.requestValidator,
  ModuleController.getModules
);

router.put(
  '/:id',
  checkSchema(ModuleSchema.updateModule),
  ErrorHandleHelper.requestValidator,
  ModuleController.updateModule
);

router.delete(
  '/:id',
  checkSchema(ModuleSchema.deleteModule),
  ErrorHandleHelper.requestValidator,
  ModuleController.deleteModule
);

router.post(
  '/seed',
  ErrorHandleHelper.requestValidator,
  ModuleController.seedModules
);

module.exports = router;
