const { dependencyType, dependencyStatus } = require('@config/options');

exports.createTaskDependency = {
  dependentTaskId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Dependent Task ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Dependent Task ID must be a valid integer',
    },
    toInt: true,
  },
  dependencyType: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [dependencyType.getValues()],
      errorMessage: `Dependency Type must be one of the following: ${dependencyType.getValues().join(', ')}`,
    },
  },
  status: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [dependencyStatus.getValues()],
      errorMessage: `Status must be one of the following: ${dependencyStatus.getValues().join(', ')}`,
    },
  },
};

exports.updateTaskDependency = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Task Dependency ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Task Dependency ID must be a valid integer',
    },
    toInt: true,
  },
  dependencyType: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [dependencyType.getValues()],
      errorMessage: `Dependency Type must be one of the following: ${dependencyType.getValues().join(', ')}`,
    },
  },
  status: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [dependencyStatus.getValues()],
      errorMessage: `Status must be one of the following: ${dependencyStatus.getValues().join(', ')}`,
    },
  },
};
