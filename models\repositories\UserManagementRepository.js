/* eslint-disable camelcase */
const {
  Designation,
  Project,
  UserOrganization,
  User,
  ProjectTeam,
  sequelize,
} = require('..');
const {
  errorMessage,
  successMessage,
  inviteStatus,
  defaultStatus,
  inviteAction,
} = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');
const EmailHelper = require('@helpers/EmailHelper');
const { Op } = require('sequelize');
const UserHelper = require('@helpers/UserHelper');

exports.validateAndInviteUser = async (data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  let { email, roleId, projectId } = data;
  const selectFields = ['id', 'name'];
  const transaction = await sequelize.transaction();
  try {
    const role = await checkExistence(
      Designation,
      { id: roleId, organizationId },
      selectFields
    );

    if (!role) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Role with Id ${roleId}`),
      };
    }

    if (projectId && projectId.length > 0) {
      const projectChecks = projectId.map((id) =>
        checkExistence(Project, { id: id }, ['id'])
      );
      const projects = await Promise.all(projectChecks);

      const invalidProjects = projects.filter((project) => !project);
      if (invalidProjects.length > 0) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(
            `One or more projects do not exist`
          ),
        };
      }
    }

    email = email.toLowerCase();
    const userExists = await checkExistence(User, { email }, ['id']);
    let userId;
    let isPrimary = false;
    if (userExists) {
      userId = userExists.id;
    } else {
      const newUser = await User.create(
        {
          email,
          organizationId,
          currentOrganizationId: organizationId,
          role: 'USER',
          designationId: roleId,
        },
        { transaction }
      );
      userId = newUser.id;
      isPrimary = true;
    }

    const userOrganizationExists = await checkExistence(
      UserOrganization,
      {
        userId,
        organizationId,
        inviteStatus: { [Op.ne]: inviteStatus.REJECTED },
      },
      ['id']
    );
    if (userOrganizationExists) {
      return {
        success: false,
        message: `User with email ${email} already exists in the organization.`,
      };
    }

    const createInvitation = await UserOrganization.create(
      {
        userId,
        designationId: roleId,
        organizationId,
        inviteStatus: inviteStatus.PENDING,
        isPrimary,
      },
      { transaction }
    );

    if (projectId && projectId.length > 0) {
      const projectAssignments = projectId.map((id) =>
        addUserToProjectTeam(
          userId,
          id,
          loggedInUser.id,
          createInvitation.id,
          transaction
        )
      );
      await Promise.all(projectAssignments);
    }

    const invitationLink = `${process.env.FRONTEND_URL}/invite?id=${createInvitation.id}`;
    const emailData = {
      to: email,
      variables: {
        invitation_link: invitationLink,
        recipient_email: email,
        role_title: role.name,
      },
    };
    await EmailHelper.sendEmail(emailData, 'role_invitation');

    await transaction.commit();
    return {
      success: true,
      message: successMessage.INVITATION_SENT,
      data: createInvitation,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while validating and inviting the user.',
    };
  }
};

const addUserToProjectTeam = async (
  userId,
  projectId,
  invitedByUserId,
  userOrganizationId,
  transaction
) => {
  try {
    const projectTeam = await ProjectTeam.create(
      {
        projectId,
        userId,
        invitedBy: invitedByUserId,
        status: defaultStatus.PENDING,
        userOrganizationId,
      },
      { transaction }
    );
    return projectTeam;
  } catch (error) {
    throw new Error('Error adding user to project team');
  }
};

exports.resendInvitation = async (inviteId) => {
  try {
    const invitation = await UserOrganization.findOne({
      where: {
        id: inviteId,
      },
      include: [
        {
          model: User,
          as: 'user',
        },
        {
          model: Designation,
          as: 'designation',
        },
      ],
    });

    if (!invitation) {
      return {
        success: false,
        message: errorMessage.INVITATION_NOT_FOUND,
      };
    }

    const { inviteStatus: invitationStatus, user, designation } = invitation;
    if (invitationStatus !== inviteStatus.PENDING) {
      return {
        success: false,
        message: errorMessage.INVITATION_NOT_IN_PENDING_STATE,
      };
    }

    // Send Email
    const invitationLink = `${process.env.FRONTEND_URL}/invite?id=${inviteId}`;
    const emailData = {
      to: user.email,
      variables: {
        invitation_link: invitationLink,
        recipient_email: user.email,
        role_title: designation.name,
      },
    };
    await EmailHelper.sendEmail(emailData, 'role_invitation');

    return {
      success: true,
      message: successMessage.INVITATION_SENT,
    };
  } catch (error) {
    throw new Error(
      `An error occurred while ${action}ing the invitation: ${error.message}`
    );
  }
};

exports.manageUserInvite = async (inviteId, data, requestDetails) => {
  const { action } = data;
  try {
    const invitation = await UserOrganization.findOne({
      where: {
        id: inviteId,
        // inviteStatus: inviteStatus.PENDING,
      },
      include: [
        {
          model: User,
          as: 'user',
        },
      ],
    });

    if (invitation.inviteStatus === inviteStatus.REJECTED) {
      return {
        success: false,
        message: errorMessage.INVITATION_REJECTED,
      };
    }

    if (invitation.isProfileCompleted === true) {
      return {
        success: false,
        message: errorMessage.INVITATION_ACTION_NOT_ALLOWED,
      };
    }

    let token = null;

    switch (action) {
      case inviteAction.ACCEPT:
        invitation.inviteStatus = inviteStatus.ACCEPTED;

        const user = invitation.user;
        user.currentOrganizationId = invitation.organizationId;

        await user.save();

        token = user.genToken();
        await ProjectTeam.update(
          { status: defaultStatus.ACCEPTED },
          {
            where: {
              userOrganizationId: inviteId,
            },
          }
        );

        await UserHelper.saveLoginHistoryAndSession(
          user,
          token,
          requestDetails
        );
        break;

      case inviteAction.REJECT:
        invitation.inviteStatus = inviteStatus.REJECTED;
        await ProjectTeam.update(
          { status: defaultStatus.REJECTED },
          {
            where: {
              userOrganizationId: inviteId,
            },
          }
        );
        break;

      default:
        return {
          success: false,
          message: 'Invalid action.',
        };
    }

    await invitation.save();

    return {
      success: true,
      message:
        action === inviteAction.ACCEPT
          ? successMessage.INVITATION_ACCEPTED
          : successMessage.INVITATION_REJECTED,
      data: {
        token: token,
      },
    };
  } catch (error) {
    throw new Error(
      `An error occurred while ${action}ing the invitation: ${error.message}`
    );
  }
};

exports.deleteUserInvite = async (inviteId) => {
  const transaction = await sequelize.transaction();
  try {
    const invitation = await UserOrganization.findOne({
      where: {
        id: inviteId,
        inviteStatus: inviteStatus.PENDING,
      },
      include: [
        {
          model: User,
          as: 'user',
        },
      ],
    });

    if (!invitation) {
      return {
        success: false,
        message: errorMessage.INVITATION_NOT_FOUND,
      };
    }

    await ProjectTeam.destroy({
      where: {
        userOrganizationId: inviteId,
      },
      transaction,
    });

    const otherInvitations = await UserOrganization.findOne({
      where: {
        userId: invitation.userId,
        id: { [Op.ne]: inviteId },
      },
    });

    await invitation.destroy({ transaction });

    if (!otherInvitations) {
      await User.destroy({
        where: {
          id: invitation.userId,
        },
        transaction,
      });
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Invitation'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(
      `An error occurred while deleting the invitation: ${error.message}`
    );
  }
};
