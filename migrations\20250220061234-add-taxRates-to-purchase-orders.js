'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('PurchaseOrder', 'taxRates', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: {},
    });
    await queryInterface.removeColumn('PurchaseOrder', 'sgst');
    await queryInterface.removeColumn('PurchaseOrder', 'cgst');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('PurchaseOrder', 'taxRates');
    await queryInterface.addColumn('PurchaseOrder', 'sgst', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    });
    await queryInterface.addColumn('PurchaseOrder', 'cgst', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    });
  },
};
