paths:
  /settings/leave-template:
    post:
      summary: Creates a new leave template
      description: Creates a new leave template with the provided details
      operationId: createLeaveTemplate
      tags:
        - Settings
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createLeaveTemplate"
      responses:
        "201":
          description: Leave template created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /settings/leave-template/{id}: 
    put:
      summary: Updates an existing leave template
      description: Updates an existing leave template with the provided details
      operationId: "updateLeaveTemplate"
      tags:
        - Settings
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createLeaveTemplate"
      responses:
        "200":
          description: Leave template updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    delete:
      summary: Deletes an existing leave template
      description: Deletes an existing leave template with the provided id
      operationId: "deleteLeaveTemplate"
      tags:
        - Settings
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Leave template deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /settings/salary-template:
    post:
      summary: Creates a new salary template
      description: Creates a new salary template with the provided details
      operationId: createSalaryTemplate
      tags:
        - Settings
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSalaryTemplate"
      responses:
        "201":
          description: Salary template created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /settings/salary-template/{id}:
    put:
      summary: Updates an existing salary template
      description: Updates an existing salary template with the provided details
      operationId: updateSalaryTemplate
      tags:
        - Settings
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSalaryTemplate"
      responses:
        "200":
          description: Salary template updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: Get salary payroll details
      description: Get salary payroll details for a specific salary template
      operationId: getSalaryPayroll
      tags:
        - Settings
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Salary payroll details retrieved successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    delete:
      summary: Delete a salary template
      description: Delete a specific salary template
      operationId: deleteSalaryTemplate
      tags:
        - Settings
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: Salary template deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createLeaveTemplate:
      type: object
      properties:
        name:
          type: string
          example: "Standard leave Template"
        description:
          type: string
          example: "Deduction"
        organizationId:
          type: integer
          example: 1
        templateEntities:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "sick leave"
              maxAllowed:
                type: integer
                example: 5
      required:
        - name
        - description
        - templateEntities
    createSalaryTemplate:
      type: object
      properties:
        organizationId:
          type: integer
          example: 1
        description:
          type: string
          example: "Deduction"
        name:
          type: string
          example: "Standard salary Template"
        templateEntities:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "basic"
              calculation:
                type: number
                example: 10000.50
              monthlyAmount:
                type: number
                example: 10000.50
              annualAmount:
                type: number
                example: 120000.50
              entityType:
                type: string
                enum: 
                  - "earning"
                  - "deduction"
                example: "earning"
              calculationType:
                type: string
                enum: 
                  - "fixed"
                  - "percentage_of_basic"
                  - "percentage_of_gross_salary"
                example: "fixed"
              durationType:
                type: string
                enum: 
                  - "monthly"
                  - "annual"
                  - "pro_rata_on_attendance"
                example: "monthly"
      required:
        - organizationId
        - description
        - name
        - templateEntities
