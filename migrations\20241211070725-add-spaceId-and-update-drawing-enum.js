'use strict';
const { drawingType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Drawing_type" ADD VALUE IF NOT EXISTS 'Space';
    `);

    await queryInterface.changeColumn('Drawing', 'type', {
      type: Sequelize.ENUM(...drawingType.getDrawingTypeArray()),
      allowNull: true,
    });

    await queryInterface.addColumn('Drawing', 'spaceId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Space',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Drawing', 'spaceId');

    await queryInterface.changeColumn('Drawing', 'type', {
      type: Sequelize.ENUM('Project', 'Sub-Project', 'Floor', 'Unit'),
      allowNull: true,
    });
  },
};
