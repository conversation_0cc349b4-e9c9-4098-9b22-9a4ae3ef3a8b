const { genRes, errorMessage, resCode } = require('@config/options');
const ApprovalWorkflowRepository = require('@models/repositories/ApprovalWorkflowRepository');

exports.createApprovalWorkflow = async (req, res) => {
  try {
    const { success, message, data } =
      await ApprovalWorkflowRepository.createApprovalWorkflow(
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateApprovalWorkflow = async (req, res) => {
  const { id: approvalWorkflowId } = req.params;
  try {
    const { success, message, data } =
      await ApprovalWorkflowRepository.updateApprovalWorkflow(
        approvalWorkflowId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
