module.exports = (sequelize, DataTypes) => {
  const CatalogueMedia = sequelize.define(
    'CatalogueMedia',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      mediaType: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  CatalogueMedia.associate = (models) => {
    CatalogueMedia.belongsTo(models.Catalogue, {
      foreignKey: 'catalogueId',
      as: 'catalogue',
      onDelete: 'CASCADE',
    });
  };

  return CatalogueMedia;
};
