const sequelize = require('sequelize');
const { Op } = sequelize;
const _ = require('lodash');
const { AccountData, User } = require('..');
const {
  errorMessage,
  successMessage,
  recordStatus,
  transactionEntryDirection,
} = require('@config/options');
const { getAccountCategoryAndTypes } = require('@config/defaultData');

exports.getAccount = async (query) => await AccountData.findOne(query);

exports.findAndCountAll = async (query) =>
  await AccountData.findAndCountAll(query);

exports.validateAndCreateAccount = async (data) => {
  try {
    const { code } = data;
    const query = {
      where: {
        code,
      },
    };

    const existingAccount = await this.getAccount(query);
    if (existingAccount) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Account Code'),
      };
    }

    const createdAmenity = await AccountData.create(data);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Account'),
      data: createdAmenity,
    };
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Account'),
      };
    }
    throw new Error(error);
  }
};

exports.updateAccountDetails = async (data) => {
  try {
    const { accountId } = data;
    const createdAmenity = await AccountData.update(
      { ...data },
      {
        where: {
          id: accountId,
        },
      }
    );

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Account'),
      data: createdAmenity,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.verifyUniqueAccountCode = async (code) => {
  try {
    const query = {
      where: {
        code,
      },
    };

    const existingAccount = await this.getAccount(query);
    if (existingAccount) {
      return {
        success: true,
        message: errorMessage.ALREADY_EXIST('Account Code'),
        data: {},
      };
    }

    return {
      success: true,
      message: errorMessage.DOES_NOT_EXIST('Account'),
      data: {},
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getAllAccountsData = async (objParams) => {
  try {
    const {
      organizationId = null,
      search = '',
      start = 0,
      limit = 10,
      parentAccountId = null,
      accountId = null,
      accountCategory = null,
      accountType = null,
    } = objParams;

    const intParentAccountId = _.toInteger(parentAccountId);

    const query = {
      where: {
        ...(organizationId && { organizationId }),
        ...(intParentAccountId > 0 && {
          parentAccountId: intParentAccountId,
        }),
        ...(accountId && { id: accountId }),
        ...(accountCategory && { accountCategory }),
        ...(!_.isEmpty(accountType) && { accountType: accountType.split(',') }),
        status: recordStatus.ACTIVE,
      },
      include: [
        {
          model: User,
          as: 'createdByUser',
          attributes: ['id', 'firstName', 'middleName', 'lastName'],
        },
        {
          model: AccountData,
          as: 'subAccounts',
          required: false,
          where: {
            status: recordStatus.ACTIVE,
          },
          include: [
            {
              model: User,
              as: 'createdByUser',
              attributes: ['id', 'firstName', 'middleName', 'lastName'],
            },
            {
              model: AccountData,
              as: 'subAccounts',
              required: false,
              where: {
                status: recordStatus.ACTIVE,
              },
              include: [
                {
                  model: User,
                  as: 'createdByUser',
                  attributes: ['id', 'firstName', 'middleName', 'lastName'],
                },
                {
                  model: AccountData,
                  as: 'subAccounts',
                  required: false,
                  where: {
                    status: recordStatus.ACTIVE,
                  },
                },
              ],
            },
          ],
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
    };

    if (_.isEmpty(accountId) && intParentAccountId === 0) {
      query.where.parentAccountId = {
        [Op.eq]: null,
      };
    }

    if (search) {
      query.where = {
        ...query.where,
        [Op.or]: [
          { name: { [Op.iLike]: `%${search}%` } },
          { code: { [Op.iLike]: `%${search}%` } },
          { description: { [Op.iLike]: `%${search}%` } },
          { bankAccountNumber: { [Op.iLike]: `%${search}%` } },
          { ifscCode: { [Op.iLike]: `%${search}%` } },
        ],
      };
    }

    const { rows, count } = await this.findAndCountAll(query, { logger: true });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Account'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteAccount = async (accountId) => {
  try {
    const query = {
      where: {
        id: accountId,
      },
    };

    const existingAccount = await this.getAccount(query);

    if (existingAccount.flagDefault) {
      return {
        success: false,
        message: errorMessage.DEFAULT_ACCOUNT_DELETE,
        data: {},
      };
    }

    await AccountData.update(
      {
        status: recordStatus.DELETED,
      },
      {
        where: {
          id: accountId,
        },
      }
    );

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Account'),
      data: {},
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.revertAccountTransactionEntry = async (objParams, transaction) => {
  try {
    const { accountId, creditAmount, debitAmount, direction } = objParams;

    const query = {
      where: {
        id: accountId,
      },
    };

    const existingAccount = await this.getAccount(query);

    let updatedField = 'totalCredit';
    let valueUsed = _.toInteger(creditAmount);
    if (_.toInteger(debitAmount) > 0) {
      updatedField = 'totalDebit';
      valueUsed = _.toInteger(debitAmount);
    }

    switch (direction) {
      case transactionEntryDirection.REVERT:
        await existingAccount.decrement(
          updatedField,
          { by: valueUsed },
          { transaction }
        );
        break;
      case transactionEntryDirection.RETAIN:
        await existingAccount.increment(
          updatedField,
          { by: valueUsed },
          { transaction }
        );
        break;
      default:
        break;
    }

    // update opening balance
    await existingAccount.update(
      {
        openingBalance:
          _.toInteger(existingAccount.totalDebit) -
          _.toInteger(existingAccount.totalCredit),
      },
      { transaction }
    );

    return true;
  } catch (error) {
    throw new Error(error);
  }
};

/**
 * This function is used to create default accounts for an organization
 * @param {*} organizationId
 * @param {*} userId
 * @returns bool
 */
exports.createDefaultAccount = async (organizationId, userId) => {
  try {
    const objAccountCategory = getAccountCategoryAndTypes();

    let arrDefaultAccountList = [];

    objAccountCategory.map((objEachAccountCategory) => {
      const { key: accountCategoryKey, arrAccountTypes } =
        objEachAccountCategory;

      arrAccountTypes.map((objEachAccountTypes) => {
        const { key: accountTypeKey } = objEachAccountTypes;

        if (!_.isEmpty(objEachAccountTypes.arrDefaultAccounts)) {
          objEachAccountTypes.arrDefaultAccounts.map((objEachAccount) => {
            const { name } = objEachAccount;

            const objParamsToAccountCreate = {
              organizationId,
              accountCategory: accountCategoryKey,
              accountType: accountTypeKey,
              name: name,
              code: _.kebabCase(`${organizationId}_${name}`),
              description: 'This is default account',
              bankAccountNumber: '',
              ifscCode: '',
              parentAccountId: null,
              totalCredit: 0,
              totalDebit: 0,
              flagDefault: true,
              createdBy: userId,
            };

            arrDefaultAccountList.push(objParamsToAccountCreate);
          });
        }
      });
    });

    await AccountData.bulkCreate(arrDefaultAccountList);

    return {
      success: true,
      message: 'Default account created for organization',
      data: {},
    };
  } catch (error) {
    console.log('error :::::::::::: ', error);
    throw new Error(error);
  }
};
