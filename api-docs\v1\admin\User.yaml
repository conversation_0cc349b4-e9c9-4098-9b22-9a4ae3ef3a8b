paths:
  /admin/user/profile:
    get:
     tags:
       - "User"
     summary: "get profile"
     description: ""
     operationId: "getProfile"
     produces:
       - "application/json"
     parameters: [ ]
     security:
       - bearerAuth: [ ]
     responses:
       "200":
         description: "get user profile details"
       "400":
         description: "Invalid Request"
       "500":
         description: "Internal Server Error"
  /admin/user/user-update:
    put:
      tags:
        - "User"
      summary: "Update your profile"
      description: "Update admin or sub admin profile"
      operationId: "updateProfile"
      requestBody:
        description: update profile
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/adminProfile"
        required: true
      produces:
        - "application/json"
      parameters: [ ]
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "user created or updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /admin/user/{id}:
    get:
      tags:
          - "User"
      summary: "Get user by id"
      description: "Get user by id"
      operationId: "getUserById"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "get User"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"  
    put:
      tags:
          - "User"
      summary: "Update user"
      description: "Update user"
      operationId: "putUpdateUser"
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createUser"
        required: true
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "User Updated Successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "User"
      summary: "Delete User"
      operationId: deleteUser
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "User Deleted successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "User"
      summary: "patch User Status"
      operationId: patchUser
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/changeStatus"
        required: true
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Status Updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /admin/user/:
    get:
      tags:
          - "User"
      summary: "Get user"
      description: "Get user"
      operationId: "getUser"
      produces:
        - "application/json"
      parameters:
        - in: "query"
          name: "startDate"
          schema:
            type: string
        - in: "query"
          name: "endDate"
          schema:
            type: string
        - in: "query"
          name: "status"
          schema:
            type: string
        - in: "query"
          name: "search"
          schema:
            type: string
        - in: "query"
          name: "start"
          schema:
            type: integer
        - in: "query"
          name: "limit"
          schema:
            type: integer
        - in: "query"
          name: "categoryId"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "get User"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    post:
      tags:
        - "User"
      summary: "Create User"
      description: ""
      operationId: "createUser"
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createUser"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Created successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
components:
  schemas:
    adminProfile:
      type: object
      properties:
        email:
          type: string
          description: enter email address
        password:
          type: string
          description: enter password
        firstName:
          type: string
          description: enter first name
        lastName:
          type: string
          description: enter last name
    createUser:
      type: object
      properties:
        firstName:
          type: string
          description: Enter First Name
        lastName:
          type: string
          description: Enter Last Name                
        email:
          type: string
          description: Enter Email
        countryCode:
          type: integer
          description: Enter Country Code  
        mobileNumber:
          type: integer
          description: Enter Mobile Number
        city:
          type: string
          description: Enter City
        state:
          type: string
          description: Enter State
        pincode:
          type: integer
          description: Enter Pincode
      required:
      - firstName
      - lastName
      - email
      - countryCode
      - mobileNumber
      - state
      - city
      - pincode
    changeStatus:
      type: object
      properties:
        status:
          type: string
          description: Enter status
      required:
      - status