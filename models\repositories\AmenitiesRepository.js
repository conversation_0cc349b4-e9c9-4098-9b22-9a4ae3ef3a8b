const sequelize = require('sequelize');
const { Op } = sequelize;
const { Amenities } = require('..');
const { errorMessage, successMessage } = require('../../config/options');

exports.getAmenity = async (query) => await Amenities.findOne(query);

exports.validateAndCreateAmenity = async (data) => {
  const { name } = data;
  try {
    const query = {
      where: {
        name: {
          [Op.iLike]: name,
        },
      },
    };

    const existingAmenity = await this.getAmenity(query);
    if (existingAmenity) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Amenity'),
      };
    }

    const createdAmenity = await Amenities.create(data);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Amenity'),
      data: createdAmenity,
    };
  } catch (error) {
    if (error.name === 'SequelizeUniqueConstraintError') {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Amenity'),
      };
    }
    throw new Error(error);
  }
};

exports.validateAndUpdateAmenity = async (amenityId, data) => {
  const { name, logo } = data;
  try {
    const queryById = {
      where: { id: amenityId },
    };
    const existingAmenity = await this.getAmenity(queryById);
    if (!existingAmenity) {
      return {
        success: false,
        message: errorMessage.DATA_NOT_FOUND,
      };
    }

    if (name) {
      const duplicateAmenity = await this.getAmenity({
        where: {
          name: {
            [Op.iLike]: name,
          },
          id: {
            [Op.ne]: amenityId,
          },
        },
      });
      if (duplicateAmenity) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST('Amenity'),
        };
      }
      existingAmenity.name = name;
    }

    if (logo) {
      existingAmenity.logo = logo;
    }

    const updatedAmenity = await existingAmenity.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Amenity'),
      data: updatedAmenity,
    };
  } catch (error) {
    throw new Error(error);
  }
};
