'use strict';

const { priceChargeCalculationType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const QuotationUnitCost = sequelize.define(
    'QuotationUnitCost',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING,
        allowNull: false,
      },
      chargeType: {
        type: DataTypes.ENUM(
          ...priceChargeCalculationType.priceChargeCalculationTypeArray()
        ),
        allowNull: false,
      },
      rate: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isTaxable: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      taxRate: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: true,
      },
      hsnCode: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      taxAmount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      amount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  QuotationUnitCost.associate = (models) => {
    QuotationUnitCost.belongsTo(models.Quotation, {
      foreignKey: 'quotationId',
      as: 'quotation',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  };

  return QuotationUnitCost;
};
