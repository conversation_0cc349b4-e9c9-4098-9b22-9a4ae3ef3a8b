const express = require('express');
const router = express.Router();
const DepartmentControl = require('../../../../controllers/api/v1/admin/Department.js');
const DepartmentSchema = require('../../../../schema-validation/admin/Department.js');
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(DepartmentSchema.createUpdateDepartment),
  ErrorHandleHelper.requestValidator,
  DepartmentControl.createDepartment
);

router.get('/', DepartmentControl.getDepartments);

router.put(
  '/:id',
  checkSchema(DepartmentSchema.createUpdateDepartment),
  ErrorHandleHelper.requestValidator,
  DepartmentControl.updateDepartment
);

router.get('/:id', DepartmentControl.getDepartmentById);

module.exports = router;
