'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('BoqCostItems', 'estimatedCost', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: true,
    });

    await queryInterface.renameTable('CostItems', 'SubCategoryCostItem');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.renameTable('SubCategoryCostItem', 'CostItems');
    await queryInterface.removeColumn('BoqCostItems', 'estimatedCost');
  },
};
