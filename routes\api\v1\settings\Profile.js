const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ProfileController = require('@controllers/v1/settings/UserProfile');
const ProfileSchema = require('@schema-validation/settings/Profile');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.put(
  '/profile/:id',
  checkSchema(ProfileSchema.updateProfile),
  ErrorHandleHelper.requestValidator,
  ProfileController.updateUserProfile
);

module.exports = router;
