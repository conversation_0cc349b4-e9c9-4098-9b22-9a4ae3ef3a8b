const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const OrganizationBrandControl = require('../../../controllers/api/v1/OrganizationBrand');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');
const OrganizationBrandSchema = require('../../../schema-validation/OrganizationBrand');

router.post(
  '/',
  checkSchema(OrganizationBrandSchema.registerOrganizationBrand),
  ErrorHandleHelper.requestValidator,
  OrganizationBrandControl.registerOrganizationBrand
);

module.exports = router;
