const UtilHelper = require('../models/helpers/UtilHelper');
const {
  getAccountCategoryOnly,
  getAllAccountTypesOnly,
} = require('./defaultData');

const options = {
  randomUsernameSize: 10,
  jwtTokenExpiry: '7d',
  defaultOTP: 5555,
  refreshTokenExpiryTime: 300,
  inviteTokenExpiryTime: '1d',
  userNamePrefix: 'BR',
  otpExpireInDays: 1,
  signedUrlExpireSeconds: 3000,
  defaultStatus: {
    ON_BOARDED: 'on_boarded',
    PENDING: 'pending',
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    BLOCKED: 'blocked',
    UNBLOCKED: 'unblocked',
    DELETED: 'deleted',
    REJECTED: 'rejected',
    COMPLETED: 'completed',
    ONGOING: 'ongoing',
    UN_ASSIGNED: 'un_assigned',
    CONTRACTED: 'contracted',
    QUALIFIED: 'qualified',
    ASSIGNED: 'assigned',
    DRAFTS: 'drafts',
    DELAYED: 'delayed',
    DUE_SOON: 'due_soon',
    ACCEPTED: 'accepted',
    APPROVED: 'approved',
    NEW: 'new',
    ON_HOLD: 'on_hold',
    ALLOTTED: 'allotted',
    ARCHIVED: 'archived',
    UNDER_APPROVAL: 'under_approval',
    BOOKED: 'booked',
    getDefaultStatusArray: () => [
      options.defaultStatus.ON_BOARDED,
      options.defaultStatus.PENDING,
      options.defaultStatus.ACTIVE,
      options.defaultStatus.INACTIVE,
      options.defaultStatus.BLOCKED,
      options.defaultStatus.UNBLOCKED,
      options.defaultStatus.DELETED,
      options.defaultStatus.REJECTED,
      options.defaultStatus.COMPLETED,
      options.defaultStatus.ONGOING,
      options.defaultStatus.UN_ASSIGNED,
      options.defaultStatus.CONTRACTED,
      options.defaultStatus.QUALIFIED,
      options.defaultStatus.ASSIGNED,
      options.defaultStatus.DRAFTS,
      options.defaultStatus.DELAYED,
      options.defaultStatus.DUE_SOON,
      options.defaultStatus.ACCEPTED,
      options.defaultStatus.APPROVED,
      options.defaultStatus.NEW,
      options.defaultStatus.ON_HOLD,
      options.defaultStatus.ALLOTTED,
      options.defaultStatus.ARCHIVED,
      options.defaultStatus.UNDER_APPROVAL,
      options.defaultStatus.BOOKED,
    ],
  },
  projectStatus: {
    NEW_PROJECT: 'new_project',
    DESIGN_PLANNING: 'design_planning',
    SANCTION: 'sanction',
    UNDER_CONSTRUCTION: 'under_construction',
    DELAYED: 'delayed',
    ON_HOLD: 'on_hold',
    NEAR_COMPLETION: 'near_completion',
    COMPLETED: 'completed',
    ARCHIVED: 'archived',
    getProjectStatusArray: () => [
      options.projectStatus.NEW_PROJECT,
      options.projectStatus.DESIGN_PLANNING,
      options.projectStatus.SANCTION,
      options.projectStatus.UNDER_CONSTRUCTION,
      options.projectStatus.DELAYED,
      options.projectStatus.ON_HOLD,
      options.projectStatus.NEAR_COMPLETION,
      options.projectStatus.COMPLETED,
      options.projectStatus.ARCHIVED,
    ],
  },
  transactionType: {
    CREDIT: 'credit',
    DEBIT: 'debit',
    getTransactionTypeArray: () => [
      options.transactionType.CREDIT,
      options.transactionType.DEBIT,
    ],
  },
  boqCalculationType: {
    PERCENTAGE: 'percentage',
    METRIC: 'metric',
    getBoqCalculationTypeArray: () => [
      options.boqCalculationType.PERCENTAGE,
      options.boqCalculationType.METRIC,
    ],
  },
  activityType: {
    CREATED: 'created',
    EDITED: 'edited',
    DELETED: 'deleted',
    MARKED: 'marked',
    getActivityTypeArray: () => [
      options.activityType.CREATED,
      options.activityType.EDITED,
      options.activityType.DELETED,
      options.activityType.MARKED,
    ],
  },
  metricType: {
    VOLUME: 'volume',
    AREA: 'area',
    LENGTH: 'length',
    NUMBER: 'number',
    WEIGHT: 'weight',
    TIME: 'time',
    getMetricTypeArray: () => [
      options.metricType.VOLUME,
      options.metricType.AREA,
      options.metricType.LENGTH,
      options.metricType.NUMBER,
      options.metricType.WEIGHT,
      options.metricType.TIME,
    ],
  },
  costType: {
    METRIC_BASED: 'metric_based',
    FIXED: 'fixed',
    getCostTypeArray: () => [
      options.costType.METRIC_BASED,
      options.costType.FIXED,
    ],
  },
  wbsActivityType: {
    CATEGORY: 'category',
    WBS_ACTIVITY: 'wbsActivity',
    getActivityTypeArray: () => [
      options.wbsActivityType.CATEGORY,
      options.wbsActivityType.WBS_ACTIVITY,
    ],
  },
  paymentTermTemplateDetailsType: {
    ON_DATE: 'on_date',
    MILESTONE: 'milestone',
    paymentTermTemplateDetailsTypeArray: () => [
      options.paymentTermTemplateDetailsType.ON_DATE,
      options.paymentTermTemplateDetailsType.MILESTONE,
    ],
  },
  structureType: {
    TOWER: 'tower',
    PLOTTED: 'plotted',
    getStructureTypeArray: () => [
      options.structureType.TOWER,
      options.structureType.PLOTTED,
    ],
  },
  projectTypeCategory: {
    PROJECT: 'project',
    SUB_PROJECT: 'sub_project',
    getProjectTypeCategoryArray: () => [
      options.projectTypeCategory.PROJECT,
      options.projectTypeCategory.SUB_PROJECT,
    ],
  },
  leaveStatus: {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    CANCELLED: 'cancelled',
    leaveStatusTypeArray: () => [
      options.leaveStatus.PENDING,
      options.leaveStatus.APPROVED,
      options.leaveStatus.REJECTED,
      options.leaveStatus.CANCELLED,
    ],
  },
  permissionsType: {
    view: 'view',
    edit: 'edit',
    restricted: 'restricted',
    getPermissionsArray: () => [
      options.permissionsType.view,
      options.permissionsType.edit,
      options.permissionsType.restricted,
    ],
  },
  templateType: {
    SALARY: 'salary',
    LEAVE: 'leave',
    ATTENDANCE: 'attendance',
    UNPAID_LEAVE_DEDUCTION: 'unpaid_leave_deduction',
    getTemplateTypeArray: () => [
      options.templateType.SALARY,
      options.templateType.LEAVE,
      options.templateType.ATTENDANCE,
      options.templateType.UNPAID_LEAVE_DEDUCTION,
    ],
  },
  taskTagType: {
    TASK_TAG: 'task_tag',
    CUSTOM_TAG: 'custom_tag',
    PRIORITY_TAG: 'priority_tag',
    DEPARTMENT_TAG: 'department_tag',
    getValues: () => [
      options.taskTagType.TASK_TAG,
      options.taskTagType.CUSTOM_TAG,
      options.taskTagType.PRIORITY_TAG,
      options.taskTagType.DEPARTMENT_TAG,
    ],
  },
  templateEntityType: {
    COMPONENT: 'component',
    DEDUCTION: 'deduction',
    EARNING: 'earning',
    LEAVE_POLICY: 'leave_policy',
    UNPAID_LEAVE: 'unpaid_leave',
    REIMBURSEMENT: 'reimbursement',
    BONUS: 'bonus',
    OVERTIME: 'overtime',
    TAX: 'tax',
    ALLOWANCE: 'allowance',
    ATTENDANCE: 'attendance',
    getTemplateEntityTypeArray: () => [
      options.templateEntityType.COMPONENT,
      options.templateEntityType.EARNING,
      options.templateEntityType.DEDUCTION,
      options.templateEntityType.LEAVE_POLICY,
      options.templateEntityType.REIMBURSEMENT,
      options.templateEntityType.BONUS,
      options.templateEntityType.OVERTIME,
      options.templateEntityType.UNPAID_LEAVE,
      options.templateEntityType.TAX,
      options.templateEntityType.ALLOWANCE,
      options.templateEntityType.ATTENDANCE,
    ],
  },
  durationType: {
    MONTHLY: 'monthly',
    ANNUAL: 'annual',
    PRO_RATA_ON_ATTENDANCE: 'pro_rata_on_attendance',
    getDurationTypeArray: () => [
      options.durationType.MONTHLY,
      options.durationType.ANNUAL,
      options.durationType.PRO_RATA_ON_ATTENDANCE,
    ],
  },
  calculationType: {
    FIXED: 'fixed',
    PERCENTAGE: 'percentage',
    PERCENTAGE_OF_BASIC: 'percentage_of_basic',
    PERCENTAGE_OF_GROSS_SALARY: 'percentage_of_gross_salary',
    ATTENDANCE_WITH_LOCATION: 'attendance_with_location',
    ATTENDANCE_WITHOUT_LOCATION: 'attendance_without_location',
    ATTENDANCE_MARK_MANUALLY: 'attendance_mark_manually',
    ATTENDANCE_OPTIONAL: 'attendance_optional',
    UNPAID_DEDUCTION_PRORATED: 'unpaid_deduction_prorated',
    UNPAID_DEDUCTION_FIXED: 'unpaid_deduction_fixed',
    UNPAID_DEDUCTION_MANUAL: 'unpaid_deduction_manual',
    getCalculationTypeArray: () => [
      options.calculationType.FIXED,
      options.calculationType.PERCENTAGE,
      options.calculationType.PERCENTAGE_OF_BASIC,
      options.calculationType.PERCENTAGE_OF_GROSS_SALARY,
      options.calculationType.ATTENDANCE_WITH_LOCATION,
      options.calculationType.ATTENDANCE_WITHOUT_LOCATION,
      options.calculationType.ATTENDANCE_MARK_MANUALLY,
      options.calculationType.ATTENDANCE_OPTIONAL,
      options.calculationType.UNPAID_DEDUCTION_PRORATED,
      options.calculationType.UNPAID_DEDUCTION_FIXED,
      options.calculationType.UNPAID_DEDUCTION_MANUAL,
    ],
  },
  gender: {
    MALE: 'male',
    FEMALE: 'female',
    OTHER: 'other',
    getGenderArray: () => [
      options.gender.MALE,
      options.gender.FEMALE,
      options.gender.OTHER,
    ],
  },
  maritalStatus: {
    SINGLE: 'single',
    MARRIED: 'married',
    DIVORCED: 'divorced',
    WIDOWED: 'widowed',
    getMaritalStatusArray: () => [
      options.maritalStatus.SINGLE,
      options.maritalStatus.MARRIED,
      options.maritalStatus.DIVORCED,
      options.maritalStatus.WIDOWED,
    ],
  },
  notificationCategory: {
    GENERAL: 'general',
    EMAIL: 'email',
    PUSH: 'push',
    SMS_WHATSAPP: 'sms_whatsapp',
    notificationCategoryArray: () => [
      options.notificationCategory.GENERAL,
      options.notificationCategory.EMAIL,
      options.notificationCategory.PUSH,
      options.notificationCategory.SMS_WHATSAPP,
    ],
  },
  loginType: {
    CREDENTIALS_LOGIN: 'credentials_login',
    loginTypeArray: () => [options.loginType.CREDENTIALS_LOGIN],
  },
  approver: {
    ROLE: 'role',
    MEMBER: 'member',
    approverArray: () => [options.approver.ROLE, options.approver.MEMBER],
  },
  escalation: {
    ROLE: 'role',
    MEMBER: 'member',
    escalationArray: () => [options.escalation.ROLE, options.escalation.MEMBER],
  },
  actionType: {
    ADD_USER_WITH_EDIT: 'ADD_USER_WITH_EDIT',
    ADD_USER_WITH_READ: 'ADD_USER_WITH_READ',
    UPDATE_USER_WITH_EDIT: 'UPDATE_USER_WITH_EDIT',
    UPDATE_USER_WITH_READ: 'UPDATE_USER_WITH_READ',
    REMOVE_USER: 'REMOVE_USER',
    EDIT_FILE: 'EDIT_FILE',
    DELETE_FILE: 'DELETE_FILE',
    EDIT_FOLDER: 'EDIT_FOLDER',
    DELETE_FOLDER: 'DELETE_FOLDER',
    ADD_FOLDER: 'ADD_FOLDER',
    ADD_FILE: 'ADD_FILE',
    getActionTypeArray: () => [
      options.actionType.ADD_USER_WITH_EDIT,
      options.actionType.ADD_USER_WITH_READ,
      options.actionType.UPDATE_USER_WITH_EDIT,
      options.actionType.UPDATE_USER_WITH_READ,
      options.actionType.REMOVE_USER,
      options.actionType.ADD_FOLDER,
      options.actionType.ADD_FILE,
      options.actionType.EDIT_FILE,
      options.actionType.DELETE_FILE,
      options.actionType.EDIT_FOLDER,
      options.actionType.DELETE_FOLDER,
    ],
  },
  attendanceStatus: {
    APPROVED_LEAVE: 'approved_leave',
    UNAPPROVED_LEAVE: 'unapproved_leave',
    FULL_DAY: 'full_day',
    HALF_DAY: 'half_day',
    COMPANY_HOLIDAY: 'company_holiday',
    WORK_FROM_HOME: 'work_from_home',
    getAttendanceStatusArray: () => [
      options.attendanceStatus.APPROVED_LEAVE,
      options.attendanceStatus.UNAPPROVED_LEAVE,
      options.attendanceStatus.FULL_DAY,
      options.attendanceStatus.HALF_DAY,
      options.attendanceStatus.COMPANY_HOLIDAY,
      options.attendanceStatus.WORK_FROM_HOME,
    ],
  },
  emailSubjects: {
    OTP: 'OTP',
    CREDENTIALS: 'Credentials',
  },
  tokenType: {
    ACCESS_TOKEN: 'access_token',
    REFRESH_TOKEN: 'refresh_token',
  },
  notificationType: {
    ACCOUNT_APPROVED: 'account_approved',
  },
  genNotificationMessage: {},
  emailType: {
    EMAIL_OTP_VERIFICATION: 'email_otp_verification',
    EMAIL_REGISTERED_SUCCESSFULLY: 'email_registered_successfully',
    EMAIL_SKILL_VERIFICATION: 'email_skill_verification',
    EMAIL_PROFILE_APPROVED: 'email_profile_approved',
    EMAIL_ACCOUNT_BLOCKED: 'email_account_blocked',
    EMAIL_PROFILE_REJECTED: 'email_profile_rejected',
    EMAIL_MOBILE_NUMBER_CHANGED: 'email_mobile_number_changed',
    EMAIL_CHANGED: 'email_changed',
  },
  resCode: {
    HTTP_OK: 200,
    HTTP_CREATE: 201,
    HTTP_NO_CONTENT: 204,
    HTTP_BAD_REQUEST: 400,
    HTTP_UNAUTHORIZED: 401,
    HTTP_FORBIDDEN: 403,
    HTTP_NOT_FOUND: 404,
    HTTP_METHOD_NOT_ALLOWED: 405,
    HTTP_CONFLICT: 409,
    HTTP_INTERNAL_SERVER_ERROR: 500,
    HTTP_SERVICE_UNAVAILABLE: 503,
  },

  errorTypes: {
    OAUTH_EXCEPTION: 'OAuthException',
    ACCESS_DENIED_EXCEPTION: 'AccessDeniedException',
    ALREADY_AUTHENTICATED: 'AlreadyAuthenticated',
    UNAUTHORIZED_ACCESS: 'UnauthorizedAccess',
    FORBIDDEN: 'Forbidden',
    INPUT_VALIDATION: 'InputValidationException',
    ACCOUNT_ALREADY_EXIST: 'AccountAlreadyExistException',
    ACCOUNT_DOES_NOT_EXIST: 'AccountDoesNotExistException',
    ENTITY_NOT_FOUND: 'EntityNotFound',
    ACCOUNT_BLOCKED: 'AccountBlocked',
    ACCOUNT_DEACTIVATED: 'AccountDeactivated',
    CONTENT_BLOCKED: 'ContentBlocked',
    CONTENT_REMOVED: 'ContentRemoved',
    PRIVATE_CONTENT: 'PrivateContent',
    PRIVATE_ACCOUNT: 'PrivateAccount',
    DUPLICATE_REQUEST: 'DuplicateRequest',
    EMAIL_NOT_VERIFIED: 'emailNotVerified',
    MOBILE_NUMBER_NOT_VERIFIED: 'mobileNumberNotVerified',
    INTERNAL_SERVER_ERROR: 'InternalServerError',
    CATCH_ERRORS: 'Oops! something went wrong.',
    INVALID_INVITE_LINK: 'InvalidInviteLink',
    INVITE_ALREADY_USED: 'InviteAlreadyAccepted',
  },
  errorMessage: {
    UNAUTHORIZED_ACCESS: 'Not authorized to perform this action',
    UPLOAD_ALL_REQUIRED_DOCUMENTS: 'Please upload all the required documents',
    SERVER_ERROR: 'Oops! something went wrong.',
    INVALID_CREDENTIALS: 'The email and/or password entered are incorrect',
    OTP_INVALID: 'Invalid Otp',
    CONTACT_ADMIN: 'Contact admin to perform edit',
    COUNTRY_CODE: 'Please add country code',
    INCORRECT_DATA: (data) => `The ${data} entered is incorrect`,
    INVALID_REQUEST: 'Invalid Request',
    USER_ACCOUNT_BLOCKED: 'Your account has been blocked, Please contact admin',
    ROLE_INVALID_LOGIN: 'Account access denied',
    NO_USER: (data) => `User does not exists with this ${data}`,
    EXISTS_USER: (data) => `A user with ${data} already exists.`,
    DOES_NOT_EXIST: (data) => `The ${data} does not exist`,
    ALREADY_EXIST: (data) => `The ${data} already exist`,
    INCORRECT_FILE_DATA: 'File contains invalid data',
    SAME_EMAIL_MOBILE_EXISTS: (data) => `User with same ${data} already exists`,
    DATA_NOT_FOUND: 'Data not found',
    MOBILE_NUMBER_ALREADY_EXISTS:
      'User with this mobile number is already exist',
    INVALID_INVITE_LINK: 'The invite link is invalid or has expired',
    INVITE_ALREADY_USED: 'The invite link has already been accepted',
    NO_PERMISSION: (data) => `The User does not have permission for ${data}`,
    REQUIRED: (data) => `The ${data} is required`,
    EXISTING_SOURCE: (data) => `A source with ${data} already exists.`,
    TOP_FLOOR: 'This is the top floor, cannot move up.',
    LOWEST_FLOOR: 'This is the lowest floor, cannot move down.',
    WEATHER_API_ERROR:
      'Failed to fetch weather data from the API. Please try again later.',
    CANNOT_REMOVE_PROJECT_CREATOR:
      'You do not have permission to remove yourself as the project creator. Please assign another user as the project creator before removing yourself.',
    LOGOUT_ERROR_MESSAGE: 'Failed to log out device session.',
    TEAM_MEMBER_ALREADY_ASSIGNED: `A team member has already been assigned to this requirement.`,
    NO_TEAM_MEMBER_AVAILABLE: 'No available team member for assignment',
    INVALID_ID: (data) =>
      `The ${data} ID you entered does not match any records. Please verify and try again.`,
    INVITATION_NOT_FOUND: `Invitation not found or already accepted/rejected.`,
    USER_ALREADY_IN_PROJECT: (data) =>
      `User ${data} is already a member of this project`,
    PERMISSION_DENIED:
      'You do not have the necessary permissions to access this resource.',
    DEFAULT_ACCOUNT_DELETE: 'Default account cannot be deleted',
    NO_VALID_EMAIL_FOR_OTP:
      'User does not have a valid email address to send OTP',
    OUT_OF_RANGE: (data) =>
      `You are not within 200 meters of the office location to ${data}`,
    DOWNLOAD_PROCESS_FAILED: (processName) =>
      `Failed to download ${processName}`,
    INVALID_STATUS: `The requested status is the same as the current status`,
    EXPIRE_DETAILS: (data) => `This ${data} Details has expired.`,
    ALREADY_BOOKED: (data) => `The ${data} is already Booked`,
    INVITATION_REJECTED: `This invitation is no longer valid as it was rejected.`,
    INVITATION_ACTION_NOT_ALLOWED: `Action cannot be performed as the profile has already been completed.`,
    INVITATION_NOT_IN_PENDING_STATE: `Sorry, the invitation can only be sent when it’s in 'Pending' state.`,
    TAX_ID_INVALID: (taxId) =>
      `Tax ID ${taxId} is invalid or does not belong to the organization. Please verify the tax ID and try again.`,
    INVALID_INPUT: (data) => `Invalid input: ${data}`,
    PRIMARY_CONTACT_PERSON:
      'Primary contact person for this customer already exists',
  },
  successMessage: {
    OTP_SEND: (type) => `An OTP has been send to your ${type}`,
    OTP_VERIFIED: (type) => `OTP has been verified`,
    LOG: (data) => `You have ${data} successfully`,
    UPDATE_SUCCESS_MESSAGE: (data) => `${data} updated successfully`,
    DELETE_SUCCESS_MESSAGE: (data) => `${data} deleted successfully`,
    REMOVED_SUCCESS_MESSAGE: (data) => `${data} removed successfully`,
    ADD_SUCCESS_MESSAGE: (data) => `${data} added successfully`,
    SAVED_SUCCESS_MESSAGE: (data) => `${data} saved successfully`,
    GENERATE_SUCCESS_MESSAGE: (data) => `${data} generate successfully`,
    CHANGED_SUCCESS_MESSAGE: (data) => `${data} changed successfully`,
    VERIFIED_SUCCESS_MESSAGE: (data) => `${data} verified successfully`,
    SEND_SUCCESS_MESSAGE: (data) => `${data} send successfully`,
    DETAIL_MESSAGE: (data) => `Fetched ${data} details successfully`,
    RESET_SUCCESS_MESSAGE: (data) => `reset ${data} successfully`,
    DUPLICATE_ADD_SUCCESS_MESSAGE: (data) =>
      `Duplicate ${data} added successfully`,
    FETCH_SUCCESS_MESSAGE: (data) => `${data} data fetched successfully`,
    LOGOUT_SUCCESS_MESSAGE: () => `You have been logged out!`,
    INVITATION_SENT: `Invitation sent successfully`,
    INVITATION_ACCEPTED: 'Invitation has been accepted successfully.',
    INVITATION_REJECTED: 'Invitation has been rejected successfully.',
    ACCOUNT_CATEGORY_TYPE: 'Account Category and Types fetched successfully',
    ACCOUNT_ENTRY_TYPE: 'Account Entries Types fetched successfully',
    USER_ONBOARDED: 'User has been onboarded successfully.',
    OTP_SENT_TO_EMAIL: (email, mfa) =>
      `OTP sent to ${email}. Please verify to ${mfa} MFA`,
    MFA_TOGGLE_SUCCESS: (isEnabled) =>
      `MFA has been ${isEnabled ? 'enabled' : 'disabled'} successfully.`,
    RESET_PASSWORD_LINK_SENT:
      'A password reset link has been sent to your email address. Please check your inbox to reset your password.',
    PASSWORD_RESET_SUCCESS:
      'Your password has been successfully reset. You can now log in with your new password.',
  },
  usersRoles: {
    SUPER_ADMIN: 'SUPER_ADMIN',
    ADMIN: 'ADMIN',
    USER: 'USER',
    BUILDER: 'BUILDER',
    VENDOR: 'VENDOR',
    BRAND: 'BRAND',
    EMPLOYEE: 'EMPLOYEE',
    SALES_AGENT: 'SALES_AGENT',
    CUSTOMER: 'CUSTOMER',
    CONTACT_PERSON: 'CONTACT_PERSON',
    getAdminArray: () => [
      options.usersRoles.SUPER_ADMIN,
      options.usersRoles.ADMIN,
    ],
    getAllRolesAsArray: () => [
      options.usersRoles.SUPER_ADMIN,
      options.usersRoles.ADMIN,
      options.usersRoles.USER,
      options.usersRoles.BUILDER,
      options.usersRoles.VENDOR,
      options.usersRoles.BRAND,
      options.usersRoles.CUSTOMER,
      options.usersRoles.CONTACT_PERSON,
      options.usersRoles.EMPLOYEE,
    ],
    getUserRolesAsArray: () => [
      options.usersRoles.USER,
      options.usersRoles.BUILDER,
      options.usersRoles.VENDOR,
      options.usersRoles.BRAND,
      options.usersRoles.CUSTOMER,
      options.usersRoles.CONTACT_PERSON,
      options.usersRoles.EMPLOYEE,
    ],
  },
  addressType: {
    EMPLOYEE: 'employee',
    PROJECT: 'project',
    ORGANIZATION: 'organization',
    CONTACT_PERSON: 'contact_person',
    WAREHOUSE: 'warehouse',
    INVITED_USER: 'invited_user',
    getAddressTypeArray: () => [
      options.addressType.EMPLOYEE,
      options.addressType.PROJECT,
      options.addressType.CONTACT_PERSON,
      options.addressType.WAREHOUSE,
    ],
  },
  facing: {
    NORTH: 'North',
    SOUTH: 'South',
    EAST: 'East',
    WEST: 'West',
    NORTH_EAST: 'North-East',
    SOUTH_EAST: 'South-East',
    SOUTH_WEST: 'South-West',
    NORTH_WEST: 'North-West',
    getFacingArray: () => [
      options.facing.NORTH,
      options.facing.SOUTH,
      options.facing.EAST,
      options.facing.WEST,
      options.facing.NORTH_EAST,
      options.facing.SOUTH_EAST,
      options.facing.SOUTH_WEST,
      options.facing.NORTH_WEST,
    ],
  },
  drawingType: {
    PROJECT: 'Project',
    SUB_PROJECT: 'Sub-Project',
    FLOOR: 'Floor',
    UNIT: 'Unit',
    SPACE: 'Space',
    getDrawingTypeArray: () => [
      options.drawingType.PROJECT,
      options.drawingType.SUB_PROJECT,
      options.drawingType.FLOOR,
      options.drawingType.UNIT,
      options.drawingType.SPACE,
    ],
  },
  customerType: {
    PERSON: 'person',
    CORPORATE: 'corporate',
    EXISTING: 'existing',
    getCustomerTypeArray: () => [
      options.customerType.PERSON,
      options.customerType.CORPORATE,
      options.customerType.EXISTING,
    ],
  },
  requirement: {
    purpose: {
      INVESTMENT: 'investment',
      END_USE: 'end_use',
      RENTAL: 'rental',
      RESALE: 'resale',
      getPurposeArray: () => [
        options.requirement.purpose.INVESTMENT,
        options.requirement.purpose.END_USE,
        options.requirement.purpose.RENTAL,
        options.requirement.purpose.RESALE,
      ],
    },
    possessionBy: {
      IMMEDIATE: 'immediate',
      ONE_MONTH: 'one_month',
      TWO_MONTHS: 'two_months',
      SIX_MONTHS: 'six_months',
      ONE_YEAR: 'one_year',
      TWO_YEARS: 'two_years',
      THREE_YEARS: 'three_years',
      FOUR_YEARS: 'four_years',
      FIVE_YEARS: 'five_years',
      SEVEN_YEARS: 'seven_years',
      TEN_YEARS: 'ten_years',
      TEN_PLUS_YEARS: 'ten_plus_years',
      getPossessionByArray: () => [
        options.requirement.possessionBy.IMMEDIATE,
        options.requirement.possessionBy.ONE_MONTH,
        options.requirement.possessionBy.TWO_MONTHS,
        options.requirement.possessionBy.SIX_MONTHS,
        options.requirement.possessionBy.ONE_YEAR,
        options.requirement.possessionBy.TWO_YEARS,
        options.requirement.possessionBy.THREE_YEARS,
        options.requirement.possessionBy.FOUR_YEARS,
        options.requirement.possessionBy.FIVE_YEARS,
        options.requirement.possessionBy.SEVEN_YEARS,
        options.requirement.possessionBy.TEN_YEARS,
        options.requirement.possessionBy.TEN_PLUS_YEARS,
      ],
    },
    fundingType: {
      HOME_LOAN: 'home_loan',
      MIXED: 'mixed',
      PERSONAL: 'personal',
      BARTER: 'barter',
      getFundingTypeArray: () => [
        options.requirement.fundingType.HOME_LOAN,
        options.requirement.fundingType.MIXED,
        options.requirement.fundingType.PERSONAL,
        options.requirement.fundingType.BARTER,
      ],
    },
    propertyType: {
      VILLA: 'villa',
      ROW_HOUSE: 'row_house',
      DUPLEX: 'duplex',
      PENTHOUSE: 'penthouse',
      SEMI_DETACHED: 'semi_detached',
      APARTMENT: 'apartment',
      PLOTTED_DEVELOPMENT: 'plotted_development',
      COMMERCIAL: 'commercial',
      getPropertyTypeArray: () => [
        options.requirement.propertyType.VILLA,
        options.requirement.propertyType.ROW_HOUSE,
        options.requirement.propertyType.DUPLEX,
        options.requirement.propertyType.PENTHOUSE,
        options.requirement.propertyType.SEMI_DETACHED,
        options.requirement.propertyType.APARTMENT,
        options.requirement.propertyType.PLOTTED_DEVELOPMENT,
        options.requirement.propertyType.COMMERCIAL,
      ],
    },
    configuration: {
      RESIDENTIAL: 'residential',
      COMMERCIAL: 'commercial',
      PLOTTED_DEVELOPMENT: 'plotted_development',
      getConfigurationArray: () => [
        options.requirement.configuration.RESIDENTIAL,
        options.requirement.configuration.COMMERCIAL,
        options.requirement.configuration.PLOTTED_DEVELOPMENT,
      ],
    },
    configurationType: {
      STUDIO: 'studio',
      ONE_BHK: '1_bhk',
      TWO_BHK: '2_bhk',
      THREE_BHK: '3_bhk',
      FOUR_BHK: '4_bhk',
      FIVE_BHK: '5_bhk',
      FIVE_PLUS_BHK: '5_plus_bhk',
      SHOP: 'shop',
      OFFICE: 'office',
      RESIDENTIAL: 'residential',
      COMMERCIAL: 'commercial',
      OTHERS: 'others',
      getConfigurationTypeArray: () => [
        options.requirement.configurationType.STUDIO,
        options.requirement.configurationType.ONE_BHK,
        options.requirement.configurationType.TWO_BHK,
        options.requirement.configurationType.THREE_BHK,
        options.requirement.configurationType.FOUR_BHK,
        options.requirement.configurationType.FIVE_BHK,
        options.requirement.configurationType.FIVE_PLUS_BHK,
        options.requirement.configurationType.SHOP,
        options.requirement.configurationType.OFFICE,
        options.requirement.configurationType.RESIDENTIAL,
        options.requirement.configurationType.COMMERCIAL,
        options.requirement.configurationType.OTHERS,
      ],
    },
    furnishingType: {
      UNFURNISHED: 'unfurnished',
      SEMI_FURNISHED: 'semi_furnished',
      FULLY_FURNISHED: 'fully_furnished',
      getFurnishingTypeArray: () => [
        options.requirement.furnishingType.UNFURNISHED,
        options.requirement.furnishingType.SEMI_FURNISHED,
        options.requirement.furnishingType.FULLY_FURNISHED,
      ],
    },
    directionPreference: {
      ANY: 'any',
      NORTH: 'north',
      EAST: 'east',
      SOUTH: 'south',
      WEST: 'west',
      NORTH_EAST: 'north_east',
      SOUTH_EAST: 'south_east',
      SOUTH_WEST: 'south_west',
      NORTH_WEST: 'north_west',
      getDirectionPreferenceArray: () => [
        options.requirement.directionPreference.ANY,
        options.requirement.directionPreference.NORTH,
        options.requirement.directionPreference.EAST,
        options.requirement.directionPreference.SOUTH,
        options.requirement.directionPreference.WEST,
        options.requirement.directionPreference.NORTH_EAST,
        options.requirement.directionPreference.SOUTH_EAST,
        options.requirement.directionPreference.SOUTH_WEST,
        options.requirement.directionPreference.NORTH_WEST,
      ],
    },
  },
  priority: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    getPriorityArray: () => [
      options.priority.LOW,
      options.priority.MEDIUM,
      options.priority.HIGH,
    ],
  },
  floorType: {
    PROJECT: 'project',
    UNIT: 'unit',
    getFloorTypeArray: () => [
      options.floorType.PROJECT,
      options.floorType.UNIT,
    ],
  },
  priceChargeCalculationType: {
    AGREEMENT_VALUE: 'agreement_value',
    PERCENTAGE_OF_AGREEMENT_VALUE: 'percentage_of_agreement_value',
    FIXED_AMOUNT: 'fixed_amount',
    SUPER_BUILT_UP_AREA: 'super_built_up_area',
    CARPET_AREA: 'carpet_area',
    TAX: 'tax',
    priceChargeCalculationTypeArray: () => [
      options.priceChargeCalculationType.AGREEMENT_VALUE,
      options.priceChargeCalculationType.PERCENTAGE_OF_AGREEMENT_VALUE,
      options.priceChargeCalculationType.FIXED_AMOUNT,
      options.priceChargeCalculationType.SUPER_BUILT_UP_AREA,
      options.priceChargeCalculationType.CARPET_AREA,
      options.priceChargeCalculationType.TAX,
    ],
  },
  paymentStageCalculationType: {
    PERCENTAGE_OF_AGREEMENT_VALUE: 'percentage_of_agreement_value',
    FIXED_AMOUNT: 'fixed_amount',
    PERCENTAGE_OF_TOTAL_SALE_VALUE: 'percentage_of_total_sale_value',
    paymentStageCalculationTypeArray: () => [
      options.paymentStageCalculationType.PERCENTAGE_OF_AGREEMENT_VALUE,
      options.paymentStageCalculationType.FIXED_AMOUNT,
      options.paymentStageCalculationType.PERCENTAGE_OF_TOTAL_SALE_VALUE,
    ],
  },
  paymentTriggerType: {
    BROKER_PAYOUT_PLAN: 'broker_payout_plan',
    PROJECT_STATUS: 'project_status',
    PROJECT_PROGRESS: 'project_progress',
    DATE: 'date',
    NONE: 'none_manual',
    paymentTriggerTypeArray: () => [
      options.paymentTriggerType.BROKER_PAYOUT_PLAN,
      options.paymentTriggerType.PROJECT_STATUS,
      options.paymentTriggerType.PROJECT_PROGRESS,
      options.paymentTriggerType.DATE,
      options.paymentTriggerType.NONE,
    ],
  },
  contactPersonRole: {
    PRIMARY: 'primary',
    SECONDARY: 'secondary',
    getContactPersonRoleArray: () => [
      options.contactPersonRole.PRIMARY,
      options.contactPersonRole.SECONDARY,
    ],
  },
  paymentPlanType: {
    BROKER_PAYOUT_PLAN: 'broker_payout_plan',
    PAYMENT_PLAN: 'payment_plan',
    paymentPlanTypeArray: () => [
      options.paymentPlanType.BROKER_PAYOUT_PLAN,
      options.paymentPlanType.PAYMENT_PLAN,
    ],
  },
  customerStatusTriggerType: {
    NEW_LEAD: 'new_lead',
    SITE_VISIT_SCHEDULED: 'site_visit_scheduled',
    SCHEDULED_DATE: 'date',
    NONE: 'none_manual',
    ALLOTMENT_LETTER_SENT: 'allotment_letter_sent',
    PROJECT_PROGRESS: 'project_progress',
    TASK: 'task',
    customerStatusTriggerTypeArray: () => [
      options.customerStatusTriggerType.NEW_LEAD,
      options.customerStatusTriggerType.SITE_VISIT_SCHEDULED,
      options.customerStatusTriggerType.SCHEDULED_DATE,
      options.customerStatusTriggerType.NONE,
      options.customerStatusTriggerType.ALLOTMENT_LETTER_SENT,
      options.customerStatusTriggerType.PROJECT_PROGRESS,
      options.customerStatusTriggerType.TASK,
    ],
  },
  contractorType: {
    INDIVIDUAL: 'individual',
    BUSINESS: 'business',
    contractorTypeArray: () => [
      options.contractorType.INDIVIDUAL,
      options.contractorType.BUSINESS,
    ],
  },
  contactPersonCategory: {
    INDIVIDUAL: 'individual',
    BUSINESS: 'business',
    getContactPersonCategoryArray: () => [
      options.contactPersonCategory.INDIVIDUAL,
      options.contactPersonCategory.BUSINESS,
    ],
  },
  contactPersonType: {
    PRIMARY: 'primary',
    SECONDARY: 'secondary',
    getContactPersonTypeArray: () => [
      options.contactPersonType.PRIMARY,
      options.contactPersonType.SECONDARY,
    ],
  },
  customerStatus: {
    NEW_LEAD: 'new_lead',
    QUALIFIED: 'qualified',
    SITE_VISIT_COMPLETED: 'site_visit_completed',
    NEGOTIATION: 'negotiation',
    ON_HOLD: 'on_hold',
    NO_FUTURE_ACTIVITY: 'no_future_activity',
    LOST: 'lost',
    UNQUALIFIED: 'unqualified',
    ARCHIVED: 'archived',
    getCustomerStatusArray: () => [
      options.customerStatus.NEW_LEAD,
      options.customerStatus.QUALIFIED,
      options.customerStatus.SITE_VISIT_COMPLETED,
      options.customerStatus.NEGOTIATION,
      options.customerStatus.ON_HOLD,
      options.customerStatus.NO_FUTURE_ACTIVITY,
      options.customerStatus.LOST,
      options.customerStatus.UNQUALIFIED,
      options.customerStatus.ARCHIVED,
    ],
  },
  bankDetailStatus: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    DELETED: 'deleted',
    contractorTypeArray: () => [
      options.bankDetailStatus.ACTIVE,
      options.bankDetailStatus.INACTIVE,
      options.bankDetailStatus.DELETED,
    ],
  },
  inviteStatus: {
    PENDING: 'pending',
    ACCEPTED: 'accepted',
    REJECTED: 'rejected',
    getInviteStatusArray: () => [
      options.inviteStatus.PENDING,
      options.inviteStatus.ACCEPTED,
      options.inviteStatus.REJECTED,
    ],
  },
  inviteAction: {
    ACCEPT: 'accept',
    REJECT: 'reject',
    getInviteActionArray: () => [
      options.inviteAction.ACCEPT,
      options.inviteAction.REJECT,
    ],
  },
  assetType: {
    AMENITIES: 'amenities',
    WBS_CATEGORY: 'wbs_category',
    WBS_SUBCATEGORY: 'wbs_subcategory',
    getAssetTypeArray: () => [
      options.assetType.AMENITIES,
      options.assetType.WBS_CATEGORY,
      options.assetType.WBS_SUBCATEGORY,
    ],
  },
  prefixedRegex: /^[A-Z]+\d*-\d+$/,
  prefixedAlphanumericRegex: /^[A-Z]+\d*-\d+[A-Z]$/,
  nonPrefixedRegex: /^\d+$/,
  nonPrefixedAlphanumericRegex: /^\d+[A-Z]$/,

  genOtp: UtilHelper.genOtp,
  genRes: UtilHelper.genRes,
  generateCloudFrontUrl: UtilHelper.generateCloudFrontUrl,
  getUploadsPath: UtilHelper.getUploadsPath,
  accessManagementType: {
    DASHBOARD: 'dashboard',
    USERS: 'users',
  },
  taskStatus: {
    TODO: 'todo',
    PENDING: 'pending',
    IN_PROGRESS: 'in_progress',
    ONGOING: 'ongoing',
    ON_HOLD: 'on_hold',
    APPROVAL_PENDING: 'approval_pending',
    REVISION_NEEDED: 'revision_needed',
    COMPLETED: 'completed',
    CANCELLED: 'cancelled',
    OVERDUE: 'overdue',
    DELAYED: 'delayed',
    ESCALATED: 'escalated',
    DELEGATED: 'delegated',
    getValues: () => [
      options.taskStatus.TODO,
      options.taskStatus.PENDING,
      options.taskStatus.IN_PROGRESS,
      options.taskStatus.ONGOING,
      options.taskStatus.ON_HOLD,
      options.taskStatus.APPROVAL_PENDING,
      options.taskStatus.REVISION_NEEDED,
      options.taskStatus.COMPLETED,
      options.taskStatus.CANCELLED,
      options.taskStatus.OVERDUE,
      options.taskStatus.DELAYED,
      options.taskStatus.ESCALATED,
      options.taskStatus.DELEGATED,
    ],
  },
  subTaskStatus: {
    PENDING: 'pending',
    COMPLETED: 'completed',
    getValues: () => [
      options.subTaskStatus.PENDING,
      options.subTaskStatus.COMPLETED,
    ],
  },
  taskPriority: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent',
    CRITICAL: 'critical',
    getValues: () => [
      options.taskPriority.LOW,
      options.taskPriority.MEDIUM,
      options.taskPriority.HIGH,
      options.taskPriority.URGENT,
      options.taskPriority.CRITICAL,
    ],
  },
  dependencyType: {
    BLOCKING: 'blocking',
    BLOCKEDBY: 'blockedBy',
    getValues: () => [
      options.dependencyType.BLOCKING,
      options.dependencyType.BLOCKEDBY,
    ],
  },
  dependOn: {
    TASK: 'task',
    REQUEST: 'request',
    ACTIVITY: 'activity',
    getValues: () => [
      options.dependOn.TASK,
      options.dependOn.REQUEST,
      options.dependOn.ACTIVITY,
    ],
  },
  dependencyStatus: {
    FS: 'fs',
    FF: 'ff',
    SS: 'ss',
    SF: 'sf',
    getValues: () => [
      options.dependencyStatus.FS,
      options.dependencyStatus.FF,
      options.dependencyStatus.SS,
      options.dependencyStatus.SF,
    ],
  },
  organizationType: {
    PRIVATE_LIMITED: 'private_limited',
    LIMITED: 'limited',
    LLP: 'llp',
    PARTNERSHIP_FIRM: 'partnership_firm',
    SOLE_PROPRIETORSHIP: 'sole_proprietorship',
    getValues: () => [
      options.organizationType.PRIVATE_LIMITED,
      options.organizationType.LIMITED,
      options.organizationType.LLP,
      options.organizationType.PARTNERSHIP_FIRM,
      options.organizationType.SOLE_PROPRIETORSHIP,
    ],
  },
  organizationTimeFormat: {
    HOURS_12: '12_hours',
    HOURS_24: '24_hours',
    getValues: () => [
      options.organizationTimeFormat.HOURS_12,
      options.organizationTimeFormat.HOURS_24,
    ],
  },
  organizationUpdateType: {
    LOCALISATION_INFORMATION: 'Localisation Information',
    BASIC_INFORMATION: 'Basic Information',
    getValues: () => [
      options.organizationUpdateType.LOCALISATION_INFORMATION,
      options.organizationUpdateType.BASIC_INFORMATION,
    ],
  },
  accountCategory: {
    DEFAULT_VALUE: 'ASSETS',
    getValues: () => {
      return getAccountCategoryOnly();
    },
  },
  accountType: {
    DEFAULT_VALUE: 'OTHER_ASSETS',
    getValues: () => {
      return getAllAccountTypesOnly();
    },
  },
  accountStateStatusInfo: {
    ACTIVE: 'active',
    DELETED: 'deleted',
    getValues: () => [
      options.accountStateStatusInfo.ACTIVE,
      options.accountStateStatusInfo.DELETED,
    ],
  },
  accountAssociation: {
    STATIC: '',
    EMPLOYEE: 'employee',
    CUSTOMER: 'customer',
    getValues: () => [
      options.accountAssociation.STATIC,
      options.accountAssociation.EMPLOYEE,
      options.accountAssociation.CUSTOMER,
    ],
  },
  transactionTypeForEntries: {
    JOURNAL: 'journal',
    EXPENSE: 'expense',
    PAYMENT_RECEIVED: 'payment_received',
    getValues: () => [
      options.transactionTypeForEntries.JOURNAL,
      options.transactionTypeForEntries.EXPENSE,
      options.transactionTypeForEntries.PAYMENT_RECEIVED,
    ],
  },
  transactionEntryDirection: {
    REVERT: 'revert',
    RETAIN: 'retain',
  },
  budgetEntryType: {
    INCOME: 'income',
    EXPENSE: 'expense',
    getValues: () => [
      options.budgetEntryType.INCOME,
      options.budgetEntryType.EXPENSE,
    ],
  },
  recordStatus: {
    ACTIVE: 'active',
    DELETED: 'deleted',
    getValues: () => [
      options.recordStatus.ACTIVE,
      options.recordStatus.DELETED,
    ],
  },
  expenseState: {
    DRAFT: 'draft',
    SAVED: 'saved',
    UNDER_APPROVAL: 'under_approval',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    getValues: () => [
      options.expenseState.DRAFT,
      options.expenseState.SAVED,
      options.expenseState.UNDER_APPROVAL,
      options.expenseState.REJECTED,
      options.expenseState.ESCALATED,
    ],
  },
  journalState: {
    DRAFT: 'draft',
    SAVED: 'saved',
    UNDER_APPROVAL: 'under_approval',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    getValues: () => [
      options.journalState.DRAFT,
      options.journalState.SAVED,
      options.journalState.UNDER_APPROVAL,
      options.journalState.REJECTED,
      options.journalState.ESCALATED,
    ],
  },
  requestPriority: {
    LOW: 'low',
    MEDIUM: 'medium',
    HIGH: 'high',
    URGENT: 'urgent',
    CRITICAL: 'critical',
    getValues: () => [
      options.requestPriority.LOW,
      options.requestPriority.MEDIUM,
      options.requestPriority.HIGH,
      options.requestPriority.URGENT,
      options.requestPriority.CRITICAL,
    ],
  },
  requestType: {
    GENERAL_REQUEST: 'general_request',
    LEAVE_REQUEST: 'leave_request',
    PAYMENT_REQUEST: 'payment_request',
    INDENT_MR_REQUEST: 'indent_mr_request',
    STOCK_ADJUSTMENT_REQUEST: 'stock_adjustment_request',
    WORK_ORDER_REQUEST: 'work_order_request',
    WBS_REQUEST: 'wbs_request',
    JOURNAL_REQUEST: 'journal_request',
    BUDGET_REQUEST: 'budget_request',
    EXPENSE_REQUEST: 'expense_request',
    CREDIT_NOTE_REQUEST: 'credit_note_request',
    QUOTATION_REQUEST: 'quotation_request',
    getValues: () => [
      options.requestType.GENERAL_REQUEST,
      options.requestType.LEAVE_REQUEST,
      options.requestType.PAYMENT_REQUEST,
      options.requestType.INDENT_MR_REQUEST,
      options.requestType.STOCK_ADJUSTMENT_REQUEST,
      options.requestType.WORK_ORDER_REQUEST,
      options.requestType.WBS_REQUEST,
      options.requestType.JOURNAL_REQUEST,
      options.requestType.BUDGET_REQUEST,
      options.requestType.EXPENSE_REQUEST,
      options.requestType.CREDIT_NOTE_REQUEST,
      options.requestType.QUOTATION_REQUEST,
    ],
  },
  requestTypeActivityMapping: {
    GENERAL_REQUEST: { moduleName: 'TaskApproval', activityName: 'New Task' },
    LEAVE_REQUEST: { moduleName: 'Employees', activityName: 'Attendance' },
    PAYMENT_REQUEST: { moduleName: 'Finance', activityName: 'Payment' },
    INDENT_MR_REQUEST: { moduleName: 'Material', activityName: 'Indent' },
    STOCK_ADJUSTMENT_REQUEST: {
      moduleName: 'Material',
      activityName: 'Stock Adjustment',
    },
    WORK_ORDER_REQUEST: {
      moduleName: 'WorkOrder',
      activityName: 'New Work Order',
    },
    WBS_REQUEST: { moduleName: 'WBS', activityName: 'Complete Activity' },
    JOURNAL_REQUEST: { moduleName: 'Finance', activityName: 'Journal Entry' },
    BUDGET_REQUEST: { moduleName: 'Finance', activityName: 'Budget' },
    EXPENSE_REQUEST: { moduleName: 'Finance', activityName: 'Expenses' },
    CREDIT_NOTE_REQUEST: { moduleName: 'Finance', activityName: 'Credit Note' },
    QUOTATION_REQUEST: { moduleName: 'CRM', activityName: 'Quotations' },
  },
  requestTypeTitle: {
    GENERAL_REQUEST: 'New General Request',
    LEAVE_REQUEST: 'New Leave Request',
    PAYMENT_REQUEST: 'New Payment Request',
    INDENT_MR_REQUEST: 'New Indent/MR Request',
    STOCK_ADJUSTMENT_REQUEST: 'New Stock Adjustment Request',
    WORK_ORDER_REQUEST: 'New Work Order Request',
    WBS_REQUEST: 'New WBS Request',
    JOURNAL_REQUEST: 'New Journal Request',
    BUDGET_REQUEST: 'New Budget Request',
    EXPENSE_REQUEST: 'New Expense Request',
    CREDIT_NOTE_REQUEST: 'New Credit Note Request',
    QUOTATION_REQUEST: 'New Quotation Request',
    getValues: () => [
      options.requestTypeTitle.GENERAL_REQUEST,
      options.requestTypeTitle.LEAVE_REQUEST,
      options.requestTypeTitle.PAYMENT_REQUEST,
      options.requestTypeTitle.INDENT_MR_REQUEST,
      options.requestTypeTitle.STOCK_ADJUSTMENT_REQUEST,
      options.requestTypeTitle.WORK_ORDER_REQUEST,
      options.requestTypeTitle.WBS_REQUEST,
      options.requestTypeTitle.JOURNAL_REQUEST,
      options.requestTypeTitle.BUDGET_REQUEST,
      options.requestTypeTitle.EXPENSE_REQUEST,
      options.requestTypeTitle.CREDIT_NOTE_REQUEST,
      options.requestTypeTitle.QUOTATION_REQUEST,
    ],
  },
  requestStatus: {
    PENDING: 'pending',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    UNDER_REVIEW: 'under_review',
    getValues: () => [
      options.requestStatus.PENDING,
      options.requestStatus.APPROVED,
      options.requestStatus.REJECTED,
      options.requestStatus.UNDER_REVIEW,
    ],
  },
  itemMediaType: {
    ITEM: 'item',
    VARIANT: 'variant',
    getValues: () => [
      options.itemMediaType.ITEM,
      options.itemMediaType.VARIANT,
    ],
  },
  itemVariantType: {
    COLOUR: 'colour',
    SIZE: 'size',
    DIMENSION: 'dimension',
    MATERIAL: 'material',
    GRADE: 'grade',
    WEIGHT: 'weight',
    FINISH: 'finish',
    USAGE: 'usage',
    PACKAGING: 'packaging',
    TYPE: 'type',
    WATTAGE: 'wattage',
    PATTERN: 'pattern',
    DESIGN: 'design',
    getValues: () => [
      options.itemVariantType.COLOUR,
      options.itemVariantType.SIZE,
      options.itemVariantType.DIMENSION,
      options.itemVariantType.MATERIAL,
      options.itemVariantType.GRADE,
      options.itemVariantType.WEIGHT,
      options.itemVariantType.FINISH,
      options.itemVariantType.USAGE,
      options.itemVariantType.PACKAGING,
      options.itemVariantType.TYPE,
      options.itemVariantType.WATTAGE,
      options.itemVariantType.PATTERN,
      options.itemVariantType.DESIGN,
    ],
  },
  unitOfMeasurement: {
    BOX: 'box',
    CENTIMETRE: 'cm',
    DOZEN: 'dz',
    FEET: 'ft',
    GRAMS: 'g',
    INCH: 'in',
    KILOGRAM: 'kg',
    KILOMETRE: 'km',
    POUND: 'lb',
    MILLIGRAM: 'mg',
    MILLILITRES: 'ml',
    METRE: 'm',
    PIECES: 'pcs',
    getValues: () => [
      options.unitOfMeasurement.BOX,
      options.unitOfMeasurement.CENTIMETRE,
      options.unitOfMeasurement.DOZEN,
      options.unitOfMeasurement.FEET,
      options.unitOfMeasurement.GRAMS,
      options.unitOfMeasurement.INCH,
      options.unitOfMeasurement.KILOGRAM,
      options.unitOfMeasurement.KILOMETRE,
      options.unitOfMeasurement.POUND,
      options.unitOfMeasurement.MILLIGRAM,
      options.unitOfMeasurement.MILLILITRES,
      options.unitOfMeasurement.METRE,
      options.unitOfMeasurement.PIECES,
    ],
  },
  reOrderPointScope: {
    ALL_WAREHOUSES: 'all_warehouses',
    EACH_WAREHOUSE: 'each_warehouse',
    getValues: () => [
      options.reOrderPointScope.ALL_WAREHOUSES,
      options.reOrderPointScope.EACH_WAREHOUSE,
    ],
  },
  itemStatus: {
    ACTIVE: 'active',
    INACTIVE: 'in_active',
    DRAFT: 'draft',
    AVAILABLE: 'available',
    OUT_OF_STOCK: 'out_of_stock',
    LOW_INVENTORY: 'low_inventory',
    ARCHIVED: 'archived',
    ORDER_PLACED: 'order_placed',
    getValues: () => [
      options.itemStatus.ACTIVE,
      options.itemStatus.INACTIVE,
      options.itemStatus.DRAFT,
      options.itemStatus.AVAILABLE,
      options.itemStatus.OUT_OF_STOCK,
      options.itemStatus.LOW_INVENTORY,
      options.itemStatus.ARCHIVED,
      options.itemStatus.ORDER_PLACED,
    ],
  },
  indentPriority: {
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low',
    URGENT: 'urgent',
    CRITICAL: 'critical',
    getValues: () => [
      options.indentPriority.HIGH,
      options.indentPriority.MEDIUM,
      options.indentPriority.LOW,
      options.indentPriority.URGENT,
      options.indentPriority.CRITICAL,
    ],
  },
  indentStatus: {
    DRAFT: 'draft',
    UNDER_APPROVAL: 'under_approval',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    IN_PROGRESS: 'in_progress',
    ON_HOLD: 'on_hold',
    DUE_SOON: 'due_soon',
    DELAYED: 'delayed',
    FULFILLED: 'fulfilled',
    getValues: () => [
      options.indentStatus.DRAFT,
      options.indentStatus.UNDER_APPROVAL,
      options.indentStatus.APPROVED,
      options.indentStatus.REJECTED,
      options.indentStatus.ESCALATED,
      options.indentStatus.IN_PROGRESS,
      options.indentStatus.ON_HOLD,
      options.indentStatus.DUE_SOON,
      options.indentStatus.DELAYED,
      options.indentStatus.FULFILLED,
    ],
  },
  indentItemStatus: {
    PENDING: 'pending',
    NOT_AVAILABLE: 'not_available',
    DELIVERED: 'delivered',
    ORDERED: 'ordered',
    getValues: () => [
      options.indentItemStatus.PENDING,
      options.indentItemStatus.NOT_AVAILABLE,
      options.indentItemStatus.DELIVERED,
      options.indentItemStatus.ORDERED,
    ],
  },
  purchaseOrderPriority: {
    HIGH: 'high',
    MEDIUM: 'medium',
    LOW: 'low',
    URGENT: 'urgent',
    CRITICAL: 'critical',
    getValues: () => [
      options.purchaseOrderPriority.HIGH,
      options.purchaseOrderPriority.MEDIUM,
      options.purchaseOrderPriority.LOW,
      options.purchaseOrderPriority.URGENT,
      options.purchaseOrderPriority.CRITICAL,
    ],
  },
  purchaseOrderStatus: {
    DRAFT: 'draft',
    UNDER_APPROVAL: 'under_approval',
    ESCALATED: 'escalated',
    APPROVED: 'approved',
    ONGOING: 'ongoing',
    DELAYED: 'delayed',
    DUE_SOON: 'due_soon',
    PENDING: 'pending',
    REJECTED: 'rejected',
    getValues: () => [
      options.purchaseOrderStatus.DRAFT,
      options.purchaseOrderStatus.UNDER_APPROVAL,
      options.purchaseOrderStatus.ESCALATED,
      options.purchaseOrderStatus.APPROVED,
      options.purchaseOrderStatus.ONGOING,
      options.purchaseOrderStatus.DELAYED,
      options.purchaseOrderStatus.DUE_SOON,
      options.purchaseOrderStatus.PENDING,
      options.purchaseOrderStatus.REJECTED,
    ],
  },
  grnMediaType: {
    GRN: 'grn',
    PARTIAL_RECEIPT: 'partial_receipt',
    getValues: () => [
      options.grnMediaType.GRN,
      options.grnMediaType.PARTIAL_RECEIPT,
    ],
  },
  grnStatus: {
    DRAFT: 'draft',
    UNDER_APPROVAL: 'under_approval',
    ESCALATED: 'escalated',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    getValues: () => [
      options.grnStatus.DRAFT,
      options.grnStatus.UNDER_APPROVAL,
      options.grnStatus.ESCALATED,
      options.grnStatus.APPROVED,
      options.grnStatus.REJECTED,
    ],
  },
  grnItemStatus: {
    PENDING: 'pending',
    PARTIAL_RECEIPT: 'partial_receipt',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    getValues: () => [
      options.grnItemStatus.PENDING,
      options.grnItemStatus.PARTIAL_RECEIPT,
      options.grnItemStatus.APPROVED,
      options.grnItemStatus.REJECTED,
    ],
  },
  documentType: {
    USER: 'user',
    EMPLOYEE: 'employee',
    getValues: () => [options.documentType.USER, options.documentType.EMPLOYEE],
  },
  leaveType: {
    PAID_LEAVE: 'paid_leave',
    EARNED_LEAVE: 'earned_leave',
    SICK_LEAVE: 'sick_leave',
    CASUAL_LEAVE: 'casual_leave',
    MATERNITY_LEAVE: 'maternity_leave',
    PATERNITY_LEAVE: 'paternity_leave',
    COMPENSATORY_OFF: 'compensatory_off',
    BEREAVEMENT_LEAVE: 'bereavement_leave',
    UNPAID_LEAVE: 'unpaid_leave',
    MARRIAGE_LEAVE: 'marriage_leave',
    ADOPTION_LEAVE: 'adoption_leave',
    STUDY_LEAVE: 'study_leave',
    SABBATICAL_LEAVE: 'sabbatical_leave',
    PUBLIC_HOLIDAY: 'public_holiday',
    PRIVILEGE_LEAVE: 'privilege_leave',
    RESTRICTED_HOLIDAY: 'restricted_holiday',

    getValues: function () {
      return [
        options.leaveType.PAID_LEAVE,
        options.leaveType.EARNED_LEAVE,
        options.leaveType.SICK_LEAVE,
        options.leaveType.CASUAL_LEAVE,
        options.leaveType.MATERNITY_LEAVE,
        options.leaveType.PATERNITY_LEAVE,
        options.leaveType.COMPENSATORY_OFF,
        options.leaveType.BEREAVEMENT_LEAVE,
        options.leaveType.UNPAID_LEAVE,
        options.leaveType.MARRIAGE_LEAVE,
        options.leaveType.ADOPTION_LEAVE,
        options.leaveType.STUDY_LEAVE,
        options.leaveType.SABBATICAL_LEAVE,
        options.leaveType.PUBLIC_HOLIDAY,
        options.leaveType.PRIVILEGE_LEAVE,
        options.leaveType.RESTRICTED_HOLIDAY,
      ];
    },
  },
  workOrderStatus: {
    DRAFT: 'draft',
    UNDER_APPROVAL: 'under_approval',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    ALLOTTED: 'allotted',
    ONGOING: 'ongoing',
    ON_HOLD: 'on_hold',
    DUE_SOON: 'due_soon',
    DELAYED: 'delayed',
    getValues: () => [
      options.workOrderStatus.DRAFT,
      options.workOrderStatus.UNDER_APPROVAL,
      options.workOrderStatus.APPROVED,
      options.workOrderStatus.REJECTED,
      options.workOrderStatus.ESCALATED,
      options.workOrderStatus.ALLOTTED,
      options.workOrderStatus.ONGOING,
      options.workOrderStatus.ON_HOLD,
      options.workOrderStatus.DUE_SOON,
      options.workOrderStatus.DELAYED,
    ],
  },
  quotationStatus: {
    ACTIVE: 'active',
    DRAFT: 'draft',
    UNDER_APPROVAL: 'under_approval',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    EXPIRING_SOON: 'expiring_soon',
    EXPIRED: 'expired',
    SHARED: 'shared',
    BOOKED: 'booked',
    ARCHIVED: 'archived',
    APPROVED: 'approved',
    getValues: () => [
      options.quotationStatus.ACTIVE,
      options.quotationStatus.DRAFT,
      options.quotationStatus.UNDER_APPROVAL,
      options.quotationStatus.REJECTED,
      options.quotationStatus.ESCALATED,
      options.quotationStatus.EXPIRING_SOON,
      options.quotationStatus.EXPIRED,
      options.quotationStatus.SHARED,
      options.quotationStatus.BOOKED,
      options.quotationStatus.ARCHIVED,
      options.quotationStatus.APPROVED,
    ],
  },
  stockAdjustmentStatus: {
    DRAFT: 'draft',
    UNDER_APPROVAL: 'under_approval',
    APPROVED: 'approved',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    ADJUSTED: 'adjusted',
    getValues: () => [
      options.stockAdjustmentStatus.DRAFT,
      options.stockAdjustmentStatus.UNDER_APPROVAL,
      options.stockAdjustmentStatus.APPROVED,
      options.stockAdjustmentStatus.REJECTED,
      options.stockAdjustmentStatus.ESCALATED,
      options.stockAdjustmentStatus.ADJUSTED,
    ],
  },
  budgetStatus: {
    DRAFT: 'draft',
    ACTIVE: 'active',
    UNDER_APPROVAL: 'under_approval',
    REJECTED: 'rejected',
    ESCALATED: 'escalated',
    INACTIVE: 'inactive',
    OVER_BUDGET: 'over_budget',
    getValues: () => [
      options.budgetStatus.DRAFT,
      options.budgetStatus.ACTIVE,
      options.budgetStatus.UNDER_APPROVAL,
      options.budgetStatus.REJECTED,
      options.budgetStatus.ESCALATED,
      options.budgetStatus.INACTIVE,
      options.budgetStatus.OVER_BUDGET,
    ],
  },
};

module.exports = options;
