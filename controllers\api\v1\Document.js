const DocumentRepository = require('../../../models/repositories/DocumentRepository');
const DocumentPermissionRepository = require('../../../models/repositories/DocumentPermissionRepository');
const { genRes, errorMessage, resCode } = require('../../../config/options');
const AWSHelpers = require('../../../models/helpers/AWSHelper');

exports.getDocuments = async (req, res) => {
  try {
    const { success, message, data } = await DocumentRepository.getMyDocuments(
      req.query,
      req.user
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createDocument = async (req, res) => {
  try {
    const { success, message } = await DocumentRepository.storeDocument(
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getPostDocumentSignedUrl = async (req, res) => {
  try {
    const { fileName, parentFolderId } = req.query;

    const {
      success,
      message,
      data: basePath,
    } = await DocumentRepository.getBasePath(parentFolderId);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    const filePath = `documents${basePath ? basePath : ''}/${Date.now()}-${fileName}`;
    const url = await AWSHelpers.generateSignedURL(filePath);
    res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { url, filePath }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.putDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const { success, message } = await DocumentRepository.updateDocument(
      id,
      req.body,
      req.user
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getPermissionUsersByDocument = async (req, res) => {
  try {
    const { id } = req.params;
    const { success, message, data } =
      await DocumentRepository.getPermissionByDocument(id);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.addUserInDocument = async (req, res) => {
  try {
    const { id } = req.params;

    const {
      success: permissionSuccess,
      message: permissionMessage,
      data: permission,
    } = await DocumentPermissionRepository.checkUserDocumentPermission(
      id,
      req.user.id
    );

    if (!permissionSuccess || !permission.canEdit) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            permissionSuccess
              ? errorMessage.NO_PERMISSION('Edit')
              : permissionMessage
          )
        );
    }

    const { success, message } = await DocumentRepository.addUserDocument(
      id,
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    res.status(resCode.HTTP_OK).json(genRes(resCode.HTTP_OK, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.updateUserDocumentPermission = async (req, res) => {
  try {
    const { id } = req.params;

    const {
      success: permissionSuccess,
      message: permissionMessage,
      data: permission,
    } = await DocumentPermissionRepository.checkUserDocumentPermission(
      id,
      req.user.id
    );

    if (!permissionSuccess || !permission.canEdit) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            permissionSuccess
              ? errorMessage.NO_PERMISSION('Edit')
              : permissionMessage
          )
        );
    }

    const { success, message } = await DocumentRepository.updateUserDocument(
      id,
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    res.status(resCode.HTTP_OK).json(genRes(resCode.HTTP_OK, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.removeUserFromDocument = async (req, res) => {
  try {
    const { id, userId } = req.params;

    const {
      success: permissionSuccess,
      message: permissionMessage,
      data: permission,
    } = await DocumentPermissionRepository.checkUserDocumentPermission(
      id,
      req.user.id
    );

    if (!permissionSuccess || !permission.canDelete) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            permissionSuccess
              ? errorMessage.NO_PERMISSION('Delete')
              : permissionMessage
          )
        );
    }

    const { success, message } =
      await DocumentRepository.removeUserFromDocument(id, userId, req.user);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, { message }));
    }

    res.status(resCode.HTTP_OK).json(genRes(resCode.HTTP_OK, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getDocumentActivity = async (req, res) => {
  try {
    const { id } = req.params;
    const { success, message, data } =
      await DocumentRepository.getDocumentActivity(id, req.query);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.downloadDocument = async (req, res) => {
  try {
    const { success, message } =
      await DocumentRepository.downloadFolderDocument(req, res);

    if (!success) {
      if (!res.headersSent) {
        return res
          .status(resCode.HTTP_BAD_REQUEST)
          .json(
            genRes(
              resCode.HTTP_BAD_REQUEST,
              message || errorMessage.DOWNLOAD_PROCESS_FAILED('Document')
            )
          );
      }
      return;
    }
  } catch (e) {
    customErrorLogger(e);

    if (!res.headersSent) {
      return res
        .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
        .json(
          genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
        );
    }
  }
};
