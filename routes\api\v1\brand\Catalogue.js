const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const CatalogueController = require('@controllers/v1/brand/Catalogue');
const CatalogueSchema = require('@schema-validation/brand/Catalogue');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(CatalogueSchema.createCatalogue),
  ErrorHandleHelper.requestValidator,
  CatalogueController.createCatalogue
);

router.put(
  '/:id',
  checkSchema(CatalogueSchema.updateCatalogue),
  ErrorHandleHelper.requestValidator,
  CatalogueController.updateCatalogue
);

router.delete(
  '/:id',
  checkSchema(CatalogueSchema.deleteCatalogue),
  ErrorHandleHelper.requestValidator,
  CatalogueController.deleteCatalogue
);

router.delete(
  '/media/:id',
  checkSchema(CatalogueSchema.deleteCatalogueMedia),
  ErrorHandleHelper.requestValidator,
  CatalogueController.deleteCatalogueMedia
);

module.exports = router;
