const { Space, Floor, Unit } = require('..');
const DrawingRepository = require('./DrawingRepository');
const { checkExistence } = require('@helpers/QueryHelper');
const {
  successMessage,
  drawingType,
  errorMessage,
} = require('@config/options');

exports.createSpaceForFloor = async (data, loggedInUser) => {
  try {
    const floor = await Floor.findByPk(data.floorId);
    if (!floor) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('floor'),
      };
    }

    const payload = {
      ...data,
      createdBy: loggedInUser.id,
    };

    const space = await Space.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Space'),
      data: space,
    };
  } catch (error) {
    throw error;
  }
};

exports.createSpaceForUnit = async (data, loggedInUser) => {
  try {
    const unit = await Unit.findByPk(data.unitId);
    if (!unit) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('unit'),
      };
    }

    const payload = {
      ...data,
      createdBy: loggedInUser.id,
    };

    const space = await Space.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Space'),
      data: space,
    };
  } catch (error) {
    throw error;
  }
};

exports.createSpace = async (data, loggedInUser) => {
  const { floorId, unitId, drawings } = data;
  try {
    let projectId;
    if (floorId) {
      const floor = await checkExistence(Floor, { id: floorId });
      if (!floor) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Floor with Id ${floorId}`),
        };
      }

      projectId = floor.projectId;
    }

    if (unitId) {
      const unit = await checkExistence(Unit, { id: unitId });
      if (!unit) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Unit with Id ${unitId}`),
        };
      }
      projectId = unit.projectId;
    }

    const payload = {
      ...data,
      createdBy: loggedInUser.id,
    };
    const space = await Space.create(payload);

    if (drawings && drawings.length > 0) {
      await DrawingRepository.createDrawings(
        drawings,
        drawingType.SPACE,
        space.id,
        loggedInUser,
        projectId
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Space'),
      data: space,
    };
  } catch (error) {
    throw error;
  }
};

exports.updateSpace = async (spaceId, data, loggedInUser) => {
  const { floorId, unitId, drawings } = data;
  try {
    let projectId;
    const space = await checkExistence(Space, { id: spaceId });
    if (!space) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Space with Id ${spaceId}`),
      };
    }

    if (floorId) {
      const floor = await checkExistence(Floor, { id: floorId });
      if (!floor) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Floor with Id ${floorId}`),
        };
      }
      projectId = floor.projectId;
    }

    if (unitId) {
      const unit = await checkExistence(Unit, { id: unitId });
      if (!unit) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Unit with Id ${unitId}`),
        };
      }
      projectId = unit.projectId;
    }

    const updatedSpace = Object.assign(space, data);
    await space.save();

    if (drawings && drawings.length > 0) {
      await DrawingRepository.deleteDrawingsBySpaceId(updatedSpace.id);
      await DrawingRepository.createDrawings(
        drawings,
        drawingType.SPACE,
        updatedSpace.id,
        loggedInUser.id,
        projectId
      );
    }
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Space'),
      data: updatedSpace,
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteSpace = async (spaceId) => {
  try {
    const space = await checkExistence(Space, { id: spaceId });
    if (!space) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Space with Id ${spaceId}`),
      };
    }

    await DrawingRepository.deleteDrawingsBySpaceId(space.id);
    await Space.destroy({ where: { id: spaceId } });
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Space'),
    };
  } catch (error) {
    throw error;
  }
};

exports.addDrwaingsToSpace = async (spaceId, data, loggedInUser) => {
  try {
    const space = await checkExistence(Space, { id: spaceId });
    if (!space) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Space with Id ${spaceId}`),
      };
    }
    if (data.drawings && data.drawings.length > 0) {
      await DrawingRepository.createDrawings(
        data.drawings,
        drawingType.SPACE,
        space.id,
        loggedInUser,
        data.projectId
      );
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Drawings'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
