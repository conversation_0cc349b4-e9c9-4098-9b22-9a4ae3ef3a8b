const OPTIONS = require('@config/options');
const UserRepository = require('@models/repositories/UserRepository');
const sequelize = require('sequelize');
const { Op } = sequelize;
const {
  genRes,
  errorMessage,
  successMessage,
  resCode,
  defaultStatus,
} = require('@config/options');

exports.changePassword = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
        status: { [Op.notIn]: [defaultStatus.DELETED] },
      },
      attributes: ['id', 'email', 'password'],
    };
    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            OPTIONS.errorMessage.DOES_NOT_EXIST('User')
          )
        );
    }
    if (!existingUser.validPassword(req.body.currentPassword)) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            OPTIONS.errorMessage.INCORRECT_DATA('current password')
          )
        );
    }
    await UserRepository.updatePassword(existingUser, req.body.newPassword);

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.SAVED_SUCCESS_MESSAGE('Password'),
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
