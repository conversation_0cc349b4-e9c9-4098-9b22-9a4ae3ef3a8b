'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('Project', {
      fields: ['createdBy'],
      type: 'foreign key',
      name: 'fk_project_createdBy',
      references: {
        table: 'User',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint('Project', 'fk_project_createdBy');
  },
};
