exports.createExpense = {
  organizationId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
    isInt: {
      errorMessage: 'organizationId must be an integer',
    },
  },
  date: {
    in: ['body'],
    isDate: {
      options: { format: 'MM-DD-YYYY' },
      errorMessage: 'Date must be in the format MM-DD-YYYY',
    },
  },
  expenseAccountId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'expenseAccountId cannot be empty',
    },
    isInt: {
      errorMessage: 'expenseAccountId must be an integer',
    },
  },
  paidThroughAccountId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'paidThroughAccountId cannot be empty',
    },
    isInt: {
      errorMessage: 'paidThroughAccountId must be an integer',
    },
  },
  amount: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'amount cannot be empty',
    },
    isInt: {
      errorMessage: 'amount must be an integer',
    },
  },
  vendorId: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        if (value === null || value === 'null') {
          return true; // Allow explicit null values
        }
        if (!Number.isInteger(value)) {
          throw new Error('vendorId must be an integer or null');
        }
        return true;
      },
    },
  },
  invoiceNumber: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'invoiceNumber must be string',
    },
  },
  notes: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'notes must be string',
    },
  },
};

exports.updateExpense = {
  expenseId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'expenseId must be an integer',
    },
  },
  date: {
    in: ['body'],
    isDate: {
      options: { format: 'MM-DD-YYYY' },
      errorMessage: 'Date must be in the format MM-DD-YYYY',
    },
  },
  expenseAccountId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'expenseAccountId cannot be empty',
    },
    isInt: {
      errorMessage: 'expenseAccountId must be an integer',
    },
  },
  paidThroughAccountId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'paidThroughAccountId cannot be empty',
    },
    isInt: {
      errorMessage: 'paidThroughAccountId must be an integer',
    },
  },
  amount: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'amount cannot be empty',
    },
    isInt: {
      errorMessage: 'amount must be an integer',
    },
  },
  vendorId: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        if (value === null || value === 'null') {
          return true; // Allow explicit null values
        }
        if (!Number.isInteger(value)) {
          throw new Error('vendorId must be an integer or null');
        }
        return true;
      },
    },
  },
  invoiceNumber: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'invoiceNumber must be string',
    },
  },
  notes: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'notes must be string',
    },
  },
};

exports.getExpense = {
  organizationId: {
    in: ['query'],
    optional: false,
    isInt: {
      errorMessage: 'organizationId ID must be an integer',
    },
  },
};

exports.getExpenseById = {
  expenseId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'expenseId must be an integer',
    },
  },
};

exports.deleteExpenseById = {
  expenseId: {
    in: ['params'],
    optional: false,
    isInt: {
      errorMessage: 'expenseId must be an integer',
    },
  },
};
