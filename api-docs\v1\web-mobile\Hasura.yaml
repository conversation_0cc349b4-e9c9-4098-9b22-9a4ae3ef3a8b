paths:
  /graphql:
    post:
      summary: "Execute a Hasura GraphQL Query"
      description: "Handles Hasura GraphQL queries with authorization and validation."
      operationId: "HasuraAuth"
      tags:
        - "Hasura Auth GraphQL"
      # parameters:
      #   - in: header
      #     name: x-graphql-operation
      #     required: true
      #     schema:
      #       type: string
      #     description: "Specifies the GraphQL operation type (e.g., get, create, update, delete)."
      #   - in: header
      #     name: x-graphql-endpoint
      #     required: true
      #     schema:
      #       type: string
      #     description: "Specifies the GraphQL endpoint being accessed (e.g., project)."
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HasuraAuth'
      security:
        - bearerAuth: []
      responses:
        '200':
          description: "Successful response from <PERSON><PERSON>."
        '400':
          description: "Validation error for request body parameters."
        '401':
          description: "Unauthorized request due to missing or invalid token."
        '500':
          description: "Internal server error."
components:
  schemas:
    HasuraAuth:
      type: object
      properties:
        query:
          type: string
          description: "GraphQL query string."
          example: |
            query MyQuery {
              User {
                id
                firstName
              }
            }
        variables:
          type: object
          description: "Optional variables object for GraphQL query."
          example: 
            id: 1
      required:
        - query
