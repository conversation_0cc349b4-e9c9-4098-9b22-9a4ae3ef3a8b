'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Warehouse', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Warehouse', 'organizationId');
  },
};
