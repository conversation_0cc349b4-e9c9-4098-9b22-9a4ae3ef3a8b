'use strict';
const { attendanceStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = (sequelize, DataTypes) => {
  const Attendance = sequelize.define(
    'Attendance',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      date: {
        type: DataTypes.DATEONLY,
        allowNull: false,
      },
      inTime: {
        type: DataTypes.TIME,
        allowNull: true,
      },
      outTime: {
        type: DataTypes.TIME,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(attendanceStatus.getAttendanceStatusArray()),
        allowNull: true,
      },
      totalDuration: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      description: {
        type: DataTypes.STRING,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Attendance.associate = (models) => {
    Attendance.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
    });

    Attendance.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updatedByUser',
    });
  };

  return Attendance;
};
