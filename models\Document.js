const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Document = sequelize.define(
    'Document',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isFolder: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      basePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
        get() {
          return OPTIONS.generateCloudFrontUrl(this.getDataValue('filePath'));
        },
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: 'active',
      },
      isDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Document.associate = (models) => {
    Document.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });

    Document.hasMany(Document, {
      foreignKey: 'parentFolderId',
      as: 'subDocuments',
      onDelete: 'CASCADE',
    });
    Document.belongsTo(Document, {
      as: 'parentDocument',
      foreignKey: 'parentFolderId',
    });

    Document.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      allowNull: true,
      onDelete: 'SET NULL',
    });

    Document.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      allowNull: true,
      onDelete: 'SET NULL',
    });

    Document.hasMany(models.DocumentPermission, {
      foreignKey: 'documentId',
      as: 'permission',
    });

    Document.hasMany(models.DocumentPermission, {
      foreignKey: 'documentId',
      as: 'permissionUser',
    });

    Document.hasMany(models.DocumentActivityLog, {
      foreignKey: 'documentId',
      as: 'activityLog',
    });
    Document.hasMany(models.FavoritesDocument, {
      foreignKey: 'documentId',
      as: 'favorites',
    });
    Document.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      allowNull: true,
    });
  };
  return Document;
};
