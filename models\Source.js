'use strict';

module.exports = (sequelize, DataTypes) => {
  const Source = sequelize.define(
    'Source',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      logo: {
        allowNull: true,
        type: DataTypes.TEXT,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: true,
        unique: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Source.associate = (models) => {
    Source.belongsTo(models.Source, {
      foreignKey: 'parentSourceId',
      as: 'parentSource',
      onDelete: 'SET NULL',
    });

    Source.hasMany(models.Customer, {
      foreignKey: 'sourceId',
      as: 'customers',
      onDelete: 'SET NULL',
    });

    Source.hasMany(models.Customer, {
      foreignKey: 'subSourceId',
      as: 'subSourceCustomers',
      onDelete: 'SET NULL',
    });

    Source.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'SET NULL',
    });
  };

  return Source;
};
