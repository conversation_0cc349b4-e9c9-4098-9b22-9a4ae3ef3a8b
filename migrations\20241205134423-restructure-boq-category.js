'use strict';
const { defaultStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQCategory', 'description');

    await queryInterface.removeColumn('BOQCategory', 'parentCategoryId');

    await queryInterface.addColumn('BOQCategory', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    });

    await queryInterface.addColumn('BOQCategory', 'status', {
      type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
      allowNull: false,
      defaultValue: defaultStatus.UN_ASSIGNED,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('BOQCategory', 'description', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('BOQCategory', 'parentCategoryId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'BOQCategory',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'RESTRICT',
    });

    await queryInterface.removeColumn('BOQCategory', 'createdBy');

    await queryInterface.removeColumn('BOQCategory', 'status');
    await queryInterface.sequelize.query(
      'DROP TYPE "enum_BOQCategory_status";'
    );
  },
};
