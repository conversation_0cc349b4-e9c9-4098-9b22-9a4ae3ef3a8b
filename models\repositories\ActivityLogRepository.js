const { ActivityLog, User } = require('..');
const { successMessage } = require('../../config/options');

exports.getActivities = async (query) => {
  try {
    const { activityOn, recordId, start = 0, limit = 10 } = query;

    const whereClause = {};
    if (activityOn && activityOn !== 'User') {
      whereClause.activityOn = activityOn;
    }
    if (recordId && activityOn !== 'User') {
      whereClause.recordId = recordId;
    }

    if (activityOn === 'User') {
      whereClause.createdBy = recordId;
    }

    const { rows: activities, count: totalCategoriesCount } =
      await ActivityLog.findAndCountAll({
        where: whereClause,
        limit: limit,
        offset: start,
        order: [['createdAt', 'DESC']],
        attributes: { exclude: ['recordId', 'createdBy'] },
        include: [
          {
            model: User,
            as: 'creator',
            attributes: [
              'id',
              'firstName',
              'lastName',
              'email',
              'profilePicture',
            ],
          },
        ],
      });

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('activities'),
      data: {
        rows: activities,
        pagination: {
          totalCount: totalCategoriesCount,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw error;
  }
};
