'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Grn = sequelize.define(
    'Grn',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      receivedOn: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.grnStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.grnStatus.UNDER_APPROVAL,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Grn.associate = (models) => {
    Grn.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    Grn.belongsTo(models.PurchaseOrder, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrder',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Grn.belongsTo(models.User, {
      foreignKey: 'vendorId',
      as: 'vendor',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    Grn.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Grn.belongsTo(models.User, {
      foreignKey: 'deliveredBy',
      as: 'deliveredByUser',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    Grn.belongsTo(models.User, {
      foreignKey: 'receivedBy',
      as: 'receivedByUser',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    Grn.belongsTo(models.Warehouse, {
      foreignKey: 'warehouseId',
      as: 'deliveryLocation',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Grn.hasMany(models.GrnMedia, {
      foreignKey: 'grnId',
      as: 'grnMedia',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Grn.hasMany(models.GrnItem, {
      foreignKey: 'grnId',
      as: 'grnItem',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return Grn;
};
