module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WorkOrderBOQMapping', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Project',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      workOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WorkOrder',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      boqItemId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'BOQItems',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      boqQuantity: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      boqUnit: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      boqRate: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      boqPrice: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addIndex(
      'WorkOrderBOQMapping',
      ['workOrderId', 'boqItemId'],
      {
        unique: true,
        name: 'unique_workOrderId_boqItemId_constraint',
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WorkOrderBOQMapping');
  },
};
