'use strict';

/** @type {import('sequelize-cli').Migration} */
'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('DesignationModulePermission', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      canView: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      canEdit: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      canCreate: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      organizationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      designationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Designation',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      moduleId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Module',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('DesignationModulePermission');
  },
};
