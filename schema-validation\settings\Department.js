exports.createDepartment = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Department name cannot be empty',
    isString: {
      errorMessage: 'Department name must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Department description must be a string',
    },
  },
};

exports.updateDepartment = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Department Id is required',
    },
    isString: {
      errorMessage: 'Department Id must be a valid string',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    optional: true,
    notEmpty: {
      errorMessage: 'Department name cannot be empty',
    },
    isString: {
      errorMessage: 'Department name must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Department description must be a string',
    },
  },
};

exports.deleteDepartment = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Department Id is required',
    },
    isString: {
      errorMessage: 'Department Id must be a valid string',
    },
  },
};
