paths:
  /crm/team-assignment:
    post:
      tags:
        - "CRM"
      summary: "Create a new Team Assignment"
      description: "This endpoint allows you to create a new team assignment by providing all the necessary details."
      operationId: "CreateTeamAssignment"
      requestBody:
        description: "The details of the new team assignment."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/team-assignment"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Team Assignment created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/team-assignment'
        "400":
          description: "Invalid input data or the assignment already exists."
        "500":
          description: "Internal Server Error"

  /crm/team-assignment/{id}:
    put:
      tags:
        - "CRM"
      summary: "Update an existing Team Assignment"
      description: "This endpoint allows you to update an existing team assignment by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateTeamAssignment"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the team assignment to be updated."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The updated information for the team assignment."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/team-assignment-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Team Assignment updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/team-assignment'
        "400":
          description: "Invalid input data or the assignment was not found."
        "404":
          description: "Team Assignment not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    team-assignment:
      type: object
      properties:
        customerId:
          type: integer
          description: "The ID of the customer associated with the team assignment."
          example: 1
        requirementId:
          type: integer
          description: "The ID of the requirement associated with the team assignment."
          example: 2
        memberId:
          type: integer
          description: "The ID of the employee (team member) associated with the team assignment."
          example: 3
        priority:
          type: string
          enum:
            - "high"
            - "medium"
            - "low"
          description: "The priority level of the team assignment."
          example: "high"
        autoAssign:
          type: boolean
          description: "Whether the team assignment is auto-assigned."
          example: true
      required:
        - customerId
        - requirementId
        - priority
        - autoAssign
      additionalProperties: false

    team-assignment-update:
      type: object
      properties:
        customerId:
          type: integer
          description: "The ID of the customer associated with the team assignment."
          example: 1
        requirementId:
          type: integer
          description: "The ID of the requirement associated with the team assignment."
          example: 2
        memberId:
          type: integer
          description: "The ID of the employee (team member) associated with the team assignment."
          example: 3
        priority:
          type: string
          enum:
            - "high"
            - "medium"
            - "low"
          description: "The priority level of the team assignment."
          example: "high"
        autoAssign:
          type: boolean
          description: "Whether the team assignment is auto-assigned."
          example: true
      required: []
      additionalProperties: false
