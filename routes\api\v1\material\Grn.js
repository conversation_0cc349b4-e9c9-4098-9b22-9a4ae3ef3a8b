const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const GrnController = require('@controllers/v1/material/Grn');
const GrnSchema = require('@schema-validation/material/Grn');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(GrnSchema.createGrn),
  ErrorHandleHelper.requestValidator,
  GrnController.createGrn
);

router.put(
  '/:id',
  checkSchema(GrnSchema.updateGrn),
  ErrorHandleHelper.requestValidator,
  GrnController.updateGrn
);

router.patch(
  '/:id',
  checkSchema(GrnSchema.updateGrnStatus),
  ErrorHandleHelper.requestValidator,
  GrnController.updateGrnStatus
);

router.delete(
  '/:id',
  checkSchema(GrnSchema.deleteGrn),
  ErrorHandleHelper.requestValidator,
  GrnController.deleteGrn
);

router.patch(
  '/item/:id',
  checkSchema(GrnSchema.updateGrnItemStatus),
  ErrorHandleHelper.requestValidator,
  GrnController.updateGrnItemStatus
);

router.delete(
  '/media/:id',
  checkSchema(GrnSchema.deleteGrnMedia),
  ErrorHandleHelper.requestValidator,
  GrnController.deleteGrnMedia
);

module.exports = router;
