paths:
  /project/{id}/payment-revision:
    post:
      summary: Create a Payment Revision
      description: Creates a new payment revision for a project with associated pricing charges.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-or-update-payment-revision"
      responses:
        '201':
          description: Payment Revision added successfully.
        '404':
          description: The Project does not exist.
        '500':
          description: Internal server error.
  /project/{id}/payment-revision/{paymentRevisionId}:
    put:
      summary: Update a Payment Revision
      description: Updates an existing payment revision for a project with associated pricing charges.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: paymentRevisionId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-or-update-payment-revision"
      responses:
        '200':
          description: Payment Revision updated successfully.
        '404':
          description: The Project or Payment Revision does not exist.
        '500':
          description: Internal server error.
  /project/{id}/payment-plan:
    post:
      summary: add a Payment Plan
      description: add project with associated payment stages.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-or-update-payment-plan"
      responses:
        '200':
          description: Payment Plan added successfully.
        '404':
          description: The Payment Plan does not exist.
        '500':
          description: Internal server error.
  /project/{id}/payment-plan/{paymentPlanId}:
    put:
      summary: update a Payment Plan
      description: update project with associated payment stages.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: paymentPlanId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-or-update-payment-plan"
      responses:
        '200':
          description: Payment Plan updated successfully.
        '404':
          description: The Payment Plan does not exist.
        '500':
          description: Internal server error.
  /project/{id}/broker-payout-plan:
    post:
      summary: add a Broker Payout Plan
      description: add project with associated payment stages for broker payout.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-payment-plan"
      responses:
        '200':
          description: Broker Payout Plan added successfully.
        '404':
          description: The Broker Payout Plan does not exist.
        '500':
          description: Internal server error.
  /project/{id}/broker-payout-plan/{brokerPayoutPlanId}:
    put:
      summary: Update a Broker Payout Plan
      description: Updates a project with associated payment stages for broker payout.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
        - name: brokerPayoutPlanId
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-or-update-payment-plan"
      responses:
        '200':
          description: Broker Payout Plan updated successfully.
        '404':
          description: The Broker Payout Plan does not exist.
        '500':
          description: Internal server error.
  /project/{id}/customer-status:
    post:
      summary: add a Customer Status
      description: add project with associated customer status.
      tags:
        - Project Payments
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/create-customer-status"
      responses:
        '200':
          description: Customer Status added successfully.
        '404':
          description: The Customer Status does not exist.
        '500':
          description: Internal server error.
components:
  schemas:
    create-or-update-payment-revision:
     type: object
     properties:
       name:
         type: string
         example: "Phase 1 Payment"
       startDate:
         type: string
         format: date
         example: "2024-12-10"
       endDate:
         type: string
         format: date
         example: "2025-01-15"
       description:
         type: string
         example: "Payment for initial project setup."
       pricingCharges:
         type: array
         items:
           type: object
           properties:
             name:
               type: string
               example: "Service Tax"
             description:
               type: string
               example: "charges for initial project setup."
             chargeType:
               type: string
               enum:
                 - agreement_value
                 - percentage_of_agreement_value
                 - fixed_amount
                 - super_built_up_area
                 - carpet_area
                 - tax
               example: "tax"
             rate:
               type: string
               example: "18%"
             taxRate:
               type: string
               example: "28%"
             hsnCode:
               type: string
               example: "998313"
             isTaxable:
               type: boolean
               example: true
    create-or-update-payment-plan:
     type: object
     properties:
       name:
         type: string
         example: "Ph 1 Payment"
       description:
         type: string
         example: "Payment for initial project setup."
       additionalTerm:
         type: string
         example: "additionalTerm for initial project setup."
       paymentStages:
         type: array
         items:
           type: object
           properties:
             name:
               type: string
               example: "Down Payment"
             description:
               type: string
               example: "Down Payment for initial project setup."
             calculationType:
               type: string
               enum:
                 - percentage_of_agreement_value
                 - fixed_amount
                 - percentage_of_total_sale_value
               example: "fixed_amount"
             amount:
               type: float
               example: 1800.10
             triggerType:
               type: string
               enum:
                 - broker_payout_plan
                 - project_status
                 - project_progress
                 - date
                 - none_manual
               example: "date"
             dueOn:
               type: string
               example: "2024-12-10"
    create-customer-status:
     type: object
     properties:
       name:
         type: string
         example: "Stamp Duty"
       description:
         type: string
         example: "Payment for initial project setup."
       triggerType:
         type: string
         enum:
           - new_lead
           - site_visit_scheduled
           - date
           - none_manual
           - allotment_letter_sent
           - project_progress
           - task
         example: "date"
       isApprovedRequired:
         type: boolean
         example: false
       dueOn:
         type: string
         example: "2024-12-10"