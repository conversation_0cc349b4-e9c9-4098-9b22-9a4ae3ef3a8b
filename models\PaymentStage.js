const {
  paymentStageCalculationType,
  paymentTriggerType,
} = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const PaymentStage = sequelize.define(
    'PaymentStage',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      calculationType: {
        type: DataTypes.ENUM(
          ...paymentStageCalculationType.paymentStageCalculationTypeArray()
        ),
        allowNull: false,
      },
      amount: {
        type: DataTypes.DECIMAL(12, 2),
        allowNull: false,
      },
      triggerType: {
        type: DataTypes.ENUM(...paymentTriggerType.paymentTriggerTypeArray()),
        allowNull: false,
      },
      dueOn: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        onUpdate: DataTypes.NOW,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  PaymentStage.associate = (models) => {
    PaymentStage.belongsTo(models.PaymentPlan, {
      foreignKey: 'paymentPlanId',
      as: 'paymentPlan',
    });
  };

  return PaymentStage;
};
