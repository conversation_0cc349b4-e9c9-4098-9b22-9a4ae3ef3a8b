const { dependencyType, dependencyStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('TaskDependency', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      taskId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Tasks',
          key: 'id',
        },
      },
      dependentTaskId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Tasks',
          key: 'id',
        },
      },
      dependencyType: {
        type: Sequelize.ENUM(dependencyType.getValues()),
        allowNull: false,
        defaultValue: dependencyType.BLOCKING,
      },
      status: {
        type: Sequelize.ENUM(dependencyStatus.getValues()),
        allowNull: false,
        defaultValue: dependencyStatus.FF,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('TaskDependency');
  },
};
