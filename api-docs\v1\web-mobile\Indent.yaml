paths:
  /material/indent:
    post:
      tags:
        - "Material"
      summary: "Create a new indent"
      description: "This endpoint allows you to create a new indent in the system"
      operationId: "CreateIndent"
      requestBody:
        description: "Indent creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateIndentRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Indent created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/indent/{id}:
    put:
      tags:
        - "Material"
      summary: "Update an existing indent"
      description: "This endpoint allows you to update an existing indent in the system"
      operationId: "UpdateIndent"
      parameters:
        - $ref: "#/components/parameters/IndentIdParam"
      requestBody:
        description: "Indent update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateIndentRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Indent updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Indent not found"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing indent"
      description: "This endpoint allows you to update the status of an existing indent in the system"
      operationId: "UpdateIndentStatus"
      parameters:
        - $ref: "#/components/parameters/IndentIdParam"
      requestBody:
        description: "Indent status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/IndentStatusUpdateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Indent status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Indent not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing indent"
      description: "This endpoint allows you to delete an existing indent in the system"
      operationId: "DeleteIndent"
      parameters:
        - $ref: "#/components/parameters/IndentIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Indent deleted successfully"
        "404":
          description: "Indent not found"
        "500":
          description: "Internal Server Error"

  /material/indent/item/{id}:
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing indent item"
      description: "This endpoint allows you to update the status of an existing indent item in the system"
      operationId: "UpdateIndentItemStatus"
      parameters:
        - $ref: "#/components/parameters/IndentItemIdParam"
      requestBody:
        description: "Indent item status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/IndentItemStatusUpdateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Indent item status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Indent item not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateIndentRequest:
      type: object
      properties:
        boqId:
          type: integer
          example: 1
        workOrderId:
          type: integer
          example: 1
        taskId:
          type: integer
          example: 1
        warehouseId:
          type: integer
          example: 1
        requiredByDate:
          type: string
          format: date
          example: "2025-03-30"
        priority:
          type: string
          enum: ["high", "medium", "low", "urgent", "critical"]
          example: "medium"
        note:
          type: string
          example: "This is a note"
        items:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
              # itemVariantId:
              #   type: integer
              #   example: 1
              requiredStock:
                type: integer
                example: 100
              note:
                type: string
                example: "Item note"
      required:
        - warehouseId
        - requiredByDate
        - priority
        - items
    UpdateIndentRequest:
      type: object
      properties:
        boqId:
          type: integer
          example: 1
        workOrderId:
          type: integer
          example: 1
        taskId:
          type: integer
          example: 1
        warehouseId:
          type: integer
          example: 1
        requiredByDate:
          type: string
          format: date
          example: "2025-03-30"
        priority:
          type: string
          enum: ["high", "medium", "low", "urgent", "critical"]
          example: "medium"
        note:
          type: string
          example: "This is a note"
        items:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
              # itemVariantId:
              #   type: integer
              #   example: 1
              requiredStock:
                type: integer
                example: 100
              note:
                type: string
                example: "Item note"
    IndentStatusUpdateRequest:
      type: object
      properties:
        status:
          type: string
          enum: [
            "draft",
            "pending_approval",
            "escalated",
            "approved",
            "rejected",
            "in_progress",
            "delayed",
            "fulfilled",
            "cancelled"
          ]
          example: "approved"
      required:
        - status
    IndentItemStatusUpdateRequest:
      type: object
      properties:
        status:
          type: string
          enum: [
            "pending",
            "not_available",
            "ordered",
            "delivered",
          ]
          example: "delivered"
      required:
        - status


  parameters:
    IndentIdParam:
      name: "id"
      in: "path"
      description: "ID of the indent to be managed"
      required: true
      schema:
        type: string

    IndentItemIdParam:
      name: "id"
      in: "path"
      description: "ID of the indent item to be managed"
      required: true
      schema:
        type: string