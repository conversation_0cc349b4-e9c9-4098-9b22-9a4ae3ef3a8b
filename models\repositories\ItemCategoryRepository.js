const { Op } = require('sequelize');
const { ItemCategory, sequelize } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.validateAndCreateItemCategory = async (data, loggedInUser) => {
  const { name } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const existingItemCategory = await ItemCategory.findOne({
      where: {
        name: {
          [Op.iLike]: name,
        },
        organizationId: organizationId,
      },
    });

    if (existingItemCategory) {
      throw new Error(
        errorMessage.ALREADY_EXIST(
          `Item Category with name: ${name} in this organization`
        )
      );
    }

    const itemCategory = await ItemCategory.create(
      {
        ...data,
        createdBy: loggedInUser.id,
        organizationId: organizationId,
      },
      { transaction }
    );

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Item Category'),
      data: itemCategory,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while creating the item category',
    };
  }
};

exports.validateAndUpdateItemCategory = async (
  itemCategoryId,
  data,
  loggedInUser
) => {
  const { name } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const itemCategory = await ItemCategory.findOne({
      where: {
        id: itemCategoryId,
      },
    });

    if (!itemCategory) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item Category'));
    }

    if (name) {
      const existingItemCategory = await ItemCategory.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
          id: {
            [Op.ne]: itemCategoryId,
          },
          organizationId: organizationId,
        },
      });

      if (existingItemCategory) {
        throw new Error(
          errorMessage.ALREADY_EXIST(
            `Item Category with name: ${name} in this organization`
          )
        );
      }
    }

    data.createdBy = loggedInUser.id;
    await itemCategory.update(data, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Item Category'),
      data: itemCategory,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the item category',
    };
  }
};

exports.validateAndDeleteItemCategory = async (itemCategoryId) => {
  const transaction = await sequelize.transaction();
  try {
    const itemCategory = await ItemCategory.findOne({
      where: {
        id: itemCategoryId,
      },
    });

    if (!itemCategory) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Item Category'));
    }

    await itemCategory.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Item Category'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while deleting the item category',
    };
  }
};
