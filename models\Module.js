'use strict';

module.exports = (sequelize, DataTypes) => {
  const Module = sequelize.define(
    'Module',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      isDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Module.associate = (models) => {
    Module.belongsTo(models.Module, {
      foreignKey: 'parentId',
      as: 'parentModule',
      onDelete: 'CASCADE',
    });

    Module.hasMany(models.Module, {
      foreignKey: 'parentId',
      as: 'childModules',
    });

    Module.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });

    Module.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });
  };

  return Module;
};
