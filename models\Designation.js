'use strict';

module.exports = (sequelize, DataTypes) => {
  const Designation = sequelize.define(
    'Designation',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Designation.associate = (models) => {
    Designation.belongsTo(models.Department, {
      foreignKey: 'departmentId',
      as: 'department',
      onDelete: 'CASCADE',
    });

    Designation.belongsTo(models.Designation, {
      foreignKey: 'reportTo',
      as: 'reporter',
      onDelete: 'SET NULL',
    });

    Designation.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });

    Designation.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });

    Designation.hasMany(models.DocumentRequire, {
      foreignKey: 'designationId',
      as: 'documentRequires',
      onDelete: 'CASCADE',
    });
  };

  return Designation;
};
