module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('BoqEntry', 'boqSubCategoryId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'BOQCategory',
        key: 'id',
      },
      onDelete: 'CASCADE',
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('BoqEntry', 'boqSubCategoryId');
  },
};
