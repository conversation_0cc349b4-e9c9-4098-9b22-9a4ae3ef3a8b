const { priority } = require('../config/options');

exports.createTeamAssignment = {
  customerId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Customer ID is required',
    },
    isInt: {
      errorMessage: 'Customer ID must be a valid integer',
    },
  },
  requirementId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Requirement ID is required',
    },
    isInt: {
      errorMessage: 'Requirement ID must be a valid integer',
    },
  },
  memberId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Member ID must be a valid integer',
    },
  },
  priority: {
    in: ['body'],
    optional: true,
    // custom: {
    //   options: (value) => {
    //     const allowedPriorities = priority.getPriorityArray();
    //     if (value && !allowedPriorities.includes(value)) {
    //       throw new Error(
    //         `Priority must be one of: ${allowedPriorities.join(', ')}`
    //       );
    //     }
    //     return true;
    //   },
    // },
  },
  autoAssign: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'AutoAssign must be a boolean value',
    },
  },
};

exports.updateTeamAssignment = {
  customerId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Customer ID must be a valid integer',
    },
  },
  requirementId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Requirement ID must be a valid integer',
    },
  },
  memberId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Member ID must be a valid integer',
    },
  },
  priority: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedPriorities = priority.getPriorityArray();
        if (value && !allowedPriorities.includes(value)) {
          throw new Error(
            `Priority must be one of: ${allowedPriorities.join(', ')}`
          );
        }
        return true;
      },
    },
  },
  autoAssign: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'AutoAssign must be a boolean value',
    },
  },
};
