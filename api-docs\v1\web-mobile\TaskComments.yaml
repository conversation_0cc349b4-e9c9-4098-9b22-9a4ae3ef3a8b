paths:
  /task/comments:
    post:
      summary: Add a new comment to a task
      description: Adds a new comment to a specific task.
      operationId: addTaskComment
      tags:
        - Task
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createTaskComment"
      responses:
        "201":
          description: Comment added successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Comment added successfully"
                  data:
                    $ref: "#/components/schemas/TaskComment"
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /task/comments/{id}:
    patch:
      summary: Update an existing comment
      description: Updates the comment text for a specific comment.
      operationId: updateTaskComment
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the comment to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateTaskComment"
      responses:
        "200":
          description: Comment updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Comment updated successfully"
                  data:
                    $ref: "#/components/schemas/TaskComment"
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    
    delete:
      summary: Delete a comment
      description: Deletes a specific comment from a task.
      operationId: deleteTaskComment
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the comment to delete
      responses:
        "200":
          description: Comment deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Comment deleted successfully"
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createTaskComment:
      type: object
      required:
        - comment
        - taskId
      properties:
        comment:
          type: string
          example: "This is a comment on the task."
          description: "The text of the comment."
        taskId:
          type: integer
          example: 1
          description: "The ID of the task the comment is associated with."

    updateTaskComment:
      type: object
      required:
        - comment
      properties:
        comment:
          type: string
          example: "Updated comment text."
          description: "The updated text of the comment."

    TaskComment:
      type: object
      properties:
        id:
          type: integer
          example: 1
        comment:
          type: string
          example: "This is a comment on the task."
        taskId:
          type: integer
          example: 1
        createdAt:
          type: string
          format: date-time
          example: "2025-01-16T10:00:00.000Z"
        updatedAt:
          type: string
          format: date-time
          example: "2025-01-17T12:30:00.000Z"
