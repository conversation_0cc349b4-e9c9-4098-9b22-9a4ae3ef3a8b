const {
  StockAdjustment,
  StockAdjustmentItem,
  Warehouse,
  Item,
  ItemVariant,
  WarehouseItem,
  sequelize,
} = require('..');
const {
  successMessage,
  errorMessage,
  stockAdjustmentStatus,
} = require('@config/options');

exports.validateAndCreateStockAdjustment = async (data, loggedInUser) => {
  const { items, warehouseId, adjustmentNumber, ...adjustmentData } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const existingAdjustment = await StockAdjustment.findOne({
      where: {
        adjustmentNumber,
      },
    });
    if (existingAdjustment) {
      throw new Error('Adjustment number already exists');
    }

    const warehouse = await Warehouse.findOne({
      where: { id: warehouseId, organizationId },
    });
    if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));

    const stockAdjustment = await StockAdjustment.create(
      {
        ...adjustmentData,
        adjustmentNumber,
        warehouseId,
        createdBy: loggedInUser.id,
        organizationId: organizationId,
      },
      { transaction }
    );

    if (items && items.length > 0) {
      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) {
          throw new Error(errorMessage.INVALID_ID('Item'));
        }

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant) {
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
          }
        }

        await StockAdjustmentItem.create(
          {
            ...item,
            stockAdjustmentId: stockAdjustment.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Stock Adjustment'),
      data: stockAdjustment,
    };
  } catch (error) {
    console.log('error', error);
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while creating the stock adjustment',
    };
  }
};

exports.validateAndUpdateStockAdjustment = async (
  adjustmentId,
  data,
  loggedInUser
) => {
  const { items, warehouseId, adjustmentNumber, ...adjustmentData } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const stockAdjustment = await StockAdjustment.findOne({
      where: {
        id: adjustmentId,
        organizationId: organizationId,
      },
    });

    if (!stockAdjustment) {
      throw new Error(errorMessage.INVALID_ID('Stock Adjustment'));
    }

    if (
      adjustmentNumber &&
      adjustmentNumber !== stockAdjustment.adjustmentNumber
    ) {
      const existingAdjustment = await StockAdjustment.findOne({
        where: {
          adjustmentNumber,
          id: { [sequelize.Op.ne]: adjustmentId },
        },
      });
      if (existingAdjustment) {
        throw new Error('Adjustment number already exists');
      }
    }

    if (warehouseId) {
      const warehouse = await Warehouse.findOne({
        where: { id: warehouseId, organizationId },
      });
      if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));
    }

    await stockAdjustment.update(
      { ...adjustmentData, adjustmentNumber, warehouseId },
      { transaction }
    );

    if (items && items.length > 0) {
      await StockAdjustmentItem.destroy({
        where: { stockAdjustmentId: stockAdjustment.id },
        transaction,
      });

      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) {
          throw new Error(errorMessage.INVALID_ID('Item'));
        }

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant) {
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
          }
        }

        await StockAdjustmentItem.create(
          {
            ...item,
            stockAdjustmentId: stockAdjustment.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Stock Adjustment'),
      data: stockAdjustment,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating the stock adjustment',
    };
  }
};

exports.validateAndUpdateStockAdjustmentStatus = async (
  adjustmentId,
  data,
  loggedInUser
) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const stockAdjustment = await StockAdjustment.findOne({
      where: {
        id: adjustmentId,
        organizationId: organizationId,
      },
    });

    if (!stockAdjustment) {
      throw new Error(errorMessage.INVALID_ID('Stock Adjustment'));
    }

    if (stockAdjustment.status === data.status) {
      throw new Error(errorMessage.INVALID_STATUS);
    }

    if (data.status === stockAdjustmentStatus.APPROVED) {
      await this.updateWarehouseItemsQuantity(
        adjustmentId,
        stockAdjustment.warehouseId
      );
    }

    data.updatedBy = loggedInUser.id;
    await stockAdjustment.update(data, { transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Stock Adjustment Status'),
      data: stockAdjustment,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating the stock adjustment status',
    };
  }
};

exports.updateWarehouseItemsQuantity = async (adjustmentId, warehouseId) => {
  try {
    const StockAdjustmentItems = await StockAdjustmentItem.findAll({
      where: {
        stockAdjustmentId: adjustmentId,
      },
    });

    for (const item of StockAdjustmentItems) {
      const warehouseItem = await WarehouseItem.findOne({
        where: {
          itemId: item.itemId,
          warehouseId,
        },
      });
      if (warehouseItem) {
        let availableQuantity = parseFloat(item.adjustment);
        availableQuantity = Math.round(availableQuantity * 100) / 100;
        warehouseItem.availableQuantity =
          parseFloat(warehouseItem.availableQuantity) + availableQuantity;

        await warehouseItem.save();
      } else {
        await WarehouseItem.create({
          itemId: item.itemId,
          warehouseId: warehouseId,
          availableQuantity: parseFloat(item.adjustment),
        });
      }
    }
  } catch (error) {
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating Warehouse Items quantity',
    };
  }
};

exports.validateAndDeleteStockAdjustment = async (adjustmentId) => {
  const transaction = await sequelize.transaction();
  try {
    const stockAdjustment = await StockAdjustment.findOne({
      where: {
        id: adjustmentId,
      },
    });

    if (!stockAdjustment) {
      throw new Error(errorMessage.INVALID_ID('Stock Adjustment'));
    }

    await StockAdjustmentItem.destroy({
      where: { stockAdjustmentId: stockAdjustment.id },
      transaction,
    });

    await stockAdjustment.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Stock Adjustment'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while deleting the stock adjustment',
    };
  }
};
