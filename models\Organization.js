'use strict';
const { organizationType, organizationTimeFormat } = require('@config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = (sequelize, DataTypes) => {
  const Organization = sequelize.define(
    'Organization',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(organizationType.getValues()),
        allowNull: true,
      },
      countryCode: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      mobile: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      country: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      about: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      website: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      panNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      tanNumber: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      dateFormat: {
        type: DataTypes.STRING,
        default: 'ddmmyy',
        allowNull: true,
      },
      measurementUnits: {
        type: DataTypes.STRING,
        default: 'Metric',
        allowNull: true,
      },
      gstinNumber: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      gstinDocument: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      address: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      state: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      city: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      pincode: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      timeFormat: {
        type: DataTypes.ENUM(organizationTimeFormat.getValues()),
        default: organizationTimeFormat.HOURS_12,
        allowNull: true,
      },
      timeZone: {
        type: DataTypes.STRING,
        default: '(GMT 5:30) India Standard Time (Asia/Kolkata)',
        allowNull: true,
      },
      numberFormat: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      currency: {
        type: DataTypes.STRING,
        default: 'INR (₹)',
        allowNull: true,
      },
      isPrimary: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      logo: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      tableName: 'Organization',
      timestamps: true,
    }
  );

  Organization.associate = function (models) {
    Organization.hasMany(models.TemplateMaster, {
      foreignKey: 'organizationId',
      as: 'templateMasters',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Organization.belongsTo(models.Workspace, {
      foreignKey: 'workspaceId',
      as: 'workspace',
    });

    Organization.hasMany(models.Department, {
      foreignKey: 'organizationId',
      as: 'departments',
    });

    Organization.hasMany(models.ActivityLog, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        activityOn: 'Organization',
      },
      as: 'activities',
    });

    Organization.belongsToMany(models.User, {
      through: 'UserOrganization',
      foreignKey: 'organizationId',
      otherKey: 'userId',
      as: 'users',
      onDelete: 'CASCADE',
    });
  };
  return Organization;
};
