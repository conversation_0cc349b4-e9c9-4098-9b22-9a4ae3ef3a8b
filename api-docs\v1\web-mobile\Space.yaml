paths:
  /project/space-floor:
    post:
      summary: Creates a new space for Floor
      description: Creates a new space with the provided details including name,floor, dimensions, and area information.
      operationId: createSpaceForFloor
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSpaceFloor"
      responses:
        "201":
          description: SpaceFloor created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/space-unit:
    post:
      summary: Creates a new space for Unit
      description: Creates a new space with the provided details including name, unit,dimensions, and area information.
      operationId: createSpaceForUnit
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSpaceUnit"
      responses:
        "201":
          description: SpaceUnit created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/space:
    post:
      summary: Creates a new space
      description: Creates a new space with the provided details, including name, unit, floor, dimensions, area, and optional drawings.
      operationId: createSpace
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createSpace"
      responses:
        "201":
          description: Space created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/space/{id}:
    put:
      summary: Updates an existing space
      description: Updates a space with the provided details such as name, dimensions, and associations like unit and floor.
      operationId: updateSpace
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the space to be updated
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateSpace"
      responses:
        "200":
          description: Space updated successfully
        "400":
          description: Invalid request
        "404":
          description: Space not found
        "500":
          description: Internal Server Error
    delete:
      summary: Deletes an existing space
      description: Deletes a space identified by its ID.
      operationId: deleteSpace
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the space to be deleted
      responses:
        "200":
          description: Space deleted successfully
        "404":
          description: Space not found
        "500":
          description: Internal Server Error

  /project/space/{id}/drawing:
    post:
      summary: Adds drawings to an existing space
      description: Adds drawings to an existing space identified by its ID.
      operationId: addDrawingsToSpace
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the space to add drawings to
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addDrawingsToSpace"
      responses:
        "200":
          description: Drawings added successfully
        "400":
          description: Invalid request or invalid space ID
        "500":
          description: Internal Server Error


components:
  schemas:
    createSpaceFloor:
      type: object
      required:
        - name
        - length
        - breadth
        - area
      properties:
        name:
          type: string
          description: The name of the space.
          example: "Space A"
        floorId:
          type: integer
          description: The ID of the floor the space is located on (optional).
          example: 2
        length:
          type: number
          format: float
          description: The length of the space.
          example: 15.5
        breadth:
          type: number
          format: float
          description: The breadth of the space.
          example: 10.2
        area:
          type: number
          format: float
          description: The calculated area of the space.
          example: 158.1

    createSpace:
      type: object
      required:
        - name
        - length
        - breadth
        - area
      properties:
        name:
          type: string
          description: The name of the space.
          example: "Space A"
        unitId:
          type: integer
          description: The ID of the unit the space belongs to (optional).
          example: 1
        floorId:
          type: integer
          description: The ID of the floor the space is located on (optional).
          example: 2
        length:
          type: number
          format: float
          description: The length of the space.
          example: 15.5
        breadth:
          type: number
          format: float
          description: The breadth of the space.
          example: 10.2
        area:
          type: number
          format: float
          description: The calculated area of the space.
          example: 158.1
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
    updateSpace:
      type: object
      properties:
        name:
          type: string
          description: Name of the space
          maxLength: 100
        unitId:
          type: integer
          description: ID of the unit associated with the space
        floorId:
          type: integer
          nullable: true
          description: ID of the floor associated with the space, if applicable
        length:
          type: number
          format: float
          description: Length of the space in meters
        breadth:
          type: number
          format: float
          description: Breadth of the space in meters
        area:
          type: number
          format: float
          description: Calculated area of the space in square meters
      required: []
      example:
        name: "Conference Room"
        unitId: 3
        floorId: 1
        length: 8.5
        breadth: 6.2
        area: 52.7

    createSpaceUnit:
      type: object
      required:
        - name
        - unitId
        - length
        - breadth
        - area
      properties:
        name:
          type: string
          description: The name of the space.
          example: "Space A"
        unitId:
          type: integer
          description: The ID of the unit the space belongs to.
          example: 1
        length:
          type: number
          format: float
          description: The length of the space.
          example: 15.5
        breadth:
          type: number
          format: float
          description: The breadth of the space.
          example: 10.2
        area:
          type: number
          format: float
          description: The calculated area of the space.
          example: 158.1

    addDrawingsToSpace:
      type: object
      required:
        - drawings
      properties:
        projectId:
          type: integer
          description: The ID of the project the space belongs to.
          example: 1
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
    