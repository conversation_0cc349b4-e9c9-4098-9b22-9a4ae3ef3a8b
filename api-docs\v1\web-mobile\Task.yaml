paths:
  /task:
    post:
      summary: Creates a new Task
      description: Creates a new Task with all required and optional fields
      operationId: createTask
      tags:
        - Task
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createTask"
      responses:
        "201":
          description: Task created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: List tasks with filters and search
      description: Fetches a list of tasks based on optional filters and search parameters.
      operationId: listTasks
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
            example: 0
          description: The page number for pagination.
        - in: query
          name: limit
          schema:
            type: integer
            example: 10
          description: The number of documents to return per page.
        - name: status
          in: query
          description: Based on Request status
          schema:
            type: array
            items:
              type: string
            example: ["pending", "in_progress","ongoing"]
        - in: query
          name: startDate
          schema:
            type: string
            format: date-time
          description: "Filter tasks starting from this date"
        - in: query
          name: endDate
          schema:
            type: string
            format: date-time
          description: "Filter tasks ending before this date"
        - in: query
          name: units.name
          schema:
            type: string
          description: "Filter tasks by unit name"
        - name: projectId
          in: query
          description: The project ID to retrieve the task
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - in: query
          name: assignedTo
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - in: query
          name: assignedBy
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - in: query
          name: createdBy
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - in: query
          name: taskTags.id
          schema:
            type: integer
          description: "Filter tasks by task tag ID"
        - in: query
          name: organizationId
          schema:
            type: integer
          description: "Filter tasks by Organization ID"
        - in: query
          name: search
          schema:
            type: string
          description: >
            Search tasks by title.
      responses:
        "200":
          description: List of tasks retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Tasks fetched successfully"
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/TaskList"
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /task/task-details/{id}:
    patch:
      summary: Updates an existing Task with full details
      description: Updates task details, including tags, units, followers, dependencies, and subtasks.
      operationId: updateTaskDetails
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - in: "path"
          name: "id"
          required: true
          schema:
            type: string
          description: "The ID of the task to update."
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateTaskDetails"
      responses:
        "200":
          description: Task updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Task updated successfully"
                  data:
                    $ref: "#/components/schemas/TaskList"
        "400":
          description: Invalid request
        "404":
          description: Task not found
        "500":
          description: Internal Server Error
  /task/details/{id}:
    get:
      summary: Fetch task details by Task ID
      description: Retrieves the details of a specific task based on the provided Task ID.
      operationId: getTaskById
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - in: "path"
          name: "id"
          required: true
          schema:
            type: string
          description: "The ID of the task to retrieve."
      responses:
        "200":
          description: Task retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/getTaskResponse"
        "404":
          description: Task not found
        "500":
          description: Internal Server Error
    patch:
      summary: Updates an existing Task
      description: Updates an existing Task. Only specified fields will be updated.
      operationId: updateTask
      tags:
        - Task
      security:
        - bearerAuth: []
      parameters:
        - in: "path"
          name: "id"
          required: true
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateTask"
      responses:
        "200":
          description: Task updated successfully
        "400":
          description: Invalid request
        "404":
          description: Task not found
        "500":
          description: Internal Server Error
components:
  schemas:
    createTask:
      type: object
      required:
        - title
      properties:
        title:
          type: string
          example: "Implement API Authentication"
          description: "The title of the task"
        description:
          type: string
          example: "Develop API authentication using JWT tokens."
          description: "Detailed description of the task"
        status:
          type: string
          example: "todo"
          enum:
            - todo
            - pending
            - in_progress
            - ongoing
            - on_hold
            - approval_pending
            - revision_needed
            - completed
            - cancelled
            - overdue
            - delayed
            - escalated
            - delegated
          description: "The current status of the task"
        startDate:
          type: string
          format: date-time
          example: "2025-01-16 10:00:00"
          description: "Start date of the task"
        endDate:
          type: string
          format: date-time
          example: "2025-01-20 18:00:00"
          description: "End date of the task"
        projectId:
          type: integer
          example: 101
          description: "ID of the associated project"
        assignedTo:
          type: integer
          example: 45
          description: "User ID of the assignee"
        assignedBy:
          type: integer
          example: 12
          description: "User ID of the assigner"
        taskTagsIds:
          type: array
          items:
            type: integer
          example: [1, 2, 3]
          description: "Array of tag IDs associated with the task"
        unitIds:
          type: array
          items:
            type: integer
          example: [5, 6]
          description: "Array of unit IDs associated with the task"
        dependencies:
          type: array
          items:
            type: object
            properties:
              dependencyType:
                type: string
                enum:
                  - blocking
                  - blockedBy
                example: "blocking"
                description: "Type of dependency"
              status:
                type: string
                enum:
                  - fs
                  - ff
                  - ss
                  - sf
                example: "fs"
                description: "Dependency status"
              taskId:
                type: integer
                example: 78
                description: "ID of the dependent task"
          description: "List of dependencies"
        subTasks:
          type: array
          items:
            type: object
            properties:
              status:
                type: string
                enum:
                  - pending
                  - completed
                example: "pending"
                description: "Status of the subtask"
              title:
                type: string
                example: "Design Database Schema"
                description: "Title of the subtask"
              startDate:
                type: string
                format: date-time
                example: "2025-01-16 10:00:00"
                description: "Start date of the subtask"
              dueDate:
                type: string
                format: date-time
                example: "2025-01-20 10:00:00"
                description: "Due date of the subtask"
          description: "List of subtasks"
        followers:
          type: array
          items:
            type: integer
          example: [4, 8, 15]
          description: "Array of user IDs following the task"
    updateTask:
      type: object
      properties:
        title:
          type: string
          example: "Update API Documentation"
          description: "The new title of the task"
        description:
          type: string
          example: "Update the API documentation to include the latest endpoints."
          description: "Updated description of the task"
        status:
          type: string
          example: "in_progress"
          enum:
            - todo
            - pending
            - in_progress
            - ongoing
            - on_hold
            - approval_pending
            - revision_needed
            - completed
            - cancelled
            - overdue
            - delayed
            - escalated
            - delegated
          description: "The new status of the task"
        priority:
          type: string
          example: "urgent"
          enum:
            - low
            - medium
            - high
            - urgent
            - critical
          description: "The updated priority level of the task"
        startDate:
          type: string
          format: date-time
          example: "2025-01-16 12:00:00"
          description: "Updated start date of the task"
        endDate:
          type: string
          format: date-time
          example: "2025-01-22 18:00:00"
          description: "Updated end date of the task"
        assignedTo:
          type: integer
          example: 50
          description: "New User ID of the assignee"
        assignedBy:
          type: integer
          example: 15
          description: "New User ID of the assigner"
    updateTaskDetails:
      type: object
      properties:
        title:
          type: string
          example: "Update API Documentation"
        description:
          type: string
          example: "Update the API documentation to include the latest endpoints."
        status:
          type: string
          enum:
            - todo
            - pending
            - in_progress
            - completed
        startDate:
          type: string
          format: date-time
          example: "2025-01-16T12:00:00Z"
        endDate:
          type: string
          format: date-time
          example: "2025-01-22T18:00:00Z"
        projectId:
          type: integer
          example: 101
        assignedTo:
          type: integer
          example: 50
        assignedBy:
          type: integer
          example: 15
        taskTagsIds:
          type: array
          items:
            type: integer
          example: [1, 2, 3]
        unitIds:
          type: array
          items:
            type: integer
          example: [5, 6]
        dependencies:
          type: array
          items:
            type: object
            properties:
              dependencyType:
                type: string
                enum:
                  - blocking
                  - blockedBy
              status:
                type: string
                enum:
                  - fs
                  - ff
                  - ss
                  - sf
              taskId:
                type: integer
                example: 78
        subTasks:
          type: array
          items:
            type: object
            properties:
              status:
                type: string
                enum:
                  - pending
                  - completed
              title:
                type: string
              startDate:
                type: string
                format: date-time
              dueDate:
                type: string
                format: date-time
        followers:
          type: array
          items:
            type: integer
          example: [4, 8, 15]
    TaskList:
      type: object
      properties:
        id:
          type: integer
          example: 9
        title:
          type: string
          example: "Update API Documentation"
        description:
          type: string
          example: "Update the API documentation to include the latest endpoints."
        status:
          type: string
          example: "in_progress"
        priority:
          type: string
          example: "urgent"
        startDate:
          type: string
          format: date-time
          example: "2025-01-20T06:30:00.000Z"
        endDate:
          type: string
          format: date-time
          example: "2025-01-22T12:30:00.000Z"
        project:
          type: object
          properties:
            id:
              type: integer
              example: 2
            name:
              type: string
              example: "Green Valley Heights"
            logo:
              type: string
              example: "https://example.com/project-logo.png"
        assignedToUser:
          $ref: "#/components/schemas/User"
        assignedByUser:
          $ref: "#/components/schemas/User"
        taskTags:
          type: array
          items:
            $ref: "#/components/schemas/Tag"
        units:
          type: array
          items:
            $ref: "#/components/schemas/Unit"
    User:
      type: object
      properties:
        id:
          type: integer
          example: 5
        firstName:
          type: string
          example: "John"
        middleName:
          type: string
          example: "A."
        lastName:
          type: string
          example: "Doe"
        profilePicture:
          type: string
          example: "https://d30z6r4nn0i2bm.cloudfront.net/uploads/undefined"
    Tag:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Site Visit"
    Unit:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Unit 1"
    getTaskResponse:
      type: object
      properties:
        result:
          type: object
          properties:
            message:
              type: string
              example: "Task data fetched successfully"
            data:
              type: object
              properties:
                id:
                  type: integer
                  example: 9
                title:
                  type: string
                  example: "Update API Documentation"
                description:
                  type: string
                  example: "Update the API documentation to include the latest endpoints."
                status:
                  type: string
                  example: "in_progress"
                priority:
                  type: string
                  example: "urgent"
                startDate:
                  type: string
                  format: date-time
                  example: "2025-01-20T06:30:00.000Z"
                endDate:
                  type: string
                  format: date-time
                  example: "2025-01-22T12:30:00.000Z"
                createdAt:
                  type: string
                  format: date-time
                  example: "2025-01-16T13:46:52.193Z"
                updatedAt:
                  type: string
                  format: date-time
                  example: "2025-01-17T10:41:54.321Z"
                project:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 2
                    name:
                      type: string
                      example: "Green Valley Heights"
                    logo:
                      type: string
                      example: "https://example.com/project-logo.png"
                assignedToUser:
                  type: object
                  properties:
                    profilePicture:
                      type: string
                      example: "https://d30z6r4nn0i2bm.cloudfront.net/uploads/undefined"
                    id:
                      type: integer
                      example: 5
                    firstName:
                      type: string
                      example: "John"
                    middleName:
                      type: string
                      example: "A."
                    lastName:
                      type: string
                      example: "Doe"
                    designation:
                      type: string
                      nullable: true
                      example: null
                assignedByUser:
                  type: object
                  properties:
                    profilePicture:
                      type: string
                      example: ""
                    id:
                      type: integer
                      example: 3
                    firstName:
                      type: string
                      example: "John"
                    middleName:
                      type: string
                      nullable: true
                      example: null
                    lastName:
                      type: string
                      example: "Koli"
                    designation:
                      type: string
                      nullable: true
                      example: null
                taskTags:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      name:
                        type: string
                        example: "Site Visit"
                units:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      name:
                        type: string
                        example: "Unit 1"
                dependencies:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 3
                      dependencyType:
                        type: string
                        example: "blocking"
                      status:
                        type: string
                        example: "fs"
                      dependentTask:
                        type: object
                        properties:
                          id:
                            type: integer
                            example: 1
                          title:
                            type: string
                            example: "Test"
                subTasks:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 4
                      status:
                        type: string
                        example: "pending"
                      title:
                        type: string
                        example: "Design Database Schema"
                      startDate:
                        type: string
                        format: date-time
                        example: "2025-01-16T04:30:00.000Z"
                      dueDate:
                        type: string
                        format: date-time
                        example: "2025-01-20T04:30:00.000Z"
                followers:
                  type: array
                  items:
                    type: object
                    properties:
                      profilePicture:
                        type: string
                        example: "https://d30z6r4nn0i2bm.cloudfront.net/uploads/undefined"
                      id:
                        type: integer
                        example: 5
                      firstName:
                        type: string
                        example: "John"
                      middleName:
                        type: string
                        nullable: true
                        example: "A."
                      lastName:
                        type: string
                        example: "Doe"