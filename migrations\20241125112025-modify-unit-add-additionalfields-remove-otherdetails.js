'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Unit', 'additionalFields', {
      type: Sequelize.JSONB,
      allowNull: true,
    });
    await queryInterface.removeColumn('Unit', 'otherDetails');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Unit', 'otherDetails', {
      type: Sequelize.JSONB,
      allowNull: true,
    });
    await queryInterface.removeColumn('Unit', 'additionalFields');
  },
};
