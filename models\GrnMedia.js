const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const GrnMedia = sequelize.define(
    'GrnMedia',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      mediaType: {
        type: DataTypes.ENUM(OPTIONS.grnMediaType.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.grnMediaType.GRN,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  GrnMedia.associate = (models) => {
    GrnMedia.belongsTo(models.Grn, {
      foreignKey: 'grnId',
      as: 'grn',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    GrnMedia.belongsTo(models.GrnItem, {
      foreignKey: 'grnItemId',
      as: 'grnItem',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return GrnMedia;
};
