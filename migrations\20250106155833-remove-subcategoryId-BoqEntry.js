module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('BoqEntry', 'boqSubCategoryId');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('BoqEntry', 'boqSubCategoryId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'BOQSubCategory',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },
};
