exports.addWorkOrderBOQMapping = {
  workOrderId: {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'Work Order ID must be a positive integer',
    },
    notEmpty: {
      errorMessage: 'Work Order ID cannot be empty',
    },
  },
  boqItemIds: {
    in: ['body'],
    isArray: {
      errorMessage: 'BOQ Item ID must be an array of integers',
    },
    custom: {
      options: (value) =>
        Array.isArray(value) &&
        value.every((id) => Number.isInteger(id) && id > 0),
      errorMessage: 'Each BOQ Item ID must be a positive integer',
    },
    notEmpty: {
      errorMessage: 'BOQ Item ID cannot be empty',
    },
  },
};

exports.deleteWorkOrderBOQMappingValidator = {
  workOrderId: {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'Work Order ID must be a positive integer',
    },
    notEmpty: {
      errorMessage: 'Work Order ID cannot be empty',
    },
  },
  boqItemId: {
    in: ['body'],
    isInt: {
      options: { min: 1 },
      errorMessage: 'BOQ Item ID must be a positive integer',
    },
    notEmpty: {
      errorMessage: 'BOQ Item ID cannot be empty',
    },
  },
};
