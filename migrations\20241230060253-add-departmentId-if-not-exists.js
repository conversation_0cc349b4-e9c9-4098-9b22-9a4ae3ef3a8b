'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Designation');

    if (!tableDescription['departmentId']) {
      await queryInterface.addColumn('Designation', 'departmentId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Department',
          key: 'id',
        },
        onDelete: 'CASCADE',
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Designation');
    if (tableDescription['departmentId']) {
      await queryInterface.removeColumn('Designation', 'departmentId');
    }
  },
};
