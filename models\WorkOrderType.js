module.exports = (sequelize, DataTypes) => {
  const WorkOrderType = sequelize.define(
    'WorkOrderType',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(50),
        unique: true,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  return WorkOrderType;
};
