const { recordStatus, expenseState } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Expense = sequelize.define(
    'Expense',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      organizationId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },

      date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      expenseAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      paidThroughAccountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      amount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: false,
      },
      vendorId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: null,
      },
      invoiceNumber: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: true,
      },
      notes: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: true,
      },
      expenseState: {
        type: DataTypes.ENUM(expenseState.getValues()),
        defaultValue: expenseState.DRAFT,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(recordStatus.getValues()),
        defaultValue: recordStatus.ACTIVE,
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Expense.associate = (models) => {
    Expense.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'NO ACTION',
    });

    Expense.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
      onDelete: 'NO ACTION',
    });

    Expense.belongsTo(models.AccountData, {
      foreignKey: 'expenseAccountId',
      as: 'expenseAccount',
      onDelete: 'NO ACTION',
    });

    Expense.belongsTo(models.AccountData, {
      foreignKey: 'paidThroughAccountId',
      as: 'paidThroughAccount',
      onDelete: 'NO ACTION',
    });
  };

  return Expense;
};
