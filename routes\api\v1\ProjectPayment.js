const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');
const ProjectPaymentControl = require('../../../controllers/api/v1/ProjectPayment');
const ProjectPaymentSchema = require('@schema-validation/ProjectPayment');

router.post(
  '/:id/payment-revision',
  checkSchema(ProjectPaymentSchema.createAndUpdatePaymentRevision),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.createPaymentRevision
);

router.put(
  '/:id/payment-revision/:paymentRevisionId',
  checkSchema(ProjectPaymentSchema.createAndUpdatePaymentRevision),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.putPaymentRevision
);

router.post(
  '/:id/payment-plan',
  checkSchema(ProjectPaymentSchema.createOrUpdatePaymentPlan),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.createPaymentPlan
);

router.put(
  '/:id/payment-plan/:paymentPlanId',
  checkSchema(ProjectPaymentSchema.createOrUpdatePaymentPlan),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.putPaymentPlan
);

router.post(
  '/:id/broker-payout-plan',
  checkSchema(ProjectPaymentSchema.createOrUpdatePaymentPlan),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.createBrokerPayoutPlan
);

router.put(
  '/:id/broker-payout-plan/:brokerPayoutPlanId',
  checkSchema(ProjectPaymentSchema.createOrUpdatePaymentPlan),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.putBrokerPayoutPlan
);

router.post(
  '/:id/customer-status',
  checkSchema(ProjectPaymentSchema.createCustomerStatus),
  ErrorHandleHelper.requestValidator,
  ProjectPaymentControl.createCustomerStatus
);
module.exports = router;
