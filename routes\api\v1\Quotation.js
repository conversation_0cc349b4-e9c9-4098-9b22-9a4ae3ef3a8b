const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');
const QuotationControl = require('../../../controllers/api/v1/crm/Quotation');
const QuotationSchema = require('../../../schema-validation/Quotation');

router.post(
  '',
  checkSchema(QuotationSchema.createOrUpdateQuotation),
  ErrorHandleHelper.requestValidator,
  QuotationControl.createQuotation
);

router.put(
  '/:id',
  checkSchema(QuotationSchema.createOrUpdateQuotation),
  ErrorHandleHelper.requestValidator,
  QuotationControl.updateQuotation
);

router.put(
  '/:id/payment-plan',
  checkSchema(QuotationSchema.createQuotationPaymentPlan),
  ErrorHandleHelper.requestValidator,
  QuotationControl.createOrUpdateQuotationPaymentPlan
);

router.put(
  '/:id/broker-payment-plan',
  checkSchema(QuotationSchema.createQuotationPaymentPlan),
  ErrorHandleHelper.requestValidator,
  QuotationControl.createOrUpdateBrokerPaymentPlan
);

router.post(
  '/:id/convert-booking',
  checkSchema(QuotationSchema.createUnitBooking),
  ErrorHandleHelper.requestValidator,
  QuotationControl.bookedQuotationUnit
);

module.exports = router;
