const { grnStatus, grnItemStatus } = require('@config/options');

exports.createGrn = {
  purchaseOrderId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Purchase Order ID must be a valid integer',
    },
  },
  receivedOn: {
    in: ['body'],
    optional: true,
    isDate: {
      errorMessage: 'Received On Date must be a valid date',
    },
  },
  vendorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Vendor ID must be a valid integer',
    },
  },
  deliveredBy: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'DeliveredBy must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'delivery location warehouse ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = grnStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  items: {
    in: ['body'],
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
};

exports.updateGrn = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'GRN ID is required',
    },
    isInt: {
      errorMessage: 'GRN ID must be a valid integer',
    },
  },
  purchaseOrderId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Purchase Order ID must be a valid integer',
    },
  },
  receivedOn: {
    in: ['body'],
    optional: true,
    isDate: {
      errorMessage: 'Received On Date must be a valid date',
    },
  },
  vendorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Vendor ID must be a valid integer',
    },
  },
  deliveredBy: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'DeliveredBy must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'delivery location warehouse ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = grnStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  items: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
};

exports.updateGrnStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'GRN ID is required',
    },
    isInt: {
      errorMessage: 'GRN ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    notEmpty: true,
    custom: {
      options: (value) => {
        const allowedValues = grnStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
};

exports.updateGrnItemStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'GRN Item ID is required',
    },
    isInt: {
      errorMessage: 'GRN Item ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    notEmpty: true,
    custom: {
      options: (value) => {
        const allowedValues = grnItemStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
  receivedQuantity: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Received Quantity must be a valid integer',
    },
  },
  reason: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Reason must be a valid string',
    },
  },
  media: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Media must be an array of objects',
    },
  },
};

exports.deleteGrn = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'GRN ID is required',
    },
    isInt: {
      errorMessage: 'GRN ID must be a valid integer',
    },
  },
};

exports.deleteGrnMedia = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'GRN Media ID is required',
    },
    isInt: {
      errorMessage: 'GRN Media ID must be a valid integer',
    },
  },
};
