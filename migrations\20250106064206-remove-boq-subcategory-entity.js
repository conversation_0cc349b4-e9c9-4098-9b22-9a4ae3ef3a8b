'use strict';

const { defaultStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'BoqEntry',
      'BOQItems_boqSubCategoryId_fkey'
    );

    await queryInterface.removeConstraint(
      'SubCategoryCostItem',
      'CostItems_subcategoryId_fkey'
    );

    await queryInterface.dropTable('BOQSubCategory');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.createTable('BOQSubCategory', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      boqCategoryId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'BOQCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: defaultStatus.UN_ASSIGNED,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      organizationId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Project',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addConstraint('BoqEntry', {
      fields: ['boqSubCategoryId'],
      type: 'foreign key',
      name: 'BOQItems_boqSubCategoryId_fkey',
      references: {
        table: 'BOQSubCategory',
        field: 'id',
      },
      onDelete: 'CASCADE',
    });

    await queryInterface.addConstraint('SubCategoryCostItem', {
      fields: ['subcategoryId'],
      type: 'foreign key',
      name: 'CostItems_subcategoryId_fkey',
      references: {
        table: 'BOQSubCategory',
        field: 'id',
      },
      onDelete: 'CASCADE',
    });
  },
};
