const { Op } = require('sequelize');
const { Warehouse, Address, Organization, sequelize } = require('..');
const {
  successMessage,
  errorMessage,
  addressType,
} = require('@config/options');

exports.createDefaultWarehouseByOrgId = async (
  organizationId,
  createdBy,
  transaction
) => {
  try {
    const organization = await Organization.findOne({
      where: {
        id: organizationId,
      },
      transaction,
    });

    if (!organization) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Organization'));
    }

    const address = await Address.findOne({
      where: {
        organizationId: organizationId,
        addressType: addressType.ORGANIZATION,
      },
      transaction,
    });

    let addressId = null;
    if (address) {
      const newAddress = await Address.create(
        {
          addressType: addressType.WAREHOUSE,
          city: address.city,
          state: address.state,
          pincode: address.pincode,
          country: address.country,
          address: address.address,
          addressLine2: address.addressLine2,
          landmark: address.landmark,
          latitude: address.latitude,
          longitude: address.longitude,
        },
        { transaction }
      );
      addressId = newAddress.id;
    }

    const defaultWarehouse = await Warehouse.create(
      {
        name: organization.name,
        createdBy: createdBy,
        organizationId: organizationId,
        addressId: addressId,
      },
      { transaction }
    );

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Default Warehouse'),
      data: defaultWarehouse,
    };
  } catch (error) {
    throw error;
  }
};

exports.validateAndCreateWarehouse = async (data, loggedInUser) => {
  const { name, address } = data;
  const { id: userId, currentOrganizationId } = loggedInUser;
  const transaction = await sequelize.transaction();
  try {
    const existingWarehouse = await Warehouse.findOne({
      where: {
        name: {
          [Op.iLike]: name,
        },
      },
    });

    if (existingWarehouse) {
      throw new Error(
        errorMessage.ALREADY_EXIST('Warehouse with the same name')
      );
    }

    let addressId = null;
    if (address) {
      const newAddress = await Address.create(
        {
          ...address,
          addressType: addressType.WAREHOUSE,
        },
        { transaction }
      );
      addressId = newAddress.id;
    }

    const warehouse = await Warehouse.create(
      {
        ...data,
        addressId: addressId,
        createdBy: userId,
        organizationId: currentOrganizationId,
      },
      { transaction }
    );

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Warehouse'),
      data: warehouse,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while creating the warehouse',
    };
  }
};

exports.validateAndUpdateWarehouse = async (
  warehouseId,
  data,
  loggedInUser
) => {
  const { name } = data;
  const transaction = await sequelize.transaction();
  try {
    const warehouse = await Warehouse.findOne({
      where: {
        id: warehouseId,
      },
    });

    if (!warehouse) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Warehouse'));
    }

    if (name) {
      const existingWarehouse = await Warehouse.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
          id: {
            [Op.ne]: warehouseId,
          },
        },
      });

      if (existingWarehouse) {
        throw new Error(
          errorMessage.ALREADY_EXIST(`Warehouse with name: ${name}`)
        );
      }
    }

    if (data.address) {
      const address = await Address.findOne({
        where: {
          id: warehouse.addressId,
        },
      });

      if (address) {
        await address.update(data.address, { transaction });
      } else {
        data.addressType = addressType.WAREHOUSE;
        const newAddress = await Address.create(data.address, { transaction });
        data.addressId = newAddress.id;
      }
    }

    data.createdBy = loggedInUser.id;
    await warehouse.update(data, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Warehouse'),
      data: warehouse,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the warehouse',
    };
  }
};

exports.validateAndDeleteWarehouse = async (warehouseId) => {
  const transaction = await sequelize.transaction();
  try {
    const warehouse = await Warehouse.findOne({
      where: {
        id: warehouseId,
      },
    });

    if (!warehouse) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Warehouse'));
    }

    if (warehouse.addressId) {
      await Address.destroy({
        where: {
          id: warehouse.addressId,
        },
        transaction,
      });
    }

    await warehouse.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Warehouse'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while deleting the warehouse',
    };
  }
};
