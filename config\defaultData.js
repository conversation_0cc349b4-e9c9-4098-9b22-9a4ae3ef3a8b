const _ = require('lodash');

exports.predefinedProjectFolders = async (
  projectId,
  projectName,
  createdBy,
  organizationId,
  parentFolderId = null
) => {
  return [
    {
      name: projectName,
      projectId,
      createdBy,
      isDefault: true,
      isFolder: true,
      organizationId,
      ...(parentFolderId ? { parentFolderId } : {}),
      subDocuments: [
        {
          name: 'Project Files',
          projectId,
          createdBy,
          isDefault: true,
          isFolder: true,
          organizationId,
          subDocuments: [
            {
              name: 'Drawings',
              projectId,
              createdBy,
              organizationId,
              isDefault: true,
              isFolder: true,
            },
          ],
        },
      ],
    },
  ];
};

exports.getAccountCategoryOnly = () => {
  return this.getAccountCategoryAndTypes().map((objEachAccountData) => {
    return objEachAccountData.key;
  });
};

exports.getAllAccountTypesOnly = () => {
  let arrAccountTypes = this.getAccountCategoryAndTypes().map(
    (objEachAccountData) => {
      return objEachAccountData.arrAccountTypes.map((objEachAccountType) => {
        return objEachAccountType.key;
      });
    }
  );

  return _.flattenDeep(arrAccountTypes);
};

exports.getAccountCategoryAndTypes = () => {
  return [
    {
      accountCategory: 'Assets',
      key: 'ASSETS',
      arrAccountTypes: [
        {
          accountType: 'Other Assets',
          key: 'OTHER_ASSETS',
        },
        {
          accountType: 'Other Current Assets',
          key: 'OTHER_CURRENT_ASSETS',
          arrDefaultAccounts: [
            {
              name: 'Employee Advance',
            },
            {
              name: 'Sales to customers',
            },
            {
              name: 'Prepaid Expenses',
            },
            {
              name: 'TDS Receivable',
            },
            {
              name: 'Goods in Transit',
            },
            {
              name: 'Advance Tax',
            },
          ],
        },
        {
          accountType: 'Cash',
          key: 'CASH',
          arrDefaultAccounts: [
            {
              name: 'Undeposited Funds',
            },
            {
              name: 'Petty Cash',
            },
          ],
        },
        {
          accountType: 'Bank',
          key: 'BANK',
          arrDefaultAccounts: [
            {
              name: 'Payroll Bank Account',
            },
          ],
        },
        {
          accountType: 'Fixed Assets',
          key: 'FIXED_ASSETS',
        },
        {
          accountType: 'Stock',
          key: 'STOCK',
          arrDefaultAccounts: [
            {
              name: 'Inventory Asset',
            },
          ],
        },
        {
          accountType: 'Payment Clearing',
          key: 'PAYMENT_CLEARING',
        },
      ],
    },
    {
      accountCategory: 'Liability',
      key: 'LIABILITY',
      arrAccountTypes: [
        {
          accountType: 'Other Current Liability',
          key: 'OTHER_CURRENT_LIABILITY',
          arrDefaultAccounts: [
            {
              name: 'Reimbursements payable',
            },
            {
              name: 'Payroll Tax Payable',
            },
            {
              name: 'Statutory Deductions Payable',
            },
            {
              name: 'Deductions Payable',
            },
            {
              name: 'Net Salary Payable',
            },
            {
              name: 'Hold Salary Payable',
            },
            {
              name: 'Employee Reimbursements',
            },
            {
              name: 'Opening Balance Adjustments',
            },
            {
              name: 'Unearned Revenue',
            },
            {
              name: 'TDS Payable',
            },
            {
              name: 'Tax Payable',
            },
          ],
        },
        {
          accountType: 'Credit Card',
          key: 'CREDIT_CARD',
        },
        {
          accountType: 'Long term liability',
          key: 'LONG_TERM_LIABILITY',
          arrDefaultAccounts: [
            {
              name: 'Mortgages',
            },
            {
              name: 'Construction Loans',
            },
          ],
        },
        {
          accountType: 'Other Liability',
          key: 'OTHER_LIABILITY',
          arrDefaultAccounts: [
            {
              name: 'Dimension Adjustments',
            },
          ],
        },
        {
          accountType: 'Overseas Tax Payable',
          key: 'OVERSEAAS_TAX_PAYABLE',
        },
      ],
    },
    {
      accountCategory: 'Equity',
      key: 'EQUITY',
      arrAccountTypes: [
        {
          accountType: 'Equity',
          key: 'TYPE_EQUITY',
          arrDefaultAccounts: [
            {
              name: 'Drawings',
            },
            {
              name: 'Investments',
            },
            {
              name: 'Distributions',
            },
            {
              name: 'Capital Stock',
            },
            {
              name: 'Retained Earnings',
            },
            {
              name: 'Dividends Paid',
            },
            {
              name: 'Owner’s Equity',
            },
            {
              name: 'Opening Balance Offset',
            },
          ],
        },
      ],
    },
    {
      accountCategory: 'Income',
      key: 'INCOME',
      arrAccountTypes: [
        {
          accountType: 'Income',
          key: 'TYPE_INCOME',
          arrDefaultAccounts: [
            {
              name: 'Other Charges',
            },
            {
              name: 'Shipping Charge',
            },
            {
              name: 'Sales',
            },
            {
              name: 'General Income',
            },
            {
              name: 'Interest Income',
            },
            {
              name: 'Late Fee Income',
            },
            {
              name: 'Discount',
            },
          ],
        },
        {
          accountType: 'Other Income',
          key: 'OTHER_INCOME',
        },
      ],
    },
    {
      accountCategory: 'Expense',
      key: 'EXPENSE',
      arrAccountTypes: [
        {
          accountType: 'Expense',
          key: 'TYPE_EXPENCE',
          arrDefaultAccounts: [
            {
              name: 'Travel Expense',
            },
            {
              name: 'Telephone Expense',
            },
            {
              name: 'Automobile Expense',
            },
            {
              name: 'IT & Internet Expense',
            },
            {
              name: 'Rent Expense',
            },
            {
              name: 'Janitorial Expense',
            },
            {
              name: 'Postage',
            },
            {
              name: 'Bad Debts',
            },
            {
              name: 'Printing & Stationary',
            },
            {
              name: 'Salaries & Employee Wages',
            },
            {
              name: 'Meals & Entertainment',
            },
            {
              name: 'Depreciation Expense',
            },
            {
              name: 'Consultant Expense',
            },
            {
              name: 'Repairs & Maintenance',
            },
            {
              name: 'Other Expenses',
            },
            {
              name: 'Lodging',
            },
            {
              name: 'Uncategorised Expense',
            },
            {
              name: 'Raw Materials & Consumables',
            },
            {
              name: 'Merchandise',
            },
            {
              name: 'Transportation Expense',
            },
            {
              name: 'Depreciation & Amortisation',
            },
            {
              name: 'Contract Assets',
            },
            {
              name: 'Office Supplies',
            },
            {
              name: 'Advertising & Marketing',
            },
            {
              name: 'Purchase Discount',
            },
            {
              name: 'Bank Fees & Charges',
            },
            {
              name: 'Credit Card Charges',
            },
          ],
        },
        {
          accountType: 'Other Expense',
          key: 'OTHER_EXPENSE',
          arrDefaultAccounts: [
            {
              name: 'Exchange Gain or loss',
            },
          ],
        },
        {
          accountType: 'Cost of goods sold',
          key: 'COST_OF_GOODS_SOLD',
          arrDefaultAccounts: [
            {
              name: 'Labour',
            },
            {
              name: 'Material',
            },
            {
              name: 'Subcontractor',
            },
            {
              name: 'Job Costing',
            },
          ],
        },
      ],
    },
    {
      accountCategory: 'Accounts Payable',
      key: 'ACCOUTN_PAYABLE',
      arrAccountTypes: [
        {
          accountType: 'Accounts Payable',
          key: 'TYPE_ACCOUTN_PAYABLE',
        },
      ],
    },
    {
      accountCategory: 'Accounts Receivable',
      key: 'ACCOUNT_RECEIVABLE',
      arrAccountTypes: [
        {
          accountType: 'Accounts Receivable',
          key: 'TYPE_ACCOUTN_RECEIVABLE',
        },
      ],
    },
  ];
};

exports.getDefaultBudgetPeriods = () => {
  return [
    {
      name: 'Monthly',
      key: 'MONTHLY',
      period: 1,
    },
    {
      name: 'Quarterly',
      key: 'QUARTERLY',
      period: 3,
    },
    {
      name: 'Half Yearly',
      key: 'HALF_YEARLY',
      period: 6,
    },
    {
      name: 'Yearly',
      key: 'YEARLY',
      period: 12,
    },
  ];
};

exports.predefinedWorkFlow = async (organizationId) => {
  return [
    { moduleName: 'TaskApproval', organizationId, activityName: 'New Task' },
    { moduleName: 'TaskApproval', organizationId, activityName: 'Edit Task' },
    { moduleName: 'TaskApproval', organizationId, activityName: 'Delete Task' },
    {
      moduleName: 'TaskApproval',
      organizationId,
      activityName: 'Complete Task',
    },
    { moduleName: 'WorkOrder', organizationId, activityName: 'New Work Order' },
    {
      moduleName: 'WorkOrder',
      organizationId,
      activityName: 'Work Order Edits',
    },
    { moduleName: 'WBS', organizationId, activityName: 'Complete Activity' },
    { moduleName: 'DPR', organizationId, activityName: 'New DPR' },
    { moduleName: 'Material', organizationId, activityName: 'Indent' },
    {
      moduleName: 'Material',
      organizationId,
      activityName: 'Stock Adjustment',
    },
    {
      moduleName: 'Material',
      organizationId,
      activityName: 'Request For Quote',
    },
    { moduleName: 'Material', organizationId, activityName: 'Purchase Order' },
    { moduleName: 'Material', organizationId, activityName: 'Goods Receipt' },
    { moduleName: 'Legal', organizationId, activityName: 'Court Cases' },
    { moduleName: 'Legal', organizationId, activityName: 'Land' },
    { moduleName: 'Legal', organizationId, activityName: 'Sanctions' },
    { moduleName: 'Finance', organizationId, activityName: 'Account' },
    { moduleName: 'Finance', organizationId, activityName: 'Journal Entry' },
    { moduleName: 'Finance', organizationId, activityName: 'Invoice' },
    { moduleName: 'Finance', organizationId, activityName: 'Credit Note' },
    { moduleName: 'Finance', organizationId, activityName: 'Payment' },
    { moduleName: 'Finance', organizationId, activityName: 'Budget' },
    { moduleName: 'Finance', organizationId, activityName: 'Expenses' },
    { moduleName: 'Finance', organizationId, activityName: 'Cheques' },
    { moduleName: 'Finance', organizationId, activityName: 'Reconciliation' },
    { moduleName: 'Finance', organizationId, activityName: 'Loans' },
    { moduleName: 'Finance', organizationId, activityName: 'Audit' },
    { moduleName: 'Reports', organizationId, activityName: 'View' },
    {
      moduleName: 'Settings',
      organizationId,
      activityName: 'Organization Settings',
    },
    {
      moduleName: 'Settings',
      organizationId,
      activityName: 'Roles and Permissions',
    },
    { moduleName: 'Settings', organizationId, activityName: 'User Management' },
    { moduleName: 'Settings', organizationId, activityName: 'Integrations' },
    {
      moduleName: 'Settings',
      organizationId,
      activityName: 'Approval Workflow',
    },
    {
      moduleName: 'Settings',
      organizationId,
      activityName: 'Billing and Plans',
    },
    { moduleName: 'Settings', organizationId, activityName: 'Templates' },
    { moduleName: 'Settings', organizationId, activityName: 'Danger Zone' },
    { moduleName: 'CRM', organizationId, activityName: 'Leads' },
    { moduleName: 'CRM', organizationId, activityName: 'Quotations' },
    { moduleName: 'Employees', organizationId, activityName: 'All Employees' },
    { moduleName: 'Employees', organizationId, activityName: 'Attendance' },
    { moduleName: 'Employees', organizationId, activityName: 'Payroll' },
    {
      moduleName: 'Sales Management',
      organizationId,
      activityName: 'Quotations',
    },
    { moduleName: 'Sales Management', organizationId, activityName: 'Orders' },
    {
      moduleName: 'Sales Management',
      organizationId,
      activityName: 'Sales Partners',
    },
    {
      moduleName: 'Sales Management',
      organizationId,
      activityName: 'Catalogues',
    },
    { moduleName: 'Sales Management', organizationId, activityName: 'Brands' },
    { moduleName: 'Sales Management', organizationId, activityName: 'Tax' },
  ];
};
