const express = require('express');

const router = express.Router();
const AuthHandler = require('../../../../models/helpers/AuthHelper');

const UserRouter = require('./User');
const SubAdminRouter = require('./SubAdmin');

const AuthRouter = require('./Auth');
const LocationRouter = require('./Location.js');
const DepartmentRouter = require('./Department.js');
const DesignationRouter = require('./Designation.js');
const MarketplaceCategoryRouter = require('./MarketplaceCategory');
const { usersRoles } = require('../../../../config/options');

router.use('/auth', AuthRouter);
router.use('/user', UserRouter);
router.use(
  '/department',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  DepartmentRouter
);

router.use(
  '/designation',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  DesignationRouter
);

router.use(
  '/location',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  LocationRouter
);

router.use(
  '/sub-admin',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  SubAdminRouter
);

router.use(
  '/marketplace/category',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  MarketplaceCategoryRouter
);

module.exports = router;
