'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('RequestDocument', 'fileName', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('RequestDocument', 'fileType', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });
    await queryInterface.addColumn('RequestDocument', 'filePath', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('RequestDocument', 'fileSize', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('RequestDocument', 'fileName');
    await queryInterface.removeColumn('RequestDocument', 'fileType');
    await queryInterface.removeColumn('RequestDocument', 'filePath');
    await queryInterface.removeColumn('RequestDocument', 'fileSize');
  },
};
