'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(
      `CREATE TYPE "enum_Unit_facing" AS ENUM (
        'North', 'South', 'East', 'West', 'North-East', 'South-East', 'South-West', 'North-West'
      );`
    );

    await queryInterface.createTable('Unit', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      isSaleable: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isNamingFormat: {
        type: Sequelize.BOOLEAN,
        defaultValue: true,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      superBuiltUpArea: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      builtUpArea: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      carpetArea: {
        type: Sequelize.FLOAT,
        allowNull: true,
      },
      facing: {
        type: Sequelize.ENUM(
          'North',
          'South',
          'East',
          'West',
          'North-East',
          'South-East',
          'South-West',
          'North-West'
        ),
        allowNull: true,
      },
      otherDetails: {
        type: Sequelize.JSONB,
        allowNull: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Project',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      unitTypeId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'UnitType',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      floorId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Floor',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.createTable('UnitAmenities', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      unitId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Unit',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      amenityId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Amenities',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('UnitAmenities');
    await queryInterface.dropTable('Unit');
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Unit_facing";'
    );
  },
};
