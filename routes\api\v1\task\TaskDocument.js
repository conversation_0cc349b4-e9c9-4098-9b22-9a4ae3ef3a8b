const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskDocumentController = require('@controllers/v1/task/TaskDocument');
const TaskDocumentSchema = require('@schema-validation/task/TaskDocument');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/attachment',
  checkSchema(TaskDocumentSchema.addTaskDocuments),
  ErrorHandleHelper.requestValidator,
  TaskDocumentController.addAttachment
);

router.delete(
  '/attachment/:id',
  ErrorHandleHelper.requestValidator,
  TaskDocumentController.deleteAttachment
);

module.exports = router;
