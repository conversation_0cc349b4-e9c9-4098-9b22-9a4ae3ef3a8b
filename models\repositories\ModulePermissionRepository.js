const {
  Module,
  Designation,
  DesignationModulePermission,
  sequelize,
} = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

const getChildModules = async (parentId) => {
  const childModules = await Module.findAll({
    where: {
      parentId,
    },
    attributes: ['id'],
  });
  return childModules;
};

exports.validateAndAssignModulePermission = async (data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const { roleId, permissions } = data;
  const selectFields = ['id', 'name'];
  const transaction = await sequelize.transaction();
  try {
    const role = await checkExistence(
      Designation,
      { id: roleId, organizationId },
      selectFields
    );

    const roleName = role ? role.name : null;

    const validationPromises = [
      roleId && !role
        ? Promise.reject(
            new Error(
              errorMessage.DOES_NOT_EXIST(
                `Role with Id: ${roleId} does not exist`
              )
            )
          )
        : Promise.resolve(),

      ...permissions.map((permission) =>
        checkExistence(Module, { id: permission.moduleId }, selectFields).then(
          (checkModule) => {
            if (!checkModule) {
              throw new Error(
                errorMessage.DOES_NOT_EXIST(
                  `Module with Id: ${permission.moduleId} does not exist`
                )
              );
            }
          }
        )
      ),

      ...permissions.map((permission) =>
        DesignationModulePermission.findOne({
          where: {
            designationId: roleId,
            organizationId,
            moduleId: permission.moduleId,
          },
          include: [
            {
              model: Module,
              as: 'module',
              where: { id: permission.moduleId, organizationId },
              attributes: ['name'],
            },
          ],
        }).then((existingPermission) => {
          if (existingPermission) {
            const moduleName = existingPermission.module.name;
            throw new Error(
              `Permission already granted for Role: ${roleName} and Module: ${moduleName}`
            );
          }
        })
      ),
    ];

    await Promise.all(validationPromises);

    const allPermissions = [];
    const addedModuleIds = new Set();

    for (const permission of permissions) {
      const existingPermission = await DesignationModulePermission.findOne({
        where: {
          designationId: roleId,
          organizationId,
          moduleId: permission.moduleId,
        },
      });

      if (!existingPermission) {
        if (!addedModuleIds.has(permission.moduleId)) {
          allPermissions.push(permission);
          addedModuleIds.add(permission.moduleId);
        }

        const childModules = await getChildModules(permission.moduleId);
        for (const childModule of childModules) {
          const childExistingPermission =
            await DesignationModulePermission.findOne({
              where: {
                designationId: roleId,
                organizationId,
                moduleId: childModule.id,
              },
            });

          if (!childExistingPermission && !addedModuleIds.has(childModule.id)) {
            allPermissions.push({
              ...permission,
              moduleId: childModule.id,
            });
            addedModuleIds.add(childModule.id);
          }
        }
      }
    }

    const createdPermissions = await Promise.all(
      allPermissions.map(async (permission) => {
        return await DesignationModulePermission.create(
          {
            designationId: roleId,
            organizationId,
            createdBy: loggedInUser.id,
            ...permission,
          },
          { transaction }
        );
      })
    );

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Module Permissions'),
      data: createdPermissions,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while creating the module permissions',
    };
  }
};

exports.validateAndUpdateModulePermission = async (
  modulePermissionId,
  data
) => {
  const transaction = await sequelize.transaction();
  try {
    const modulePermission = await checkExistence(DesignationModulePermission, {
      id: modulePermissionId,
    });
    if (!modulePermission) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(
          `Module Permission with id: ${modulePermissionId} does not exist`
        )
      );
    }

    Object.assign(modulePermission, data);
    await modulePermission.save({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Module Permission'),
      data: modulePermission,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating the module permission',
    };
  }
};

exports.updateModulePermissionsByRoleId = async (roleId, data) => {
  const { permissions } = data;
  const transaction = await sequelize.transaction();
  try {
    for (const permission of permissions) {
      const modulePermission = await checkExistence(
        DesignationModulePermission,
        {
          moduleId: permission.id,
          designationId: roleId,
        }
      );

      if (!modulePermission) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Module Permission with id: ${permission.id} and roleId: ${roleId} does not exist`
          )
        );
      }

      Object.assign(modulePermission, permission);
      await modulePermission.save({ transaction });
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Module Permissions'),
      data: permissions,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating the module permissions',
    };
  }
};

exports.removeModulePermission = async (modulePermissionId) => {
  const transaction = await sequelize.transaction();
  try {
    const modulePermission = await checkExistence(
      DesignationModulePermission,
      { id: modulePermissionId },
      ['id']
    );
    if (!modulePermission) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(
          `Module Permission with id: ${modulePermissionId} does not exist`
        )
      );
    }

    await DesignationModulePermission.destroy({
      where: { id: modulePermissionId },
      transaction,
    });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Module Permission'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while deleting the module permission',
    };
  }
};

exports.assignDefaultPermissionsToRole = async (
  organizationId,
  createdBy,
  designationId,
  transaction
) => {
  const defaultModules = [
    'Edit Dashboard',
    'Tasks',
    'Requests',
    'Projects',
    'Files',
    'Communication',
  ];
  try {
    const permissionsToCreate = [];
    for (const moduleName of defaultModules) {
      const module = await Module.findOne({
        where: {
          name: moduleName,
        },
      });
      if (!module) {
        console.log(
          `Module "${moduleName}" not found in organization ${organizationId}`
        );
        continue;
      }

      const permission = {
        moduleId: module.id,
        isEnabled: true,
        organizationId: organizationId,
        createdBy: createdBy,
        designationId: designationId,
      };
      permissionsToCreate.push(permission);
    }

    if (permissionsToCreate.length > 0) {
      await DesignationModulePermission.bulkCreate(permissionsToCreate, {
        transaction,
      });
    }

    console.log('Default permissions assigned successfully.');
    return {
      success: true,
      message: 'Default permissions have been assigned successfully.',
    };
  } catch (error) {
    console.error('Error in assigning default permissions:', error);
    throw error;
  }
};

exports.findPermissionsByRoleId = async (roleId) => {
  try {
    const permissions = await DesignationModulePermission.findAll({
      where: {
        designationId: roleId,
        isEnabled: true,
      },
      attributes: { exclude: ['createdAt', 'updatedAt'] },
      include: [
        {
          model: Module,
          as: 'module',
          attributes: ['id', 'name'],
        },
      ],
    });
    return permissions;
  } catch (error) {
    return {
      success: false,
      message:
        error.message || 'An error occurred while fetching the permissions',
    };
  }
};
