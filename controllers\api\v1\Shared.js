const {
  resCode,
  genRes,
  errorTypes,
  errorMessage,
  generateCloudFrontUrl,
} = require('../../../config/options');

const PostalPincodeRepository = require('../../../models/repositories/PostalPincodeRepository');
const AWSHelpers = require('../../../models/helpers/AWSHelper');
const AssetRepository = require('@repo/AssetRepository');

exports.postUploadMedia = async (req, res) => {
  if (req.file) {
    return res.json(
      genRes(resCode.HTTP_OK, {
        data: req.file,
        cdn: generateCloudFrontUrl(req.file.key),
      })
    );
  }
  //   customErrorLogger(e);
  return res
    .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
    .json(
      genRes(
        resCode.HTTP_INTERNAL_SERVER_ERROR,
        errorMessage.SERVER_ERROR,
        errorTypes.INTERNAL_SERVER_ERROR
      )
    );
};

exports.getPostSignedURL = async (req, res) => {
  try {
    const filePath = `uploads/${Date.now()}-${req.query.fileName}`;
    const url = await AWSHelpers.generateSignedURL(filePath);
    res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { url, filePath }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.searchByPincode = async (req, res) => {
  try {
    const response = await PostalPincodeRepository.searchByPincode(
      req.params.pincode
    );
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, response[0]));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createAsset = async (req, res) => {
  try {
    const { success, message, data } =
      await AssetRepository.validateAndCreateAsset(req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateAsset = async (req, res) => {
  const { id: assetId } = req.params;
  try {
    const { success, message, data } =
      await AssetRepository.validateAndUpdateAsset(assetId, req.body);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteAsset = async (req, res) => {
  const { id: assetId } = req.params;
  try {
    const { success, message } =
      await AssetRepository.validateAndDeleteAsset(assetId);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
