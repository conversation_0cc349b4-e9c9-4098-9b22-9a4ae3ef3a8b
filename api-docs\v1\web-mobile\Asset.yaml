paths:
  /shared/asset:
    post:
      tags:
        - "Shared"
      summary: "Create a new Asset"
      description: "This endpoint allows you to create a new asset by providing all necessary details."
      operationId: "CreateAsset"
      requestBody:
        description: "The details of the new asset to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/AssetCreateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Asset has been created successfully."
        "400":
          description: "Invalid input data or asset already exists."
        "500":
          description: "Internal Server Error"

  /shared/asset/{id}:
    put:
      tags:
        - "Shared"
      summary: "Update an existing Asset"
      description: "This endpoint allows you to update the details of an existing asset by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateAsset"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the asset to be updated."
          schema:
            type: integer
            example: 12345
      requestBody:
        description: "The updated information for the asset. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/AssetUpdateRequest"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Asset has been updated successfully."
        "400":
          description: "Invalid input data, asset not found, or asset already exists."
        "404":
          description: "Asset not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Shared"
      summary: "Delete an existing Asset"
      description: "This endpoint allows you to delete an existing asset by providing its `id` in the URL path."
      operationId: "DeleteAsset"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the asset to be deleted."
          schema:
            type: integer
            example: 12345
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Asset has been deleted successfully."
        "400":
          description: "Invalid input data or asset not found."
        "404":
          description: "Asset not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    AssetCreateRequest:
      type: object
      properties:
        assetType:
          type: string
          description: "The type of the asset"
          enum:
            - amenities
            - wbs_category
            - wbs_subcategory
        name:
          type: string
          description: "The name of the asset"
          example: "Swimming Pool"
        logo:
          type: string
          description: "Logo for the asset"
          example: "https://example.com/logo.png"
      required:
        - assetType
        - name

    AssetUpdateRequest:
      type: object
      properties:
        assetType:
          type: string
          description: "The type of the asset"
          enum:
            - amenities
            - wbs_category
            - wbs_subcategory
        name:
          type: string
          description: "The name of the asset"
          example: "Clubhouse"
        logo:
          type: string
          description: "Logo for the asset"
          example: "https://example.com/logo.png"

  parameters:
    AssetIdParam:
      name: id
      in: path
      required: true
      description: "The ID of the asset"
      schema:
        type: integer
        example: 1