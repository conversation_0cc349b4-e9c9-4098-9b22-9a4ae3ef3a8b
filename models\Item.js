'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Item = sequelize.define(
    'Item',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      sku: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      unitOfMeasurement: {
        type: DataTypes.ENUM(OPTIONS.unitOfMeasurement.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.unitOfMeasurement.BOX,
      },
      hsn: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      taxRate: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      reOrderPoint: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      reOrderPointScope: {
        type: DataTypes.ENUM(OPTIONS.reOrderPointScope.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.reOrderPointScope.ALL_WAREHOUSES,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.itemStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.itemStatus.ACTIVE,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Item.associate = (models) => {
    Item.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });

    Item.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Item.belongsTo(models.ItemCategory, {
      foreignKey: 'categoryId',
      as: 'itemCategory',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Item.hasMany(models.ItemMedia, {
      foreignKey: 'itemId',
      as: 'itemMedia',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Item.hasMany(models.ItemVariant, {
      foreignKey: 'itemId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Item.hasMany(models.WarehouseItem, {
      foreignKey: 'itemId',
      as: 'warehouseItems',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Item.belongsToMany(models.Tax, {
      through: 'ItemTax',
      foreignKey: 'itemId',
      as: 'itemTaxes',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Item.hasMany(models.ActivityLog, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        activityOn: 'Item',
      },
      as: 'activities',
    });
  };

  return Item;
};
