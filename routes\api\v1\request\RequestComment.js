const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const RequestCommentsController = require('@controllers/v1/request/RequestComment');
const RequestCommentSchema = require('@schema-validation/request/RequestComment');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(RequestCommentSchema.createRequestCommentValidation),
  ErrorHandleHelper.requestValidator,
  RequestCommentsController.addComment
);

router.patch(
  '/:id',
  checkSchema(RequestCommentSchema.updateRequestCommentValidation),
  ErrorHandleHelper.requestValidator,
  RequestCommentsController.updateRequestComment
);

router.delete(
  '/:id',
  checkSchema(RequestCommentSchema.deleteRequestCommentValidation),
  ErrorHandleHelper.requestValidator,
  RequestCommentsController.deleteRequestComment
);

module.exports = router;
