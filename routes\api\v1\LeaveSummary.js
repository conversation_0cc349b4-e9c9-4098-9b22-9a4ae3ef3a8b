const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const LeaveSummaryController = require('@controllers/v1/LeaveSummary');
const LeaveSummarySchema = require('@schema-validation/LeaveSummary');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/summary',
  checkSchema(LeaveSummarySchema.createLeaveSummary),
  ErrorHandleHelper.requestValidator,
  LeaveSummaryController.createLeaveSummary
);

router.patch(
  '/summary/:id',
  checkSchema(LeaveSummarySchema.updateLeaveSummary),
  ErrorHandleHelper.requestValidator,
  LeaveSummaryController.updateLeaveSummary
);

router.delete(
  '/summary/:id',
  ErrorHandleHelper.requestValidator,
  LeaveSummaryController.deleteLeaveSummary
);

router.get(
  '/summary/:userId',
  ErrorHandleHelper.requestValidator,
  LeaveSummaryController.getLeaveSummary
);

module.exports = router;
