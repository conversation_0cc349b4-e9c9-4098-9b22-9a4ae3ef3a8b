const { paymentTermTemplateDetailsType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WorkOrderPaymentSchedules');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WorkOrderPaymentSchedules', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      workOrderId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'WorkOrder',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      templateDetailId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'PaymentTermTemplateDetails',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      description: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      type: {
        type: Sequelize.ENUM(
          paymentTermTemplateDetailsType.paymentTermTemplateDetailsTypeArray()
        ),
        allowNull: false,
        defaultValue: paymentTermTemplateDetailsType.ON_DATE,
      },
      schedule: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      agreementValue: {
        type: Sequelize.DECIMAL(5, 2),
        allowNull: false,
      },
      tax: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      amount: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: false,
      },
      isCustom: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },
};
