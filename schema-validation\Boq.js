exports.createBOQMetric = {
  metricValue: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Metric value cannot be empty',
    },
    isString: {
      errorMessage: 'Metric value must be a string',
    },
  },
  label: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Metric label cannot be empty',
    },
    isString: {
      errorMessage: 'Metric label must be a string',
    },
  },
};

exports.createBOQCategory = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Category name cannot be empty',
    },
    isString: {
      errorMessage: 'Category name must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
  boqMetricId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'BOQ Metric ID cannot be empty',
    },
    isInt: {
      errorMessage: 'BOQ Metric ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Metric ID must be greater than 0',
    },
  },
};

exports.createBOQSubCategory = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'SubCategory name cannot be empty',
    },
    isString: {
      errorMessage: 'SubCategory name must be a string',
    },
  },
};

exports.updateBOQSubCategory = {
  name: {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'SubCategory name must be a string',
    },
  },
  parentCategoryId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Parent category ID must be a valid integer',
    },
  },
};

exports.updateBOQCategory = {
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Category name must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
  boqMetricId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'BOQ Metric ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Metric ID must be greater than 0',
    },
  },
};

exports.createBOQItem = {
  name: {
    in: ['body'],
    isString: {
      errorMessage: 'Name must be a string',
    },
    trim: true,
    notEmpty: {
      errorMessage: 'Name is required',
    },
  },
  boqCategoryId: {
    in: ['body'],
    isInt: {
      errorMessage: 'BOQ Category ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Category ID must be greater than 0',
    },
    notEmpty: {
      errorMessage: 'BOQ Category ID cannot be empty',
    },
  },
  boqSubCategoryId: {
    in: ['body'],
    isInt: {
      errorMessage: 'BOQ Subcategory ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Subcategory ID must be greater than 0',
    },
    notEmpty: {
      errorMessage: 'BOQ Subcategory ID cannot be empty',
    },
  },
  boqMetricId: {
    in: ['body'],
    isInt: {
      errorMessage: 'BOQ Metric ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Metric ID must be greater than 0',
    },
    notEmpty: {
      errorMessage: 'BOQ Metric ID cannot be empty',
    },
  },
  length: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Length must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Length must be greater than or equal to 0',
    },
  },
  breadth: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Breadth must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Breadth must be greater than or equal to 0',
    },
  },
  height: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Height must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Height must be greater than or equal to 0',
    },
  },
  total: {
    in: ['body'],
    isDecimal: {
      errorMessage: 'Total must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Total must be greater than or equal to 0',
    },
  },
};

exports.updateBOQItem = {
  name: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Name must be a string',
    },
    trim: true,
  },
  boqCategory: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'BOQ Category ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Category ID must be greater than 0',
    },
  },
  boqSubCategoryId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'BOQ Subcategory ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Subcategory ID must be greater than 0',
    },
  },
  boqMetricId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'BOQ Metric ID must be an integer',
    },
    custom: {
      options: (value) => value > 0,
      errorMessage: 'BOQ Metric ID must be greater than 0',
    },
  },
  length: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Length must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Length must be greater than or equal to 0',
    },
  },
  breadth: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Breadth must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Breadth must be greater than or equal to 0',
    },
  },
  height: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Height must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Height must be greater than or equal to 0',
    },
  },
  total: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Total must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Total must be greater than or equal to 0',
    },
  },
  calculationType: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Calculation type must be a string',
    },
    isIn: {
      options: [['metric', 'percentage']],
      errorMessage: 'Calculation type must be either metric or percentage',
    },
  },
  labourCost: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Labour cost must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Labour cost must be greater than or equal to 0',
    },
  },
  materialCost: {
    in: ['body'],
    optional: true,
    isDecimal: {
      errorMessage: 'Material cost must be a decimal number',
    },
    custom: {
      options: (value) => value >= 0,
      errorMessage: 'Material cost must be greater than or equal to 0',
    },
  },
};
