paths:
  /settings/module-permission:
    post:
      tags:
        - "Settings"
      summary: "Assign permissions to modules for a specific role"
      description: "This endpoint allows you to assign permissions (view, edit, create) to modules for a specific role."
      operationId: "AssignModulePermission"
      requestBody:
        description: "The details of the permissions to be assigned to modules for a given role."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/ModulePermissionRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Permissions have been successfully assigned to the module."
        "400":
          description: "Invalid input data, or the specified role/module already exists."
        "500":
          description: "Internal Server Error"

  /settings/module-permission/{roleId}:
    put:
      tags:
        - "Settings"
      summary: "Update permissions for a specific role and module"
      description: "This endpoint allows you to update the permissions (view, edit, create) for a specific module based on the provided role ID."
      operationId: "UpdateModulePermissionForRole"
      parameters:
        - name: "roleId"
          in: "path"
          description: "The ID of the role for which module permissions are being updated."
          required: true
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The permissions to be updated for the module (view, edit, create) for the specified role."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/ModulePermissionUpdateRequest"
        required: true
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Permissions have been successfully updated for the specified role."
        "400":
          description: "Invalid input data, or module permissions not found for the provided role ID."
        "404":
          description: "Module permission not found for the provided role ID."
        "500":
          description: "Internal Server Error"

  /settings/module-permission/{id}:
    delete:
      tags:
        - "Settings"
      summary: "Remove permissions for a specific module"
      description: "This endpoint allows you to remove the permissions for a specific module."
      operationId: "RemoveModulePermission"
      parameters:
        - $ref: "#/components/schemas/ModulePermissionIdParam"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Permissions have been successfully removed."
        "400":
          description: "Invalid input data or module permission not found."
        "404":
          description: "Module permission not found for the specified ID."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    ModulePermissionRequest:
      type: object
      properties:
        roleId:
          type: integer
          example: 2
        permissions:
          type: array
          items:
            type: object
            properties:
              moduleId:
                type: integer
                example: 101
              canEdit:
                type: boolean
                example: true
              canCreate:
                type: boolean
                example: false
              isEnabled:
                type: boolean
                example: false
              isAssigned:
                type: boolean
                example: false
          example:
            - moduleId: 101
              canEdit: true
              canCreate: false
              isEnabled: false
              isAssigned: false
      required:
        - roleId
        - permissions

    ModulePermissionUpdateRequest:
      type: object
      properties:
        permissions:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 101
              canEdit:
                type: boolean
                example: true
              canCreate:
                type: boolean
                example: false
              isEnabled:
                type: boolean
                example: false
              isAssigned:
                type: boolean
                example: false
          example:
            - id: 101
              canEdit: true
              canCreate: false
              isEnabled: false
              isAssigned: false

    ModulePermissionIdParam:
      name: id
      in: path
      required: true
      description: "The ID of the module whose permissions you want to update or remove."
      schema:
        type: integer
        example: 1
