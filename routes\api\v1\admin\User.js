const express = require('express');

const router = express.Router();
const { checkSchema } = require('express-validator');

const AuthHandler = require('../../../../models/helpers/AuthHelper');
const UserControl = require('../../../../controllers/api/v1/admin/User');
const UserSchema = require('../../../../schema-validation/admin/User');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');
const { usersRoles } = require('../../../../config/options');
const ImportTemplate = require('../../../../models/helpers/ImportTemplateHelper');

router.post(
  '/login',
  checkSchema(UserSchema.emailLogin),
  ErrorHandleHelper.requestValidator,
  UserControl.login
);

router.put(
  '/user-update',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  checkSchema(UserSchema.updateInfo),
  ErrorHandleHelper.requestValidator,
  UserControl.userUpdate
);

router.get(
  '/profile',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  UserControl.getUserProfile
);

router.patch(
  '/:id',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  checkSchema(UserSchema.changeStatus),
  ErrorHandleHelper.requestValidator,
  UserControl.changeStatus
);

router.delete(
  '/:id',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  UserControl.deleteUser
);

router.get(
  '/:id',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  UserControl.getById
);

router.get(
  '/',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  checkSchema(UserSchema.listing),
  ErrorHandleHelper.requestValidator,
  UserControl.getUserListing
);

router.post(
  '/',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  checkSchema(UserSchema.createOrUpdate),
  ErrorHandleHelper.requestValidator,
  UserControl.postCreateUser
);

router.put(
  '/:id',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  checkSchema(UserSchema.createOrUpdate),
  ErrorHandleHelper.requestValidator,
  UserControl.putUpdateUser
);

router.delete(
  '/:id',
  AuthHandler.authenticateJWT(usersRoles.getAdminArray()),
  UserControl.deleteUser
);
router.post(
  '/bulk',
  ImportTemplate.single('file'),
  UserControl.bulkCreateUsers
);
module.exports = router;
