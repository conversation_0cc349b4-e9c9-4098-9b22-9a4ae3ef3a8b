module.exports = (sequelize, DataTypes) => {
  const DocumentRequire = sequelize.define(
    'DocumentRequire',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(50),
        allowNull: false,
      },
      isRequiredField: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
      isUploadRequired: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  DocumentRequire.associate = (models) => {
    DocumentRequire.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation',
      onDelete: 'CASCADE',
    });
  };

  return DocumentRequire;
};
