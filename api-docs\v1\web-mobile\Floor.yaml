paths:
  /project/{id}/floor:
    post:
      summary: Create a Floor for a Project
      description: Adds a new floor to a specific project.
      operationId: createFloor
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the project to which the floor belongs.
          schema:
            type: integer
            example: 123
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createFloor"
      responses:
        "201":
          description: Floor created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
          
  /project/floor/{id}/move-up:
    patch:
      summary: Move the Floor up in the project order
      description: Moves the floor up to the previous position in the order.
      operationId: moveFloorUp
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the floor to be moved up.
          schema:
            type: integer
            example: 123
      responses:
        "200":
          description: Floor moved up successfully
        "400":
          description: Invalid request, no floor to move up
        "404":
          description: Floor not found
        "500":
          description: Internal Server Error

  /project/floor/{id}/move-down:
    patch:
      summary: Move the Floor down in the project order
      description: Moves the floor down to the previous position in the order.
      operationId: moveFloorDown
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the floor to be moved down.
          schema:
            type: integer
            example: 123
      responses:
        "200":
          description: Floor moved down successfully
        "400":
          description: Invalid request, no floor to move down
        "404":
          description: Floor not found
        "500":
          description: Internal Server Error

  /project/floor/{id}:
    put:
      summary: Update Floor Details
      description: Updates an existing floor's details for a specific project.
      operationId: updateFloor
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the floor to update.
          schema:
            type: integer
            example: 1
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateFloor"
      responses:
        "200":
          description: Floor updated successfully
        "400":
          description: Invalid request
        "404":
          description: Floor not found
        "500":
          description: Internal Server Error
    delete:
      summary: Delete Floor
      description: Deletes an existing floor from a project.
      operationId: deleteFloor
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the floor to delete.
          schema:
            type: integer
            example: 1
      responses:
        "200":
          description: Floor deleted successfully
        "404":
          description: Floor not found
        "500":
          description: Internal Server Error

  /project/{id}/floor/{floorId}/duplicate:
    post:
      summary: Duplicate a floor
      description: Creates a duplicate of the specified floor under a given ID.
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: The ID of the resource to which the floor belongs.
          required: true
          schema:
            type: string
        - name: floorId
          in: path
          description: The ID of the floor to duplicate.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successfully duplicated the floor.
        '400':
          description: The floor does not exist.
        '404':
          description: Floor or resource not found.
        '500':
          description: Internal server error.

  /project/floor/{id}/drawing:
    post:
      summary: Add drawings to a Floor
      description: Adds drawings (files or URLs) to an existing floor in a specific project.
      operationId: addDrawingsToFloor
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the floor to which drawings are added.
          schema:
            type: integer
            example: 123
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/addDrawingsToFloor"
      responses:
        "200":
          description: Drawings added successfully
        "400":
          description: Invalid request, drawings data is malformed
        "404":
          description: Floor not found
        "500":
          description: Internal Server Error

          
components:
  schemas:
    createFloor:
      type: object
      properties:
        isBasement:
          type: boolean
          example: false
          description: "Indicates whether the floor is a basement level."
        floorType:
          type: string
          enum: 
            - "project"
            - "unit"
          description: "The type of floor, either 'project' or 'unit'."
          example: "project"
        unitId:
          type: integer
          description: "The ID of the unit to which the floor belongs. when floorType is unit"
          example: 123
        name:
          type: string
          example: "Ground Floor"
          description: "The name of the floor."
        buildUpArea:
          type: number
          format: float
          example: 1500
          description: "The built-up area of the floor in square feet."
        isNamingFormat:
          type: boolean
          example: true
          description: "Indicates if a specific naming format is applied to this floor."
        namingPrefix:
          type: string
          nullable: true
          example: "GF-"
          description: "The prefix used in the floor's naming format."
        isAlphabets:
          type: boolean
          example: true
          description: "Indicates if the floor naming format uses alphabets."
        isNumeric:
          type: boolean
          example: false
          description: "Indicates if the floor naming format uses numbers."
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
      required:
      - name
      - floorType
      - buildUpArea
          
    updateFloor:
      type: object
      properties:
        projectId:
          type: integer
          description: "The ID of the project to which the floor belongs."
          example: 123
        isBasement:
          type: boolean
          example: false
          description: "Indicates whether the floor is a basement level."
        floorType:
          type: string
          enum: 
            - "project"
            - "unit"
          description: "The type of floor, either 'project' or 'unit'."
          example: "project"
        name:
          type: string
          example: "Updated Ground Floor"
          description: "The name of the floor."
        buildUpArea:
          type: number
          format: float
          example: 1600
          description: "The updated built-up area of the floor in square feet."
        isNamingFormat:
          type: boolean
          example: false
          description: "Indicates if a specific naming format is applied to this floor."
        namingPrefix:
          type: string
          nullable: true
          example: "GF-Updated"
          description: "The prefix used in the floor's naming format."
        isAlphabets:
          type: boolean
          example: false
          description: "Indicates if the floor naming format uses alphabets."
        isNumeric:
          type: boolean
          example: true
          description: "Indicates if the floor naming format uses numbers."
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "https://example.com/drawings/drawing1.png"
    
    addDrawingsToFloor:
      type: object
      properties:
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "https://example.com/drawings/drawing1.png"
