paths:
  /settings/company:
    post:
      tags:
        - "Settings"
      summary: "Create a new company"
      description: "This endpoint allows you to create a new company with the provided details."
      operationId: "CreateCompany"
      requestBody:
        description: "The details of the company to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CompanyRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "The company has been successfully created."
        "400":
          description: "Invalid input data or company already exists."
        "500":
          description: "Internal Server Error"
  /settings/company/{id}/about:
    get:
      tags:
        - "Settings"
      summary: "Get Company about details"
      description: "This endpoint returns company details based on company id"
      operationId: "getCompanyDetail"
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the company
          schema:
            type: integer
      responses:
        "200":
          description: "organization fetched successfully."
        "400":
          description: "The organization does not exist."
        "500":
          description: "Internal Server Error"
      security:
        - bearerAuth: []
  /settings/company/{id}/update-basic-info:
    patch:
      tags:
        - "Settings"
      summary: "Update basic information of a company"
      description: "This endpoint allows updating the basic information of a company based on its ID. Only the provided fields will be updated."
      operationId: "updateBasicInfo"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the company to update."
          schema:
            type: integer
      requestBody:
        description: "The fields to update for the company. All fields are optional."
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  example: "Tech Innovators Ltd."
                type:
                  type: string
                  example: "private_limited"
                mobile:
                  type: string
                  example: "+1234567890"
                website:
                  type: string
                  example: "https://www.techinnovators.com"
                address:
                  type: string
                  example: "1234 Tech Street, Silicon Valley"
                panNumber:
                  type: string
                  example: "**********"
                gstinNumber:
                  type: string
                  example: "22**********2Z5"
                tanNumber:
                  type: string
                  example: "DEL12345A"
                country:
                  type: string
                  example: "India"
                about:
                  type: string
                  example: "Leading technology company specializing in innovative solutions."
      responses:
        "200":
          description: "Company information updated successfully."
        "400":
          description: "Invalid input data or company does not exist."
        "500":
          description: "Internal Server Error."
      security:
        - bearerAuth: []
  /settings/company/{id}/update-localisation:
    patch:
      tags:
        - "Settings"
      summary: "Update localisation settings of a company"
      description: "This endpoint allows updating the localisation settings of a company based on its ID. Only the provided fields will be updated."
      operationId: "updateLocalisation"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the company to update."
          schema:
            type: integer
      requestBody:
        description: "The localisation settings to update for the company. All fields are optional."
        content:
          application/json:
            schema:
              type: object
              properties:
                dateFormat:
                  type: string
                  example: "DD-MM-YYYY"
                measurementUnits:
                  type: string
                  example: "metric"
                timeFormat:
                  type: string
                  example: "24_hours"
                timeZone:
                  type: string
                  example: "(GMT 5:30) India Standard Time (Asia/Kolkata)"
                numberFormat:
                  type: string
                  example: "#,##0.###"
                currency:
                  type: string
                  example: "INR (₹)"
      responses:
        "200":
          description: "Company localisation settings updated successfully."
        "400":
          description: "Invalid input data or company does not exist."
        "500":
          description: "Internal Server Error."
      security:
        - bearerAuth: []
  /settings/company/{id}/bank-details:
    get:
      summary: "Get Organization Bank Details - mobile & web"
      operationId: getOrganizationBankDetails
      tags:
        - Settings
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: The ID of the organization
          schema:
            type: integer
      responses:
        "200":
          description: Organization bank details fetched successfully
        "400":
          description: "The Organization does not exist"
        "500":
          description: Internal Server Error
  /settings/company/bank-detail:
    post:
      tags:
        - "Settings"
      summary: "Create a new bank detail for Organization"
      description: "This endpoint allows you to create a new bank detail with the provided details for Organization."
      operationId: "CreateBankDetailsForOrganization"
      requestBody:
        description: "The details of the bank account to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/BankDetailsRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "The bank detail has been successfully created."
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error"
  /settings/company/{id}/bank-detail:
    delete:
      tags:
        - "Settings"
      summary: "Mark a bank detail as deleted"
      description: "This endpoint allows you to mark a bank detail as deleted by updating its status to 'deleted'."
      operationId: "DeleteBankDetail"
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the bank detail to be deleted
          schema:
            type: integer
      responses:
        "200":
          description: "Bank detail has been successfully marked as deleted."
        "400":
          description: "The bank detail does not exist or is already deleted."
        "500":
          description: "Internal Server Error"
      security:
        - bearerAuth: []

  /settings/company/{id}/tax:
    post:
      tags:
        - "Settings"
      summary: "Create a new tax for the company"
      description: "This endpoint allows you to create a new tax for the specified company."
      operationId: "createCompanyTax"
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the organization
          schema:
            type: integer
      requestBody:
        description: "The details of the tax to be created."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TaxRequest"
        required: true
      responses:
        "201":
          description: "The tax has been successfully created."
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error."
      security:
        - bearerAuth: []

  /settings/company/tax/{id}:
    patch:
      tags:
        - "Settings"
      summary: "Update a tax for the company"
      description: "This endpoint allows you to update an existing tax for the specified company."
      operationId: "updateCompanyTax"
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the tax
          schema:
            type: integer
      requestBody:
        description: "The fields to update for the tax."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/TaxRequest"
        required: true
      responses:
        "200":
          description: "The tax has been successfully updated."
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error."
      security:
        - bearerAuth: []
    delete:
      tags:
        - "Settings"
      summary: "Delete a tax for the company"
      description: "This endpoint allows you to delete a tax for the specified company."
      operationId: "deleteCompanyTax"
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the tax to be deleted
          schema:
            type: integer
      responses:
        "200":
          description: "The tax has been successfully deleted."
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error."
      security:
        - bearerAuth: []
        
components:
  schemas:
    CompanyRequest:
      type: object
      properties:
        name:
          type: string
          example: "Tech Innovators Ltd."
        gstinNumber:
          type: string
          example: "ABC1234567Z1"
        gstinDocument:
          type: string
          example: "document_link.pdf"
        address:
          type: string
          example: "1234 Tech Street, Silicon Valley"
        city:
          type: string
          example: "San Francisco"
        pincode:
          type: string
          example: "941056"
        logo:
          type: string
          example: "https://example.com/project-logo.png"
      required:
        - name
        - gstinNumber
        - address
        - city
        - pincode
        - isPrimary

    BankDetailsRequest:
      type: object
      properties:
        accountName:
          type: string
          example: "John Doe"
        accountNumber:
          type: string
          example: "************"
        ifscCode:
          type: string
          example: "SBIN0000123"
        bankName:
          type: string
          example: "State Bank of India"
        organizationId:
          type: integer
          example: 7
      required:
        - accountName
        - accountNumber
        - ifscCode
        - bankName
        - organizationId

    TaxRequest:
      type: object
      properties:
        name:
          type: string
          example: "Sales Tax"
        calculation:
          type: string
          example: "percentage"
        accountId:
          type: integer
          example: 1
        description:
          type: string
          example: "Tax applicable on sales."
      required:
        - name
        - calculation