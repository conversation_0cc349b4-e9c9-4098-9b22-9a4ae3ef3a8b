const { defaultStatus } = require('@config/options');

exports.sendInvitation = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Project Id is required',
    },
    isInt: {
      errorMessage: 'Project Id must be a valid integer',
    },
  },
  userId: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'User Id is required',
    },
    isInt: {
      errorMessage: 'User Id must be a valid integer',
    },
  },
};

exports.updateInvitationStatus = {
  status: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Status is required',
    },
    custom: {
      options: (value) => {
        const allowedValues = [defaultStatus.ACCEPTED, defaultStatus.REJECTED];
        if (!allowedValues.includes(value)) {
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid status value provided',
    },
  },
};

exports.validateAndRemoveTeamMember = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Project Id is required',
    },
    isInt: {
      errorMessage: 'Project Id must be a valid integer',
    },
  },
};
