'use strict';
const { indentItemStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('IndentItem', 'status', {
      type: Sequelize.ENUM(...indentItemStatus.getValues()),
      allowNull: false,
      defaultValue: indentItemStatus.PENDING,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('IndentItem', 'status');
  },
};
