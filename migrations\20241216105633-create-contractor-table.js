'use strict';
const { contractorType } = require('../config/options.js');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Contractor', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      contractorType: {
        type: Sequelize.ENUM(...contractorType.contractorTypeArray()),
        allowNull: false,
      },
      logo: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      businessName: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      firstName: {
        type: Sequelize.STRING(100),
        allowNull: true,
      },
      lastName: {
        type: Sequelize.STRING(100),
      },
      countryCode: {
        type: Sequelize.STRING(5),
        allowNull: true,
      },
      mobileNumber: {
        type: Sequelize.STRING(15),
        allowNull: false,
      },
      email: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      gstNumber: {
        type: Sequelize.STRING(15),
        allowNull: true,
      },
      panNumber: {
        type: Sequelize.STRING(10),
        allowNull: true,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      pinCode: {
        type: Sequelize.STRING(6),
        allowNull: true,
      },
      about: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_Contractors_contractorType";'
    );
    await queryInterface.dropTable('Contractor');
  },
};
