'use strict';
const { itemStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Item', 'status', {
      type: Sequelize.ENUM(...itemStatus.getValues()),
      allowNull: false,
      defaultValue: itemStatus.ACTIVE,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Item', 'status');
  },
};
