const { RequestDocument, Request } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.addRequestAttachment = async (data, loggedInUser) => {
  try {
    const existingRequest = await Request.findOne({
      where: { id: data.requestId },
    });

    if (!existingRequest) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Request'),
      };
    }
    const requestDocumentPayload = data.document.map((document) => ({
      requestId: data.requestId,
      createdBy: loggedInUser.id,
      ...document,
    }));

    const document = await RequestDocument.bulkCreate(requestDocumentPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Attachment'),
      data: document,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteAttachment = async (documentId) => {
  try {
    const document = await RequestDocument.findByPk(documentId);
    if (!document) {
      return {
        success: false,
        message: errorMessage.NO_USER('documentId'),
      };
    }

    await document.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Attachment'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
