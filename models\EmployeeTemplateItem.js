'use strict';
const {
  calculationType,
  templateEntityType,
  durationType,
} = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const EmployeeTemplateItem = sequelize.define(
    'EmployeeTemplateItem',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      calculation: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      calculationType: {
        type: DataTypes.ENUM(calculationType.getCalculationTypeArray()),
        allowNull: true,
      },
      monthlyAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      maxAllowed: {
        type: DataTypes.DECIMAL(3, 1),
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      entityType: {
        type: DataTypes.ENUM(templateEntityType.getTemplateEntityTypeArray()),
        allowNull: false,
      },
      durationType: {
        type: DataTypes.ENUM(durationType.getDurationTypeArray()),
        allowNull: true,
      },
      calculation: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      annualAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  EmployeeTemplateItem.associate = function (models) {
    EmployeeTemplateItem.belongsTo(models.Employee, {
      foreignKey: 'employeeId',
      as: 'employee',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    EmployeeTemplateItem.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      allowNull: true,
    });

    EmployeeTemplateItem.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      allowNull: true,
    });

    EmployeeTemplateItem.belongsTo(models.EmployeeTemplate, {
      foreignKey: 'employeeTemplateId',
      as: 'employeeTemplate',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return EmployeeTemplateItem;
};
