'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      CREATE VIEW "Attendance_With_Duration" AS
      SELECT 
        "employeeId",
        "date",
        "status",
        EXTRACT(EPOCH FROM ("outTime" - "inTime")) / 3600 AS duration_in_hours
      FROM 
        "Attendance"
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS "Attendance_With_Duration";
    `);
  },
};
