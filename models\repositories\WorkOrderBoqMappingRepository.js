const {
  WorkOrderBOQMapping,
  BoqEntry,
  Project,
  WorkOrder,
  WorkOrderType,
  sequelize,
  Contractor,
  Organization,
} = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');
const { Op, Sequelize } = require('sequelize');

exports.createWorkOrderBOQMapping = async (data, loggedInUser) => {
  const { projectId, workOrderId, boqItemIds } = data;
  const transaction = await sequelize.transaction();

  try {
    const project = await Project.findOne({ where: { id: projectId } });
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Project'),
      };
    }

    const workOrder = await WorkOrder.findOne({ where: { id: workOrderId } });
    if (!workOrder) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Work Order'),
      };
    }

    const createdMappings = [];
    for (const boqItemId of boqItemIds) {
      const boqItem = await BoqEntry.findOne({
        where: { id: boqItemId },
        include: ['metric'],
      });

      if (!boqItem) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`BOQ Item with ID ${boqItemId}`),
        };
      }

      const existingMapping = await WorkOrderBOQMapping.findOne({
        where: { boqItemId, workOrderId, projectId },
      });

      if (existingMapping) {
        await transaction.rollback();
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(`BOQ Item with ID ${boqItemId}`),
        };
      }

      await boqItem.update({ status: 'ongoing' }, { transaction });

      const boqRate = boqItem.total;
      const payload = {
        projectId,
        workOrderId,
        boqItemId,
        boqRate,
        createdBy: loggedInUser.id,
      };

      const workOrderBOQMapping = await WorkOrderBOQMapping.create(payload, {
        transaction,
      });
      createdMappings.push(workOrderBOQMapping);
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('WorkOrderBOQMapping'),
      data: createdMappings,
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};

exports.listWorkorder = async (query) => {
  const {
    start = 0,
    limit = 10,
    fromDate,
    toDate,
    type,
    status,
    projectId,
    organizationId,
    contractorName,
    search,
    workCategory,
  } = query;

  const dateFilter = {};
  const filters = {};
  const searchFilter = {};

  try {
    if (projectId) {
      filters.projectId = {
        [Op.in]: Array.isArray(projectId) ? projectId : [projectId],
      };
    }
    if (organizationId) filters['organizationId'] = organizationId;
    if (contractorName) {
      const contractorNames = Array.isArray(contractorName)
        ? contractorName
        : [contractorName];

      filters[Op.or] = contractorNames.map((name) => {
        const [firstName, lastName] = name.split(' ');
        return {
          '$contractor.firstName$': firstName,
          ...(lastName && { '$contractor.lastName$': lastName }),
        };
      });
    }

    if (workCategory) {
      filters[Op.and] = [
        Sequelize.literal(
          `"activities"::jsonb @> '[{"categoryName": "${workCategory}"}]'`
        ),
      ];
    }

    if (fromDate) dateFilter.createdAt = { [Op.gte]: new Date(fromDate) };
    if (toDate) {
      dateFilter.createdAt = {
        ...dateFilter.createdAt,
        [Op.lte]: new Date(toDate),
      };
    }

    const typeFilter = type ? { '$type.name$': type } : {};
    let statusFilter = {};

    if (status) {
      statusFilter.status = {
        [Op.in]: Array.isArray(status) ? status : [status],
      };
    }

    if (search) {
      searchFilter[Op.or] = [
        { workOrderNumber: { [Op.like]: `%${search}%` } },
        { '$contractor.firstName$': { [Op.like]: `%${search}%` } },
        { '$contractor.lastName$': { [Op.like]: `%${search}%` } },
        Sequelize.literal(
          `"activities"::jsonb @> '[{"categoryName": "${search}"}]'`
        ),
      ];
    }
    const { rows: workOrders, count: totalCount } =
      await WorkOrder.findAndCountAll({
        where: {
          ...filters,
          ...dateFilter,
          ...typeFilter,
          ...statusFilter,
          ...searchFilter,
        },
        include: [
          {
            model: WorkOrderType,
            as: 'type',
            attributes: ['name'],
          },
          {
            model: Contractor,
            as: 'contractor',
            attributes: ['firstName', 'lastName', 'logo'],
          },
          {
            model: Project,
            as: 'project',
            attributes: ['id', 'name'],
          },
          {
            model: Organization,
            as: 'organization',
            attributes: ['id', 'name'],
          },
        ],
        limit,
        offset: start,
        order: [['createdAt', 'DESC']],
      });

    if (workOrders.length === 0) {
      return {
        success: true,
        message: successMessage.DETAIL_MESSAGE(
          'No WorkOrders found based on filters'
        ),
        data: {
          rows: [],
          pagination: {
            totalCount: 0,
            start,
            limit,
          },
        },
      };
    }

    const rows = workOrders.map((workOrder) => {
      const activities = workOrder.activities || [];
      const workCategories = [
        ...new Set(activities.map((activity) => activity.categoryName)),
      ].join(', ');

      return {
        workOrderId: workOrder.id,
        workOrderNumber: workOrder.workOrderNumber,
        type: workOrder.type ? workOrder.type.name : null,
        contractorName: workOrder.contractor
          ? `${workOrder.contractor.firstName} ${workOrder.contractor.lastName}`.trim()
          : null,
        contractorProfile: workOrder.contractor
          ? workOrder.contractor.logo
          : null,
        projectId: workOrder.project ? workOrder.project.id : null,
        projectName: workOrder.project ? workOrder.project.name : null,
        organizationId: workOrder.organization
          ? workOrder.organization.id
          : null,
        workCategory: workCategories,
        fromDate: workOrder.fromDate,
        toDate: workOrder.toDate,
        status: workOrder.status,
        workOrderValue: workOrder.workOrderValue,
      };
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('WorkOrders'),
      data: {
        rows,
        pagination: {
          totalCount,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteWorkOrderBOQMapping = async (data) => {
  const { workOrderId, boqItemId } = data;

  const transaction = await sequelize.transaction();
  try {
    const workOrder = await checkExistence(WorkOrder, {
      id: workOrderId,
    });
    if (!workOrder) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Work Order with ID ${workOrderId}`
        ),
      };
    }

    const mapping = await checkExistence(WorkOrderBOQMapping, {
      workOrderId,
      boqItemId,
    });

    if (!mapping) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `WorkOrderBOQMapping for BOQ Item ID ${boqItemId}`
        ),
      };
    }

    await WorkOrderBOQMapping.destroy(
      {
        where: { id: mapping.id },
      },
      { transaction }
    );

    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('WorkOrderBOQMapping'),
    };
  } catch (error) {
    await transaction.rollback();
    throw error;
  }
};
