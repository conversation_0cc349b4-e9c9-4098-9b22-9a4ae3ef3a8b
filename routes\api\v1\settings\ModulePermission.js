const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ModulePermissionController = require('@controllers/v1/settings/ModulePermission');
const ModulePermissionSchema = require('@schema-validation/settings/ModulePermission');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(ModulePermissionSchema.assignModulePermission),
  ErrorHandleHelper.requestValidator,
  ModulePermissionController.assignModulePermission
);

router.put(
  '/:roleId',
  checkSchema(ModulePermissionSchema.updateModulePermission),
  ErrorHandleHelper.requestValidator,
  ModulePermissionController.updateModulePermission
);

router.delete(
  '/:id',
  checkSchema(ModulePermissionSchema.removeModulePermission),
  ErrorHandleHelper.requestValidator,
  ModulePermissionController.removeModulePermission
);

module.exports = router;
