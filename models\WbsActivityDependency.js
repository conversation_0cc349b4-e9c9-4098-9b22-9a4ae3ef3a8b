'use strict';

const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const WbsActivityDependency = sequelize.define(
    'WbsActivityDependency',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      dependencyType: {
        type: DataTypes.ENUM(...OPTIONS.dependencyType.getValues()),
        allowNull: false,
      },
      dependOn: {
        type: DataTypes.ENUM(...OPTIONS.dependOn.getValues()),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(...OPTIONS.dependencyStatus.getValues()),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WbsActivityDependency.associate = (models) => {
    WbsActivityDependency.belongsTo(models.WbsActivity, {
      foreignKey: 'wbsActivityId',
      as: 'wbsActivity',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivityDependency.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'task',
      allowNull: true,
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivityDependency.belongsTo(models.WbsActivity, {
      foreignKey: 'activityId',
      as: 'dependentActivity',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
  };

  return WbsActivityDependency;
};
