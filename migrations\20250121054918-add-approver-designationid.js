module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn(
      'ApprovalWorkflow',
      'approverDesignationId',
      {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Designation',
          key: 'id',
        },
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn(
      'ApprovalWorkflow',
      'approverDesignationId'
    );
  },
};
