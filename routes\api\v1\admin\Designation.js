const express = require('express');
const router = express.Router();
const DesignationControl = require('../../../../controllers/api/v1/admin/Designation.js');
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');
const DesignationSchema = require('../../../../schema-validation/admin/Designation.js');

router.post(
  '/',
  checkSchema(DesignationSchema.createUpdateDesignation),
  ErrorHandleHelper.requestValidator,
  DesignationControl.createDesignation
);

router.get('/', DesignationControl.getDesignations);

router.put(
  '/:id',
  checkSchema(DesignationSchema.createUpdateDesignation),
  ErrorHandleHelper.requestValidator,
  DesignationControl.updateDesignation
);

router.get('/:id', DesignationControl.getDesignationById);

module.exports = router;
