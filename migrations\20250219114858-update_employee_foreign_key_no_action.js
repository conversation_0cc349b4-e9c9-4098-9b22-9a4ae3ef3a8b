'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('Employee', 'Employee_userId_fkey');

    await queryInterface.addConstraint('Employee', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'Employee_userId_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'Employee',
      'Employee_reportedTo_fkey'
    );

    await queryInterface.addConstraint('Employee', {
      fields: ['reportedTo'],
      type: 'foreign key',
      name: 'Employee_reportedTo_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint('Employee', 'Employee_userId_fkey');

    await queryInterface.addConstraint('Employee', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'Employee_userId_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      allowNull: false,
    });

    await queryInterface.removeConstraint(
      'Employee',
      'Employee_reportedTo_fkey'
    );

    await queryInterface.addConstraint('Employee', {
      fields: ['reportedTo'],
      type: 'foreign key',
      name: 'Employee_reportedTo_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      allowNull: false,
    });
  },
};
