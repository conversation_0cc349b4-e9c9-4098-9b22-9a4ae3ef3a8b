module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'BOQCategory',
      'BOQCategory_boqMetricId_fkey'
    );
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addConstraint('BOQCategory', {
      fields: ['boqMetricId'],
      type: 'foreign key',
      name: 'BOQCategory_boqMetricId_fkey',
      references: {
        table: 'BOQMetric',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  },
};
