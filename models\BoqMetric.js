const { metricType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const BOQMetric = sequelize.define(
    'BOQMetric',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      metricValue: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      label: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      metricType: {
        type: DataTypes.ENUM(metricType.getMetricTypeArray()),
        allowNull: false,
        defaultValue: metricType.VOLUME,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  return BOQMetric;
};
