paths:
  /admin/location:
    post:
      summary: Create a new office location
      tags:
        - Office Location
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createUpdateLocation"
      produces:
        - application/json
      responses:
        '201':
          description: Office location created successfully
        '400':
          description: Invalid Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    get:
      summary: List office locations
      description: Fetches a list of all office locations within the organization with pagination and search functionality.
      tags:
        - Office Location
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
        - in: query
          name: limit
          schema:
            type: integer
        - in: query
          name: search
          schema:
            type: string
      responses:
        '200':
          description: Successful response
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
  /admin/location/{id}:
    put:
      summary: Update an existing office location
      tags:
        - Office Location
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createUpdateLocation"
      responses:
        '200':
          description: Office location updated successfully
        '400':
          description: Invalid Request
        '401':
          description: Unauthorized
        '500':
          description: Internal Server Error
    get:
      summary: Get an office location by ID
      tags:
        - Office Location
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          schema:
            type: integer
      responses:
        "200":
          description: "Fetched office location details successfully"
        "400":
          description: "Invalid Request"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"  

components:
  schemas:
    createUpdateLocation:
      type: object
      properties:
        officeName:
          type: string
          description: The name of the office
        address:
          type: string
          description: The address of the office
