'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('PurchaseOrderItem', 'taxRates', {
      type: Sequelize.JSONB,
      allowNull: true,
      defaultValue: {},
    });
    await queryInterface.removeColumn('PurchaseOrderItem', 'sgst');
    await queryInterface.removeColumn('PurchaseOrderItem', 'cgst');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('PurchaseOrderItem', 'taxRates');
    await queryInterface.addColumn('PurchaseOrderItem', 'sgst', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    });
    await queryInterface.addColumn('PurchaseOrderItem', 'cgst', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0.0,
    });
  },
};
