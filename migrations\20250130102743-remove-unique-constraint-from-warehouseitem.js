'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Check if the constraint exists before trying to remove it
    const constraints = await queryInterface.sequelize.query(
      "SELECT CONSTRAINT_NAME FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE TABLE_NAME = 'WarehouseItem' AND CONSTRAINT_NAME = 'unique_item_warehouse';",
      { type: Sequelize.QueryTypes.SELECT }
    );

    // If the constraint exists, remove it
    if (constraints.length > 0) {
      await queryInterface.removeConstraint(
        'WarehouseItem',
        'unique_item_warehouse'
      );
    } else {
      console.log(
        'Constraint "unique_item_warehouse" does not exist, skipping removal.'
      );
    }
  },

  async down(queryInterface, Sequelize) {
    // Add the constraint back in the down migration
    await queryInterface.addConstraint('WarehouseItem', {
      fields: ['itemId', 'warehouseId'],
      type: 'unique',
      name: 'unique_item_warehouse',
    });
  },
};
