'use strict';

const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Quotation = sequelize.define(
    'Quotation',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      quotationCode: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
      },
      expireDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      pricingRevision: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      pricingRevisionTotalAmount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      paymentPlan: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      brokerPaymentPlan: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      paymentPlanTotalAmount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      paymentPlanAgreementAmount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      paymentPlanBalance: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      homeLoanRequired: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
      totalBrokerageAmount: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(...OPTIONS.quotationStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.quotationStatus.DRAFT,
      },
      saleAgentId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      brokerAdditionTerm: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      termsAndCondition: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      holdPropertyUntilExpiry: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Quotation.associate = (models) => {
    Quotation.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      as: 'unit',
      onDelete: 'CASCADE',
    });

    Quotation.belongsTo(models.Customer, {
      foreignKey: 'customerId',
      as: 'user',
      onDelete: 'SET NULL',
    });

    Quotation.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onDelete: 'SET NULL',
    });

    Quotation.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });

    Quotation.belongsTo(models.User, {
      foreignKey: 'saleAgentId',
      as: 'saleAgent',
      onDelete: 'SET NULL',
    });

    Quotation.hasMany(models.QuotationUnitCost, {
      foreignKey: 'quotationId',
      as: 'unitCosts',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    Quotation.hasMany(models.QuotationPaymentPlan, {
      foreignKey: 'quotationId',
      as: 'paymentPlans',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
    Quotation.hasOne(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        requestType: 'quotation_request',
      },
      as: 'request',
    });

    Quotation.hasMany(models.ProjectBookedUnit, {
      foreignKey: 'quotationId',
      as: 'projectBookedUnit',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  };

  Quotation.afterCreate(async (quotation, options) => {
    const unitId = quotation.unitId || '00';
    const quotationId = quotation.id;
    const today = new Date();
    const formattedDate = `${String(today.getDate()).padStart(2, '0')}${String(today.getMonth() + 1).padStart(2, '0')}${today.getFullYear()}`;
    quotation.quotationCode = `Q${unitId}${quotationId}${formattedDate}`;
    await quotation.save();
  });

  return Quotation;
};
