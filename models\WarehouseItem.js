'use strict';

module.exports = (sequelize, DataTypes) => {
  const WarehouseItem = sequelize.define(
    'WarehouseItem',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      availableQuantity: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WarehouseItem.associate = (models) => {
    WarehouseItem.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    WarehouseItem.belongsTo(models.Warehouse, {
      foreignKey: 'warehouseId',
      as: 'warehouse',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    WarehouseItem.belongsTo(models.ItemVariant, {
      foreignKey: 'itemVariantId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return WarehouseItem;
};
