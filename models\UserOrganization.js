'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const UserOrganization = sequelize.define(
    'UserOrganization',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      isPrimary: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      userId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      organizationId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      designationId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Designation',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      inviteStatus: {
        type: DataTypes.ENUM(OPTIONS.inviteStatus.getInviteStatusArray()),
        allowNull: true,
      },
      projectId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
          model: 'Project',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      attendanceMarkType: {
        type: DataTypes.ENUM(OPTIONS.calculationType.getCalculationTypeArray()),
        allowNull: true,
      },
      unpaidLeaveDeductionType: {
        type: DataTypes.ENUM(OPTIONS.calculationType.getCalculationTypeArray()),
        allowNull: true,
      },
      unpaidLeaveDeductionAmount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
      },
      isProfileCompleted: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  UserOrganization.associate = (models) => {
    UserOrganization.belongsTo(models.Designation, {
      foreignKey: 'designationId',
      as: 'designation',
    });

    UserOrganization.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });

    UserOrganization.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    UserOrganization.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });
  };

  return UserOrganization;
};
