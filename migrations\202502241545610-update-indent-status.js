'use strict';

const { indentStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Indent');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Indent" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Indent_status_new') THEN
          CREATE TYPE "enum_Indent_status_new" AS ENUM (${indentStatus
            .getValues()
            .map((status) => `'${status}'`)
            .join(', ')});
        END IF;
      END $$;
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Indent" ALTER COLUMN "status" TYPE "enum_Indent_status_new"
        USING status::text::"enum_Indent_status_new";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Indent_status') THEN
          DROP TYPE "enum_Indent_status";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Indent_status_new" RENAME TO "enum_Indent_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Indent" ALTER COLUMN "status" SET DEFAULT '${indentStatus.DRAFT}';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Indent');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Indent" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Indent_status_old') THEN
          CREATE TYPE "enum_Indent_status_old" AS ENUM ('pending_approval');
        END IF;
      END $$;
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Indent" ALTER COLUMN "status" TYPE "enum_Indent_status_old"
        USING status::text::"enum_Indent_status_old";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Indent_status') THEN
          DROP TYPE "enum_Indent_status";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Indent_status_old" RENAME TO "enum_Indent_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Indent" ALTER COLUMN "status" SET DEFAULT 'pending_approval';
    `);
  },
};
