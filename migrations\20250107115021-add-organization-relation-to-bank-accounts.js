const { bankDetailStatus } = require('../config/options.js');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('BankDetail', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.changeColumn('BankDetail', 'userId', {
      type: Sequelize.INTEGER,
      allowNull: true,
    });

    await queryInterface.addColumn('BankDetail', 'status', {
      type: Sequelize.ENUM(...bankDetailStatus.contractorTypeArray()),
      defaultValue: bankDetailStatus.ACTIVE,
      allowNull: false,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('BankDetail', 'organizationId');

    await queryInterface.changeColumn('BankDetail', 'userId', {
      type: Sequelize.INTEGER,
      allowNull: false,
    });

    await queryInterface.removeColumn('BankDetail', 'status');
    await queryInterface.sequelize.query(
      'DROP TYPE IF EXISTS "enum_BankDetail_status";'
    );
  },
};
