'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'Warehouse',
      'Warehouse_addressId_fkey'
    );

    await queryInterface.addConstraint('Warehouse', {
      fields: ['addressId'],
      type: 'foreign key',
      name: 'Warehouse_addressId_fkey',
      references: {
        table: 'Address',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'Warehouse',
      'Warehouse_addressId_fkey'
    );

    await queryInterface.addConstraint('Warehouse', {
      fields: ['addressId'],
      type: 'foreign key',
      name: 'Warehouse_addressId_fkey',
      references: {
        table: 'Address',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
  },
};
