module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      -- Drop the trigger if it exists
      DROP TRIGGER IF EXISTS trigger_update_total_duration ON "Attendance";

      -- Drop the function if it exists
      DROP FUNCTION IF EXISTS update_total_duration;

      -- Create the function
      CREATE OR REPLACE FUNCTION update_total_duration()
      RETURNS TRIGGER AS $$
      BEGIN
        IF NEW."outTime" IS NOT NULL AND NEW."inTime" IS NOT NULL THEN
          NEW."totalDuration" := ROUND(EXTRACT(EPOCH FROM (NEW."outTime" - NEW."inTime")) / 3600, 2);
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      -- Create the trigger
      CREATE TRIGGER trigger_update_total_duration
      BEFORE UPDATE ON "Attendance"
      FOR EACH ROW
      WHEN (OLD."outTime" IS DISTINCT FROM NEW."outTime")
      EXECUTE FUNCTION update_total_duration();
    `);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_update_total_duration ON "Attendance";
      DROP FUNCTION IF EXISTS update_total_duration;
    `);
  },
};
