paths:
  /shared/upload:
    post :
      tags:
        - "Shared"
      summary: "Upload media"
      description: "Uploads a file to the server using multipart/form-data."
      operationId: "uploadMedia"
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                file:
                  type: string
                  format: binary
                  description: "The file to be uploaded."
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "File uploaded successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"      
  /shared/signed-url:
      get :
        tags:
          - "Shared"
        summary: "Signed url"
        description: ""
        operationId: "generateUrl"
        produces:
          - "application/json"
        parameters: 
          - in: query
            name: fileName
            schema:
                type: string
            description: file name
            required: true
        security:
          - bearerAuth: []
        responses:
          "200":
            description: "signed url"
          "400":
            description: "Invalid Request"
          "500":
            description: "Internal Server Error"
  /shared/pincode/{pincode}:
    get:
      tags:
        - "Shared"
      summary: "Get pincode"
      description: ""
      operationId: "getPincode"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "pincode"
          schema:
            type: string
          description: "Pincode"   
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "pinocde details"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"                   