const { budgetEntryType, recordStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const BudgetEntry = sequelize.define(
    'BudgetEntry',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      budgetId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      accountId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      budgetEntryType: {
        type: DataTypes.ENUM(budgetEntryType.getValues()),
        default: budgetEntryType.INCOME,
        allowNull: false,
      },
      budgetData: {
        type: DataTypes.JSONB,
        default: '',
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(recordStatus.getValues()),
        default: recordStatus.ACTIVE,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  BudgetEntry.associate = (models) => {
    BudgetEntry.belongsTo(models.Budget, {
      foreignKey: 'budgetId',
      as: 'budget',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    BudgetEntry.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return BudgetEntry;
};
