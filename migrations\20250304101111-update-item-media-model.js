'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('ItemMedia', 'fileName', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.changeColumn('ItemMedia', 'fileType', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });
    await queryInterface.changeColumn('ItemMedia', 'filePath', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.changeColumn('ItemMedia', 'fileSize', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('ItemMedia', 'fileName', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });
    await queryInterface.changeColumn('ItemMedia', 'fileType', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });
    await queryInterface.changeColumn('ItemMedia', 'filePath', {
      type: Sequelize.TEXT,
      allowNull: false,
    });
    await queryInterface.changeColumn('ItemMedia', 'fileSize', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
  },
};
