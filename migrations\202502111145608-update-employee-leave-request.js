'use strict';
const { leaveType } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription =
      await queryInterface.describeTable('EmployeeLeave');

    if (tableDescription.fromDate && !tableDescription.fromDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'fromDate', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    }

    if (tableDescription.toDate && !tableDescription.toDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'toDate', {
        type: Sequelize.DATE,
        allowNull: true,
      });
    }

    if (tableDescription.leaveType && !tableDescription.leaveType.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'leaveType', {
        type: Sequelize.ENUM(...leaveType.getValues()),
        allowNull: true,
      });
    }

    if (tableDescription.totalDays && !tableDescription.totalDays.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'totalDays', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }

    if (tableDescription.entityId && !tableDescription.entityId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'entityId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }

    if (tableDescription.employeeId && !tableDescription.employeeId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'employeeId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }

    if (tableDescription.userId && !tableDescription.userId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'userId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });
    }
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription =
      await queryInterface.describeTable('EmployeeLeave');

    if (tableDescription.fromDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'fromDate', {
        type: Sequelize.DATE,
        allowNull: false,
      });
    }

    if (tableDescription.toDate.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'toDate', {
        type: Sequelize.DATE,
        allowNull: false,
      });
    }

    if (tableDescription.leaveType.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'leaveType', {
        type: Sequelize.ENUM(...leaveType.getValues()),
        allowNull: false,
      });
    }

    if (tableDescription.totalDays.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'totalDays', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }

    if (tableDescription.entityId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'entityId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }

    if (tableDescription.employeeId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'employeeId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }

    if (tableDescription.userId.allowNull) {
      await queryInterface.changeColumn('EmployeeLeave', 'userId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });
    }
  },
};
