'use strict';

module.exports = (sequelize, DataTypes) => {
  const Tax = sequelize.define(
    'Tax',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      calculation: {
        allowNull: true,
        type: DataTypes.STRING,
      },
      description: {
        allowNull: true,
        type: DataTypes.TEXT,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Tax.associate = (models) => {
    Tax.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });

    Tax.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Tax.belongsTo(models.AccountData, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });

    Tax.belongsToMany(models.Item, {
      through: 'ItemTax',
      foreignKey: 'taxId',
      as: 'taxes',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return Tax;
};
