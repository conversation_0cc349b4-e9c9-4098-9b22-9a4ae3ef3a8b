'use strict';
const OPTIONS = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('User', 'maritalStatus', {
      type: Sequelize.ENUM(OPTIONS.maritalStatus.getMaritalStatusArray()),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('User', 'maritalStatus');
  },
};
