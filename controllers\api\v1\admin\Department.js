const DepartmentRepository = require('../../../../models/repositories/DepartmentRepository.js');
const {
  genRes,
  errorMessage,
  resCode,
  errorTypes,
  successMessage,
} = require('../../../../config/options');

exports.createDepartment = async (req, res) => {
  try {
    const { success, message, data } =
      await DepartmentRepository.checkAndCreateDepartment(req.body);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res
      .status(resCode.HTTP_CREATE)
      .json(genRes(resCode.HTTP_CREATE, { message, data }));
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getDepartmentById = async (req, res) => {
  try {
    const department = await DepartmentRepository.getDepartment({
      where: { id: req.params.id },
    });
    if (!department) {
      return res
        .status(resCode.HTTP_NOT_FOUND)
        .json(
          genRes(
            resCode.HTTP_NOT_FOUND,
            errorMessage.DOES_NOT_EXIST('department'),
            errorTypes.ENTITY_NOT_FOUND
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message: successMessage.DETAIL_MESSAGE('department'),
        data: department,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getDepartments = async (req, res) => {
  try {
    const { start = 0, limit = 10, search = '' } = req.query;
    const { message, data } = await DepartmentRepository.getDepartments({
      start,
      limit,
      search,
    });

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.updateDepartment = async (req, res) => {
  try {
    const { success, message, data } =
      await DepartmentRepository.checkAndUpdateDepartment(
        req.params.id,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }

    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data }));
  } catch (error) {
    customErrorLogger(error);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};
