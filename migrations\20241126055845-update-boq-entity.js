const { defaultStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('BOQItems', 'status', {
      type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
      allowNull: false,
      defaultValue: defaultStatus.UN_ASSIGNED,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('BOQItems', 'status');
  },
};
