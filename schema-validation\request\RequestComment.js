exports.createRequestCommentValidation = {
  comment: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Comment is required and cannot be empty',
    },
    isString: {
      errorMessage: 'Comment must be a valid string',
    },
    trim: true,
  },
  requestId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Request ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Request ID must be a valid integer',
    },
    toInt: true,
  },
};

exports.updateRequestCommentValidation = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Comment ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Comment ID must be an integer',
    },
    toInt: true,
  },
  comment: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Comment is required and cannot be empty',
    },
    isString: {
      errorMessage: 'Comment must be a valid string',
    },
    trim: true,
  },
};

exports.deleteRequestCommentValidation = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Comment ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Comment ID must be an integer',
    },
    toInt: true,
  },
};
