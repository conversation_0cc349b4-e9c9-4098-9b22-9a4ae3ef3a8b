module.exports = (sequelize, DataTypes) => {
  const Space = sequelize.define(
    'Space',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      length: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      breadth: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
      area: {
        type: DataTypes.FLOAT,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Space.associate = (models) => {
    Space.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      as: 'unit',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Space.belongsTo(models.Floor, {
      foreignKey: 'floorId',
      as: 'floor',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Space.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Space.hasMany(models.Drawing, {
      foreignKey: 'spaceId',
      as: 'drawings',
      onDelete: 'CASCADE',
    });
  };

  return Space;
};
