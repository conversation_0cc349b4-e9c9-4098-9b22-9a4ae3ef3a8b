const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const RequestDocumentController = require('@controllers/v1/request/RequestDocument');
const RequestDocumentSchema = require('@schema-validation/request/RequestDocument');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(RequestDocumentSchema.addRequestDocuments),
  ErrorHandleHelper.requestValidator,
  RequestDocumentController.addAttachment
);

router.delete(
  '/:id',
  ErrorHandleHelper.requestValidator,
  RequestDocumentController.deleteAttachment
);

module.exports = router;
