paths:
  /material/purchase-order:
    post:
      tags:
        - "Material"
      summary: "Create a new purchase order"
      description: "This endpoint allows you to create a new purchase order in the system"
      operationId: "CreatePurchaseOrder"
      requestBody:
        description: "Purchase order creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreatePurchaseOrderRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Purchase order created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/purchase-order/{id}:
    put:
      tags:
        - "Material"
      summary: "Update an existing purchase order"
      description: "This endpoint allows you to update an existing purchase order in the system"
      operationId: "UpdatePurchaseOrder"
      parameters:
        - $ref: "#/components/parameters/PurchaseOrderIdParam"
      requestBody:
        description: "Purchase order update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdatePurchaseOrderRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Purchase order updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Purchase order not found"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing purchase order"
      description: "This endpoint allows you to update the status of an existing purchase order"
      operationId: "UpdatePurchaseOrderStatus"
      parameters:
        - $ref: "#/components/parameters/PurchaseOrderIdParam"
      requestBody:
        description: "Purchase order status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/PurchaseOrderStatusUpdateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Purchase order status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Purchase order not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing purchase order"
      description: "This endpoint allows you to delete an existing purchase order"
      operationId: "DeletePurchaseOrder"
      parameters:
        - $ref: "#/components/parameters/PurchaseOrderIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Purchase order deleted successfully"
        "404":
          description: "Purchase order not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreatePurchaseOrderRequest:
      type: object
      properties:
        indentId:
          type: integer
          example: 1
        workOrderId:
          type: integer
          example: 1
        taskId:
          type: integer
          example: 1
        vendorId:
          type: integer
          example: 1
        warehouseId:
          type: integer
          example: 1
        requiredByDate:
          type: string
          format: date
          example: "2025-03-30"
        priority:
          type: string
          enum: ["high", "medium", "low", "urgent", "critical"]
          example: "high"
        note:
          type: string
          example: "This is a note"
        subTotal:
          type: number
          format: double
          example: 100.00
        taxRates:
          type: object
          example: {
            gst: 5.00,
            sgst: 2.50,
            cgst: 2.50
          }
        discount:
          type: number
          format: double
          example: 10.00
        total:
          type: number
          format: double
          example: 90.00
        items:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
              subTotal:
                type: number
                format: double
                example: 100.00
              taxRates:
                type: object
                example: {
                  gst: 5.00,
                  sgst: 2.50,
                  cgst: 2.50
                }
              discount:
                type: number
                format: double
                example: 10.00
              total:
                type: number
                format: double
                example: 90.00
      required:
        - requiredByDate
        - priority
        - items

    UpdatePurchaseOrderRequest:
      type: object
      properties:
        indentId:
          type: integer
          example: 1
        workOrderId:
          type: integer
          example: 1
        taskId:
          type: integer
          example: 1
        vendorId:
          type: integer
          example: 1
        warehouseId:
          type: integer
          example: 1
        requiredByDate:
          type: string
          format: date
          example: "2025-03-30"
        priority:
          type: string
          enum: ["high", "medium", "low", "urgent", "critical"]
          example: "medium"
        note:
          type: string
          example: "Updated note"
        subTotal:
          type: number
          format: double
          example: 100.00
        taxRates:
          type: object
          example: {
            gst: 5.00,
            sgst: 2.50,
            cgst: 2.50
          }
        discount:
          type: number
          format: double
          example: 10.00
        total:
          type: number
          format: double
          example: 90.00
        items:
          type: array
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
              subTotal:
                type: number
                format: double
                example: 100.00
              taxRates:
                type: object
                example: {
                  gst: 5.00,
                  sgst: 2.50,
                  cgst: 2.50
                }
              discount:
                type: number
                format: double
                example: 10.00
              total:
                type: number
                format: double
                example: 90.00

    PurchaseOrderStatusUpdateRequest:
      type: object
      properties:
        status:
          type: string
          enum: ["draft", "under_approval", "escalated", "approved", "ongoing", "delayed", "due_soon", "pending", "rejected"]
          example: "approved"
      required:
        - status

  parameters:
    PurchaseOrderIdParam:
      name: "id"
      in: "path"
      description: "ID of the purchase order to be managed"
      required: true
      schema:
        type: integer
