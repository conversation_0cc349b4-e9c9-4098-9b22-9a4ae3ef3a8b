paths:
  /organization-brand:
    post:
      tags:
        - "Organization Brand"
      summary: "Create organization brand"
      description: "Create a new organization brand"
      operationId: "createOrganizationBrand"
      requestBody:
        description: "Organization brand creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/organization-brand-create-update"
        required: true
      security:
        - bearerAuth: []
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Organization brand created successfully"
        "400":
          description: "Invalid Request | Missing required fields"
        "500":
          description: "Internal Server Error"
components:
  schemas:
    organization-brand-create-update:
      type: object
      properties:
        name:
          type: string
          description: "Organization brand name"
          example: "My Organization"
        organizationBrandMedia:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                description: "Media file name"
                example: "logo.png"
              fileType:
                type: string
                description: "Media file type"
                example: "image/png"
              filePath:
                type: string
                description: "Media file path"
                example: "/path/to/logo.png"
              fileSize:
                type: string
                description: "Media file size"
                example: "1024"
              description:
                type: string
                description: "Media file description"
                example: "this brand trade mark document"
      required:
        - name
        - organizationBrandMedia