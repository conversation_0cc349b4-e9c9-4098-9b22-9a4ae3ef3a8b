# Seeders Management

This document provides instructions on how to manage seeders in the project.

## Requirements

Ensure you have Node.js and npm installed in your environment. Refer to the main README.md for installation instructions.

## Running Seeders

To run all seeders, use the following command:

    $ npx sequelize-cli db:seed:all

This will execute all seed files in the `seeders` directory.

## Creating a Seeder

To create a new seeder, use the following command:

    $ npx sequelize-cli seed:generate --name <seeder_name>

Replace `<seeder_name>` with a descriptive name for your seeder. This will create a new file in the `seeders` directory.

## Example Seeder

Here is an example of a seeder file:
