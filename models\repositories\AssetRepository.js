const { Op } = require('sequelize');
const { Asset, sequelize } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.validateAndCreateAsset = async (data) => {
  const { assetType, name } = data;
  const transaction = await sequelize.transaction();
  try {
    const query = {
      where: {
        name: {
          [Op.iLike]: name,
        },
        assetType: assetType,
      },
    };

    const existingAsset = await Asset.findOne(query);
    if (existingAsset) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(`Asset with name: ${name}`),
      };
    }

    const createdAsset = await Asset.create(data, { transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Asset'),
      data: createdAsset,
    };
  } catch (error) {
    await transaction.rollback();
    if (error.name === 'SequelizeUniqueConstraintError') {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Asset'),
      };
    }
    throw new Error(error);
  }
};

exports.validateAndUpdateAsset = async (assetId, data) => {
  const { assetType, name } = data;
  const transaction = await sequelize.transaction();
  try {
    const existingAsset = await Asset.findOne({
      where: {
        id: assetId,
      },
    });

    if (!existingAsset) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Asset with Id: ${assetId}`),
      };
    }

    if (name) {
      const existingAssetWithName = await Asset.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
          assetType: assetType,
          id: {
            [Op.ne]: assetId,
          },
        },
      });

      if (existingAssetWithName) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST(`Asset with name: ${name}`),
        };
      }
    }

    const updatedAsset = await existingAsset.update(data, { transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Asset'),
      data: updatedAsset,
    };
  } catch (error) {
    await transaction.rollback();
    if (error.name === 'SequelizeUniqueConstraintError') {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Asset'),
      };
    }
    throw new Error(error);
  }
};

exports.validateAndDeleteAsset = async (assetId) => {
  const transaction = await sequelize.transaction();
  try {
    const existingAsset = await Asset.findOne({
      where: {
        id: assetId,
      },
    });

    if (!existingAsset) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Asset with Id: ${assetId}`),
      };
    }

    await existingAsset.destroy({ transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Asset'),
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};
