module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('PaymentTermTemplateDetails', 'tax', {
      type: Sequelize.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.0,
    });

    await queryInterface.addColumn('PaymentTermTemplateDetails', 'amount', {
      type: Sequelize.DECIMAL(15, 2),
      allowNull: false,
      defaultValue: 0.0,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('PaymentTermTemplateDetails', 'tax');
    await queryInterface.removeColumn('PaymentTermTemplateDetails', 'amount');
  },
};
