const {
  dependencyType,
  dependOn,
  dependencyStatus,
  defaultStatus,
} = require('@config/options');

exports.createOrUpdateWbsActivityCategory = {
  iconUrl: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'iconUrl cannot be empty',
    },
    isString: {
      errorMessage: 'iconUrl must be a string',
    },
  },
  organizationId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
    isInt: {
      errorMessage: 'organizationId must be a number',
    },
  },
  color: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'color cannot be empty',
    },
    isString: {
      errorMessage: 'color must be a string',
    },
  },
  projectId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'projectId cannot be empty',
    },
    isInt: {
      errorMessage: 'projectId must be a number',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
  },
  shortCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'shortCode cannot be empty',
    },
    isString: {
      errorMessage: 'shortCode must be a string',
    },
  },
};

exports.createOrUpdateWbsActivity = {
  parentWbsActivityId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'parent folder ID is required',
    },
    isInt: {
      errorMessage: 'parent folder ID must be number',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
  },
  wbsCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'wbsCode cannot be empty',
    },
    isString: {
      errorMessage: 'wbsCode must be a string',
    },
  },
  organizationId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
    isInt: {
      errorMessage: 'organizationId must be a number',
    },
  },
  projectId: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'projectId cannot be empty',
    },
    isInt: {
      errorMessage: 'projectId must be a number',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  startDate: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDate: {
      errorMessage: 'startDate must be a date',
    },
  },
  endDate: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isDate: {
      errorMessage: 'endDate must be a date',
    },
  },
  metricValue: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'metricValue is required',
    },
    isString: {
      errorMessage: 'metricValue must be string',
    },
  },
  unitIds: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'unitIds is required',
    },
    isArray: {
      errorMessage: 'unitIds must be an array',
    },
  },
  floorIds: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'floorIds is required',
    },
    isArray: {
      errorMessage: 'floorIds must be an array',
    },
  },
  metricType: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'metricType is required',
    },
    isString: {
      errorMessage: 'metricType must be string',
    },
  },
  rate: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'rate is required',
    },
    isDecimal: {
      errorMessage: 'rate must be a decimal number',
    },
  },
  total: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'total is required',
    },
    isDecimal: {
      errorMessage: 'total must be a decimal number',
    },
  },
  estimatedActivityBudget: {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'estimatedActivityBudget is required',
    },
    isDecimal: {
      errorMessage: 'estimatedActivityBudget must be a decimal number',
    },
  },
  accountId: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'accountId must be a number',
    },
  },
  'dependencies.*.dependencyType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [dependencyType.getValues()],
      errorMessage: 'dependencyType must be either  blocking or blockedBy',
    },
  },
  'dependencies.*.dependOn': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [dependOn.getValues()],
      errorMessage:
        'dependOn must be either value of task, request or activity',
    },
  },
  'dependencies.*.status': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [dependencyStatus.getValues()],
      errorMessage:
        'status must be one of the following values: fs, ff, ss, sf',
    },
  },
  'dependencies.*.taskId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'taskId must be a number',
    },
  },
  'dependencies.*.activityId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'activityId must be a number',
    },
  },
  'comments.*.comment': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'comment must be a string',
    },
  },
  'comments.*.userId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'userId must be a number',
    },
  },
  'documents.*.documentId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'documentId must be a number',
    },
  },
  'documents.*.documentUrl': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'documentUrl must be a string',
    },
  },
  'qualityControls.*.templateId': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'documentUrl must be a int',
    },
  },
};

exports.addWbsComment = {
  comment: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'comment cannot be empty',
    },
    isString: {
      errorMessage: 'comment must be a string',
    },
  },
};

exports.addWbsDocument = {
  documentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'documentId must be a number',
    },
  },
  documentUrl: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'documentUrl must be a string',
    },
  },
};

exports.getWbsActivity = {
  parentActivityId: {
    in: ['query'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'parentActivityId is required',
    },
    isInt: {
      errorMessage: 'parentActivityId must be an integer',
    },
  },
  projectId: {
    in: ['query'],
    notEmpty: {
      errorMessage: 'parentActivityId is required',
    },
    isInt: {
      errorMessage: 'projectId must be an integer',
    },
  },
  subProjectIds: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isArray: true,
    isInt: {
      errorMessage: 'subProjectIds must be an array of integers',
    },
  },
  unitIds: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isArray: true,
    isInt: {
      errorMessage: 'unitId must be an array of integers',
    },
  },
  start: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'offset must be an integer',
    },
    default: 0,
  },
  limit: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'limit must be an integer',
    },
    default: 10,
  },
  startDate: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isDate: {
      errorMessage: 'startDate must be a date',
    },
  },
  endDate: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isDate: {
      errorMessage: 'endDate must be a date',
    },
  },
  search: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'search must be a string',
    },
  },
  status: {
    in: ['query'],
    optional: { options: { nullable: true } },
    isArray: true,
    isIn: {
      options: [defaultStatus.getDefaultStatusArray()],
      errorMessage: 'Invalid contractorType',
    },
  },
};
