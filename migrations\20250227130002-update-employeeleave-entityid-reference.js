'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.changeColumn('EmployeeLeave', 'entityId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'EmployeeTemplateItem',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.changeColumn('EmployeeLeave', 'entityId', {
      type: Sequelize.INTEGER,
      references: {
        model: 'TemplateEntity',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  },
};
