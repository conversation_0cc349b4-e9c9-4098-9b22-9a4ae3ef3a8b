module.exports = (sequelize, DataTypes) => {
  const TaskTagMapping = sequelize.define(
    'TaskTagMapping',
    {
      taskId: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        references: {
          model: 'Tasks',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      tagId: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        references: {
          model: 'TaskTags',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
    },
    {
      timestamps: false,
      tableName: 'TaskTagMapping',
      freezeTableName: true,
    }
  );

  TaskTagMapping.associate = (models) => {
    TaskTagMapping.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      onDelete: 'CASCADE',
    });
    TaskTagMapping.belongsTo(models.TaskTags, {
      foreignKey: 'tagId',
      onDelete: 'CASCADE',
    });
  };

  return TaskTagMapping;
};
