module.exports = (sequelize, DataTypes) => {
  const TaskComments = sequelize.define(
    'TaskComments',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      comment: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TaskComments.associate = (models) => {
    TaskComments.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'task',
    });

    TaskComments.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return TaskComments;
};
