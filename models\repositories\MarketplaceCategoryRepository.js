const { Op } = require('sequelize');
const { MarketplaceCategory, sequelize } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.validateAndCreateCategory = async (data, loggedInUser) => {
  const { name, parentId } = data;
  const transaction = await sequelize.transaction();
  try {
    const existingCategory = await MarketplaceCategory.findOne({
      where: {
        name: {
          [Op.iLike]: name,
        },
      },
    });

    if (existingCategory) {
      throw new Error(
        errorMessage.ALREADY_EXIST(
          `Marketplace Category with name: ${name} already exists`
        )
      );
    }

    if (parentId) {
      const parentCategory = await MarketplaceCategory.findOne({
        where: {
          id: parentId,
        },
      });

      if (!parentCategory) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Category with ID: ${parentId} does not exist`
          )
        );
      }
    }

    const marketplaceCategory = await MarketplaceCategory.create(
      {
        ...data,
        userId: loggedInUser.id,
      },
      { transaction }
    );

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Marketplace Category'),
      data: marketplaceCategory,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while creating the marketplace category',
    };
  }
};

exports.validateAndUpdateCategory = async (categoryId, data) => {
  const transaction = await sequelize.transaction();
  const { name, parentId } = data;
  try {
    const marketplaceCategory = await MarketplaceCategory.findOne({
      where: {
        id: categoryId,
      },
    });

    if (!marketplaceCategory) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Marketplace Category'));
    }

    if (parentId && parentId === Number(categoryId)) {
      throw new Error(
        errorMessage.INVALID_INPUT(
          `Parent ID cannot be the same as Category ID`
        )
      );
    }

    if (name) {
      const existingCategory = await MarketplaceCategory.findOne({
        where: {
          name: {
            [Op.iLike]: name,
          },
          id: {
            [Op.ne]: categoryId,
          },
        },
      });

      if (existingCategory) {
        throw new Error(
          errorMessage.ALREADY_EXIST(
            `Marketplace Category with name: ${name} already exists`
          )
        );
      }
    }

    if (parentId) {
      const parentCategory = await MarketplaceCategory.findOne({
        where: {
          id: parentId,
        },
      });

      if (!parentCategory) {
        throw new Error(
          errorMessage.DOES_NOT_EXIST(
            `Parent Category with ID: ${parentId} does not exist`
          )
        );
      }
    }

    await marketplaceCategory.update(data, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Marketplace Category'),
      data: marketplaceCategory,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating the marketplace category',
    };
  }
};

exports.validateAndDeleteCategory = async (categoryId) => {
  const transaction = await sequelize.transaction();
  try {
    const marketplaceCategory = await MarketplaceCategory.findOne({
      where: {
        id: categoryId,
      },
    });

    if (!marketplaceCategory) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Marketplace Category'));
    }

    await marketplaceCategory.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Marketplace Category'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while deleting the marketplace category',
    };
  }
};
