'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('Organization', 'type', {
      type: Sequelize.ENUM(
        'private_limited',
        'limited',
        'llp',
        'partnership_firm',
        'sole_proprietorship'
      ),
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'mobile', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'country', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'about', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'website', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'panNumber', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'tanNumber', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'dateFormat', {
      type: Sequelize.STRING,
      defaultValue: 'ddmmyy',
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'measurementUnits', {
      type: Sequelize.STRING,
      defaultValue: 'Metric',
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'timeFormat', {
      type: Sequelize.ENUM('12_hours', '24_hours'),
      defaultValue: '12_hours',
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'timeZone', {
      type: Sequelize.STRING,
      defaultValue: '(GMT 5:30) India Standard Time (Asia/Kolkata)',
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'numberFormat', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Organization', 'currency', {
      type: Sequelize.STRING,
      defaultValue: 'INR (₹)',
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Organization', 'type');
    await queryInterface.removeColumn('Organization', 'mobile');
    await queryInterface.removeColumn('Organization', 'country');
    await queryInterface.removeColumn('Organization', 'about');
    await queryInterface.removeColumn('Organization', 'website');
    await queryInterface.removeColumn('Organization', 'panNumber');
    await queryInterface.removeColumn('Organization', 'tanNumber');
    await queryInterface.removeColumn('Organization', 'dateFormat');
    await queryInterface.removeColumn('Organization', 'measurementUnits');
    await queryInterface.removeColumn('Organization', 'timeFormat');
    await queryInterface.removeColumn('Organization', 'timeZone');
    await queryInterface.removeColumn('Organization', 'numberFormat');
    await queryInterface.removeColumn('Organization', 'currency');
  },
};
