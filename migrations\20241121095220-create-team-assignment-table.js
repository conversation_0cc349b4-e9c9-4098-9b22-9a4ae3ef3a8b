'use strict';
const { priority } = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('TeamAssignment', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      priority: {
        type: Sequelize.ENUM(...priority.getPriorityArray()),
        allowNull: true,
      },
      autoAssign: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      notes: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      customerId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Customer',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      requirementId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Requirement',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      memberId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Employee',
          key: 'id',
        },
        onDelete: 'SET NULL',
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('TeamAssignment');
  },
};
