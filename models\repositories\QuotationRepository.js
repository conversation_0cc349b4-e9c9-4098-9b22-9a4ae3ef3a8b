const sequelize = require('sequelize');
const { Op } = sequelize;
const {
  Quotation,
  QuotationPaymentPlan,
  QuotationUnitCost,
  User,
  ProjectBookedUnit,
  ApprovalWorkflow,
  Project,
  Request,
} = require('..');
const {
  successMessage,
  defaultStatus,
  errorMessage,
  usersRoles,
  paymentPlanType,
  quotationStatus,
} = require('@config/options');
const UnitRepository = require('./UnitRepository');
const CustomerRepository = require('./CustomerRepository');
const { sequelize: sequelizeTransaction } = require('../../models/index');

exports.findOne = async (query) => await Quotation.findOne(query);

exports.findAll = async (query) => await Quotation.findAll(query);

exports.createQuotation = async (data, loggedInUser) => {
  const query = {
    where: {
      id: data.customerId,
    },
    include: [
      {
        model: User,
        as: 'user',
        where: {
          status: { [Op.notIn]: [defaultStatus.DELETED] },
          role: [usersRoles.CUSTOMER],
        },
      },
    ],
  };
  const checkUser = await CustomerRepository.findOne(query);

  if (!checkUser) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Customer'),
    };
  }

  const existingUnit = await UnitRepository.findOne(
    {
      where: { id: data.unitId },
    },
    {
      include: [
        {
          model: Project,
          as: 'project',
          where: { status: { [Op.notIn]: [defaultStatus.DELETED] } },
        },
      ],
    }
  );

  if (!existingUnit) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Unit'),
    };
  }
  const payload = {
    ...data,
    projectId: existingUnit.projectId,
    status: defaultStatus.DRAFTS,
    createdBy: loggedInUser.id,
  };

  const quotation = await Quotation.create(payload, {
    include: [
      {
        model: QuotationUnitCost,
        as: 'unitCosts',
      },
    ],
  });

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Quotation'),
    data: quotation,
  };
};

exports.updateQuotationWithPaymentUnitCost = async (
  quotationId,
  data,
  loggedInUser
) => {
  if (data.unitId) {
    const existingUnit = await UnitRepository.findOne({
      where: { id: data.unitId },
    });
    if (!existingUnit) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Unit'),
      };
    }
  }

  const existingQuotation = await Quotation.findOne({
    where: {
      id: quotationId,
      status: {
        [Op.notIn]: [quotationStatus.ARCHIVED, quotationStatus.EXPIRED],
      },
    },
  });

  if (!existingQuotation) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Unit'),
    };
  }

  const payload = {
    ...data,
    status: data.status || existingQuotation.status,
    createdBy: loggedInUser.id,
  };

  Object.assign(existingQuotation, payload);
  const quotation = await existingQuotation.save();
  if (data.unitCosts && data.unitCosts.length > 0) {
    await this.createOrUpdatePricingRevision(
      quotationId,
      data.unitCosts,
      loggedInUser
    );
  }

  if (data.holdPropertyUntilExpiry) {
    await this.createOrUpdateProjectBookedUnit(
      existingQuotation.id,
      existingQuotation.unitId,
      loggedInUser,
      defaultStatus.ON_HOLD
    );
  }

  if (data)
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Quotation'),
      data: quotation,
    };
};

exports.createOrUpdateProjectBookedUnit = async (
  quotationId,
  unitId,
  loggedInUser,
  status
) => {
  const existingProjectBookedUnit = await ProjectBookedUnit.findOne({
    where: {
      quotationId: quotationId,
      unitId: unitId,
    },
  });

  if (!existingProjectBookedUnit) {
    return await ProjectBookedUnit.create({
      quotationId: quotationId,
      unitId: unitId,
      createdBy: loggedInUser.id,
      status: status,
    });
  } else {
    existingProjectBookedUnit.status = status;
    return await existingProjectBookedUnit.save();
  }
};
exports.createOrUpdatePricingRevision = async (
  quotationId,
  unitCosts,
  loggedInUser
) => {
  const query = {
    where: {
      quotationId,
    },
  };

  const existingPricingUnitCost = await QuotationUnitCost.findAll(query);

  if (existingPricingUnitCost && existingPricingUnitCost.length > 0) {
    await QuotationUnitCost.destroy(query);
  }

  const quotationUnitCost = unitCosts.map((cost) => {
    return {
      ...cost,
      quotationId: quotationId,
    };
  });

  return await QuotationUnitCost.bulkCreate(quotationUnitCost);
};

exports.createOrUpdateQuotationPaymentPlan = async (
  quotationId,
  data,
  loggedInUser,
  planType = paymentPlanType.PAYMENT_PLAN
) => {
  const existingQuotation = await Quotation.findOne({
    where: {
      id: quotationId,
      status: { [Op.notIn]: [defaultStatus.DELETED] },
    },
  });

  if (!existingQuotation) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Unit'),
    };
  }

  Object.assign(existingQuotation, data);
  const quotation = await existingQuotation.save();
  if (data.paymentPlans && data.paymentPlans.length > 0) {
    await this.createOrUpdatePaymentPlanStage(
      quotationId,
      data.paymentPlans,
      loggedInUser,
      planType
    );
  }
  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Quotation'),
    data: quotation,
  };
};

exports.createOrUpdatePaymentPlanStage = async (
  quotationId,
  data,
  loggedInUser,
  planType
) => {
  const query = {
    where: {
      quotationId,
      planType: planType,
    },
  };

  const existingPaymentPlanStage = await QuotationPaymentPlan.findAll(query);

  if (existingPaymentPlanStage && existingPaymentPlanStage.length > 0) {
    await QuotationPaymentPlan.destroy(query);
  }

  const quotationPaymentPlan = data.map((stage) => {
    return {
      ...stage,
      quotationId: quotationId,
      planType: planType,
    };
  });

  return await QuotationPaymentPlan.bulkCreate(quotationPaymentPlan);
};

exports.bookedQuotationUnit = async (quotationId, data, loggedInUser) => {
  const existingQuotation = await Quotation.findOne({
    where: {
      id: quotationId,
      status: { [Op.notIn]: [defaultStatus.DELETED] },
    },
    include: [
      {
        model: Request,
        as: 'request',
        required: false,
        where: {
          status: { [Op.notIn]: [defaultStatus.REJECTED] },
        },
        attributes: ['id', 'status', 'requestType', 'recordId'],
      },
      {
        model: ProjectBookedUnit,
        as: 'projectBookedUnit',
        required: false,
        where: {
          status: defaultStatus.BOOKED,
        },
        attributes: ['id', 'quotationId', 'status', 'unitId'],
      },
    ],
  });

  const { success, message } = await this.checkRequestIsExistForQuotation(
    existingQuotation.request,
    loggedInUser
  );

  if (!success) {
    return {
      success: false,
      message,
    };
  }

  if (!existingQuotation) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('Quotation'),
    };
  }

  if (
    !existingQuotation ||
    new Date(existingQuotation.expireDate) < new Date()
  ) {
    return {
      success: false,
      message: errorMessage.EXPIRE_DETAILS('Quotation'),
    };
  }

  if (
    !existingQuotation ||
    new Date(existingQuotation.expireDate) < new Date()
  ) {
    return {
      success: false,
      message: errorMessage.EXPIRE_DETAILS('Quotation'),
    };
  }

  if (
    existingQuotation.projectBookedUnit &&
    existingQuotation.projectBookedUnit.length > 0
  ) {
    if (
      !(existingQuotation.projectBookedUnit?.status === defaultStatus.ON_HOLD)
    ) {
      return {
        success: false,
        message: errorMessage.ALREADY_BOOKED('Unit'),
      };
    }
  }

  const bookedUnit = await this.createOrUpdateProjectBookedUnit(
    existingQuotation.id,
    existingQuotation.unitId,
    loggedInUser,
    defaultStatus.BOOKED
  );

  existingQuotation.status = quotationStatus.BOOKED;
  const quotation = await existingQuotation.save();

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Quotation Booked For Unit'),
    data: {
      quotation,
      bookedUnit,
    },
  };
};

exports.checkRequestIsExistForQuotation = async (
  quotationRequest,
  loggedInUser
) => {
  if (quotationRequest && quotationRequest.length > 1) {
    return {
      success: false,
      message: errorMessage.INCORRECT_DATA('Request'),
    };
  }

  if (quotationRequest && quotationRequest.status !== defaultStatus.APPROVED) {
    return {
      success: false,
      message: errorMessage.REQUIRED('Request approval'),
    };
  }

  const existingApprovalWorkflow = await ApprovalWorkflow.findOne({
    where: {
      moduleName: ['CRM'],
      activityName: ['Quotations'],
      organizationId: loggedInUser.currentOrganizationId,
    },
  });

  if (
    existingApprovalWorkflow &&
    (existingApprovalWorkflow.approverUserId ||
      existingApprovalWorkflow.approverMemberId ||
      existingApprovalWorkflow.escalationUserId ||
      existingApprovalWorkflow.escalationDesignationId ||
      existingApprovalWorkflow.approverDesignationId)
  ) {
    return {
      success: false,
      message: errorMessage.REQUIRED('Request of Approval'),
    };
  }

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Request'),
  };
};

exports.checkAndMarkQuotationExpire = async () => {
  try {
    const transaction = await sequelizeTransaction.transaction();
    const query = {
      where: {
        status: { [Op.not]: [quotationStatus.BOOKED, quotationStatus.EXPIRED] },
        expireDate: { [Op.lt]: new Date() },
      },
      include: [
        {
          model: ProjectBookedUnit,
          as: 'projectBookedUnit',
          required: false,
          where: {
            status: { [Op.in]: [defaultStatus.ON_HOLD] },
          },
          attributes: ['id', 'quotationId', 'status', 'unitId'],
        },
      ],
    };

    const unExpireQuotations = await Quotation.findAll(query, { transaction });
    if (unExpireQuotations.length === 0) {
      await transaction.commit();
      return { success: true, message: 'No expired quotations found.' };
    }

    const quotationIds = unExpireQuotations.map((q) => q.id);
    const bookedUnitIds = unExpireQuotations
      .flatMap((q) => q.projectBookedUnit)
      .map((unit) => unit.id);

    await Quotation.update(
      { status: quotationStatus.EXPIRED },
      { where: { id: { [Op.in]: quotationIds } }, transaction }
    );

    if (bookedUnitIds.length > 0) {
      await ProjectBookedUnit.destroy({
        where: { id: { [Op.in]: bookedUnitIds } },
        transaction,
      });
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Quotation'),
      data: {
        updatedQuotationIds: quotationIds,
        deletedProjectBookedIds: bookedUnitIds,
      },
    };
  } catch (error) {
    await transaction.rollback();
    console.error(error);
    throw new Error(error);
  }
};
