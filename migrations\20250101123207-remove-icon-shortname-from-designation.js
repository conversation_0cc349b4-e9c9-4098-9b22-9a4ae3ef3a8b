'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Designation', 'icon');
    await queryInterface.removeColumn('Designation', 'shortName');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('Designation', 'icon', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Designation', 'shortName', {
      type: Sequelize.STRING(50),
      allowNull: true,
    });
  },
};
