module.exports = (sequelize, DataTypes) => {
  const OrganizationBrand = sequelize.define(
    'OrganizationBrand',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
    },
    {
      tableName: 'OrganizationBrand',
      timestamps: true,
    }
  );

  OrganizationBrand.associate = function (models) {
    this.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    OrganizationBrand.hasMany(models.OrganizationBrandMedia, {
      foreignKey: 'id',
      as: 'organizationBrandMedia',
    });
  };

  return OrganizationBrand;
};
