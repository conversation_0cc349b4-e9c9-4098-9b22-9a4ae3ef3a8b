'use strict';

const { taskPriority } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Tasks', 'priority');
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Tasks', 'priority', {
      type: Sequelize.ENUM(taskPriority.getValues()),
      allowNull: false,
      defaultValue: taskPriority.MEDIUM,
    });
  },
};
