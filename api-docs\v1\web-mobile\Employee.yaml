paths:
  /employee:
    post:
      summary: "Creates a new employee - mobile & web"
      description: Registers a new employee within the organization. Requires the employee's personal and professional details in the request body.
      operationId: createEmployee
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
      tags:
        - Employee
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAnUpdateEmployee"
      responses:
        "201":
          description: Employee created successfully
        "400":
          description: User exist or Invalid request
        "500":
          description: "Internal Server Error"
  /employee/{id}:
    put:
      summary: "Update employee details - mobile & web"
      description: Updates an existing employee's personal and professional details within the organization.
      operationId: updateEmployee
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: The ID of the employee to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAnUpdateEmployee"
      responses:
        "200":
          description: Employee details updated successfully
        "400":
          description: Employee not found or invalid request
        "500":
          description: "Internal Server Error"
  /employee/{id}/status:
    patch:
      summary: "Update employee status - mobile & web"
      description: Updates the status of an existing employee within the organization.
      operationId: updateEmployeeStatus
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: The ID of the employee to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateEmployeeStatus"
      responses:
        "200":
          description: Employee status updated successfully
        "400":
          description: Employee not found or invalid request
        "500":
          description: "Internal Server Error"
  /employee/{id}/salary:
    post:
      summary: "Add Employee Salary Payroll - mobile & web"
      operationId: addEmployeeSalaryPayroll
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: The ID of the employee
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAndUpdateSalaryPayroll"
      responses:
        "200":
          description: Employee salary payroll added successfully
        "400":
          description: "The Employee does not exist or Employee Salary Entity does not exist"
        "500":
          description: Internal Server Error
    put:
      summary: Update Employee Salary Payroll
      operationId: updateEmployeeSalaryPayroll
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the employee
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createAndUpdateSalaryPayroll"
      responses:
        "200":
          description: Employee salary payroll updated successfully
        "400":
          description: "The Employee does not exist or Employee Salary Entity does not exist"
        "500":
          description: Internal Server Error
    get:
      summary: Get Employee Salary
      operationId: getEmployeeSalary
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the employee
          schema:
            type: integer
      responses:
        "200":
          description: Employee salary retrieved successfully
        "400":
          description: "The Employee does not exist"
        "500":
          description: Internal Server Error
  /employee/{id}/leave:
    post:
      summary: Add an Employee Leave Policy
      operationId: addEmployeeLeavePolicy
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the employee Id
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createLeavePolicy"
      responses:
        "200":
          description: Employee leave policy added successfully
        "400":
          description: "The Employee does not exist"
        "500":
          description: Internal Server Error
  /employee/{id}/attendance:
    post:
      summary: Add an Employee Attendance
      operationId: addEmployeeAttendance
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the employee
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createEmployeeAttendance"
      responses:
        "200":
          description: Employee attendance added successfully
        "400":
          description: "The Employee does not exist"
        "500":
          description: Internal Server Error
  /employee/{id}/bank-details:
    get:
      summary: "Get Employee Bank Details - mobile & web"
      operationId: getEmployeeBankDetails
      tags:
        - Employee
      security:
        - bearerAuth: []
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
        - name: id
          in: path
          required: true
          description: The ID of the employee
          schema:
            type: integer
      responses:
        "200":
          description: Employee bank details fetched successfully
        "400":
          description: "The Employee does not exist"
        "500":
          description: Internal Server Error

components:
  schemas:
    createAnUpdateEmployee:
      type: object
      properties:
        email:
          type: string
          example: "<EMAIL>"
          description: "The email of the employee."
        gender:
          type: string
          enum: ["male", "female", "other"]
          description: "The gender of the employee."
        profilePicture:
          type: string
          example: "path/to/profile-picture.jpg"
          description: "The profilePicture of the employee."
        firstName:
          type: string
          example: "John"
          description: "The first name of the employee."
        middleName:
          type: string
          example: "A."
          description: "The middle name of the employee."
        lastName:
          type: string
          example: "Doe"
          description: "The last name of the employee."
        countryCode:
          type: string
          example: "91"
          description: "The country code of the employee's mobile number."
        mobileNumber:
          type: string
          example: "9876543210"
          description: "The mobile number of the employee."
        personalEmail:
          type: string
          example: "<EMAIL>"
          description: "The personal email of the employee."
        locationId:
          type: string
          example: "1234"
          description: "The location ID where the employee is based."
        designationId:
          type: string
          example: "5678"
          description: "The designation ID of the employee."
        alternateCountryCode:
          type: string
          example: "1"
          description: "The alternate country code for the employee's mobile number."
        alternateMobileNumber:
          type: string
          example: "9876543210"
          description: "The alternate mobile number of the employee."
        dateOfBirth:
          type: string
          format: date
          example: "1990-01-01"
          description: "The date of birth of the employee."
        employeeCode:
          type: string
          example: "E123"
          description: "The employee code."
        maritalStatus:
          type: string
          enum: ["single", "married", "divorced", "widowed"]
          description: "The marital status of the employee."
        dateOfJoining:
          type: string
          format: date
          example: "2023-01-01"
          description: "The date when the employee joined the organization."
        reportedTo:
          type: integer
          example: 1
          description: "The ID of the employee's manager."
        addressDetails:
          type: object
          properties:
            address:
              type: string
              example: "123 Street, City"
              description: "The address of the employee."
            city:
              type: string
              example: "City Name"
              description: "The city where the employee resides."
            pincode:
              type: string
              example: "123456"
              description: "The postal code."
            state:
              type: string
              example: "State Name"
              description: "The state where the employee resides."
        employeeDocument:
          type: array
          items:
            type: object
            properties:
              id:
                type: integer
                example: 1
                description: "The ID of the employee document."
              cardNumber:
                type: string
                example: "ABC123456"
                description: "The document card number."
              name:
                type: string
                example: "Identity Card"
                description: "The name of the document."
              description:
                type: string
                example: "This document is an identity proof."
                description: "A brief description of the document."
              documentPath:
                type: string
                example: "/path/to/document"
                description: "The path where the document is stored."
        bankDetails:
          type: object
          properties:
            id:
              type: string
              example: "1"
              description: "The ID of the bank details record."
            accountName:
              type: string
              example: "John Doe"
              description: "The name of the account holder."
            accountNumber:
              type: string
              example: "**********"
              description: "The account number of the employee."
            bankName:
              type: string
              example: "Bank of America"
              description: "The name of the bank."
            ifscCode:
              type: string
              example: "BOFAUS3N"
              description: "The IFSC code of the bank branch."
    createLeavePolicy:
      type: object
      properties:
        employeeLeave:
          type: array
          items:
            type: object
            properties:
              entityId:
                type: integer
                example: 1
              maxAllowed:
                type: integer
                example: 4
        employeeAdditionalLeave:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                example: "Sick Leave"
              maxAllowed:
                type: integer
                example: 4
        attendance:
          type: object
          properties:
            entityId:
              type: integer
              example: 1
            calculationType:
              type: string
              enum: 
                - "attendance_with_location"
                - "attendance_without_location"
                - "attendance_mark_manually"
                - "attendance_optional"
              example: "attendance_with_location"
        unpaidLeave: 
          type: object
          properties:
            entityId:
              type: integer
              example: 1
            calculationType:
              type: string
              enum: 
                - "unpaid_deduction_prorated"
                - "unpaid_deduction_fixed"
                - "unpaid_deduction_manual"
              example: "unpaid_deduction_prorated"
            calculation:
              type: number
              format: float
              example: 1000.50
        templateId:
          type: integer
          example: 1
      required:
        - employeeLeave
        - templateId
        - attendance
        - unpaidLeave
    createAndUpdateSalaryPayroll:
      type: object
      properties:
        employeeSalary:
          type: array
          items:
            type: object
            properties:
              monthlyAmount:
                type: number
                format: float
                description: The monthly amount for the salary component
                example: 5000.00
              annualAmount:
                type: number
                format: float
                description: The annual amount for the salary component
                example: 60000.00
              entityId:
                type: integer
                description: The entity ID associated with the salary entity
                example: 1
              calculation:
                type: number
                format: float
                description: The calculation value for the salary component
                example: 1000.50
              calculationType:
                type: string
                description: The type of calculation (fixed or percentage)
                example: "fixed"
        templateId:
          type: integer
          description: The ID of the leave policy template
      required:
        - employeeSalary
        - templateId
    createEmployeeAttendance:
      type: object
      properties:
        date:
          type: string
          format: date
          example: "2024-12-01"
        inTime:
          type: string
          format: time
          example: "09:00:00"
        outTime:
          type: string
          format: time
          example: "19:00:00"
        status:
          type: string
          example: "full_day"
        description:
          type: string
          example: "Clock-In"
    updateEmployeeStatus:
      type: object
      properties:
        status:
          type: string
          example: "archived"
          description: "The status of the employee."