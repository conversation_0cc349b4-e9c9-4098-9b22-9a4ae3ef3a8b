'use strict';

const { recordStatus, expenseState } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */

    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.createTable(
        'Expense',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
          },
          organizationId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'Organization',
              key: 'id',
            },
          },
          date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          expenseAccountId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'AccountData',
              key: 'id',
            },
          },
          paidThroughAccountId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'AccountData',
              key: 'id',
            },
          },
          amount: {
            type: Sequelize.DECIMAL(20, 2),
            allowNull: false,
          },
          vendorId: {
            type: Sequelize.INTEGER,
            allowNull: true,
            defaultValue: null,
          },
          invoiceNumber: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: true,
          },
          notes: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: true,
          },
          expenseState: {
            type: Sequelize.ENUM(expenseState.getValues()),
            defaultValue: expenseState.APPROVED,
            allowNull: false,
          },
          status: {
            type: Sequelize.ENUM(recordStatus.getValues()),
            defaultValue: recordStatus.ACTIVE,
            allowNull: false,
          },
          createdBy: {
            type: Sequelize.INTEGER,
            allowNull: true,
            references: {
              model: 'User',
              key: 'id',
            },
            defaultValue: null,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        { transaction: t }
      );
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.dropTable('Expense', { transaction: t });
    });
  },
};
