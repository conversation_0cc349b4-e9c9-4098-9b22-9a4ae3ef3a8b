'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'ActivityLog',
      'ActivityLog_employeeId_fkey'
    );
    await queryInterface.removeConstraint(
      'ActivityLog',
      'ActivityLog_organizationId_fkey'
    );
    await queryInterface.removeConstraint(
      'ActivityLog',
      'ActivityLog_taskId_fkey'
    );
    await queryInterface.removeConstraint(
      'ActivityLog',
      'ActivityLog_createdBy_fkey'
    );

    await queryInterface.removeColumn('ActivityLog', 'employeeId');
    await queryInterface.removeColumn('ActivityLog', 'organizationId');
    await queryInterface.removeColumn('ActivityLog', 'taskId');

    await queryInterface.changeColumn('ActivityLog', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
    });

    await queryInterface.addIndex('ActivityLog', ['activityOn', 'recordId'], {
      name: 'activity_polymorphic_index',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeIndex(
      'ActivityLog',
      'activity_polymorphic_index'
    );

    await queryInterface.addColumn('ActivityLog', 'employeeId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Employee',
        key: 'id',
      },
    });

    await queryInterface.addColumn('ActivityLog', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
    });

    await queryInterface.addColumn('ActivityLog', 'taskId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Tasks',
        key: 'id',
      },
    });

    await queryInterface.changeColumn('ActivityLog', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Employee',
        key: 'id',
      },
    });
  },
};
