const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Employee = sequelize.define(
    'Employee',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      employeeCode: {
        type: DataTypes.TEXT,
        allowNull: false,
        unique: true,
      },
      dateOfJoining: {
        type: DataTypes.DATEONLY,
        allowNull: true,
      },
      maritalStatus: {
        type: DataTypes.ENUM(OPTIONS.maritalStatus.getMaritalStatusArray()),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Employee.associate = (models) => {
    Employee.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      allowNull: true,
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });

    Employee.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      allowNull: true,
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Employee.belongsTo(models.User, {
      foreignKey: 'reportedTo',
      as: 'reportingMember',
      allowNull: true,
      onUpdate: 'NO ACTION',
      onDelete: 'CASCADE',
    });

    // Employee.belongsTo(models.User, {
    //   foreignKey: 'userId',
    //   as: 'user',
    //   onUpdate: 'CASCADE',
    //   onDelete: 'SET NULL',
    // });

    // Employee.belongsTo(models.User, {
    //   foreignKey: 'reportedTo',
    //   as: 'reportingMember',
    //   onUpdate: 'CASCADE',
    //   onDelete: 'SET NULL',
    // });
  };

  return Employee;
};
