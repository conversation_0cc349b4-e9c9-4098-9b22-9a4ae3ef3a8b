const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const CompanyController = require('@controllers/v1/settings/Company');
const CompanySchema = require('@schema-validation/settings/Company');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(CompanySchema.createOrganization),
  ErrorHandleHelper.requestValidator,
  CompanyController.createCompany
);

router.get(
  '/:id/about',
  checkSchema(CompanySchema.getOrganizationDetails),
  ErrorHandleHelper.requestValidator,
  CompanyController.getOrganizationDetails
);

router.patch(
  '/:id/update-basic-info',
  checkSchema(CompanySchema.updateBasicInfo),
  ErrorHandleHelper.requestValidator,
  CompanyController.updateOrganization
);

router.patch(
  '/:id/update-localisation',
  checkSchema(CompanySchema.updateLocalisation),
  ErrorHandleHelper.requestValidator,
  CompanyController.updateOrganization
);

router.get(
  '/:id/bank-details',
  checkSchema(CompanySchema.getBankDetails),
  ErrorHandleHelper.requestValidator,
  CompanyController.getBankDetails
);

router.post(
  '/bank-detail',
  checkSchema(CompanySchema.createBankDetails),
  ErrorHandleHelper.requestValidator,
  CompanyController.createCompanyBankDetails
);

router.delete(
  '/:id/bank-detail',
  checkSchema(CompanySchema.deleteBankDetails),
  ErrorHandleHelper.requestValidator,
  CompanyController.deleteCompanyBankDetail
);

router.post(
  '/:id/tax',
  checkSchema(CompanySchema.createCompanyTax),
  ErrorHandleHelper.requestValidator,
  CompanyController.createCompanyTax
);

router.patch(
  '/tax/:id',
  checkSchema(CompanySchema.updateCompanyTax),
  ErrorHandleHelper.requestValidator,
  CompanyController.updateCompanyTax
);

router.delete(
  '/tax/:id',
  checkSchema(CompanySchema.deleteCompanyTax),
  ErrorHandleHelper.requestValidator,
  CompanyController.deleteCompanyTax
);

module.exports = router;
