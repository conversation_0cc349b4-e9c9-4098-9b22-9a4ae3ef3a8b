const {
  calculationType,
  templateEntityType,
  durationType,
} = require('@config/options');

exports.getSalaryTemplate = {
  organizationId: {
    in: ['query'],
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
    isInt: {
      errorMessage: 'organizationId must be int',
    },
  },
};
exports.createOrUpdateSalaryTemplate = {
  name: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  organizationId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
    isInt: {
      errorMessage: 'organizationId must be int',
    },
  },
  'templateEntities.*.name': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'templateEntities.*.calculation': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'calculation cannot be empty',
    },
    isDecimal: {
      errorMessage: 'calculation must be a decimal number',
    },
  },
  'templateEntities.*.monthlyAmount': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'monthlyAmount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'monthlyAmount must be a decimal number',
    },
  },
  'templateEntities.*.annualAmount': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'annualAmount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'annualAmount must be a decimal number',
    },
  },
  'templateEntities.*.entityType': {
    in: ['body'],
    isIn: {
      options: [templateEntityType.getTemplateEntityTypeArray()],
      errorMessage: 'entityType must enum',
    },
    isString: {
      errorMessage: 'entityType must be a entityType',
    },
  },
  'templateEntities.*.calculationType': {
    in: ['body'],
    isIn: {
      options: [calculationType.getCalculationTypeArray()],
      errorMessage: 'calculationType must enum',
    },
    isString: {
      errorMessage: 'calculationType must be a calculationType',
    },
  },
  'templateEntities.*.durationType': {
    in: ['body'],
    isIn: {
      options: [durationType.getDurationTypeArray()],
      errorMessage: 'durationType must enum',
    },
    isString: {
      errorMessage: 'durationType must be a string',
    },
  },
};

exports.createOrUpdateLeaveTemplate = {
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  organizationId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'organizationId cannot be empty',
    },
    isInt: {
      errorMessage: 'organizationId must be int',
    },
  },
  name: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'templateEntities.*.name': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'templateEntities.*.maxAllowed': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'maxAllowed cannot be empty',
    },
    isInt: {
      errorMessage: 'maxAllowed must be int',
    },
  },
};
