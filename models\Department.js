module.exports = (sequelize, DataTypes) => {
  const Department = sequelize.define(
    'Department',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: true,
        set(value) {
          if (value !== null && value !== undefined) {
            this.setDataValue('name', value.toLowerCase());
          } else {
            this.setDataValue('name', value);
          }
        },
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Department.associate = (models) => {
    Department.hasMany(models.Designation, {
      foreignKey: 'departmentId',
      as: 'designations',
    });

    Department.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });

    Department.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  };
  return Department;
};
