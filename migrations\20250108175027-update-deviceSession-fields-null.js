'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('DeviceSession', 'deviceName', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.changeColumn('DeviceSession', 'ipAddress', {
      type: Sequelize.STRING(45),
      allowNull: true,
    });

    await queryInterface.changeColumn('DeviceSession', 'sessionToken', {
      type: Sequelize.TEXT,
      allowNull: true,
      unique: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.changeColumn('DeviceSession', 'deviceName', {
      type: Sequelize.STRING(255),
      allowNull: false,
    });

    await queryInterface.changeColumn('DeviceSession', 'ipAddress', {
      type: Sequelize.STRING(45),
      allowNull: false,
    });

    await queryInterface.changeColumn('DeviceSession', 'sessionToken', {
      type: Sequelize.TEXT,
      allowNull: false,
      unique: true,
    });
  },
};
