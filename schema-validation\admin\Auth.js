exports.passwordLogin = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Field cannot be empty',
    isString: {
      errorMessage: 'Field must be string',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
    isString: {
      errorMessage: 'Password must be string',
    },
  },
};

exports.resetPasswordRequest = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      errorMessage: 'Invalid email format',
    },
  },
};

exports.verifyOtpRequest = {
  tempOtp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'OTP cannot be empty',
    isString: {
      errorMessage: 'OTP must be a string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'OTP must be 6 digits',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be a string',
    },
    isEmail: {
      errorMessage: 'Invalid email format',
    },
  },
};

exports.resetPassword = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be a string',
    },
    isEmail: {
      errorMessage: 'Invalid email format',
    },
  },
  newPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'New password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Confirm password cannot be empty',
    custom: {
      options: (value, { req }) => {
        if (value !== req.body.newPassword) {
          throw new Error('Confirm password must match new password');
        }
        return true;
      },
    },
  },
  tempOtp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'OTP cannot be empty',
    isString: {
      errorMessage: 'OTP must be a string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'OTP must be 6 digits',
    },
  },
};
