const { Op } = require('sequelize');
const { Department, Designation, TaskTags } = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');
const { taskTagType } = require('@config/options');

exports.getDepartment = async (query) => await Department.findOne(query);

exports.checkExistDepartmentName = async (name) => {
  try {
    const existingDepartment = await Department.findOne({
      where: { name },
      attributes: ['id', 'name'],
    });

    return {
      success: !existingDepartment,
      message: existingDepartment
        ? errorMessage.ALREADY_EXIST('department')
        : null,
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.checkDuplicate = async (body, id) => {
  try {
    return await this.getDepartment({
      where: {
        name: { [Op.iLike]: `${body.name}%` },
        ...(id && { id: { [Op.not]: id } }),
      },
    });
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndCreateDepartment = async (body) => {
  try {
    const existingDepartmentCheck = await this.checkDuplicate(body);
    if (existingDepartmentCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Department with same name'),
      };
    }

    const department = await Department.create(body);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('department'),
      data: department,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getDepartments = async ({ start, limit, search }) => {
  try {
    const whereClause = search
      ? {
          [Op.or]: [{ name: { [Op.iLike]: `%${search}%` } }],
        }
      : {};

    const { count, rows } = await Department.findAndCountAll({
      where: whereClause,
      offset: start,
      limit: limit,
      attributes: ['id', 'name', 'createdAt', 'updatedAt'],
      include: [
        {
          model: Designation,
          as: 'designations',
          required: false,
          attributes: ['id', 'name'],
        },
      ],
      order: [['createdAt', 'DESC']],
    });

    return {
      message: successMessage.DETAIL_MESSAGE('Department'),
      data: {
        rows,
        pagination: {
          totalCount: count,
          start,
          limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndUpdateDepartment = async (id, body) => {
  try {
    const existingDepartment = await this.getDepartment({ where: { id } });

    if (!existingDepartment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Department'),
      };
    }

    const duplicateNameCheck = await this.checkDuplicate(body, id);

    if (duplicateNameCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('Department with this name'),
      };
    }

    existingDepartment.name = body.name;
    existingDepartment.description = body.description;
    await existingDepartment.save();

    return {
      success: true,
      data: existingDepartment,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Department'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.userCheckAndCreateDepartment = async (data, loggedInUser) => {
  const { name } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  try {
    const existingDepartmentCheck = await checkExistence(Department, {
      name: { [Op.iLike]: name },
      organizationId,
    });

    if (existingDepartmentCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(
          'Department with same name in this organization'
        ),
      };
    }

    const department = await Department.create({
      ...data,
      organizationId,
      createdBy: loggedInUser.id,
    });

    await TaskTags.create({
      name: department.name,
      type: taskTagType.DEPARTMENT_TAG,
      organizationId: department.organizationId,
    });

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('department'),
      data: department,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.userCheckAndUpdateDepartment = async (
  departmentId,
  data,
  loggedInUser
) => {
  const organizationId = loggedInUser.currentOrganizationId;
  try {
    const existingDepartment = await checkExistence(Department, {
      id: departmentId,
    });
    if (!existingDepartment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `The Department with id: ${departmentId}`
        ),
      };
    }

    const duplicateNameCheck = await checkExistence(Department, {
      name: { [Op.iLike]: data.name },
      organizationId,
      id: { [Op.not]: departmentId },
    });
    if (duplicateNameCheck) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST(
          `Department with this name: ${data.name}`
        ),
      };
    }

    existingDepartment.name = data.name;
    existingDepartment.description = data.description;
    await existingDepartment.save();

    return {
      success: true,
      data: existingDepartment,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Department'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.userCheckAndDeleteDepartment = async (departmentId) => {
  try {
    const existingDepartment = await checkExistence(Department, {
      id: departmentId,
    });
    if (!existingDepartment) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `The Department with id: ${departmentId}`
        ),
      };
    }

    await TaskTags.destroy({
      where: {
        name: existingDepartment.name,
        organizationId: existingDepartment.organizationId,
        type: taskTagType.DEPARTMENT_TAG,
      },
    });

    await existingDepartment.destroy();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Department'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
