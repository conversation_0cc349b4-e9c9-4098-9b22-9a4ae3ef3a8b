'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const table = await queryInterface.describeTable('Floor');

    if (table.floorType) {
      await queryInterface.removeColumn('Floor', 'floorType');
      await queryInterface.sequelize.query(
        'DROP TYPE IF EXISTS "enum_Floor_floorType"'
      );
    }

    await queryInterface.addColumn('Floor', 'floorType', {
      type: Sequelize.ENUM('project', 'unit'),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    const table = await queryInterface.describeTable('Floor');

    if (table.floorType) {
      await queryInterface.removeColumn('Floor', 'floorType');
      await queryInterface.sequelize.query(
        'DROP TYPE IF EXISTS "enum_Floor_floorType"'
      );
    }

    await queryInterface.addColumn('Floor', 'floorType', {
      type: Sequelize.ENUM('Project', 'Unit'),
      allowNull: true,
    });
  },
};
