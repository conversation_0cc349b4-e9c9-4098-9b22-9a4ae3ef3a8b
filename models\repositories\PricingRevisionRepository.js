const { PricingRevision, PricingCharge } = require('..');
const { successMessage } = require('../../config/options');
const ProjectRepository = require('./ProjectRepository');
exports.findOne = async (query) => await PricingRevision.findOne(query);
exports.findAndCountAll = async (query = {}) => {
  return await PricingRevision.findAndCountAll(query);
};
exports.findAll = async (query = {}) => {
  return await PricingRevision.findAll(query);
};

exports.bulkCreatePricingCharge = async (data) =>
  await PricingCharge.bulkCreate(data);

exports.createPaymentRevision = async (projectId, data, loggedInUser) => {
  const { success, message } =
    await ProjectRepository.checkAndGetProject(projectId);

  if (!success) {
    return {
      success: false,
      message,
    };
  }
  const payload = {
    name: data.name,
    startDate: data.startDate,
    endDate: data.endDate,
    description: data.description,
    projectId: projectId,
    createdBy: loggedInUser.id,
    ...(data.pricingCharges
      ? {
          pricingCharges: data.pricingCharges.map((charge) => ({
            ...charge,
            projectId: projectId,
          })),
        }
      : {}),
  };

  const pricingRevision = await PricingRevision.create(payload, {
    include: [
      {
        model: PricingCharge,
        as: 'pricingCharges',
      },
    ],
  });

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Payment Revision'),
    data: pricingRevision,
  };
};

exports.checkAndUpdatePaymentRevision = async (
  projectId,
  paymentRevisionId,
  data,
  loggedInUser
) => {
  const query = {
    where: {
      id: paymentRevisionId,
    },
  };

  const existingPricingRevision = await this.findOne(query);

  existingPricingRevision.name = data.name || existingPricingRevision.name;
  existingPricingRevision.startDate =
    data.startDate || existingPricingRevision.startDate;
  existingPricingRevision.endDate =
    data.endDate || existingPricingRevision.endDate;
  existingPricingRevision.description =
    data.description || existingPricingRevision.description;
  existingPricingRevision.save();

  if (data.pricingCharges) {
    await PricingCharge.destroy({
      where: {
        pricingRevisionId: paymentRevisionId,
      },
    });

    const pricingCharges = data.pricingCharges.map((charge) => ({
      ...charge,
      pricingRevisionId: existingPricingRevision.id,
      projectId: projectId,
    }));

    await this.bulkCreatePricingCharge(pricingCharges);
  }
  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Payment Revision'),
  };
};
