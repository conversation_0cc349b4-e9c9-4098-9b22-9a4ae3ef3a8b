'use strict';
const { attendanceStatus } = require('../config/options');

module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('Attendance', 'description');

    await queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS "Attendance_With_Duration";
    `);

    await queryInterface.removeConstraint(
      'Attendance',
      'Attendance_employeeId_fkey'
    );
    await queryInterface.removeColumn('Attendance', 'employeeId');

    await queryInterface.addColumn('Attendance', 'totalDuration', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });

    await queryInterface.changeColumn('Attendance', 'status', {
      type: Sequelize.ENUM(attendanceStatus.getAttendanceStatusArray()),
      allowNull: true,
    });

    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION update_total_duration()
      RETURNS TRIGGER AS $$
      BEGIN
        IF NEW."outTime" IS NOT NULL AND NEW."inTime" IS NOT NULL THEN
          NEW."totalDuration" := EXTRACT(EPOCH FROM (NEW."outTime" - NEW."inTime")) / 3600;
        END IF;
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;

      CREATE TRIGGER trigger_update_total_duration
      BEFORE UPDATE ON "Attendance"
      FOR EACH ROW
      WHEN (OLD."outTime" IS DISTINCT FROM NEW."outTime")
      EXECUTE FUNCTION update_total_duration();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      DROP VIEW IF EXISTS "Attendance_With_Duration";
    `);

    await queryInterface.addColumn('Attendance', 'description', {
      type: Sequelize.STRING,
      allowNull: true,
    });

    await queryInterface.addColumn('Attendance', 'employeeId', {
      type: Sequelize.INTEGER,
      allowNull: false,
      references: {
        model: 'Employee',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.addConstraint('Attendance', {
      fields: ['employeeId'],
      type: 'foreign key',
      name: 'Attendance_employeeId_fkey',
      references: {
        table: 'Employee',
        field: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    await queryInterface.removeColumn('Attendance', 'totalDuration');

    await queryInterface.changeColumn('Attendance', 'status', {
      type: Sequelize.ENUM(attendanceStatus.getAttendanceStatusArray()),
      allowNull: false,
    });

    await queryInterface.sequelize.query(`
      CREATE VIEW "Attendance_With_Duration" AS
      SELECT 
        "employeeId",
        "date",
        "status",
        EXTRACT(EPOCH FROM ("outTime" - "inTime")) / 3600 AS duration_in_hours
      FROM 
        "Attendance";
    `);

    await queryInterface.sequelize.query(`
      DROP TRIGGER IF EXISTS trigger_update_total_duration ON "Attendance";
      DROP FUNCTION IF EXISTS update_total_duration;
    `);
  },
};
