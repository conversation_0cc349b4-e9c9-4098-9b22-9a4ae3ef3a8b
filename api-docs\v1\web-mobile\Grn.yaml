paths:
  /material/grn:
    post:
      tags:
        - "Material"
      summary: "Create a new GRN"
      description: "This endpoint allows you to create a new Goods Receipt Note (GRN) in the system"
      operationId: "CreateGrn"
      requestBody:
        description: "GRN creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateGrnRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "GRN created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /material/grn/{id}:
    put:
      tags:
        - "Material"
      summary: "Update an existing GRN"
      description: "This endpoint allows you to update an existing GRN in the system"
      operationId: "UpdateGrn"
      parameters:
        - $ref: "#/components/parameters/GrnIdParam"
      requestBody:
        description: "GRN update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateGrnRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "GRN updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "GRN not found"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing GRN"
      description: "This endpoint allows you to update the status of an existing GRN"
      operationId: "UpdateGrnStatus"
      parameters:
        - $ref: "#/components/parameters/GrnIdParam"
      requestBody:
        description: "GRN status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/GrnStatusUpdateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "GRN status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "GRN not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Material"
      summary: "Delete an existing GRN"
      description: "This endpoint allows you to delete an existing GRN"
      operationId: "DeleteGrn"
      parameters:
        - $ref: "#/components/parameters/GrnIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "GRN deleted successfully"
        "404":
          description: "GRN not found"
        "500":
          description: "Internal Server Error"

  /material/grn/item/{id}:
    patch:
      tags:
        - "Material"
      summary: "Update the status of an existing GRN item"
      description: "This endpoint allows you to update the status of an existing GRN item"
      operationId: "UpdateGrnItemStatus"
      parameters:
        - $ref: "#/components/parameters/GrnItemIdParam"
      requestBody:
        description: "GRN item status update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateGrnItemStatusRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "GRN item status updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "GRN item not found"
        "500":
          description: "Internal Server Error"

  /material/grn/media/{id}:
    delete:
      tags:
        - "Material"
      summary: "Delete GRN media"
      description: "This endpoint allows you to delete media associated with an existing GRN in the system"
      operationId: "DeleteGrnMedia"
      parameters:
        - $ref: "#/components/parameters/GrnMediaIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "GRN media deleted successfully"
        "404":
          description: "GRN media not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateGrnRequest:
      type: object
      properties:
        purchaseOrderId:
          type: integer
          example: 1
          description: "ID of the associated purchase order"
        receivedOn:
          type: string
          format: date
          example: "2025-03-30"
          description: "Date when goods were received"
        vendorId:
          type: integer
          example: 1
          description: "ID of the vendor"
        deliveredBy:
          type: integer
          example: 1
          description: "ID of the person who delivered the goods"
        warehouseId:
          type: integer
          example: 1
          description: "ID of the warehouse where the goods were received"
        status:
          type: string
          enum: ["draft", "under_approval", "escalated", "approved", "rejected"]
          example: "draft"
          description: "Current status of the GRN"
        note:
          type: string
          example: "This is a note about the GRN"
          description: "Additional notes about the GRN"
        items:
          type: array
          description: "List of items received"
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
                description: "ID of the item"
              itemVariantId:
                type: integer
                example: 1
                description: "ID of the item variant"
              totalQuantity:
                type: number
                example: 10
                description: "Total quantity received"
        media:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5
      required:
        - purchaseOrderId
        - items

    UpdateGrnRequest:
      type: object
      properties:
        purchaseOrderId:
          type: integer
          example: 1
          description: "ID of the associated purchase order"
        receivedOn:
          type: string
          format: date
          example: "2025-03-30"
          description: "Date when goods were received"
        vendorId:
          type: integer
          example: 1
          description: "ID of the vendor"
        deliveredBy:
          type: integer
          example: 1
          description: "ID of the person who delivered the goods"
        warehouseId:
          type: integer
          example: 1
          description: "ID of the warehouse where the goods were received"
        status:
          type: string
          enum: ["draft", "under_approval", "escalated", "approved", "rejected"]
          example: "under_approval"
          description: "Current status of the GRN"
        note:
          type: string
          example: "Updated note about the GRN"
          description: "Additional notes about the GRN"
        items:
          type: array
          description: "List of items received"
          items:
            type: object
            properties:
              itemId:
                type: integer
                example: 1
                description: "ID of the item"
              itemVariantId:
                type: integer
                example: 1
                description: "ID of the item variant"
              totalQuantity:
                type: number
                example: 10
                description: "Total quantity received"
        media:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5

    GrnStatusUpdateRequest:
      type: object
      properties:
        status:
          type: string
          enum: ["draft", "under_approval", "escalated", "approved", "rejected"]
          example: "approved"
          description: "New status for the GRN"
      required:
        - status

    UpdateGrnItemStatusRequest:
      type: object
      properties:
        status:
          type: string
          enum: ["partial_receipt", "approved", "rejected"]
          example: "approved"
          description: "New status for the GRN item"
        receivedQuantity:
          type: integer
          example: 10
          description: "Quantity received"
        reason:
          type: string
          example: "Received in good condition"
          description: "Reason for the status update"
        media:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "image.jpg"
              fileType:
                type: string
                example: "image/jpeg"
              filePath:
                type: string
                example: "/uploads/image.jpg"
              fileSize:
                type: number
                example: 1024.5
      required:
        - status

    # UpdateGrnItemStatusRequestV2:
    #   type: object
    #   properties:
    #     status:
    #       type: string
    #       enum: ["draft", "under_approval", "escalated", "approved", "rejected"]
    #       example: "approved"
    #       description: "New status for the GRN item"
    #     receivedQuantity:
    #       type: integer
    #       example: 10
    #       description: "Quantity received"
    #     reason:
    #       type: string
    #       example: "Received in good condition"
    #       description: "Reason for the status update"
    #     media:
    #       type: array
    #       items:
    #         type: object
    #         properties:
    #           fileName:
    #             type: string
    #             example: "image.jpg"
    #           fileType:
    #             type: string
    #             example: "image/jpeg"
    #           filePath:
    #             type: string
    #             example: "/uploads/image.jpg"
    #           fileSize:
    #             type: number
    #             example: 1024.5

  parameters:
    GrnIdParam:
      name: "id"
      in: "path"
      description: "ID of the GRN to be managed"
      required: true
      schema:
        type: integer

    GrnItemIdParam:
      name: "id"
      in: "path"
      description: "ID of the GRN item to be managed"
      required: true
      schema:
        type: integer

    GrnMediaIdParam:
      name: "id"
      in: "path"
      description: "ID of the GRN media to be deleted"
      required: true
      schema:
        type: integer
