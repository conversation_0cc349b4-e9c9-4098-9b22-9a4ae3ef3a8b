'use strict';
const { documentType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('EmployeeDocument', 'documentType', {
      type: Sequelize.ENUM(...documentType.getValues()),
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeDocument', 'organizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Organization',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('EmployeeDocument', 'documentType');
    await queryInterface.removeColumn('EmployeeDocument', 'organizationId');
  },
};
