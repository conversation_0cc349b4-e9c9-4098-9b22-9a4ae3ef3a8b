module.exports = (sequelize, DataTypes) => {
  const MarketplaceCategory = sequelize.define(
    'MarketplaceCategory',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      imageUrl: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  MarketplaceCategory.associate = (models) => {
    MarketplaceCategory.belongsTo(models.MarketplaceCategory, {
      foreignKey: 'parentId',
      as: 'parentCategory',
      onDelete: 'CASCADE',
    });

    MarketplaceCategory.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'createdBy',
      onDelete: 'SET NULL',
    });
  };

  return MarketplaceCategory;
};
