exports.passwordLogin = {
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req }) => !req.body.email,
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req }) => !req.body.email,
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req }) => !(req.body.mobileNumber && req.body.countryCode),
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.email,
      else: (value) => false,
    },
    errorMessage: 'Password cannot be empty',
    isString: {
      errorMessage: 'Password must be string',
    },
  },
};

exports.sendOtp = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};

exports.verifyOtp = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  tempOtp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Temp OTP cannot be empty',
    isString: {
      errorMessage: 'Temp OTP must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};

exports.updateInfo = {
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  profilePicture: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => !!req.body.profilePicture,
      else: (value) => false,
    },
  },
  dateOfBirth: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'dateOfBirth cannot be empty',
    isString: {
      errorMessage: 'dateOfBirth must be string',
    },
  },
  city: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'City cannot be empty',
    isString: {
      errorMessage: 'City must be string',
    },
  },
  state: {
    in: ['body'],
    trim: true,
    notEmpty: false,
    errorMessage: 'State cannot be empty',
    isString: {
      errorMessage: 'State must be string',
    },
  },
  pincode: {
    in: ['body'],
    trim: true,
    notEmpty: false,
    errorMessage: 'Pincode cannot be empty',
    isString: {
      errorMessage: 'Pincode must be string',
    },
  },
  countryName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country name cannot be empty',
    isString: {
      errorMessage: 'Country name must be string',
    },
  },
};
exports.signUp = {
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
  },
};

exports.generatePassword = {
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'confirm password cannot be empty',
    isString: {
      errorMessage: 'Confirm password must be string',
    },
    custom: {
      options: (value, { req }) =>
        req.body.confirmPassword === req.body.password,
      errorMessage: 'confirm password does not match',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
    isString: {
      errorMessage: 'Password must be string',
    },
  },
};
exports.changePassword = {
  newPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'New password cannot be empty',
  },
  currentPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Current password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'confirm password cannot be empty',
    custom: {
      options: (value, { req }) =>
        req.body.newPassword === req.body.confirmPassword,
      errorMessage: 'confirm password does not match',
    },
  },
};
const emailMobile = {
  type: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Type cannot be empty',
    isIn: {
      options: [['email', 'mobileNumber']],
      errorMessage: `Type value must be email or mobileNumber`,
    },
    isString: {
      errorMessage: 'Type must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type !== 'email',
      else: (value) => false,
    },
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: {
      if: (value, { req, location, path }) => req.body.type === 'email',
      else: (value) => false,
    },
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};
exports.addEmailMobileNumber = {
  ...emailMobile,
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
  },
};
exports.markPrimary = {
  ...emailMobile,
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
  },
};

exports.verifyEmail = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be string',
    },
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};

exports.register = {
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'New password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Confirm password cannot be empty',
    custom: {
      options: (value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Confirm password must match new password');
        }
        return true;
      },
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  'organization.name': {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Organization name must be string',
    },
  },
  'organization.gstinNumber': {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'GSTIN number must be string',
    },
  },
  'organization.gstinDocument': {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'GSTIN document must be string',
    },
  },
  'organization.address': {
    in: ['body'],
    trim: true,
    // notEmpty: true,
    optional: true,
    errorMessage: 'Organization name cannot be empty',
    isString: {
      errorMessage: 'Organization name must be string',
    },
  },
  'organization.city': {
    in: ['body'],
    trim: true,
    // notEmpty: true,
    optional: true,
    errorMessage: 'City cannot be empty',
    isString: {
      errorMessage: 'City must be string',
    },
  },
  'organization.pincode': {
    in: ['body'],
    trim: true,
    // notEmpty: true,
    optional: true,
    errorMessage: 'Pincode cannot be empty',
    isString: {
      errorMessage: 'Pincode must be string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'pincode must be 6 characters long',
    },
  },
  'organization.workspaceId': {
    in: ['body'],
    trim: true,
    // notEmpty: true,
    optional: true,
    errorMessage: 'Workspace ID cannot be empty',
    isInt: {
      errorMessage: 'Workspace ID must be number',
    },
  },
  'organizationMedia.*.filePath': {
    in: ['body'],
    // notEmpty: false,
    optional: true,
    isString: {
      errorMessage: 'File path must be a string',
    },
  },
  'organizationMedia.*.fileType': {
    in: ['body'],
    // notEmpty: false,
    optional: true,
    isString: {
      errorMessage: 'File type must be a string',
    },
  },
  'organizationMedia.*.inputValue': {
    in: ['body'],
    // notEmpty: false,
    optional: true,
    isString: {
      errorMessage: 'Input value must be a string',
    },
  },
  'organizationMedia.*.fileName': {
    in: ['body'],
    // notEmpty: false,
    optional: true,
    isString: {
      errorMessage: 'File name must be a string',
    },
  },
  'organizationMedia.*.fileSize': {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'File size must be a number',
    },
  },
  'organizationMedia.*.requiredDocumentId': {
    in: ['body'],
    // notEmpty: true,
    optional: true,
    errorMessage: 'Required document ID cannot be empty',
    isInt: {
      errorMessage: 'Required document ID must be a number',
    },
  },
};

exports.onboardInvitedUser = {
  firstName: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'New password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Confirm password cannot be empty',
    custom: {
      options: (value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Confirm password must match new password');
        }
        return true;
      },
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  isBusiness: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'isBusiness must be a boolean value',
    isBoolean: {
      errorMessage: 'isBusiness must be a boolean',
    },
  },
  businessName: {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Business name must be a string',
    isString: {
      errorMessage: 'Business name must be string',
    },
  },
  documents: {
    in: ['body'],
    isArray: {
      errorMessage: 'Documents must be an array',
    },
    optional: true,
    custom: {
      options: (value) => {
        value.forEach((doc) => {
          if (typeof doc.cardNumber !== 'string' || !doc.cardNumber) {
            throw new Error('Each document must have a valid cardNumber');
          }
          if (typeof doc.name !== 'string' || !doc.name) {
            throw new Error('Each document must have a valid name');
          }
          if (typeof doc.documentPath !== 'string' || !doc.documentPath) {
            throw new Error('Each document must have a valid documentPath');
          }
        });
        return true;
      },
    },
  },
  address: {
    in: ['body'],
    optional: true,
    isObject: {
      errorMessage: 'Address must be an object',
    },
    // custom: {
    //   options: (value) => {
    //     if (value) {
    //       const {
    //         address,
    //         addressLine2,
    //         landmark,
    //         city,
    //         state,
    //         pincode,
    //         country,
    //         latitude,
    //         longitude,
    //       } = value;
    //       if (typeof address !== 'string' || !address) {
    //         throw new Error('Address must be a valid string');
    //       }
    //       if (typeof addressLine2 !== 'string' || !addressLine2) {
    //         throw new Error('addressLine2 must be a valid string');
    //       }
    //       if (typeof landmark !== 'string' || !landmark) {
    //         throw new Error('Landmark must be a valid string');
    //       }
    //       if (typeof city !== 'string' || !city) {
    //         throw new Error('City must be a valid string');
    //       }
    //       if (typeof state !== 'string' || !state) {
    //         throw new Error('State must be a valid string');
    //       }
    //       if (typeof country !== 'string' || !country) {
    //         throw new Error('Country must be a valid string');
    //       }
    //       if (typeof pincode !== 'string' || !pincode) {
    //         throw new Error('Pin code must be a valid string');
    //       }
    //       if (typeof latitude !== 'number' || isNaN(latitude)) {
    //         throw new Error('Latitude must be a valid number');
    //       }
    //       if (typeof longitude !== 'number' || isNaN(longitude)) {
    //         throw new Error('Longitude must be a valid number');
    //       }
    //     }
    //     return true;
    //   },
    // },
  },
};

exports.toggleMfa = {
  isMfaEnabled: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'isMfaEnabled is required.',
    isBoolean: {
      errorMessage: 'isMfaEnabled must be a boolean value.',
    },
  },
};

exports.verifyMfa = {
  otp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'OTP cannot be empty',
    isInt: {
      errorMessage: 'OTP must be number',
    },
  },
};

exports.switchOrganization = {
  organizationId: {
    in: ['params'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Organization ID cannot be empty',
    isString: {
      errorMessage: 'Organization ID must be a string',
    },
  },
};

exports.forgotPassword = {
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isEmail: {
      bail: true,
      errorMessage: 'Enter a valid Email',
    },
  },
};

exports.resetPassword = {
  token: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'token cannot be empty',
    isString: {
      errorMessage: 'token must be string',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
    isString: {
      errorMessage: 'Password must be string',
    },
  },
};

exports.registerBrand = {
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'New password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Confirm password cannot be empty',
    custom: {
      options: (value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Confirm password must match new password');
        }
        return true;
      },
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  'organization.name': {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'Organization name must be string',
    },
  },
  'organization.gstinNumber': {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'GSTIN number must be string',
    },
  },
  'organization.gstinDocument': {
    in: ['body'],
    trim: true,
    optional: true,
    isString: {
      errorMessage: 'GSTIN document must be string',
    },
  },
  'organization.address': {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Organization name cannot be empty',
    isString: {
      errorMessage: 'Organization name must be string',
    },
  },
  'organization.city': {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'City cannot be empty',
    isString: {
      errorMessage: 'City must be string',
    },
  },
  'organization.pincode': {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Pincode cannot be empty',
    isString: {
      errorMessage: 'Pincode must be string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'pincode must be 6 characters long',
    },
  },
  'organization.workspaceId': {
    in: ['body'],
    trim: true,
    optional: true,
    errorMessage: 'Workspace ID cannot be empty',
    isInt: {
      errorMessage: 'Workspace ID must be number',
    },
  },
  'organizationMedia.*.filePath': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'File path must be a string',
    },
  },
  'organizationMedia.*.fileType': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'File type must be a string',
    },
  },
  'organizationMedia.*.inputValue': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Input value must be a string',
    },
  },
  'organizationMedia.*.fileName': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'File name must be a string',
    },
  },
  'organizationMedia.*.fileSize': {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'File size must be a number',
    },
  },
  'organizationMedia.*.requiredDocumentId': {
    in: ['body'],
    optional: true,
    errorMessage: 'Required document ID cannot be empty',
    isInt: {
      errorMessage: 'Required document ID must be a number',
    },
  },
  brandName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Brand name cannot be empty',
    isString: {
      errorMessage: 'Brand name must be string',
    },
  },
  'brandMedia.*.filePath': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'File path must be a string',
    },
  },
  'brandMedia.*.fileType': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'File type must be a string',
    },
  },
  'brandMedia.*.inputValue': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Input value must be a string',
    },
  },
  'brandMedia.*.fileName': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'File name must be a string',
    },
  },
  'brandMedia.*.fileSize': {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'File size must be a number',
    },
  },
  'brandMedia.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be a string',
    },
  },
};
