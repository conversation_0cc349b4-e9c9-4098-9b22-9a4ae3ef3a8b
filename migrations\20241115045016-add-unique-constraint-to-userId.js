'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('BankDetail', {
      fields: ['userId'],
      type: 'unique',
      name: 'unique_userId_constraint_bankDetail',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'BankDetail',
      'unique_userId_constraint_bankDetail'
    );
  },
};
