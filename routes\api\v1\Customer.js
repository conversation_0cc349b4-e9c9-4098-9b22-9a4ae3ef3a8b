const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const CustomerController = require('@controllers/v1/crm/Customer');
const CustomerSchema = require('@schema-validation/Customer');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(CustomerSchema.createCustomer),
  ErrorHandleHelper.requestValidator,
  CustomerController.createCustomer
);

router.put(
  '/:id',
  checkSchema(CustomerSchema.updateCustomer),
  ErrorHandleHelper.requestValidator,
  CustomerController.updateCustomer
);

router.patch(
  '/:id',
  checkSchema(CustomerSchema.updateCustomerStatus),
  ErrorHandleHelper.requestValidator,
  CustomerController.updateCustomerStatus
);

router.delete(
  '/:id',
  checkSchema(CustomerSchema.deleteCustomer),
  ErrorHandleHelper.requestValidator,
  CustomerController.deleteCustomer
);

module.exports = router;
