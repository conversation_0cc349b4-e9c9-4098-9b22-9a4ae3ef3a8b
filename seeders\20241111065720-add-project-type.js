'use strict';

const { structureType, projectTypeCategory } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const projectTypes = await queryInterface.sequelize.query(
      'SELECT * FROM "ProjectType" LIMIT 1;',
      { type: queryInterface.sequelize.QueryTypes.SELECT }
    );

    if (projectTypes.length === 0) {
      await queryInterface.bulkInsert('ProjectType', [
        {
          name: 'Appartment',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Row-House',
          projectCategory: projectTypeCategory.SUB_PROJECT,
          structureType: structureType.PLOTTED,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Warehouse',
          projectCategory: projectTypeCategory.SUB_PROJECT,
          structureType: structureType.PLOTTED,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Commercial Complex',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Industrial Park',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.PLOTTED,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Township',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Residential Building',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Shopping Mall',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Mixed-Use Development',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Business Center',
          projectCategory: projectTypeCategory.PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          name: 'Plotted Development',
          projectCategory: projectTypeCategory.SUB_PROJECT,
          structureType: structureType.TOWER,
          createdBy: null,
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]);
    }
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.bulkDelete('ProjectType', null, {});
  },
};
