const { stockAdjustmentStatus } = require('@config/options');

exports.createStockAdjustment = {
  warehouseId: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Warehouse ID is required',
    isInt: {
      errorMessage: 'Warehouse ID must be a valid integer',
    },
  },
  adjustmentNumber: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Adjustment Number is required',
    isInt: {
      errorMessage: 'Adjustment Number must be a valid integer',
    },
  },
  adjustmentDate: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Adjustment Date is required',
    isDate: {
      errorMessage: 'Adjustment Date must be a valid date',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  items: {
    in: ['body'],
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
};

exports.updateStockAdjustment = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Stock Adjustment ID is required',
    },
    isInt: {
      errorMessage: 'Stock Adjustment ID must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Warehouse ID must be a valid integer',
    },
  },
  adjustmentNumber: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Adjustment Number must be a valid integer',
    },
  },
  adjustmentDate: {
    in: ['body'],
    optional: true,
    isDate: {
      errorMessage: 'Adjustment Date must be a valid date',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  items: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
};

exports.updateStockAdjustmentStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Stock Adjustment ID is required',
    },
    isInt: {
      errorMessage: 'Stock Adjustment ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    notEmpty: true,
    custom: {
      options: (value) => {
        const allowedValues = stockAdjustmentStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
};

exports.deleteStockAdjustment = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Stock Adjustment ID is required',
    },
    isInt: {
      errorMessage: 'Stock Adjustment ID must be a valid integer',
    },
  },
};
