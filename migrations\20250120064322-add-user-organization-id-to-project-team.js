'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('ProjectTeam', 'userOrganizationId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'UserOrganization',
        key: 'id',
      },
      onDelete: 'CASCADE',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('ProjectTeam', 'userOrganizationId');
  },
};
