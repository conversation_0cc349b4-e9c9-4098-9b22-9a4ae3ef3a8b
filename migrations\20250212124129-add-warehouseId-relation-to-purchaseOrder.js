'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('PurchaseOrder', 'warehouseId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Warehouse',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('PurchaseOrder', 'warehouseId');
  },
};
