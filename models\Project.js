const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Project = sequelize.define(
    'Project',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(100),
        allowNull: false,
      },
      logo: {
        type: DataTypes.STRING,
        allowNull: true,
      },
      projectCode: {
        type: DataTypes.STRING,
        allowNull: true,
        unique: true,
      },
      reraNumber: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      startDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      endDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      about: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.projectStatus.getProjectStatusArray()),
        allowNull: false,
        defaultValue: OPTIONS.projectStatus.NEW_PROJECT,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      featuresAndSpecifications: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      additionalFields: {
        type: DataTypes.JSONB,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Project.associate = (models) => {
    Project.belongsTo(models.Project, {
      foreignKey: 'parentProjectId',
      as: 'parentProject',
      onDelete: 'SET NULL',
    });

    Project.belongsTo(models.ProjectType, {
      foreignKey: 'projectTypeId',
      as: 'type',
    });

    Project.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
    });

    Project.belongsTo(models.Address, {
      foreignKey: 'addressId',
      as: 'address',
      onDelete: 'SET NULL',
      allowNull: true,
    });

    Project.hasMany(models.Document, {
      foreignKey: 'projectId',
      as: 'documents',
      onDelete: 'SET NULL',
    });

    Project.hasMany(models.Drawing, {
      foreignKey: 'projectId',
      as: 'drawings',
      onDelete: 'SET NULL',
    });

    Project.hasMany(models.Requirement, {
      foreignKey: 'projectId',
      as: 'requirements',
      onDelete: 'SET NULL',
    });

    Project.hasMany(models.Requirement, {
      foreignKey: 'subProjectId',
      as: 'subProjectRequirements',
      onDelete: 'SET NULL',
    });

    Project.belongsToMany(models.Amenities, {
      through: 'ProjectAmenities',
      foreignKey: 'projectId',
      otherKey: 'amenityId',
      as: 'amenities',
      onDelete: 'CASCADE',
    });

    Project.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Project.belongsToMany(models.User, {
      through: 'ProjectTeam',
      foreignKey: 'projectId',
      otherKey: 'userId',
      as: 'teamMembers',
      onDelete: 'CASCADE',
    });

    Project.hasMany(models.ProjectMedia, {
      foreignKey: 'projectId',
      as: 'projectMedia',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Project.hasMany(models.WorkOrder, {
      foreignKey: 'projectId',
      as: 'workOrders',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
    Project.hasMany(models.ActivityLog, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        activityOn: 'Project',
      },
      as: 'activities',
    });
  };

  return Project;
};
