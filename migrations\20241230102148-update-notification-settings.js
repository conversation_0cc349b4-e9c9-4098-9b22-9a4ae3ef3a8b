'use strict';

const checkTableExists = async (queryInterface, tableName) => {
  const tableExists = await queryInterface.sequelize.query(
    `SELECT EXISTS (
      SELECT FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_name = :tableName
    )`,
    {
      replacements: { tableName },
      type: queryInterface.sequelize.QueryTypes.SELECT,
    }
  );
  return tableExists[0].exists;
};

const getExistingConstraints = async (queryInterface, tableName) => {
  const constraints = await queryInterface.sequelize.query(
    `SELECT constraint_name 
     FROM information_schema.table_constraints 
     WHERE table_name = :tableName 
     AND constraint_type = 'FOREIGN KEY'`,
    {
      replacements: { tableName },
      type: queryInterface.sequelize.QueryTypes.SELECT,
    }
  );
  return constraints;
};

const removeConstraints = async (queryInterface, tableName, constraints) => {
  for (const constraint of constraints) {
    try {
      await queryInterface.removeConstraint(
        tableName,
        constraint.constraint_name
      );
    } catch (error) {
      console.warn(
        `Warning: Could not remove constraint ${constraint.constraint_name}:`,
        error.message
      );
    }
  }
};

module.exports = {
  up: async (queryInterface, Sequelize) => {
    try {
      const tableName = 'NotificationSettings';
      const exists = await checkTableExists(queryInterface, tableName);

      if (!exists) {
        throw new Error(`Table ${tableName} does not exist`);
      }

      const constraints = await getExistingConstraints(
        queryInterface,
        tableName
      );
      await removeConstraints(queryInterface, tableName, constraints);

      await queryInterface.changeColumn(tableName, 'organizationId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });

      await queryInterface.changeColumn(tableName, 'userId', {
        type: Sequelize.INTEGER,
        allowNull: true,
      });

      console.log('Adding new constraints...');
      await queryInterface.addConstraint('NotificationSettings', {
        fields: ['organizationId'],
        type: 'foreign key',
        name: 'NotificationSettings_organizationId_fkey',
        references: {
          table: 'Organization',
          field: 'id',
        },
        onDelete: 'CASCADE',
      });

      await queryInterface.addConstraint('NotificationSettings', {
        fields: ['userId'],
        type: 'foreign key',
        name: 'NotificationSettings_userId_fkey',
        references: {
          table: 'User',
          field: 'id',
        },
        onDelete: 'CASCADE',
      });
    } catch (error) {
      throw error;
    }
  },

  down: async (queryInterface, Sequelize) => {
    try {
      const tableName = 'NotificationSettings';
      const constraints = await getExistingConstraints(
        queryInterface,
        tableName
      );

      await removeConstraints(queryInterface, tableName, constraints);

      await queryInterface.changeColumn(tableName, 'organizationId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });

      await queryInterface.changeColumn(tableName, 'userId', {
        type: Sequelize.INTEGER,
        allowNull: false,
      });

      await queryInterface.addConstraint('NotificationSettings', {
        fields: ['organizationId'],
        type: 'foreign key',
        name: 'NotificationSettings_organizationId_fkey',
        references: {
          table: 'Organization',
          field: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      });

      await queryInterface.addConstraint('NotificationSettings', {
        fields: ['userId'],
        type: 'foreign key',
        name: 'NotificationSettings_userId_fkey',
        references: {
          table: 'User',
          field: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      });
    } catch (error) {
      throw error;
    }
  },
};
