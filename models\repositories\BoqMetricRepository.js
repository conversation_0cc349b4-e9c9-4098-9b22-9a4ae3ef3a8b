const { BOQMetric } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.createBOQMetric = async (data) => {
  try {
    const existingMetric = await BOQMetric.findOne({
      where: { metricValue: data.metricValue },
    });

    if (existingMetric) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('boqMetric'),
      };
    }

    const boqMetric = await BOQMetric.create(data);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('BOQ Metric'),
      data: boqMetric,
    };
  } catch (error) {
    throw error;
  }
};
