const { Op } = require('sequelize');
const { Module, sequelize } = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.validateAndCreateModule = async (data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const { parentId } = data;
  const selectFields = ['id'];
  const transaction = await sequelize.transaction();
  try {
    const validationPromises = [
      parentId
        ? checkExistence(Module, { id: parentId }, selectFields).then(
            (checkParent) => {
              if (!checkParent) {
                throw new Error(
                  errorMessage.DOES_NOT_EXIST(
                    `Parent module with id: ${parentId} does not exist`
                  )
                );
              }
            }
          )
        : Promise.resolve(),

      checkExistence(
        Module,
        { organizationId, name: { [Op.iLike]: data.name } },
        selectFields
      ).then((duplicateName<PERSON>heck) => {
        if (duplicateNameCheck) {
          throw new Error(
            errorMessage.ALREADY_EXIST(
              `Module with this name already exists: ${data.name}`
            )
          );
        }
      }),
    ];

    await Promise.all(validationPromises);

    const createdModule = await Module.create(
      {
        ...data,
        organizationId,
        createdBy: loggedInUser.id,
      },
      { transaction }
    );
    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Module'),
      data: createdModule,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the module',
    };
  }
};

exports.validateAndUpdateModule = async (moduleId, data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const { parentId, name } = data;
  const selectFields = ['id'];
  const transaction = await sequelize.transaction();
  try {
    const module = await checkExistence(Module, { id: moduleId });
    if (!module) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(
          `Module with id: ${moduleId} does not exist`
        )
      );
    }

    const validations = [
      parentId
        ? checkExistence(Module, { id: parentId }, selectFields).then(
            (checkParent) => {
              if (!checkParent) {
                throw new Error(
                  errorMessage.DOES_NOT_EXIST(
                    `Parent module with id: ${parentId} does not exist`
                  )
                );
              }
            }
          )
        : Promise.resolve(),

      name
        ? checkExistence(
            Module,
            {
              organizationId,
              name: { [Op.iLike]: name },
              id: { [Op.ne]: moduleId },
            },
            selectFields
          ).then((checkName) => {
            if (checkName) {
              throw new Error(
                errorMessage.ALREADY_EXIST(
                  `Module with this name already exists: ${name}`
                )
              );
            }
          })
        : Promise.resolve(),
    ];

    await Promise.all(validations);

    Object.assign(module, data);
    await module.save({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Module'),
      data: module,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the module',
    };
  }
};

exports.deleteModule = async (moduleId) => {
  const transaction = await sequelize.transaction();
  try {
    const module = await checkExistence(Module, { id: moduleId }, ['id']);
    if (!module) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(
          `Module with id: ${moduleId} does not exist`
        )
      );
    }

    await Module.destroy({ where: { id: moduleId }, transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Module'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the module',
    };
  }
};

exports.getAllModules = async (queryParamas) => {
  const { organizationId, limit = 10, offset = 0 } = queryParamas;
  try {
    const query = {
      organizationId: organizationId,
      parentId: null,
    };

    const totalCount = await Module.count({ where: query });
    const modules = await Module.findAll({
      where: query,
      include: [
        {
          model: Module,
          as: 'childModules',
          required: false,
        },
      ],
      limit: limit,
      offset: offset,
    });

    return {
      success: true,
      message: 'Modules fetched successfully',
      count: totalCount,
      data: modules,
    };
  } catch (error) {
    throw new Error(error);
  }
};
