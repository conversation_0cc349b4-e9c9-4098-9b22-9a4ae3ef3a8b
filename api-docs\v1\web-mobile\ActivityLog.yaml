paths:
  /activitylog/list:
    get:
      summary: Lists of Activities Globally
      description: Fetches all Activities associated with a specific model
      operationId: listActivities
      tags:
        - ActivityLog
      security:
        - bearerAuth: []
      parameters:
        - name: start
          in: query
          required: false
          description: Start for the page
          schema:
            type: integer
            example: 0
        - name: limit
          in: query
          required: false
          description: Limit for the page
          schema:
            type: integer
            example: 10
        - name: activityOn
          in: query
          required: false
          description: Filter activities based on the model type (e.g., Organization, Task, Request)
          schema:
            type: string
            enum:
              - Organization
              - Task
              - Request
              - User
              - Project
              - WorkOrder
            example: Task
        - name: recordId
          in: query
          required: false
          description: ID of the record associated with the activity (e.g., Organization, Task, Request)
          schema:
            type: integer
            example: 1
      responses:
        "200":
          description: Activity fetched successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
