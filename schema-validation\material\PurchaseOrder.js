const {
  purchaseOrderPriority,
  purchaseOrderStatus,
} = require('@config/options');

exports.createPurchaseOrder = {
  indentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Indent ID must be a valid integer',
    },
  },
  workOrderId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'WorkOrder ID must be a valid integer',
    },
  },
  taskId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Task ID must be a valid integer',
    },
  },
  vendorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Vendor ID must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'delivery location warehouse ID must be a valid integer',
    },
  },
  requiredByDate: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'RequiredBy Date is required',
    isDate: {
      errorMessage: 'RequiredBy Date must be a valid date',
    },
  },
  priority: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Priority is required',
    custom: {
      options: (value) => {
        const allowedValues = purchaseOrderPriority.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Priority: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Priority value provided',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  subTotal: {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'SubTotal must be a valid decimal',
    },
  },
  taxRates: {
    in: ['body'],
    optional: true,
    isObject: {
      errorMessage: 'TaxRates must be a valid object',
    },
  },
  discount: {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'Discount must be a valid decimal',
    },
  },
  total: {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'Total must be a valid decimal',
    },
  },
  items: {
    in: ['body'],
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
};

exports.updatePurchaseOrder = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Purchase Order ID is required',
    },
    isInt: {
      errorMessage: 'Purchase Order ID must be a valid integer',
    },
  },
  indentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Indent ID must be a valid integer',
    },
  },
  workOrderId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'WorkOrder ID must be a valid integer',
    },
  },
  taskId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Task ID must be a valid integer',
    },
  },
  vendorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Vendor ID must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'delivery location warehouse ID must be a valid integer',
    },
  },
  requiredByDate: {
    in: ['body'],
    optional: true,
    errorMessage: 'RequiredBy Date is required',
    isDate: {
      errorMessage: 'RequiredBy Date must be a valid date',
    },
  },
  priority: {
    in: ['body'],
    optional: true,
    errorMessage: 'Priority is required',
    custom: {
      options: (value) => {
        const allowedValues = purchaseOrderPriority.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Priority: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Priority value provided',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  subTotal: {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'SubTotal must be a valid decimal',
    },
  },
  taxRates: {
    in: ['body'],
    optional: true,
    isObject: {
      errorMessage: 'TaxRates must be a valid object',
    },
  },
  discount: {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'Discount must be a valid decimal',
    },
  },
  total: {
    in: ['body'],
    notEmpty: true,
    isDecimal: {
      errorMessage: 'Total must be a valid decimal',
    },
  },
  items: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
};

exports.updatePurchaseOrderStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Purchase Order ID is required',
    },
    isInt: {
      errorMessage: 'Purchase Order ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    notEmpty: true,
    custom: {
      options: (value) => {
        const allowedValues = purchaseOrderStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
};

exports.deletePurchaseOrder = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Purchase Order ID is required',
    },
    isInt: {
      errorMessage: 'Purchase Order ID must be a valid integer',
    },
  },
};
