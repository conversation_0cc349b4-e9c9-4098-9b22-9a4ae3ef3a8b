paths:
  /user-leave/summary:
    post:
      summary: Add a new leave summary
      description: Adds a new Leave summary for the user
      operationId: addLeaveSummary
      tags:
        - User
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createLeaveSummary"
      responses:
        "201":
          description: Leave Summary added successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /user-leave/summary/{id}:
    patch:
      summary: Update an existing leaveSummary
      description: Updates the leave summary for user.
      operationId: updateLeaveSummary
      tags:
        - User
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the leave summary
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateLeaveSummary"
      responses:
        "200":
          description: Leave Summary updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    
    delete:
      summary: Delete a Summary
      description: Deletes a Leave summary for user
      operationId: deleteLeaveSummary
      tags:
        - User
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the leave summary to delete
      responses:
        "200":
          description: leave summary deleted successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /user-leave/summary/{userId}:
    get:
      summary: Get LeaveSummary based on userId
      description: Get All Leave Summary based on userId
      operationId: getLeaveSummary
      tags:
        - User
      security:
        - bearerAuth: []
      parameters:
        - name: userId
          in: path
          required: true
          schema:
            type: integer
          description: ID of the User
        - name: "start"
          in: "query"
          description: "The starting index for pagination. Default is 0."
          required: false
          schema:
            type: integer
            default: 0
        - name: "limit"
          in: "query"
          description: "The number of records to retrieve. Default is 10."
          required: false
          schema:
            type: integer
            default: 10
      responses:
        "200":
          description: Leave Summary updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

components:
  schemas:
    createLeaveSummary:
      type: object
      required:
        - leaveType
        - maxAllowed
        - used
      properties:
        leaveType:
          type: string
          description: Type of leave .
        maxAllowed:
          type: integer
          description: Maximum number of leaves allowed.
        used:
          type: integer
          description: Number of leaves used.

    updateLeaveSummary:
      type: object
      properties:
        leaveType:
          type: string
          description: Type of leave.
        maxAllowed:
          type: integer
          description: Maximum number of leaves allowed (optional for update).
        used:
          type: integer
          description: Number of leaves used (optional for update).
