const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ItemCategoryController = require('@controllers/v1/material/ItemCategory');
const ItemCategorySchema = require('@schema-validation/material/ItemCategory');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(ItemCategorySchema.createItemCategory),
  ErrorHandleHelper.requestValidator,
  ItemCategoryController.createItemCategory
);

router.patch(
  '/:id',
  checkSchema(ItemCategorySchema.updateItemCategory),
  ErrorHandleHelper.requestValidator,
  ItemCategoryController.updateItemCategory
);

router.delete(
  '/:id',
  checkSchema(ItemCategorySchema.deleteItemCategory),
  ErrorHandleHelper.requestValidator,
  ItemCategoryController.deleteItemCategory
);

module.exports = router;
