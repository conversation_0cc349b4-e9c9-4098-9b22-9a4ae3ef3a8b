const OPTIONS = require('@config/options');
module.exports = (sequelize, DataTypes) => {
  const EmployeeDocument = sequelize.define(
    'EmployeeDocument',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      cardNumber: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      documentPath: {
        type: DataTypes.TEXT,
        allowNull: true,
        get() {
          return OPTIONS.generateCloudFrontUrl(
            this.getDataValue('documentPath')
          );
        },
      },
      documentType: {
        type: DataTypes.ENUM(OPTIONS.documentType.getValues()),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  EmployeeDocument.associate = (models) => {
    EmployeeDocument.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    EmployeeDocument.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    EmployeeDocument.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    EmployeeDocument.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return EmployeeDocument;
};
