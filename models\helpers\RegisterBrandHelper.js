const {
  Organization,
  Brand,
  DesignationMedia,
  BrandMedia,
  User,
  UserOrganization,
  Address,
} = require('..');
const {
  errorMessage,
  usersRoles,
  inviteStatus,
  addressType,
} = require('@config/options');
const { Op } = require('sequelize');
const { generatePassword } = require('@helpers/UtilHelper');

const validateOrganization = async (name, gstinNumber) => {
  try {
    const whereConditions = {
      [Op.or]: [{ name }, { gstinNumber }],
    };

    const existingOrganization = await Organization.findOne({
      where: whereConditions,
    });

    if (existingOrganization) {
      if (existingOrganization.name === name) {
        throw new Error(
          errorMessage.ALREADY_EXIST('Organization with the same name')
        );
      }
      if (existingOrganization.gstinNumber === gstinNumber) {
        throw new Error(
          errorMessage.ALREADY_EXIST('Organization with the same GSTIN number')
        );
      }
    }
  } catch (error) {
    throw new Error(`Validation error: ${error.message}`);
  }
};

const createOrganization = async (data, transaction) => {
  try {
    const {
      name,
      gstinNumber,
      address,
      city,
      pincode,
      workspaceId,
      countryCode,
      mobileNumber,
    } = data;
    return await Organization.create(
      {
        name,
        gstinNumber,
        address,
        city,
        pincode,
        workspaceId,
        countryCode,
        mobile: mobileNumber,
      },
      { transaction }
    );
  } catch (error) {
    throw new Error(`Error creating organization: ${error.message}`);
  }
};

const createBrand = async (brandName, organizationId, userId, transaction) => {
  try {
    return await Brand.create(
      {
        name: brandName,
        organizationId,
        userId,
      },
      { transaction }
    );
  } catch (error) {
    throw new Error(`Error creating brand: ${error.message}`);
  }
};

const handleMediaUpload = async (
  media,
  parentId,
  userId,
  isBrand = false,
  transaction
) => {
  if (media && media.length > 0) {
    try {
      const mediaData = media.map((item) => {
        return {
          ...item,
          ...(isBrand
            ? { brandId: parentId }
            : { organizationId: parentId, userId }),
        };
      });
      if (isBrand) {
        await BrandMedia.bulkCreate(mediaData, { transaction });
      } else {
        await DesignationMedia.bulkCreate(mediaData, { transaction });
      }
    } catch (error) {
      throw new Error(`Error uploading media: ${error.message}`);
    }
  }
};

const updateUserDetails = async (userId, userDetails, transaction) => {
  const {
    firstName,
    lastName,
    password,
    countryCode,
    mobileNumber,
    organizationId,
  } = userDetails;
  try {
    const hashPassword = await generatePassword(password);

    const updateData = {
      firstName,
      lastName,
      password: hashPassword,
      countryCode,
      mobileNumber,
      organizationId,
      currentOrganizationId: organizationId,
      role: usersRoles.BRAND,
    };

    await User.update(updateData, {
      where: { id: userId },
      transaction,
    });
  } catch (error) {
    throw new Error(`Error updating user details: ${error.message}`);
  }
};

const createAddressForOrganization = async (
  data,
  organizationId,
  transaction
) => {
  return await Address.create(
    {
      ...data,
      addressType: addressType.ORGANIZATION,
      organizationId,
    },
    { transaction }
  );
};

const createUserOrganization = async (
  loggedInUser,
  organizationId,
  transaction
) => {
  return await UserOrganization.create(
    {
      userId: loggedInUser.id,
      designationId: loggedInUser.designationId,
      organizationId: organizationId,
      inviteStatus: inviteStatus.ACCEPTED,
      isPrimary: true,
    },
    { transaction }
  );
};

module.exports = {
  createOrganization,
  createBrand,
  validateOrganization,
  handleMediaUpload,
  updateUserDetails,
  createAddressForOrganization,
  createUserOrganization,
};
