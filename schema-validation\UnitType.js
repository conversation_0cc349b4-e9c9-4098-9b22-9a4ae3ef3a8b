exports.createUnitType = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
};

exports.updateUnitType = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
  },
};
