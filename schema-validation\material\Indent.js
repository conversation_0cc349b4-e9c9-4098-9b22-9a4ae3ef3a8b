const {
  indentPriority,
  indentStatus,
  indentItemStatus,
} = require('@config/options');

exports.createIndent = {
  boqId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Boq ID must be a valid integer',
    },
  },
  workOrderId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Work Order ID must be a valid integer',
    },
  },
  taskId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Task ID must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Warehouse ID is required',
    isInt: {
      errorMessage: 'Warehouse ID must be a valid integer',
    },
  },
  requiredByDate: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Required By Date is required',
    isDate: {
      errorMessage: 'Required By Date must be a valid date',
    },
  },
  priority: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Priority is required',
    custom: {
      options: (value) => {
        const allowedValues = indentPriority.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Priority: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Priority value provided',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  items: {
    in: ['body'],
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
};

exports.updateIndent = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Indent ID is required',
    },
    isInt: {
      errorMessage: 'Indent ID must be a valid integer',
    },
  },
  boqId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Boq ID must be a valid integer',
    },
  },
  workOrderId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Work Order ID must be a valid integer',
    },
  },
  taskId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Task ID must be a valid integer',
    },
  },
  warehouseId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Warehouse ID must be a valid integer',
    },
  },
  requiredByDate: {
    in: ['body'],
    optional: true,
    isDate: {
      errorMessage: 'Required By Date must be a valid date',
    },
  },
  priority: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = indentPriority.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Priority: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Priority value provided',
    },
  },
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Note must be a valid string',
    },
  },
  items: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Items must be an array',
    },
  },
};

exports.updateIndentStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Indent ID is required',
    },
    isInt: {
      errorMessage: 'Indent ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = indentStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
};

exports.deleteIndent = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Indent ID is required',
    },
    isInt: {
      errorMessage: 'Indent ID must be a valid integer',
    },
  },
};

exports.updateIndentItemStatus = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Indent Item ID is required',
    },
    isInt: {
      errorMessage: 'Indent Item ID must be a valid integer',
    },
  },
  status: {
    in: ['body'],
    notEmpty: true,
    custom: {
      options: (value) => {
        const allowedValues = indentItemStatus.getValues();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid Status: ${value}. Allowed values are: ${indentItemStatus.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid Status provided',
    },
  },
};
