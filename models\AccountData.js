const {
  accountCategory,
  accountType,
  recordStatus,
  accountAssociation,
} = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const AccountData = sequelize.define(
    'AccountData',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      organizationId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      accountCategory: {
        type: DataTypes.ENUM(accountCategory.getValues()),
        defaultValue: accountCategory.DEFAULT_VALUE,
        allowNull: false,
      },
      accountType: {
        type: DataTypes.ENUM(accountType.getValues()),
        defaultValue: accountType.DEFAULT_VALUE,
        allowNull: false,
      },
      referenceId: {
        type: DataTypes.INTEGER,
        defaultValue: null,
        allowNull: true,
      },
      referenceType: {
        type: DataTypes.STRING,
        defaultValue: accountAssociation.STATIC,
        allowNull: true,
      },
      name: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: false,
      },
      code: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: false,
        unique: true,
      },
      description: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: true,
      },
      bankAccountNumber: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: true,
      },
      ifscCode: {
        type: DataTypes.STRING,
        defaultValue: '',
        allowNull: true,
      },
      parentAccountId: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: null,
      },
      totalCredit: {
        type: DataTypes.DECIMAL(20, 2),
        defaultValue: '',
        allowNull: true,
      },
      totalDebit: {
        type: DataTypes.DECIMAL(20, 2),
        defaultValue: '',
        allowNull: true,
      },
      openingBalance: {
        type: DataTypes.DECIMAL(20, 2),
        defaultValue: 0,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(recordStatus.getValues()),
        defaultValue: recordStatus.ACTIVE,
        allowNull: false,
      },
      flagDefault: {
        type: DataTypes.BOOLEAN,
        allowNull: true,
        defaultValue: false,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  AccountData.associate = (models) => {
    AccountData.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'NO ACTION',
    });

    AccountData.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
      onDelete: 'NO ACTION',
    });

    AccountData.hasMany(models.AccountData, {
      foreignKey: 'parentAccountId',
      as: 'subAccounts',
      onDelete: 'NO ACTION',
    });

    AccountData.belongsTo(models.AccountData, {
      foreignKey: 'parentAccountId',
      as: 'parentAccount',
      onDelete: 'NO ACTION',
    });

    AccountData.hasMany(models.Expense, {
      foreignKey: 'expenseAccountId',
      as: 'expenseAccount',
      onDelete: 'NO ACTION',
    });

    AccountData.hasMany(models.Expense, {
      foreignKey: 'paidThroughAccountId',
      as: 'paidThroughAccount',
      onDelete: 'NO ACTION',
    });
  };

  return AccountData;
};
