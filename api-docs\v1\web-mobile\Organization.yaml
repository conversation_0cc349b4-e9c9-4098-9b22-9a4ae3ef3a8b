paths:
  /organization/{inviteId}:
    get:
      tags:
        - "Organization"
      summary: "Retrieve Organization Details by Invite ID"
      description: "Fetch organization details using an invite ID from the request path"
      operationId: "GetOrganizationByInviteId"
      parameters:
        - name: "inviteId"
          in: "path"
          description: "ID of the invite to be managed"
          required: true
          schema:
            type: integer
            example: 10  # Example ID, adjust as needed
      produces:
        - "application/json"
      responses:
        "200":
          description: "Successfully retrieved organization details"
        "400":
          description: "Invalid invite ID or missing headers"
        "404":
          description: "Organization not found for the given invite ID"
        "500":
          description: "Internal Server Error"

components:
  parameters:
    inviteIdParam:
      name: "inviteId"
      in: "path"
      description: "ID of the invite to be managed"
      required: true
      schema:
        type: integer
        example: 10
