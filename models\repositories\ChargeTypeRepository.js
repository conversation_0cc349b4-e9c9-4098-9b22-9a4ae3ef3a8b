const { Project, ChargeTypes } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.findOne = async (query) => await Project.findOne(query);

exports.findChargeType = async (query) => await ChargeTypes.findOne(query);

exports.addChargeType = async (projectId, data, loggedInUser) => {
  try {
    const query = {
      where: {
        id: projectId,
      },
    };
    const project = await this.findOne(query);

    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('project'),
      };
    }

    const payload = {
      ...data,
      projectId,
      createdBy: loggedInUser.id,
    };

    const chargeType = await ChargeTypes.create(payload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('ChargeType'),
      data: chargeType,
    };
  } catch (error) {
    throw error;
  }
};

exports.updateChargeType = async (chargeTypeId, data, loggedInUser) => {
  try {
    const query = {
      where: { id: chargeTypeId },
    };
    const chargeType = await this.findChargeType(query);

    if (!chargeType) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('ChargeType'),
      };
    }

    if (chargeType.createdBy !== loggedInUser.id) {
      return {
        success: false,
        message: errorMessage.UNAUTHORIZED_ACCESS,
      };
    }

    const updatedData = {
      ...data,
      updatedAt: new Date(),
    };

    await chargeType.update(updatedData);

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('ChargeType'),
      data: chargeType,
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteChargeType = async (chargeTypeId) => {
  try {
    const query = {
      where: { id: chargeTypeId },
    };
    const chargeType = await this.findChargeType(query);

    if (!chargeType) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('ChargeType'),
      };
    }

    await chargeType.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('ChargeType'),
    };
  } catch (error) {
    throw error;
  }
};
