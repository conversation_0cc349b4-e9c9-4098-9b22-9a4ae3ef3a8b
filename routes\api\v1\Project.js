const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const ProjectControl = require('../../../controllers/api/v1/Project');
const ProjectSchema = require('../../../schema-validation/Project');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(ProjectSchema.createOrUpdateProjectAndSubProject),
  ErrorHandleHelper.requestValidator,
  ProjectControl.createProject
);

router.get(
  '',
  checkSchema(ProjectSchema.getProjects),
  ErrorHandleHelper.requestValidator,
  ProjectControl.getProjects
);

router.put(
  '/:id',
  checkSchema(ProjectSchema.createOrUpdateProjectAndSubProject),
  ErrorHandleHelper.requestValidator,
  ProjectControl.putProject
);

router.post(
  '/:id/sub-project',
  checkSchema(ProjectSchema.createOrUpdateProjectAndSubProject),
  ErrorHandleHelper.requestValidator,
  ProjectControl.createSubProject
);

router.get(
  '/detect-weather',
  ErrorHandleHelper.requestValidator,
  ProjectControl.detectWeather
);

router.post(
  '/:id/drawing',
  checkSchema(ProjectSchema.validateAddDrawingsToProject),
  ErrorHandleHelper.requestValidator,
  ProjectControl.addDrwaingsToProject
);

module.exports = router;
