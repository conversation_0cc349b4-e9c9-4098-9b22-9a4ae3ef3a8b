'use strict';

const {
  dependencyType,
  dependOn,
  dependencyStatus,
} = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('WbsActivityDependency', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      wbsActivityId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'WbsActivity',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: false,
      },
      dependencyType: {
        type: Sequelize.ENUM(...dependencyType.getValues()),
        allowNull: false,
      },
      dependOn: {
        type: Sequelize.ENUM(...dependOn.getValues()),
        allowNull: false,
      },
      taskId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Tasks',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      activityId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'WbsActivity',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: false,
      },
      status: {
        type: Sequelize.ENUM(...dependencyStatus.getValues()),
        allowNull: false,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: 'CURRENT_TIMESTAMP',
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('WbsActivityDependency');
  },
};
