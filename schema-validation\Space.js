exports.createSpaceForFloor = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1, max: 100 },
      errorMessage: 'Name must be between 1 and 100 characters',
    },
  },
  floorId: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'Floor ID must be an integer',
    },
    toInt: true,
  },
  length: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Length cannot be empty',
    },
    isFloat: {
      errorMessage: 'Length must be a number',
    },
    toFloat: true,
  },
  breadth: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Breadth cannot be empty',
    },
    isFloat: {
      errorMessage: 'Breadth must be a number',
    },
    toFloat: true,
  },
  area: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Area cannot be empty',
    },
    isFloat: {
      errorMessage: 'Area must be a number',
    },
    toFloat: true,
  },
};

exports.createSpaceForUnit = {
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1, max: 100 },
      errorMessage: 'Name must be between 1 and 100 characters',
    },
  },
  unitId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Unit ID cannot be empty',
    },
    isInt: {
      errorMessage: 'Unit ID must be an integer',
    },
    toInt: true,
  },
  length: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Length cannot be empty',
    },
    isFloat: {
      errorMessage: 'Length must be a number',
    },
    toFloat: true,
  },
  breadth: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Breadth cannot be empty',
    },
    isFloat: {
      errorMessage: 'Breadth must be a number',
    },
    toFloat: true,
  },
  area: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Area cannot be empty',
    },
    isFloat: {
      errorMessage: 'Area must be a number',
    },
    toFloat: true,
  },
};

exports.createSpace = {
  unitId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Unit ID must be an integer',
    },
    toInt: true,
  },
  floorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Floor ID must be an integer',
    },
    toInt: true,
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1, max: 100 },
      errorMessage: 'Name must be between 1 and 100 characters',
    },
  },
  length: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Length cannot be empty',
    },
    isFloat: {
      errorMessage: 'Length must be a number',
    },
    toFloat: true,
  },
  breadth: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Breadth cannot be empty',
    },
    isFloat: {
      errorMessage: 'Breadth must be a number',
    },
    toFloat: true,
  },
  area: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Area cannot be empty',
    },
    isFloat: {
      errorMessage: 'Area must be a number',
    },
    toFloat: true,
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};

exports.updateSpace = {
  unitId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Unit ID must be an integer',
    },
    toInt: true,
  },
  floorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Floor ID must be an integer',
    },
    toInt: true,
  },
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1, max: 100 },
      errorMessage: 'Name must be between 1 and 100 characters',
    },
  },
  length: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Length must be a number',
    },
    toFloat: true,
  },
  breadth: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Breadth must be a number',
    },
    toFloat: true,
  },
  area: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Area must be a number',
    },
    toFloat: true,
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};

exports.validateRemoveSpace = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Space Id is required',
    },
    isInt: {
      errorMessage: 'Space Id must be a valid integer',
    },
  },
};

exports.validateAddDrawingsToSpace = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Space Id is required',
    },
    isInt: {
      errorMessage: 'Space Id must be a valid integer',
    },
  },
  projectId: {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'Project ID must be a valid integer',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};
