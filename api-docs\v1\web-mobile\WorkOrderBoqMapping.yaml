paths:
  /project/{id}/workorder-boq-mapping:
    post:
      summary: Creates a new Work Order BOQ Mapping
      description: Creates new mappings between a work order and multiple BOQ items with provided details, including work order ID and BOQ item IDs.
      operationId: createWorkOrderBOQMapping
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The project ID associated with the work order
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createWorkOrderBOQMapping"
      responses:
        "201":
          description: Work Order BOQ Mappings created successfully
        "400":
          description: Invalid request data
        "500":
          description: Internal Server Error

  /project/workorder-boq-mapping:
    delete:
      summary: Deletes a Work Order BOQ Mapping
      description: Deletes a mapping between a work order and a specific BOQ item based on provided work order ID and BOQ item ID.
      operationId: deleteWorkOrderBOQMapping
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/deleteWorkOrderBOQMapping"
      responses:
        "200":
          description: Work Order BOQ Mapping deleted successfully
        "400":
          description: Invalid request data or mapping does not exist
        "500":
          description: Internal Server Error

  /project/workorder-boq-mapping-listing:
    get:
      summary: Lists all Work Order BOQ Mappings for a project
      description: Retrieves a list of work order BOQ mappings for a specific project ID, including details like work order number, type, contractor name, work category, work order value, schedule, and status.
      operationId: listWorkOrderBOQMapping
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: projectId
          in: query
          description: The project ID to retrieve the work order BOQ mappings
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - name: organizationId
          in: query
          description: The organization ID to retrieve the work order BOQ mappings
          schema:
            type: string
        - name: start
          in: query
          description: The start number want to retrieve
          schema:
            type: number
        - name: limit
          in: query
          description: The page limit want to retrieve
          schema:
            type: number
        - name: fromDate
          in: query
          description: Date From which you want to see data
          schema:
            type: date
        - name: toDate
          in: query
          description: Date To which you want to see data
          schema:
            type: date
        - name: type
          in: query
          description: Based on WorkOrderType
          schema:
            type: string
        - name: status
          in: query
          description: Based on WorkOrder status
          schema:
            type: array
            items:
              type: string
            example: ["draft", "under_approval","approved"]
        - name: workCategory
          in: query
          description: Based on WorkOrder Category
          schema:
            type: string
        - name: contractorName
          in: query
          description: Based on WorkOrder ContractorName
          schema:
            type: array
            items:
              type: string
            example: ["John Doe","Sudheer Ch"]
        - name: search
          in: query
          description: Search based on workCategory,status,contractorname,type,workordernumber
          schema:
            type: string
      responses:
        "200":
          description: List of Work Order BOQ Mappings retrieved successfully
        "400":
          description: Bad request, invalid input parameters
        "500":
          description: Internal Server Error

components:
  schemas:
    createWorkOrderBOQMapping:
      type: object
      properties:
        workOrderId:
          type: integer
          description: The ID of the work order to associate with the BOQ items.
          example: 1
        boqItemIds:
          type: array
          items:
            type: integer
          description: The IDs of the BOQ items to be mapped.
          example: [1, 2, 3]

    deleteWorkOrderBOQMapping:
      type: object
      properties:
        workOrderId:
          type: integer
          description: The ID of the work order to disassociate from the BOQ item.
          example: 1
        boqItemId:
          type: integer
          description: The ID of the BOQ item to be unmapped.
          example: 1
