'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const IndentItem = sequelize.define(
    'IndentItem',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      requiredStock: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.indentItemStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.indentItemStatus.PENDING,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  IndentItem.associate = (models) => {
    IndentItem.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    IndentItem.belongsTo(models.Item, {
      foreignKey: 'itemId',
      as: 'item',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    IndentItem.belongsTo(models.Indent, {
      foreignKey: 'indentId',
      as: 'indent',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    IndentItem.belongsTo(models.ItemVariant, {
      foreignKey: 'itemVariantId',
      as: 'itemVariant',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return IndentItem;
};
