const { defaultStatus } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const BOQCategory = sequelize.define(
    'BOQCategory',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: defaultStatus.UN_ASSIGNED,
      },
      cost: {
        type: DataTypes.JSON,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  BOQCategory.associate = (models) => {
    BOQCategory.belongsTo(models.BOQMetric, {
      foreignKey: 'boqMetricId',
      as: 'boqMetric',
    });

    BOQCategory.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    BOQCategory.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    BOQCategory.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    BOQCategory.hasMany(models.BoqEntry, {
      foreignKey: 'boqSubCategoryId',
      as: 'boqItems',
      onDelete: 'CASCADE',
    });

    BOQCategory.hasMany(BOQCategory, {
      foreignKey: 'parentCategoryId',
      as: 'subCategories',
      onDelete: 'CASCADE',
    });
  };

  return BOQCategory;
};
