const {
  Grn,
  GrnItem,
  GrnMedia,
  PurchaseOrder,
  Item,
  ItemVariant,
  User,
  Warehouse,
  WarehouseItem,
  sequelize,
} = require('..');
const {
  successMessage,
  errorMessage,
  grnMediaType,
  grnStatus,
} = require('@config/options');

exports.validateAndCreateGrn = async (data, loggedInUser) => {
  const {
    items,
    vendorId,
    purchaseOrderId,
    deliveredBy,
    warehouseId,
    media,
    ...grnData
  } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const purchaseOrder = await PurchaseOrder.findOne({
      where: { id: purchaseOrderId, organizationId },
    });
    if (!purchaseOrder) {
      throw new Error(errorMessage.INVALID_ID('Purchase Order'));
    }

    if (vendorId) {
      const vendor = await User.findOne({
        where: { id: vendorId },
      });
      if (!vendor) throw new Error(errorMessage.INVALID_ID('Vendor'));
    }

    if (deliveredBy) {
      const deliveredByUser = await User.findOne({
        where: { id: deliveredBy },
      });
      if (!deliveredByUser)
        throw new Error(errorMessage.INVALID_ID('Delivered By User'));
    }

    if (warehouseId) {
      const warehouse = await Warehouse.findOne({
        where: { id: warehouseId },
      });
      if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));
    }

    const grn = await Grn.create(
      {
        ...grnData,
        purchaseOrderId,
        vendorId,
        deliveredBy,
        warehouseId,
        createdBy: loggedInUser.id,
        organizationId: organizationId,
      },
      { transaction }
    );

    if (items && items.length > 0) {
      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) throw new Error(errorMessage.INVALID_ID('Item'));

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant)
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
        }

        await GrnItem.create(
          {
            ...item,
            grnId: grn.id,
            createdBy: loggedInUser.id,
            receivedQuantity: item.totalQuantity,
          },
          { transaction }
        );
      }
    }

    // Handle GRN-level media
    if (media && media.length > 0) {
      for (const mediaItem of media) {
        await GrnMedia.create(
          {
            ...mediaItem,
            grnId: grn.id,
            mediaType: grnMediaType.GRN,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('GRN'),
      data: grn,
    };
  } catch (error) {
    console.log('error', error);
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the GRN',
    };
  }
};

exports.validateAndUpdateGrn = async (grnId, data, loggedInUser) => {
  const {
    items,
    vendorId,
    purchaseOrderId,
    deliveredBy,
    media,
    warehouseId,
    ...grnData
  } = data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const grn = await Grn.findOne({
      where: {
        id: grnId,
        organizationId: organizationId,
      },
    });

    if (!grn) {
      throw new Error(errorMessage.INVALID_ID('GRN'));
    }

    if (purchaseOrderId) {
      const purchaseOrder = await PurchaseOrder.findOne({
        where: { id: purchaseOrderId, organizationId },
      });
      if (!purchaseOrder)
        throw new Error(errorMessage.INVALID_ID('Purchase Order'));
    }

    if (vendorId) {
      const vendor = await User.findOne({
        where: { id: vendorId },
      });
      if (!vendor) throw new Error(errorMessage.INVALID_ID('Vendor'));
    }

    if (deliveredBy) {
      const deliveredByUser = await User.findOne({
        where: { id: deliveredBy },
      });
      if (!deliveredByUser)
        throw new Error(errorMessage.INVALID_ID('Delivered By User'));
    }

    if (warehouseId) {
      const warehouse = await Warehouse.findOne({
        where: { id: warehouseId },
      });
      if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));
    }

    await grn.update(
      {
        ...grnData,
        purchaseOrderId,
        vendorId,
        deliveredBy,
        warehouseId,
      },
      { transaction }
    );

    if (items && items.length > 0) {
      // Delete existing items and their media
      const existingItems = await GrnItem.findAll({
        where: { grnId: grn.id },
      });

      for (const existingItem of existingItems) {
        await GrnMedia.destroy({
          where: { grnItemId: existingItem.id },
          transaction,
        });
      }

      await GrnItem.destroy({
        where: { grnId: grn.id },
        transaction,
      });

      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) throw new Error(errorMessage.INVALID_ID('Item'));

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant)
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
        }

        await GrnItem.create(
          {
            ...item,
            grnId: grn.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    // Handle GRN-level media
    if (media) {
      // Frontend is not sending the whole payload of media
      // await GrnMedia.destroy({
      //   where: {
      //     grnId: grn.id,
      //     grnItemId: null,
      //   },
      //   transaction,
      // });

      if (media.length > 0) {
        for (const mediaItem of media) {
          await GrnMedia.create(
            {
              ...mediaItem,
              grnId: grn.id,
              mediaType: grnMediaType.GRN,
            },
            { transaction }
          );
        }
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('GRN'),
      data: grn,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the GRN',
    };
  }
};

exports.validateAndUpdateGrnStatus = async (grnId, data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const grn = await Grn.findOne({
      where: {
        id: grnId,
        organizationId: organizationId,
      },
    });

    if (!grn) {
      throw new Error(errorMessage.INVALID_ID('GRN'));
    }

    // if (grn.status === data.status) {
    //   throw new Error(errorMessage.INVALID_STATUS);
    // }

    if (data.status === grnStatus.APPROVED) {
      if (grn.warehouseId && grn.warehouseId !== null) {
        await this.updateWarehouseItemsQuantity(grnId, grn.warehouseId);
      }
    }

    data.updatedBy = loggedInUser.id;
    await grn.update(data, { transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('GRN Status'),
      data: grn,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the GRN status',
    };
  }
};

exports.updateWarehouseItemsQuantity = async (grnId, warehouseId) => {
  try {
    const GrnItems = await GrnItem.findAll({
      where: {
        grnId: grnId,
      },
    });

    for (const item of GrnItems) {
      const warehouseItem = await WarehouseItem.findOne({
        where: {
          itemId: item.itemId,
          warehouseId,
        },
      });
      if (warehouseItem) {
        let receivedQuantity = parseFloat(item.receivedQuantity);
        receivedQuantity = Math.round(receivedQuantity * 100) / 100;

        warehouseItem.availableQuantity =
          parseFloat(warehouseItem.availableQuantity) + receivedQuantity;

        await warehouseItem.save();
      } else {
        await WarehouseItem.create({
          itemId: item.itemId,
          warehouseId: warehouseId,
          availableQuantity: parseFloat(item.receivedQuantity),
        });
      }
    }
  } catch (error) {
    return {
      success: false,
      message:
        error.message ||
        'An error occurred while updating Warehouse Items quantity',
    };
  }
};

exports.validateAndUpdateGrnItemStatus = async (
  grnItemId,
  data,
  loggedInUser
) => {
  const transaction = await sequelize.transaction();
  try {
    const grnItem = await GrnItem.findOne({
      where: {
        id: grnItemId,
      },
    });

    if (!grnItem) {
      throw new Error(errorMessage.INVALID_ID('GRN Item'));
    }

    // Update the receivedBy field in the Grn table
    const grn = await Grn.findOne({
      where: {
        id: grnItem.grnId,
      },
    });

    if (grn) {
      await grn.update(
        {
          receivedBy: loggedInUser.id,
        },
        { transaction }
      );
    }

    // Update the grnItem
    await grnItem.update(data, { transaction });

    // Check if status is equal to partial_receipt
    if (data.status === 'partial_receipt') {
      const { media } = data;

      // Save media items in GrnMedia
      if (media && media.length > 0) {
        await GrnMedia.bulkCreate(
          media.map((mediaItem) => ({
            ...mediaItem,
            grnItemId: grnItem.id,
            mediaType: grnMediaType.PARTIAL_RECEIPT,
          })),
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('GRN Status'),
      data: grnItem,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the GRN status',
    };
  }
};

exports.validateAndDeleteGrn = async (grnId) => {
  const transaction = await sequelize.transaction();
  try {
    const grn = await Grn.findOne({
      where: {
        id: grnId,
      },
    });

    if (!grn) {
      throw new Error(errorMessage.INVALID_ID('GRN'));
    }

    // Delete associated GRN items
    await GrnItem.destroy({
      where: { grnId: grn.id },
      transaction,
    });

    // Delete associated GRN media
    await GrnMedia.destroy({
      where: { grnId: grn.id },
      transaction,
    });

    await grn.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('GRN'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the GRN',
    };
  }
};

exports.validateAndDeleteGrnMedia = async (grnMediaId) => {
  const transaction = await sequelize.transaction();
  try {
    const grnMedia = await GrnMedia.findOne({
      where: {
        id: grnMediaId,
      },
    });

    if (!grnMedia) {
      throw new Error(errorMessage.INVALID_ID('GRN Media'));
    }

    await grnMedia.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('GRN Media'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while deleting the GRN media',
    };
  }
};
