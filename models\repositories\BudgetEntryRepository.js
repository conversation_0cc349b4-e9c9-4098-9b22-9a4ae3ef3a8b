const { BudgetEntry } = require('..');
const { successMessage, recordStatus } = require('../../config/options');

exports.getBudget = async (query) => await BudgetEntry.findOne(query);

exports.findAndCountAll = async (query) =>
  await BudgetEntry.findAndCountAll(query);

exports.createBudgetEntry = async (objParams, transaction) => {
  try {
    const { budgetId, accountId, budgetData, type } = objParams;

    const createdBudgetEntry = await BudgetEntry.create(
      {
        budgetId,
        accountId,
        budgetEntryType: type,
        budgetData,
        status: recordStatus.ACTIVE,
      },
      {
        transaction,
      }
    );

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Budget Entry'),
      data: createdBudgetEntry,
    };
  } catch (error) {
    throw error;
  }
};

exports.updateBudgetEntry = async (objParams, transaction) => {
  try {
    const { budgetEntryId, accountId, budgetData } = objParams;

    const updateBudgetEntry = await BudgetEntry.update(
      {
        accountId,
        budgetData,
      },
      {
        where: {
          id: budgetEntryId,
        },
      },
      { transaction }
    );

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Budget Entry'),
      data: updateBudgetEntry,
    };
  } catch (error) {
    throw error;
  }
};

exports.bulkCreateBudgetEntry = async (objParams) => {
  try {
    const { arrBudgetEntry, budgetId, type } = objParams;

    const arrDataToInsert = arrBudgetEntry.map((objEachBudgetEntry) => {
      return {
        budgetId,
        accountId: objEachBudgetEntry.accountId,
        budgetEntryType: type,
        budgetData: objEachBudgetEntry.budgetData,
        budgetEntryState: accountStateStatusInfo.ACTIVE,
      };
    });

    const budgetEntryBulkCreated =
      await BudgetEntry.bulkCreate(arrDataToInsert);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Budget Entry'),
      data: budgetEntryBulkCreated,
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteBulkBudgetEntry = async (objParams, transaction) => {
  try {
    const { budgetId, budgetEntryType } = objParams;

    await BudgetEntry.update(
      {
        status: recordStatus.DELETED,
      },
      {
        where: {
          budgetId,
          budgetEntryType,
          status: recordStatus.ACTIVE,
        },
      },
      {
        transaction,
      }
    );

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Budget Entry'),
      data: {},
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteBudgetEntryData = async (budgetEntryId, transaction) => {
  try {
    await BudgetEntry.update(
      {
        status: recordStatus.DELETED,
      },
      {
        where: {
          id: budgetEntryId,
        },
      },
      {
        transaction,
      }
    );

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Budget Entry'),
      data: {},
    };
  } catch (error) {
    throw error;
  }
};
