const {
  Indent,
  IndentItem,
  BoqEntry,
  WorkOrder,
  Tasks,
  Warehouse,
  Item,
  ItemVariant,
  sequelize,
} = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { collectChanges, logActivities } = require('@helpers/ActivityLogHelper');

exports.validateAndCreateIndent = async (data, loggedInUser) => {
  const { items, boqId, workOrderId, taskId, warehouseId, ...indentData } =
    data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    if (boqId) {
      const boqEntry = await BoqEntry.findOne({
        where: { id: boqId, organizationId },
      });
      if (!boqEntry) throw new Error(errorMessage.INVALID_ID('Boq'));
    }

    if (workOrderId) {
      const workOrder = await WorkOrder.findOne({
        where: { id: workOrderId, organizationId },
      });
      if (!workOrder) throw new Error(errorMessage.INVALID_ID('Work Order'));
    }

    if (taskId) {
      const task = await Tasks.findOne({
        where: { id: taskId },
      });
      if (!task) throw new Error(errorMessage.INVALID_ID('Task'));
    }

    const warehouse = await Warehouse.findOne({
      where: { id: warehouseId, organizationId },
    });
    if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));

    const indent = await Indent.create(
      {
        ...indentData,
        boqId,
        workOrderId,
        taskId,
        warehouseId,
        createdBy: loggedInUser.id,
        organizationId: organizationId,
      },
      { transaction }
    );

    if (items && items.length > 0) {
      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) throw new Error(errorMessage.INVALID_ID('Item'));

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant)
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
        }

        await IndentItem.create(
          {
            ...item,
            indentId: indent.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Indent'),
      data: indent,
    };
  } catch (error) {
    console.log('error', error);
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the indent',
    };
  }
};

exports.validateAndUpdateIndent = async (indentId, data, loggedInUser) => {
  const { items, boqId, workOrderId, taskId, warehouseId, ...indentData } =
    data;
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const indent = await Indent.findOne({
      where: {
        id: indentId,
        organizationId: organizationId,
      },
    });

    if (!indent) {
      throw new Error(errorMessage.INVALID_ID('Indent'));
    }

    if (boqId) {
      const boqEntry = await BoqEntry.findOne({
        where: { id: boqId, organizationId },
      });
      if (!boqEntry) throw new Error(errorMessage.INVALID_ID('Boq'));
    }

    if (workOrderId) {
      const workOrder = await WorkOrder.findOne({
        where: { id: workOrderId, organizationId },
      });
      if (!workOrder) throw new Error(errorMessage.INVALID_ID('Work Order'));
    }

    if (taskId) {
      const task = await Tasks.findOne({
        where: { id: taskId },
      });
      if (!task) throw new Error(errorMessage.INVALID_ID('Task'));
    }

    if (warehouseId) {
      const warehouse = await Warehouse.findOne({
        where: { id: warehouseId, organizationId },
      });
      if (!warehouse) throw new Error(errorMessage.INVALID_ID('Warehouse'));
    }

    // Collect changes
    const fieldsToCheck = [
      'boqId',
      'workOrderId',
      'taskId',
      'warehouseId',
      'requiredByDate',
      'priority',
      'note',
    ];
    const changes = await collectChanges(indent, data, fieldsToCheck);
    await logActivities(indent, changes, loggedInUser, transaction);

    await indent.update(
      { ...indentData, boqId, workOrderId, taskId, warehouseId },
      { transaction }
    );

    if (items && items.length > 0) {
      await IndentItem.destroy({
        where: { indentId: indent.id },
        transaction,
      });

      for (const item of items) {
        const itemRecord = await Item.findOne({
          where: { id: item.itemId, organizationId },
        });
        if (!itemRecord) throw new Error(errorMessage.INVALID_ID('Item'));

        if (item.itemVariantId) {
          const itemVariant = await ItemVariant.findOne({
            where: {
              id: item.itemVariantId,
              itemId: item.itemId,
              organizationId,
            },
          });
          if (!itemVariant)
            throw new Error(errorMessage.INVALID_ID('Item Variant'));
        }

        await IndentItem.create(
          {
            ...item,
            indentId: indent.id,
            createdBy: loggedInUser.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Indent'),
      data: indent,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the indent',
    };
  }
};

exports.validateAndUpdateIndentStatus = async (
  indentId,
  data,
  loggedInUser
) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const transaction = await sequelize.transaction();
  try {
    const indent = await Indent.findOne({
      where: {
        id: indentId,
        organizationId: organizationId,
      },
    });

    if (!indent) {
      throw new Error(errorMessage.INVALID_ID('Indent'));
    }

    // Collect changes
    const fieldsToCheck = ['status'];
    const changes = await collectChanges(indent, data, fieldsToCheck);
    await logActivities(indent, changes, loggedInUser, transaction);

    await indent.update(data, { transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Item Status'),
      data: indent,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the item',
    };
  }
};

exports.validateAndDeleteIndent = async (indentId) => {
  const transaction = await sequelize.transaction();
  try {
    const indent = await Indent.findOne({
      where: {
        id: indentId,
      },
    });

    if (!indent) {
      throw new Error(errorMessage.INVALID_ID('Indent'));
    }

    await IndentItem.destroy({
      where: { indentId: indent.id },
      transaction,
    });

    await indent.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Indent'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the indent',
    };
  }
};

exports.validateAndUpdateIndentItemStatus = async (indentItemId, data) => {
  const transaction = await sequelize.transaction();
  try {
    const indentItem = await IndentItem.findOne({
      where: {
        id: indentItemId,
      },
    });

    if (!indentItem) {
      throw new Error(errorMessage.INVALID_ID('Indent Item'));
    }

    await indentItem.update(data, { transaction });

    await transaction.commit();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Item Status'),
      data: indentItem,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the item',
    };
  }
};
