'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_userId_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'BankDetail_userId_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_organizationId_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['organizationId'],
      type: 'foreign key',
      name: 'BankDetail_organizationId_fkey',
      references: {
        table: 'Organization',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_createdBy_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['createdBy'],
      type: 'foreign key',
      name: 'BankDetail_createdBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_updatedBy_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['updatedBy'],
      type: 'foreign key',
      name: 'BankDetail_updatedBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    // Revert changes in the down method
    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_userId_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['userId'],
      type: 'foreign key',
      name: 'BankDetail_userId_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_organizationId_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['organizationId'],
      type: 'foreign key',
      name: 'BankDetail_organizationId_fkey',
      references: {
        table: 'Organization',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_createdBy_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['createdBy'],
      type: 'foreign key',
      name: 'BankDetail_createdBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      allowNull: true,
    });

    await queryInterface.removeConstraint(
      'BankDetail',
      'BankDetail_updatedBy_fkey'
    );

    await queryInterface.addConstraint('BankDetail', {
      fields: ['updatedBy'],
      type: 'foreign key',
      name: 'BankDetail_updatedBy_fkey',
      references: {
        table: 'User',
        field: 'id',
      },
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
      allowNull: true,
    });
  },
};
