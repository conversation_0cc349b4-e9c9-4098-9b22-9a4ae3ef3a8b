'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    // Modify the existing table to allow NULL in specific columns
    await queryInterface.changeColumn('DesignationMedia', 'fileName', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });
    await queryInterface.changeColumn('DesignationMedia', 'fileType', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });
    await queryInterface.changeColumn('DesignationMedia', 'inputValue', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.changeColumn('DesignationMedia', 'filePath', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.changeColumn('DesignationMedia', 'fileSize', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    // Revert the changes if needed
    await queryInterface.changeColumn('DesignationMedia', 'fileName', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });
    await queryInterface.changeColumn('DesignationMedia', 'fileType', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });
    await queryInterface.changeColumn('DesignationMedia', 'filePath', {
      type: Sequelize.TEXT,
      allowNull: false,
    });
    await queryInterface.changeColumn('DesignationMedia', 'inputValue', {
      type: Sequelize.TEXT,
      allowNull: false,
    });
    await queryInterface.changeColumn('DesignationMedia', 'fileSize', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: false,
    });
  },
};
