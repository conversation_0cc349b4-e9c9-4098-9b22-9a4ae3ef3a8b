const express = require('express');
const router = express.Router();
const AuthControl = require('../../../../controllers/api/v1/admin/Auth');
const AuthSchema = require('../../../../schema-validation/admin/Auth');
const { checkSchema } = require('express-validator');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.post(
  '/login',
  checkSchema(AuthSchema.passwordLogin),
  ErrorHandleHelper.requestValidator,
  AuthControl.login
);

router.post(
  '/forgot-password',
  checkSchema(AuthSchema.resetPasswordRequest),
  ErrorHandleHelper.requestValidator,
  AuthControl.sendResetPasswordEmailOTP
);

router.post(
  '/verify-forget-password',
  checkSchema(AuthSchema.verifyOtpRequest),
  ErrorHandleHelper.requestValidator,
  AuthControl.verifyResetPasswordEmailOTP
);

router.post(
  '/reset-password',
  checkSchema(AuthSchema.resetPassword),
  ErrorHandleHelper.requestValidator,
  AuthControl.resetPasswordEmailByEmailOtp
);

module.exports = router;
