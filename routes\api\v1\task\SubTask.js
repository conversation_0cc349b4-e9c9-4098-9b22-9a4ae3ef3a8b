const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const SubTaskController = require('@controllers/v1/task/SubTask');
const SubTaskSchema = require('@schema-validation/task/SubTask');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/:id/subtask',
  checkSchema(SubTaskSchema.createSubTaskValidation),
  ErrorHandleHelper.requestValidator,
  SubTaskController.createSubTask
);

router.patch(
  '/subtask/:id',
  checkSchema(SubTaskSchema.updateSubTaskValidation),
  ErrorHandleHelper.requestValidator,
  SubTaskController.updateSubTask
);

router.delete(
  '/subtask/:id',
  ErrorHandleHelper.requestValidator,
  SubTaskController.deleteSubTask
);

module.exports = router;
