const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const BudgetController = require('../../../../controllers/api/v1/accounts/Budget');

const BudgetSchema = require('../../../../schema-validation/account/Budget');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(BudgetSchema.createJournal),
  ErrorHandleHelper.requestValidator,
  BudgetController.createBudget
);

router.get(
  '/',
  checkSchema(BudgetSchema.getBudget),
  ErrorHandleHelper.requestValidator,
  BudgetController.getBudgetList
);

router.get(
  '/:budgetId',
  checkSchema(BudgetSchema.getBudgetById),
  ErrorHandleHelper.requestValidator,
  BudgetController.getBudgetById
);

router.delete(
  '/:budgetId',
  checkSchema(BudgetSchema.deleteBudget),
  ErrorHandleHelper.requestValidator,
  BudgetController.deleteBudget
);

router.put(
  '/:budgetId',
  checkSchema(BudgetSchema.updateJournal),
  ErrorHandleHelper.requestValidator,
  BudgetController.updateBudgetDetails
);

module.exports = router;
