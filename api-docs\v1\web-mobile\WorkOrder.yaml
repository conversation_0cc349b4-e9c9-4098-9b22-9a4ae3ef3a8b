paths:
  /project/work-order/type:
    post:
      summary: Creates a new work order type
      description: Creates a new work order type with the provided details including name.
      operationId: createWorkOrderType
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createWorkOrderType"
      responses:
        "201":
          description: Work order type created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/{id}/work-order:
    post:
      summary: Creates a new work order for a project
      description: Creates a new work order for the specified project using the provided work order details, including work order number, type, and schedule date.
      operationId: createWorkOrder
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the project to which the unit belongs."
          schema:
            type: integer
            example: 456
      tags:
        - Project
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createWorkOrder"
      responses:
        "201":
          description: Work order created successfully
        "400":
          description: Invalid request or missing fields
        "500":
          description: Internal Server Error

  /project/work-order/{id}:
    put:
      summary: Update an existing work order
      description: Updates the details of a specified work order, including the work order number, project ID, work order type, and schedule date.
      operationId: updateWorkOrder
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the work order to be updated.
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateWorkOrder"
      responses:
        "200":
          description: Work order updated successfully
        "400":
          description: Invalid request
        "404":
          description: Work order not found
        "500":
          description: Internal Server Error

  /project/work-order/{id}/details:
    get:
      summary: Retrieves a list of work orders for a project
      description: Fetches the work order details including work order number, project name, work order type, and schedule for a specific project.
      operationId: listWorkOrders
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: The ID of the workorder
          required: true
          schema:
            type: integer
      responses:
        "200":
          description: List of work orders retrieved successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error

  /project/work-order/status-count:
    get:
      tags:
        - "Project"
      summary: "Get Workorder Status Count"
      description: "This endpoint returns the count of workorder records grouped by their status."
      operationId: "GetWorkorderStatusCount"
      security:
        - bearerAuth: []
      parameters:
        - name: projectId
          in: query
          required: false
          description: ID of the project to fetch status count
          schema:
            type: integer
            example: 1
        - name: organizationId
          in: query
          required: false
          description: ID of the Organization to fetch status count
          schema:
            type: integer
            example: 1
      responses:
        "200":
          description: "Successfully fetched the workorder status count."
        "400":
          description: "Bad request. Invalid parameters provided."
        "500":
          description: "Internal Server Error."

  /project/work-order/{id}/allot:
    patch:
      summary: Allot contractorId to workorder
      description: Allot the Workorder with contractorId and update the status for it
      operationId: allotWorkOrder
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the work order to be updated.
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/allotWorkOrder"
      responses:
        "200":
          description: Work order allotted successfully
        "400":
          description: Invalid request
        "404":
          description: Work order not found
        "500":
          description: Internal Server Error

components:
  schemas:
    createWorkOrderType:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          description: The name of the work order type
          example: Contractor

    createWorkOrder:
      type: object
      properties:
        workOrderNumber:
          type: string
          description: The unique work order number
          example: "WO12345"
        workOrderType:
          type: integer
          description: The type of the work order (reference to WorkOrderType)
          example: 2
        fromDate:
          type: string
          format: date
          description: The start date for the work order
          example: "2024-12-01"
        toDate:
          type: string
          format: date
          description: The end date for the work order
          example: "2024-12-10"

    updateWorkOrder:
      type: object
      properties:
        workOrderNumber:
          type: string
          description: Work order number
          example: "WO-12345"
        projectId:
          type: integer
          description: Project ID the work order is associated with
          example: 1
        workOrderType:
          type: integer
          description: Type of the work order (e.g., regular, emergency)
          example: 2
        termsAndCondition:
          type: string
          description: terms and condition
          example: "All services will be delivered as per the agreed schedule and guidelines."
        customDetails:
          type: object
          description: Additional metadata or information related to the work order
          example: 
            additionalInfo: "This work order requires special approval"
            notes: "Ensure environmental compliance during the project"
            properties:
              additionalInfo:
                type: string
                description: Additional details about the work order
                example: "This work order requires special approval"
              notes:
                type: string
                description: Additional notes for the work order
                example: "Ensure environmental compliance during the project"
        activities:
          type: array
          description: List of activities associated with the work order
          items:
            type: object
            properties:
              name:
                type: string
                description: Name of the activity
                example: "Excavation Work"
              quantity:
                type: number
                format: float
                description: Quantity of the activity
                example: 100
              unit:
                type: string
                description: Unit of measurement for the activity
                example: "kg"
              rate:
                type: number
                format: float
                description: Rate per unit for the activity
                example: 10.5
              amount:
                type: number
                format: float
                description: Total amount for the activity
                example: 1050
              description:
                type: string
                description: Description of the activity
                example: "Excavation work required for foundation"
              categoryName:
                type: string
                description: Name of the category
                example: "Earthwork"
              subCategoryName:
                type: string
                description: Name of the subcategory
                example: "Excavation"
              categoryId:
                type: integer
                description: ID of the category
                example: 1
              subCategoryId:
                type: integer
                description: ID of the subcategory
                example: 2
              status:
                type: string
                description: Status of the activity (e.g., active, completed)
                example: "active"
              isCompleted:
                type: boolean
                description: Whether the activity is completed
                example: false
        workOrderValue:
          type: number
          format: float
          description: Total value of all activities in the work order
          example: 5000

    allotWorkOrder:
      type: object
      properties:
        contractorId:
          type: integer
          description: ContractorId
          example: 1


   
