'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const PurchaseOrder = sequelize.define(
    'PurchaseOrder',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      requiredByDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      priority: {
        type: DataTypes.ENUM(OPTIONS.purchaseOrderPriority.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.purchaseOrderPriority.MEDIUM,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.purchaseOrderStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.purchaseOrderStatus.PENDING,
      },
      subTotal: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      taxRates: {
        type: DataTypes.JSONB,
        allowNull: true,
        defaultValue: {},
      },
      discount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
      total: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0.0,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  PurchaseOrder.associate = (models) => {
    PurchaseOrder.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.User, {
      foreignKey: 'approvedBy',
      as: 'approver',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.User, {
      foreignKey: 'vendorId',
      as: 'vendor',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.Indent, {
      foreignKey: 'indentId',
      as: 'indent',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.WorkOrder, {
      foreignKey: 'workOrderId',
      as: 'workOrder',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'tasks',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.belongsTo(models.Warehouse, {
      foreignKey: 'warehouseId',
      as: 'deliveryLocation',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    PurchaseOrder.hasMany(models.PurchaseOrderItem, {
      foreignKey: 'purchaseOrderId',
      as: 'purchaseOrderItem',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return PurchaseOrder;
};
