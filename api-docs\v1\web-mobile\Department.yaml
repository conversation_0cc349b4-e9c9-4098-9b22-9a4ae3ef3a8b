paths:
  /settings/department:
    post:
      tags:
        - "Settings"
      summary: "Create a new Department"
      description: "This endpoint allows you to create a new department by providing all necessary details."
      operationId: "CreateDepartment"
      requestBody:
        description: "The details of the new department to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/DepartmentCreateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Department has been created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
        "400":
          description: "Invalid input data or department already exists."
        "500":
          description: "Internal Server Error"

  /settings/department/{id}:      
    put:
      tags:
        - "Settings"
      summary: "Update an existing Department"
      description: "This endpoint allows you to update the details of an existing department by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateDepartment"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the department to be updated."
          schema:
            type: string
            example: "12345"
      requestBody:
        description: "The updated information for the department. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/DepartmentUpdateRequest"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Department has been updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Department'
        "400":
          description: "Invalid input data, department not found, or department already exists."
        "404":
          description: "Department not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Settings"
      summary: "Delete an existing Department"
      description: "This endpoint allows you to delete an existing department by providing its `id` in the URL path."
      operationId: "DeleteDepartment"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the department to be deleted."
          schema:
            type: string
            example: "12345"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Department has been deleted successfully."
        "400":
          description: "Invalid input data or department not found."
        "404":
          description: "Department not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    Department:
      type: object
    DepartmentCreateRequest:
      type: object
      required:
        - name
      properties:
        name:
          type: string
          example: "Human Resources"
        description:
          type: string
          example: "Handles recruitment and employee relations"
    DepartmentUpdateRequest:
      type: object
      properties:
        name:
          type: string
          example: "Human Resources"
        description:
          type: string
          example: "Handles recruitment and employee relations"