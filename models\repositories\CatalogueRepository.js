const { Op } = require('sequelize');
const {
  Catalogue,
  CatalogueMedia,
  MarketplaceCategory,
  sequelize,
} = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.validateAndCreateCatalogue = async (data, loggedInUser) => {
  const { title, categoryId, media } = data;
  const transaction = await sequelize.transaction();
  try {
    const existingCatalogue = await Catalogue.findOne({
      where: {
        title: {
          [Op.iLike]: title,
        },
      },
    });

    if (existingCatalogue) {
      throw new Error(
        errorMessage.ALREADY_EXIST(
          `Catalogue with title: ${title} already exists`
        )
      );
    }

    const category = await MarketplaceCategory.findOne({
      where: {
        id: categoryId,
      },
    });

    if (!category) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Marketplace Category'));
    }

    const catalogue = await Catalogue.create(
      {
        ...data,
        brandId: loggedInUser.id,
        marketplaceCategoryId: categoryId,
      },
      { transaction }
    );

    if (media && media.length > 0) {
      for (const mediaItem of media) {
        await CatalogueMedia.create(
          {
            ...mediaItem,
            catalogueId: catalogue.id,
          },
          { transaction }
        );
      }
    }

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Catalogue'),
      data: catalogue,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while creating the catalogue',
    };
  }
};

exports.validateAndUpdateCatalogue = async (catalogueId, data) => {
  const { categoryId, media } = data;
  const transaction = await sequelize.transaction();
  try {
    const catalogue = await Catalogue.findOne({
      where: {
        id: catalogueId,
      },
    });

    if (!catalogue) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Catalogue'));
    }

    if (categoryId) {
      const category = await MarketplaceCategory.findOne({
        where: {
          id: categoryId,
        },
      });

      if (!category) {
        throw new Error(errorMessage.DOES_NOT_EXIST('Marketplace Category'));
      }
      data.marketplaceCategoryId = categoryId;
    }

    await catalogue.update(data, { transaction });

    if (media && media.length > 0) {
      for (const mediaItem of media) {
        await CatalogueMedia.create(
          {
            ...mediaItem,
            catalogueId: catalogue.id,
          },
          { transaction }
        );
      }
    }
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Catalogue'),
      data: catalogue,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the catalogue',
    };
  }
};

exports.validateAndDeleteCatalogue = async (catalogueId) => {
  const transaction = await sequelize.transaction();
  try {
    const catalogue = await Catalogue.findOne({
      where: {
        id: catalogueId,
      },
    });

    if (!catalogue) {
      throw new Error(errorMessage.DOES_NOT_EXIST('Catalogue'));
    }

    await catalogue.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Catalogue'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while deleting the catalogue',
    };
  }
};

exports.validateAndDeleteCatalogueMedia = async (catalogueMediaId) => {
  const transaction = await sequelize.transaction();
  try {
    const catalogueMedia = await CatalogueMedia.findOne({
      where: {
        id: catalogueMediaId,
      },
    });

    if (!catalogueMedia) {
      throw new Error(errorMessage.INVALID_ID('Catalogue Media'));
    }

    await catalogueMedia.destroy({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Catalogue Media'),
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message:
        error.message || 'An error occurred while deleting the Catalogue media',
    };
  }
};
