paths:
  /settings/security/change-password:
    patch:
      tags:
        - "Settings"
      summary: "change password in Setting"
      description: "Change Password for the User in Setting Screen"
      operationId: "changePassword-setting" 
      requestBody:
        description: change password for setting
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/change-password-setting"
        required: true
      produces:
        - "application/json" 
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Password saved successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    change-password-setting:
      type: object
      properties:
        newPassword:
            type: string
            description: enter new password
        currentPassword:
            type: string
            description: enter current password
        confirmPassword:
          type: string
          description: enter confirm password
      required:
        - password
        - confirmPassword