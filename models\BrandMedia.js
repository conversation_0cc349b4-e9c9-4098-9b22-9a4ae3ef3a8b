'use strict';

module.exports = (sequelize, DataTypes) => {
  const BrandMedia = sequelize.define(
    'BrandMedia',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileName: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileType: {
        type: DataTypes.STRING(100),
        allowNull: true,
      },
      filePath: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      fileSize: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      tableName: 'BrandMedia',
      timestamps: true,
    }
  );

  BrandMedia.associate = function (models) {
    BrandMedia.belongsTo(models.Brand, {
      foreignKey: 'brandId',
      as: 'brand',
      onDelete: 'CASCADE',
    });
  };

  return BrandMedia;
};
