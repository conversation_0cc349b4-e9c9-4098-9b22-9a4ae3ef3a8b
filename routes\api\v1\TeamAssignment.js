const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TeamAssignmentController = require('@controllers/v1/crm/TeamAssignment');
const TeamAssignmentSchema = require('@schema-validation/TeamAssignment');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(TeamAssignmentSchema.createTeamAssignment),
  ErrorHandleHelper.requestValidator,
  TeamAssignmentController.createTeamAssignment
);

router.put(
  '/:id',
  checkSchema(TeamAssignmentSchema.updateTeamAssignment),
  ErrorHandleHelper.requestValidator,
  TeamAssignmentController.updateTeamAssignment
);

module.exports = router;
