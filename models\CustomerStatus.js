'use strict';

const { customerStatusTriggerType } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const CustomerStatus = sequelize.define(
    'CustomerStatus',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      triggerType: {
        type: DataTypes.ENUM(
          ...customerStatusTriggerType.customerStatusTriggerTypeArray()
        ),
        allowNull: false,
      },
      dueOn: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      isApprovedRequired: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  CustomerStatus.associate = function (models) {
    CustomerStatus.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });
    CustomerStatus.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });
  };

  return CustomerStatus;
};
