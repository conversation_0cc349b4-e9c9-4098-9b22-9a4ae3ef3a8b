paths:
  /crm/notes:
    post:
      tags:
        - "CRM"
      summary: "Create a new Note"
      description: "This endpoint allows you to create a new note associated with a specific customer."
      operationId: "CreateNote"
      requestBody:
        description: "The details of the note to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/note"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Note has been created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/note'
        "400":
          description: "Invalid input data or note already exists."
        "500":
          description: "Internal Server Error"

  /crm/notes/{id}:      
    put:
      tags:
        - "CRM"
      summary: "Update an existing Note"
      description: "This endpoint allows you to update an existing note by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateNote"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the note to be updated."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The updated information for the note. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/note-update"
        required: false
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Note has been updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/note'
        "400":
          description: "Invalid input data, note not found, or note already exists."
        "404":
          description: "Note not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "CRM"
      summary: "Delete an existing Note"
      description: "This endpoint allows you to delete an existing note by providing its `id` in the URL path."
      operationId: "DeleteNote"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the note to be deleted."
          schema:
            type: integer
            example: 123
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Note has been deleted successfully."
        "400":
          description: "Invalid input data or note not found."
        "404":
          description: "Note not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    note:
      type: object
      properties:
        customerId:
          type: integer
          description: "The ID of the associated customer."
          example: 101
        note:
          type: string
          description: "The content of the note."
          example: "This is an important note."
      required:
        - customerId
        - note
      additionalProperties: false
    note-update:
      type: object
      properties:
        note:
          type: string
          description: "The content of the updated note."
          example: "This is an updated note."
      required: []
      additionalProperties: false
