module.exports = (sequelize, DataTypes) => {
  const WbsDocument = sequelize.define(
    'WbsDocument',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      documentUrl: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WbsDocument.associate = (models) => {
    WbsDocument.belongsTo(models.WbsActivity, {
      foreignKey: 'wbsActivityId',
      as: 'wbsActivity',
    });

    WbsDocument.belongsTo(models.Document, {
      foreignKey: 'documentId',
      as: 'document',
      allowNull: true,
    });

    WbsDocument.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    WbsDocument.belongsTo(models.User, {
      foreignKey: 'updatedBy',
      as: 'updater',
    });
  };

  return WbsDocument;
};
