exports.addRequestDocuments = {
  requestId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Request ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Request ID must be an integer',
    },
    toInt: true,
  },
  'document.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'document.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'document.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'document.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};
