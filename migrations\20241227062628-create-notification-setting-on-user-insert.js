'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION create_notification_settings_on_user_insert()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Insert an entry for each category, defaulting organizationId to NULL if not provided
        INSERT INTO "NotificationSettings" (
          "userId",
          "organizationId",
          "category",
          "taskUpdate",
          "systemAnnouncement",
          "securityUpdates",
          "projectProgress",
          "alertsCriticalEvents",
          "createdAt",
          "updatedAt"
        )
        VALUES 
          (NEW.id, COALESCE(NEW."organizationId", NULL), 'general', false, false, false, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
          (NEW.id, COALESCE(NEW."organizationId", NULL), 'email', false, false, false, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
          (NEW.id, COALESCE(NEW."organizationId", NULL), 'push', false, false, false, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP),
          (NEW.id, COALESCE(NEW."organizationId", NULL), 'sms_whatsapp', false, false, false, false, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);

        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await queryInterface.sequelize.query(`
      CREATE TRIGGER user_notification_settings_creation
      AFTER INSERT ON "User"
      FOR EACH ROW
      EXECUTE FUNCTION create_notification_settings_on_user_insert();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `DROP TRIGGER IF EXISTS user_notification_settings_creation ON "User";`
    );

    await queryInterface.sequelize.query(
      `DROP FUNCTION IF EXISTS create_notification_settings_on_user_insert();`
    );
  },
};
