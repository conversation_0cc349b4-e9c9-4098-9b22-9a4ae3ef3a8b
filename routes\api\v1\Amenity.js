const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const AmenityController = require('../../../controllers/api/v1/Amenity');
const AmenitySchema = require('../../../schema-validation/Amenity');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(AmenitySchema.createAmenity),
  ErrorHandleHelper.requestValidator,
  AmenityController.createAmenity
);

router.put(
  '/:id',
  checkSchema(AmenitySchema.updateAmenity),
  ErrorHandleHelper.requestValidator,
  AmenityController.updateAmenity
);

module.exports = router;
