exports.registerOrganizationBrand = {
  name: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'organizationBrandMedia.*.fileName': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'fileName is required',
    },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
  'organizationBrandMedia.*.fileType': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'fileType is required',
    },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'organizationBrandMedia.*.filePath': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'filePath is required',
    },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'organizationBrandMedia.*.fileSize': {
    in: ['body'],
    notEmpty: {
      errorMessage: 'fileSize is required',
    },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'organizationBrandMedia.*.description': {
    in: ['body'],
    isString: {
      errorMessage: 'description must be string',
    },
  },
};
