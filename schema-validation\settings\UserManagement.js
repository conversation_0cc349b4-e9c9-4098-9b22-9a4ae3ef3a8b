const { inviteAction } = require('@config/options');

exports.inviteUser = {
  email: {
    in: ['body'],
    notEmpty: true,
    isEmail: {
      errorMessage: 'Email must be a valid email address',
    },
  },
  roleId: {
    in: ['body'],
    notEmpty: true,
    isInt: {
      errorMessage: 'roleId must be a valid integer',
    },
  },
  projectId: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'projectId must be an array',
    },
    custom: {
      options: (value) => {
        if (value && value.length > 0) {
          for (const id of value) {
            if (!Number.isInteger(id)) {
              throw new Error('Each projectId must be a valid integer');
            }
          }
        }
        return true;
      },
    },
  },
};

exports.resendInvitation = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Invite Id is required',
    },
    isInt: {
      errorMessage: 'Invite Id must be a valid integer',
    },
  },
};

exports.manageInvite = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Invite Id is required',
    },
    isInt: {
      errorMessage: 'Invite Id must be a valid integer',
    },
  },
  action: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Action is required',
    },
    custom: {
      options: (value) => {
        const allowedValues = inviteAction.getInviteActionArray();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid action: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid action provided',
    },
  },
};

exports.deleteInvite = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Invite Id is required',
    },
    isInt: {
      errorMessage: 'Invite Id must be a valid integer',
    },
  },
};
