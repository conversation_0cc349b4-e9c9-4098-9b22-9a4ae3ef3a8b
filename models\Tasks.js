const { taskStatus } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Tasks = sequelize.define(
    'Tasks',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      title: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(taskStatus.getValues()),
        allowNull: false,
        defaultValue: taskStatus.TODO,
      },
      startDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      endDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      projectId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Project',
          key: 'id',
        },
        allowNull: true,
      },
      assignedTo: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      assignedBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      updatedBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Tasks.associate = (models) => {
    Tasks.belongsToMany(models.TaskTags, {
      through: 'TaskTagMapping',
      foreignKey: 'taskId',
      otherKey: 'tagId',
      as: 'taskTags',
    });
    Tasks.belongsToMany(models.Unit, {
      through: 'TaskUnitMapping',
      foreignKey: 'taskId',
      otherKey: 'unitId',
      as: 'units',
    });
    Tasks.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });
    Tasks.belongsTo(models.User, {
      foreignKey: 'assignedTo',
      as: 'assignedToUser',
    });
    Tasks.belongsTo(models.User, {
      foreignKey: 'assignedBy',
      as: 'assignedByUser',
    });
    Tasks.hasMany(models.TaskDependency, {
      foreignKey: 'taskId',
      as: 'dependencies',
    });
    Tasks.hasMany(models.SubTasks, {
      foreignKey: 'taskId',
      as: 'subTasks',
    });
    Tasks.belongsToMany(models.User, {
      through: 'TaskFollowers',
      foreignKey: 'taskId',
      otherKey: 'userId',
      as: 'followers',
    });
    Tasks.hasMany(models.ActivityLog, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        activityOn: 'Task',
      },
      as: 'activities',
    });
    Tasks.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });
  };

  return Tasks;
};
