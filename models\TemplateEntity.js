'use strict';
const {
  templateEntityType,
  durationType,
  calculationType,
} = require('../config/options');

/** @type {import('sequelize-cli').ModelAttributes} */
module.exports = (sequelize, DataTypes) => {
  const TemplateEntity = sequelize.define(
    'TemplateEntity',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      entityType: {
        type: DataTypes.ENUM(templateEntityType.getTemplateEntityTypeArray()),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      isVisible: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      monthlyAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      annualAmount: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      calculationType: {
        type: DataTypes.ENUM(calculationType.getCalculationTypeArray()),
        allowNull: false,
      },
      maxAllowed: {
        type: DataTypes.DECIMAL(3, 1),
        allowNull: true,
      },
      durationType: {
        type: DataTypes.ENUM(durationType.getDurationTypeArray()),
        allowNull: true,
      },
      calculation: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TemplateEntity.associate = function (models) {
    TemplateEntity.belongsTo(models.TemplateMaster, {
      foreignKey: 'templateId',
      as: 'templateMaster',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });
  };

  return TemplateEntity;
};
