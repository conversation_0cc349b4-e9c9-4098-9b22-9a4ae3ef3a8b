'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('EmployeeTemplate', {
      fields: ['employeeId', 'templateId'],
      type: 'unique',
      name: 'unique_employee_template',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'EmployeeTemplate',
      'unique_employee_template'
    );
  },
};
