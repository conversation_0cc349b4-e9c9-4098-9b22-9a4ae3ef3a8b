const { Workspace } = require('..');
const { errorMessage } = require('../../config/options');

exports.findOne = async (query) => await Workspace.findOne(query);
exports.findAndCountAll = async (query = {}) => {
  return await Workspace.findAndCountAll(query);
};
exports.findAll = async (query = {}) => {
  return await Workspace.findAll(query);
};
exports.checkExistWorkspaceName = async (name) => {
  try {
    const existingWorkspace = await Workspace.findOne({
      where: { name },
      attributes: [
        'id',
        'name',
        'category',
        'amount',
        'totalAmount',
        'discountAmount',
        'description',
      ],
    });

    return {
      success: !existingWorkspace,
      message: existingWorkspace
        ? errorMessage.ALREADY_EXIST('workspace')
        : null,
    };
  } catch (e) {
    throw new Error(e);
  }
};
