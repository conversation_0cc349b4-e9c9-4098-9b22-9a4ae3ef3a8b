const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const PurchaseOrderController = require('@controllers/v1/material/PurchaseOrder');
const PurchaseOrderSchema = require('@schema-validation/material/PurchaseOrder');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(PurchaseOrderSchema.createPurchaseOrder),
  ErrorHandleHelper.requestValidator,
  PurchaseOrderController.createPurchaseOrder
);

router.put(
  '/:id',
  checkSchema(PurchaseOrderSchema.updatePurchaseOrder),
  ErrorHandleHelper.requestValidator,
  PurchaseOrderController.updatePurchaseOrder
);

router.patch(
  '/:id',
  checkSchema(PurchaseOrderSchema.updatePurchaseOrderStatus),
  ErrorHandleHelper.requestValidator,
  PurchaseOrderController.updatePurchaseOrderStatus
);

router.delete(
  '/:id',
  checkSchema(PurchaseOrderSchema.deletePurchaseOrder),
  ErrorHandleHelper.requestValidator,
  PurchaseOrderController.deletePurchaseOrder
);

module.exports = router;
