'use strict';
const { templateEntityType, durationType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('EmployeeTemplateItem', 'name', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeTemplateItem', 'entityType', {
      type: Sequelize.ENUM(templateEntityType.getTemplateEntityTypeArray()),
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeTemplateItem', 'durationType', {
      type: Sequelize.ENUM(durationType.getDurationTypeArray()),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('EmployeeTemplateItem', 'name');
    await queryInterface.removeColumn('EmployeeTemplateItem', 'entityType');
    await queryInterface.removeColumn('EmployeeTemplateItem', 'durationType');
  },
};
