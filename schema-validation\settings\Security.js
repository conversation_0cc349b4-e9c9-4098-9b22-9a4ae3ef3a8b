exports.changePassword = {
  newPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'New password cannot be empty',
  },
  currentPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Current password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'confirm password cannot be empty',
    custom: {
      options: (value, { req }) =>
        req.body.newPassword === req.body.confirmPassword,
      errorMessage: 'confirm password does not match',
    },
  },
};
