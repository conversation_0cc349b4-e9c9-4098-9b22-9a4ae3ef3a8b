module.exports = (sequelize, DataTypes) => {
  const RequestComment = sequelize.define(
    'RequestComment',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      comment: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  RequestComment.associate = (models) => {
    RequestComment.belongsTo(models.Request, {
      foreignKey: 'requestId',
      as: 'request',
      onDelete: 'CASCADE',
    });
    RequestComment.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return RequestComment;
};
