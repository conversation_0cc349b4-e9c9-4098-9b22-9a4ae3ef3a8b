paths:
  /request:
    post:
      summary: Create a new Request
      description: |
        Endpoint to create a new request with required and optional fields.
        
        **Notes:**
        - For `requestType` **general_request**, `recordId` is not needed.
        - Refer to the below enums for valid `requestType` values:
          - general_request
          - leave_request
          - payment_request
          - indent_mr_request
          - stock_adjustment_request
          - work_order_request
          - wbs_request
          - journal_request
          - budget_request
          - expense_request
          - credit_note_request
          - quotation_request
      operationId: createRequest
      tags:
        - Request
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/createRequest"
      responses:
        "201":
          description: Request created successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
    get:
      summary: List requests with filters and search
      description: Fetches a list of requests based on optional filters and search parameters.
      operationId: listRequests
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
            example: 0
          description: The page number for pagination.
        - in: query
          name: limit
          schema:
            type: integer
            example: 10
          description: The number of documents to return per page.
        - name: status
          in: query
          description: Based on Request status
          schema:
            type: array
            items:
              type: string
            example: ["pending", "approved","rejected"]
        - name: priority
          in: query
          description: Based on Request priority
          schema:
            type: array
            items:
              type: string
            example: ["low", "medium"]
        - in: query
          name: dueDate
          schema:
            type: string
            format: date-time
            example: "2022-01-31"
          description: "Filter requests based on dueDate"
        - name: requestedTo
          in: query
          description: "Filter requests by requestedTo user ID"
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - name: requestedBy
          in: query
          description: "Filter requests by requestedBy user ID"
          schema:
            type: array
            items:
              type: integer
            example: [1, 3]
        - in: query
          name: search
          schema:
            type: string
          description: >
            Search requests by title.
      responses:
        "200":
          description: List of requests retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/RequestListResponse"
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
  /request/{id}:
    patch:
      summary: Updates an existing Request
      description: Updates an existing Request. Only the fields provided in the request body will be updated.
      operationId: updateRequest
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - in: "path"
          name: "id"
          required: true
          schema:
            type: string
          description: "The ID of the request to update."
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/updateRequest"
      responses:
        "200":
          description: Request updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Request updated successfully"
        "400":
          description: Invalid request
        "404":
          description: Request not found
        "500":
          description: Internal Server Error
  /request/details/{id}:
    get:
      summary: Fetch request details by Request ID
      description: Retrieves the details of a specific request based on the provided Request ID.
      operationId: getRequestById
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - in: "path"
          name: "id"
          required: true
          schema:
            type: string
          description: "The ID of the request to retrieve."
      responses:
        "200":
          description: Request retrieved successfully
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/getRequestResponse"
        "404":
          description: Request not found
        "500":
          description: Internal Server Error
  /request/task/status-count:
    get:
      summary: Get Request and Task status Count
      description: Get all status count based on Task or Request
      operationId: getStatusCount
      tags:
        - Request
      security:
        - bearerAuth: []
      parameters:
        - name: type
          in: query
          description: The type of status count to fetch. Can be 'task' or 'request'. Default is 'request'.
          required: true
          schema:
            type: string
            enum: 
              - task
              - request
            default: request
        - name: userId
          in: query
          description: based on the userId
          required: false
          schema:
            type: integer
        - name: organizationId
          in: query
          description: based on the userId
          required: false
          schema:
            type: integer
      responses:
        "200":
          description: Request updated successfully
        "400":
          description: Invalid request
        "500":
          description: Internal Server Error
components:
  schemas:
    RequestListResponse:
      type: object
      properties:
        result:
          type: object
          properties:
            message:
              type: string
              example: "Request fetched successfully"
            data:
              type: object
              properties:
                rows:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 1
                      title:
                        type: string
                        example: "Request for budget approval"
                      requestType:
                        type: string
                        example: "general_request"
                      priority:
                        type: string
                        example: "high"
                      status:
                        type: string
                        example: "under_review"
                      dueDate:
                        type: string
                        format: date-time
                        example: "2025-01-30T15:00:00.000Z"
                      notes:
                        type: string
                        example: "Please prioritize this request."
                      createdAt:
                        type: string
                        format: date-time
                        example: "2025-01-24T04:48:11.071Z"
                      updatedAt:
                        type: string
                        format: date-time
                        example: "2025-01-24T07:53:11.271Z"
                      receiver:
                        type: object
                        properties:
                          profilePicture:
                            type: string
                            example: ""
                          id:
                            type: integer
                            example: 3
                          firstName:
                            type: string
                            example: "Ajay"
                          middleName:
                            type: string
                            nullable: true
                            example: null
                          lastName:
                            type: string
                            example: "Koli"
                          designation:
                            type: string
                            nullable: true
                            example: null
                      approver:
                        type: object
                        properties:
                          profilePicture:
                            type: string
                            example: ""
                          id:
                            type: integer
                            example: 1
                          firstName:
                            type: string
                            nullable: true
                            example: null
                          middleName:
                            type: string
                            nullable: true
                            example: null
                          lastName:
                            type: string
                            nullable: true
                            example: null
                          designation:
                            type: string
                            nullable: true
                            example: null
                pagination:
                  type: object
                  properties:
                    totalCount:
                      type: integer
                      example: 1
                    start:
                      type: integer
                      example: 0
                    limit:
                      type: integer
                      example: 10
    getRequestResponse: 
      type: object
      properties:
        result:
          type: object
          properties:
            message:
              type: string
              example: "Request data fetched successfully"
            data:
              type: object
              properties:
                request:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 1
                    title:
                      type: string
                      example: "Request for budget approval"
                    requestType:
                      type: string
                      example: "general_request"
                    priority:
                      type: string
                      example: "high"
                    status:
                      type: string
                      example: "under_review"
                    dueDate:
                      type: string
                      format: date-time
                      example: "2025-01-30T15:00:00.000Z"
                    notes:
                      type: string
                      example: "Please prioritize this request."
                    createdAt:
                      type: string
                      format: date-time
                      example: "2025-01-24T04:48:11.071Z"
                    updatedAt:
                      type: string
                      format: date-time
                      example: "2025-01-24T07:53:11.271Z"
                    receiver:
                      type: object
                      properties:
                        profilePicture:
                          type: string
                          example: ""
                        id:
                          type: integer
                          example: 3
                        firstName:
                          type: string
                          example: "Ajay"
                        middleName:
                          type: string
                          nullable: true
                          example: null
                        lastName:
                          type: string
                          example: "Koli"
                        designation:
                          type: string
                          nullable: true
                          example: null
                    approver:
                      type: object
                      properties:
                        profilePicture:
                          type: string
                          example: ""
                        id:
                          type: integer
                          example: 1
                        firstName:
                          type: string
                          nullable: true
                          example: null
                        middleName:
                          type: string
                          nullable: true
                          example: null
                        lastName:
                          type: string
                          nullable: true
                          example: null
                        designation:
                          type: string
                          nullable: true
                          example: null
                activities:
                  type: array
                  items:
                    type: object
                    properties:
                      id:
                        type: integer
                        example: 53
                      actionType:
                        type: string
                        example: "edited"
                      activityDescription:
                        type: string
                        example: "Assigned request to Ajay Koli"
                      activityOn:
                        type: string
                        example: "Request"
                      recordId:
                        type: integer
                        example: 1
                      createdAt:
                        type: string
                        format: date-time
                        example: "2025-01-24T07:53:11.255Z"
                      updatedAt:
                        type: string
                        format: date-time
                        example: "2025-01-24T07:53:11.255Z"
                      createdBy:
                        type: integer
                        example: 1
    createRequest:
      type: object
      properties:
        title:
          type: string
          example: "Request for budget approval"
          description: "Title of the request"
        requestedTo:
          type: integer
          example: 101
          description: "User ID to whom the request is made"
        recordId:
          type: integer
          example: 101
          description: "record ID for which the request is made"
        leaveForEmployeeId:
          type: integer
          example: 101
          description: "leaveForEmployeeId for which Employee the request is made"
        requestType:
          type: string
          example: "general_request"
          enum:
            - general_request
            - leave_request
            - payment_request
            - indent_mr_request
            - stock_adjustment_request
            - work_order_request
            - wbs_request
            - journal_request
            - budget_request
            - expense_request
            - credit_note_request
            - quotation_request
          description: "Type of the request"
        priority:
          type: string
          example: "high"
          enum:
            - low
            - medium
            - high
            - urgent
            - critical
          description: "Priority level of the request"
        dueDate:
          type: string
          format: date-time
          example: "2025-01-30T12:00:00Z"
          description: "Due date for the request in ISO 8601 format"
        notes:
          type: string
          example: "Please approve this request as soon as possible."
          description: "Additional notes or comments regarding the request"
        fromDate:
          type: string
          format: date-time
          example: "2025-01-30T10:00:00Z"
          description: "Start date for the leave (mandatory for leave_request)"
        toDate:
          type: string
          format: date-time
          example: "2025-02-05T18:00:00Z"
          description: "End date for the leave (mandatory for leave_request)"
        leaveType:
          type: string
          example: "sick_leave"
          enum:
            - paid_leave
            - sick_leave
            - casual_leave
            - maternity_leave
            - paternity_leave
        totalDays:
          type: integer
          example: 5
          description: "Total days of leave (mandatory for leave_request)"
        documents:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "document1.pdf"
              fileType:
                type: string
                maxLength: 100
                example: "application/pdf"
              filePath:
                type: string
                example: "/path/to/document1.pdf"
              fileSize:
                type: number
                format: decimal
                example: 1234.56
            description: "Array of document objects related to the request"
      required:
        - requestedTo
        - requestType
    updateRequest:
      type: object
      properties:
        requestedTo:
          type: integer
          description: "The user ID to whom the request is assigned."
          example: 123
        priority:
          type: string
          description: "The priority of the request."
          enum:
            - low
            - medium
            - high
            - urgent
            - critical
          example: "high"
        dueDate:
          type: string
          format: date-time
          description: "The due date for the request."
          example: "2025-01-30T15:00:00Z"
        notes:
          type: string
          description: "Additional notes or comments for the request."
          example: "Please prioritize this request."
        status:
          type: string
          description: "The current status of the request."
          enum:
            - pending
            - approved
            - rejected
            - under_review
          example: "under_review"