const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskController = require('@controllers/v1/task/Task');
const TaskSchema = require('@schema-validation/task/Task');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(TaskSchema.createTaskSchema),
  ErrorHandleHelper.requestValidator,
  TaskController.createTask
);

router.patch(
  '/details/:id',
  checkSchema(TaskSchema.updateTaskSchema),
  ErrorHandleHelper.requestValidator,
  TaskController.updateTask
);

router.patch(
  '/task-details/:id',
  checkSchema(TaskSchema.updateTaskDetailsSchema),
  ErrorHandleHelper.requestValidator,
  TaskController.updateTaskDetails
);

router.get(
  '/details/:id',
  checkSchema(TaskSchema.getTaskDetailsSchema),
  ErrorHandleHelper.requestValidator,
  TaskController.getTaskDetails
);

router.get(
  '/',
  checkSchema(TaskSchema.taskListingSchema),
  ErrorHandleHelper.requestValidator,
  TaskController.getTaskList
);

module.exports = router;
