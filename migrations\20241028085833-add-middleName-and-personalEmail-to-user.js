'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('User', 'middleName', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });

    await queryInterface.addColumn('User', 'personalEmail', {
      type: Sequelize.STRING(100),
      allowNull: true,
    });
    await queryInterface.addColumn('User', 'alternateCountryCode', {
      type: Sequelize.STRING(10),
      allowNull: true,
    });
    await queryInterface.addColumn('User', 'alternateMobileNumber', {
      type: Sequelize.STRING(20),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('User', 'middleName');
    await queryInterface.removeColumn('User', 'personalEmail');
    await queryInterface.removeColumn('User', 'alternateCountryCode');
    await queryInterface.removeColumn('User', 'alternateMobileNumber');
  },
};
