'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Customer = sequelize.define(
    'Customer',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      type: {
        type: DataTypes.ENUM(OPTIONS.customerType.getCustomerTypeArray()),
        allowNull: false,
      },
      firstName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      lastName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      email: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true,
      },
      countryCode: {
        type: DataTypes.STRING(5),
        allowNull: true,
      },
      contactNumber: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      profilePicture: {
        allowNull: true,
        type: DataTypes.TEXT,
        get() {
          return OPTIONS.generateCloudFrontUrl(
            this.getDataValue('profilePicture')
          );
        },
        set(file) {
          if (file) {
            this.setDataValue(
              'profilePicture',
              `uploads/${file.split('uploads/')[1]}`
            );
          }
        },
      },
      businessName: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.customerStatus.getCustomerStatusArray()),
        allowNull: false,
        defaultValue: OPTIONS.customerStatus.NEW_LEAD,
      },
      isArchived: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Customer.associate = (models) => {
    Customer.belongsTo(models.Source, {
      foreignKey: 'sourceId',
      as: 'source',
      onDelete: 'SET NULL',
    });

    Customer.belongsTo(models.Source, {
      foreignKey: 'subSourceId',
      as: 'subSource',
      onDelete: 'SET NULL',
    });

    Customer.hasMany(models.Requirement, {
      foreignKey: 'customerId',
      as: 'requirements',
      onDelete: 'SET NULL',
    });

    Customer.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
    });

    Customer.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'SET NULL',
    });

    Customer.hasMany(models.ContactPerson, {
      foreignKey: 'customerId',
      as: 'contactPersons',
      onDelete: 'CASCADE',
    });

    Customer.hasMany(models.Notes, {
      foreignKey: 'customerId',
      as: 'notes',
      onDelete: 'CASCADE',
    });

    Customer.hasMany(models.TeamAssignment, {
      foreignKey: 'customerId',
      as: 'teamAssignments',
      onDelete: 'SET NULL',
    });

    Customer.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });
  };

  return Customer;
};
