const { Customer, Notes } = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.validateAndCreateNotes = async (data, loggedInUser) => {
  const { customerId } = data;
  try {
    const existingCustomer = await checkExistence(Customer, { id: customerId });
    if (!existingCustomer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Customer with Id ${customerId}`),
      };
    }

    data.createdBy = loggedInUser.id;
    const createdNotes = await Notes.create(data);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Notes'),
      data: createdNotes,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndUpdateNotes = async (notesId, data) => {
  try {
    const notes = await checkExistence(Notes, {
      id: notesId,
    });
    if (!notes) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Notes with the given ID'),
      };
    }

    Object.assign(notes, data);
    const updatedNotes = await notes.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Notes'),
      data: updatedNotes,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndDeleteNotes = async (notesId) => {
  try {
    const notes = await checkExistence(Notes, { id: notesId }, ['id']);
    if (!notes) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Notes with the given ID'),
      };
    }

    await notes.destroy();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Notes'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
