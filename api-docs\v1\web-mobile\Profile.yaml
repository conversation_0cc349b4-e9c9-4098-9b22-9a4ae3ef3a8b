paths:
  /settings/profile/{id}:
    put:
      tags:
        - "Settings"
      summary: "Update a user profile"
      description: "This endpoint allows you to update a user profile by providing the necessary details."
      operationId: "updateUserProfile"
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: id
          required: true
          schema:
            type: integer
          description: ID of the user
      requestBody:
        description: "The details of the user profile to be updated."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/ProfileRequest"
        required: true
      responses:
        "200":
          description: "Profile has been updated successfully."
        "400":
          description: "Invalid input data or profile not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    ProfileRequest:
      type: object
      required:
        - email
        - role
      properties:
        email:
          type: string
          example: "<EMAIL>"
        personalEmail:
          type: string
          example: "<EMAIL>"
        role:
          type: string
          example: "EMPLOYEE"
        firstName:
          type: string
          example: "John"
        middleName:
          type: string
          example: "A."
        lastName:
          type: string
          example: "Doe"
        profilePicture:
          type: string
          example: "https://example.com/profile.jpg"
        dateOfBirth:
          type: string
          format: date
          example: "1990-01-01"
        gender:
          type: string
          example: "male"
        about:
          type: string
          example: "Software engineer with a passion for coding."
        countryCode:
          type: string
          example: "91"
        mobileNumber:
          type: string
          example: "9090789056"
        alternateCountryCode:
          type: string
          example: "91"
        alternateMobileNumber:
          type: string
          example: "8090789050"
        maritalStatus:
          type: string
          example: "married"
        employeeCode:
          type: string
          example: "E1200"
        locationId:
          type: string
          example: "1234"
          description: "The location ID where the user is based."
        panNumber:
          type: string
          example: "3457JHY78"
          description: "panNumber of User"
        dateOfJoining:
          type: date
          example: "2025-01-29"
          description: "The date when the user joined"
        addressDetails:
          type: object
          properties:
            address:
              type: string
              example: "123 Street, City"
              description: "The address of the employee."
            city:
              type: string
              example: "City Name"
              description: "The city where the employee resides."
            pincode:
              type: string
              example: "123456"
              description: "The postal code."
            state:
              type: string
              example: "State Name"
              description: "The state where the employee resides."
        bankDetails:
          type: object
          properties:
            id:
              type: string
              example: "1"
              description: "The ID of the bank details record."
            accountName:
              type: string
              example: "John Doe"
              description: "The name of the account holder."
            accountNumber:
              type: string
              example: "**********"
              description: "The account number of the user."
            bankName:
              type: string
              example: "Bank of America"
              description: "The name of the bank."
            ifscCode:
              type: string
              example: "BOFAUS3N"
              description: "The IFSC code of the bank branch."
