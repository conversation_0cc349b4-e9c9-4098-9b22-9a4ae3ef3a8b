  components:
    parameters:
      xBrickoApplicationHeader:
        description: Specifies the platform type (erp-web or mobile).
        in: header
        name: x-bricko-application
        required: false
        schema:
          enum:
            - erp-web
            - mobile
          type: string
  paths:
    /auth/verify:
      post:
        tags:
          - "Auth"
        security:
          - bearerAuth: []
        summary: "Verify mobile number and email - mobile & web"
        description: "Api to verify mobile number and email"
        operationId: "verifyEmailMobile"
        parameters:
          - $ref: '#/components/parameters/xBrickoApplicationHeader'
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/send-otp"
          required: true
        responses:
          '200':
            description: "OTP sent successfully"
          '400':
            description: "Invalid Request"
          '401':
            description: "Unauthorized"
          '500':
            description: "Internal Server Error"

    /auth/resend-otp:
      post:
        tags:
          - "Auth"
        summary: "Resend OTP - mobile & web"
        description: "Api to resend OTP to the user"
        operationId: "resendOtp"
        parameters:
          - $ref: '#/components/parameters/xBrickoApplicationHeader'
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/send-otp"
          required: true
        responses:
          '200':
            description: "OTP resent successfully"
          '400':
            description: "Invalid Request"
          '401':
            description: "Unauthorized"
          '500':
            description: "Internal Server Error"

    /auth/verify-otp:
      patch:
        tags:
          - "Auth"
        summary: "Verify OTP - mobile & web"
        description: "Api to verify OTP"
        operationId: "verifyOtp"
        parameters:
          - $ref: '#/components/parameters/xBrickoApplicationHeader'
        requestBody:
          description: 
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/verify-otp"
          required: true
        responses:
          '200':
            description: "OTP verified successfully"
          '400':
            description: "Invalid Request or Incorrect OTP"
          '500':
            description: "Internal Server Error"
