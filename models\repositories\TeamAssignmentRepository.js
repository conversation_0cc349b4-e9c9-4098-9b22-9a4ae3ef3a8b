const { Customer, Requirement, Employee, TeamAssignment } = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.validateAndCreateTeamAssignment = async (data, loggedInUser) => {
  const { customerId, requirementId, memberId, autoAssign } = data;
  const { currentOrganizationId } = loggedInUser;
  try {
    const [requirement, existingAssignment, customer] = await Promise.all([
      checkExistence(Requirement, { id: requirementId }),
      checkExistence(TeamAssignment, { requirementId: requirementId }),
      checkExistence(Customer, { id: customerId }),
    ]);

    if (!requirement) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Requirement`),
      };
    }

    if (existingAssignment && existingAssignment.memberId !== null) {
      return {
        success: false,
        message: errorMessage.TEAM_MEMBER_ALREADY_ASSIGNED,
      };
    }

    if (!customer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Customer`),
      };
    }

    let employee = null;
    if (autoAssign) {
      employee = await this.getNextAvailableEmployee(currentOrganizationId);
      if (!employee) {
        return {
          success: false,
          message: errorMessage.NO_TEAM_MEMBER_AVAILABLE,
        };
      }
      data.memberId = employee.id;
    } else {
      employee = await checkExistence(Employee, { userId: memberId });
      if (!employee) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST(`Team Member`),
        };
      }
      data.memberId = employee.id;
    }

    data.organizationId = loggedInUser.currentOrganizationId;
    const createdTeamAssignment = await TeamAssignment.create(data);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Team Assignment'),
      data: createdTeamAssignment,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getNextAvailableEmployee = async (currentOrganizationId) => {
  try {
    const assignedEmployees = await TeamAssignment.findAll({
      where: { organizationId: currentOrganizationId },
      attributes: ['memberId'],
    });

    const assignedEmployeeIds = assignedEmployees.map((emp) => emp.memberId);

    // Fetch employees belonging to the current organization
    const availableEmployees = await Employee.findAll({
      where: {
        organizationId: currentOrganizationId,
      },
      attributes: ['id'],
    });

    // Separate out employees into assigned and free (unassigned)
    const freeEmployees = availableEmployees.filter(
      (emp) => !assignedEmployeeIds.includes(emp.id)
    );

    // If there are free employees, return the first one (prioritize free employees)
    if (freeEmployees.length > 0) {
      return freeEmployees[0];
    }

    // If no free employees, continue with the round-robin logic for already assigned employees
    const lastAssignedEmployee = await TeamAssignment.findOne({
      order: [['createdAt', 'DESC']],
      where: { organizationId: currentOrganizationId },
      attributes: ['memberId'],
    });

    let nextEmployee = null;
    if (lastAssignedEmployee) {
      const lastAssignedEmployeeId = lastAssignedEmployee.memberId;
      const lastAssignedEmployeeIndex = availableEmployees.findIndex(
        (emp) => emp.id === lastAssignedEmployeeId
      );

      if (lastAssignedEmployeeIndex !== -1) {
        const nextIndex =
          (lastAssignedEmployeeIndex + 1) % availableEmployees.length;
        nextEmployee = availableEmployees[nextIndex];
      }
    } else {
      nextEmployee = availableEmployees[0];
    }

    return nextEmployee;
  } catch (error) {
    throw new Error('Error in round-robin assignment logic: ' + error.message);
  }
};

exports.validateAndUpdateTeamAssignment = async (teamAssignmentId, data) => {
  const { customerId, requirementId, memberId, autoAssign } = data;
  try {
    const [teamAssignment, customer, requirement] = await Promise.all([
      checkExistence(TeamAssignment, { id: teamAssignmentId }),
      customerId
        ? checkExistence(Customer, { id: customerId })
        : Promise.resolve(null),
      requirementId
        ? checkExistence(Requirement, { id: requirementId })
        : Promise.resolve(null),
    ]);

    if (!teamAssignment) {
      return {
        success: false,
        message: errorMessage.INVALID_ID(`Team Assignment`),
      };
    }

    if (customerId && !customer) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Customer`),
      };
    }

    if (requirementId && !requirement) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Requirement`),
      };
    }

    if (autoAssign === true) {
      const nextEmployee = await this.getNextAvailableEmployee(requirementId);
      if (!nextEmployee) {
        return {
          success: false,
          message: errorMessage.NO_TEAM_MEMBER_AVAILABLE,
        };
      }
      data.memberId = nextEmployee.id;
    } else {
      if (memberId) {
        const employee = await checkExistence(Employee, { userId: memberId });
        if (!employee) {
          return {
            success: false,
            message: errorMessage.DOES_NOT_EXIST(`Team Member`),
          };
        }
        data.memberId = employee.id;
      }
    }

    Object.assign(teamAssignment, data);
    const updatedTeamAssignment = await teamAssignment.save();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Team Assignment'),
      data: updatedTeamAssignment,
    };
  } catch (error) {
    throw new Error(error);
  }
};
