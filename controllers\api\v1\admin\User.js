const sequelize = require('sequelize');
const { AccessManagement } = require('../../../../models');
const ExceljsHelper = require('../../../../models/helpers/ExceljsHelper');
const UserRepository = require('../../../../models/repositories/UserRepository');
const {
  resCode,
  genRes,
  errorTypes,
  defaultStatus,
  errorMessage,
  successMessage,
} = require('../../../../config/options');
const { userAttributes } = require('../../../../models/helpers/UserHelper');

const { Op } = sequelize;

exports.login = async (req, res) => {
  try {
    const { success, message, data } =
      await UserRepository.checkAndAdminLoginWithPassword(req.body);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.userUpdate = async (req, res) => {
  try {
    const query = {
      where: {
        id: req.user.id,
      },
    };
    const payloadUser = await UserRepository.updateUser(query, req.body);
    if (!payloadUser.success) {
      const { message } = payloadUser;
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, { message }));
    }
    const message = successMessage.UPDATE_SUCCESS_MESSAGE('Profile');
    const outputData = UserRepository.modifyOutputData(payloadUser.data);
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message, data: outputData }));
  } catch (e) {
    customErrorLogger(e);
    res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getUserProfile = async (req, res) => {
  try {
    const { id } = req.user;
    const query = {
      where: {
        id,
        status: defaultStatus.ACTIVE,
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'countryCode',
        'mobileNumber',
        'email',
        'profilePicture',
        'status',
        'role',
        'isMobileNumberVerified',
        'isEmailVerified',
      ],
      include: [
        {
          model: AccessManagement,
          as: 'accessManagement',
          attributes: [
            'id',
            'type',
            'view',
            'add',
            'edit',
            'remove',
            'status',
            'userId',
          ],
        },
      ],
    };
    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, errorMessage.DOES_NOT_EXIST('User'))
        );
    }
    const data = UserHelper.userProfileData(existingUser);

    return res.status(resCode.HTTP_OK).json(genRes(resCode.HTTP_OK, { data }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.changeStatus = async (req, res) => {
  try {
    const query = {
      where: {
        id: req.params.id,
        role: usersRoles.USER,
        status: { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: userAttributes(),
    };
    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, errorMessage.DOES_NOT_EXIST('user'))
        );
    }
    const status =
      existingUser.status === defaultStatus.ACTIVE
        ? defaultStatus.INACTIVE
        : defaultStatus.ACTIVE;
    await UserRepository.patchUpdateStatus(existingUser, status, false);
    const message = successMessage.UPDATE_SUCCESS_MESSAGE('Status');
    return res.json(genRes(resCode.HTTP_OK, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.deleteUser = async (req, res) => {
  try {
    const query = {
      where: {
        id: req.params.id,
        role: usersRoles.USER,
        status: { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: userAttributes(),
    };
    const existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, errorMessage.DOES_NOT_EXIST('User'))
        );
    }

    await UserRepository.patchUpdateStatus(
      existingUser,
      defaultStatus.DELETED,
      true
    );
    const message = successMessage.DELETE_SUCCESS_MESSAGE('User');
    return res.json(genRes(resCode.HTTP_OK, { message }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.getById = async (req, res) => {
  try {
    const query = {
      where: {
        id: req.params.id,
        role: usersRoles.USER,
        status: { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: [
        'id',
        'userName',
        'firstName',
        'lastName',
        'dateOfBirth',
        'email',
        'mobileNumber',
        'countryName',
        'countryCode',
        'state',
        'city',
        'address',
        'pincode',
        'createdAt',
      ],
    };
    let existingUser = await UserRepository.getUser(query);
    if (!existingUser) {
      const error = errorMessage.DOES_NOT_EXIST('User');
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, error, errorTypes.INPUT_VALIDATION)
        );
    }
    return res.json(genRes(resCode.HTTP_OK, { data: existingUser }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getUserListing = async (req, res) => {
  try {
    const {
      startDate = null,
      endDate = null,
      search = null,
      status = null,
      limit = 10,
      start = 0,
      order = ['createdAt', 'DESC'],
    } = req.query;
    const query = {
      where: {
        role: usersRoles.USER,
        ...(search && {
          [Op.or]: [
            sequelize.where(
              sequelize.fn(
                'concat',
                sequelize.col('firstName'),
                ' ',
                sequelize.col('lastName')
              ),
              {
                [Op.iLike]: `%${search}%`,
              }
            ),
            { email: { [Op.iLike]: `%${search}%` } },
            { mobileNumber: { [Op.iLike]: `%${search}%` } },
            { userName: { [Op.iLike]: `%${search}%` } },
          ],
        }),
        status: status
          ? status.split(',')
          : { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        'mobileNumber',
        'countryName',
        'countryCode',
        'city',
        'state',
        'pincode',
        'status',
        'createdAt',
        'userName',
      ],
      order: [order],
      offset: start,
      limit,
    };
    const dateCondition = [];
    if (startDate && endDate) {
      dateCondition.push(
        sequelize.where(sequelize.fn('date', sequelize.col('User.createdAt')), {
          [Op.gte]: startDate,
          [Op.lte]: endDate,
        })
      );
    } else if (startDate) {
      dateCondition.push(
        sequelize.where(
          sequelize.fn('date', sequelize.col('User.createdAt')),
          '>=',
          startDate
        )
      );
    } else if (endDate) {
      dateCondition.push(
        sequelize.where(
          sequelize.fn('date', sequelize.col('User.createdAt')),
          '<=',
          endDate
        )
      );
    }
    if (dateCondition.length > 0) {
      query.where[Op.and] = dateCondition;
    }
    const existingUser = await UserRepository.getUsersAndCount(query);
    return res.json(genRes(resCode.HTTP_OK, { data: existingUser }));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.postCreateUser = async (req, res) => {
  try {
    req.body.isFromAdmin = true;
    if (
      req.body.userVerification &&
      req.body.userVerification.verificationNumber
    ) {
      const isRegistrationNumberExists = await checkExistingVerificationNumber(
        req.body.userVerification
      );
      if (isRegistrationNumberExists.success) {
        return res
          .status(resCode.HTTP_BAD_REQUEST)
          .json(
            genRes(resCode.HTTP_BAD_REQUEST, isRegistrationNumberExists.message)
          );
      }
    }
    const responseUser = await UserRepository.checkAndCreate(req.body);
    if (!responseUser.success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, responseUser.message));
    }
    triggerUserAnalytics(
      {
        ...responseUser.data,
      },
      userAnalyticTypes.SIGN_UP
    ).then();
    if (
      req.body.userVerification &&
      req.body.userVerification.verificationNumber
    ) {
      req.body.userVerification.userId = responseUser.data.id;
      await createUserVerificationOrUpdate(req.body.userVerification);
    }
    delete responseUser.success;
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, responseUser));
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.putUpdateUser = async (req, res) => {
  try {
    const query = {
      where: {
        id: req.params.id,
        role: usersRoles.USER,
        status: { [Op.not]: [defaultStatus.DELETED] },
      },
      attributes: [
        'id',
        'firstName',
        'lastName',
        'email',
        'countryCode',
        'mobileNumber',
        'city',
        'state',
        'pincode',
        'isFromAdmin',
      ],
    };
    const { success, data, message } = await UserRepository.updateUser(
      query,
      req.body
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, { message }));
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        data,
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(
          resCode.HTTP_INTERNAL_SERVER_ERROR,
          errorMessage.SERVER_ERROR,
          errorTypes.INTERNAL_SERVER_ERROR
        )
      );
  }
};

exports.bulkCreateUsers = async (req, res) => {
  try {
    const data = await ExceljsHelper.readExcelData(req.file.path);
    if (data.length > 50) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, 'Limit to 50 records'));
    }
    if (data.length === 0) {
      return res
        .status(resCode.HTTP_OK)
        .json(genRes(resCode.HTTP_OK, 'No records'));
    }
    await UserRepository.bulkCreate(data);
    const message = successMessage.SAVED_SUCCESS_MESSAGE('User');
    return res
      .status(resCode.HTTP_OK)
      .json(genRes(resCode.HTTP_OK, { message }));
  } catch (e) {
    customErrorLogger(e);
    res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
