const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const BoqControl = require('../../../controllers/api/v1/Boq');
const BoqSchema = require('../../../schema-validation/Boq');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/boq/metric',
  checkSchema(BoqSchema.createBOQMetric),
  ErrorHandleHelper.requestValidator,
  BoqControl.createBOQMetric
);

router.post(
  '/:id/boq/category',
  checkSchema(BoqSchema.createBOQCategory),
  ErrorHandleHelper.requestValidator,
  BoqControl.createBOQCategory
);

router.put(
  '/boq/category/:id',
  checkSchema(BoqSchema.updateBOQCategory),
  ErrorHandleHelper.requestValidator,
  BoqControl.updateCategory
);

router.post(
  '/:id/boq/item',
  checkSchema(BoqSchema.createBOQItem),
  ErrorHandleHelper.requestValidator,
  BoqControl.addBOQItem
);

router.put(
  '/boq/item/:id',
  checkSchema(BoqSchema.updateBOQItem),
  ErrorHandleHelper.requestValidator,
  BoqControl.updateBOQItem
);

router.get(
  '/boq/items',
  ErrorHandleHelper.requestValidator,
  BoqControl.listBOQItems
);

router.post(
  '/:id/boq/sub-category',
  checkSchema(BoqSchema.createBOQSubCategory),
  ErrorHandleHelper.requestValidator,
  BoqControl.createBOQSubCategory
);

router.delete(
  '/boq/item/:id',
  ErrorHandleHelper.requestValidator,
  BoqControl.deleteBoqItems
);

router.get(
  '/boq/item/details/:id',
  ErrorHandleHelper.requestValidator,
  BoqControl.getBoqItemById
);

router.delete(
  '/boq/item/:id/soft-delete',
  ErrorHandleHelper.requestValidator,
  BoqControl.softDeleteBoqItems
);

router.get(
  '/boq/category/details/:id',
  ErrorHandleHelper.requestValidator,
  BoqControl.getBoqCategoryById
);

router.get(
  '/boq/sub-category/details/:id',
  ErrorHandleHelper.requestValidator,
  BoqControl.getBoqSubCategoryById
);

router.patch(
  '/boq/sub-category/:id',
  checkSchema(BoqSchema.updateBOQSubCategory),
  ErrorHandleHelper.requestValidator,
  BoqControl.updateSubCategory
);

router.delete(
  '/boq/category/:id/soft-delete',
  ErrorHandleHelper.requestValidator,
  BoqControl.softDeleteBoqCategory
);

router.delete(
  '/boq/sub-category/:id/soft-delete',
  ErrorHandleHelper.requestValidator,
  BoqControl.softDeleteBoqSubCategory
);

router.get(
  '/boq/status-count',
  ErrorHandleHelper.requestValidator,
  BoqControl.boqStatusCount
);

router.get(
  '/boq/category/status-count',
  ErrorHandleHelper.requestValidator,
  BoqControl.boqCategoryStatusCount
);

router.patch(
  '/boq/item/:id/status-update',
  ErrorHandleHelper.requestValidator,
  BoqControl.boqStatusUpdate
);

module.exports = router;
