paths:
  /settings/role:
    post:
      tags:
        - "Settings"
      summary: "Create a new Role"
      description: "This endpoint allows you to create a new role by providing all necessary details."
      operationId: "CreateRole"
      requestBody:
        description: "The details of the new role to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/RoleCreateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Role has been created successfully."
        "400":
          description: "Invalid input data or role already exists."
        "500":
          description: "Internal Server Error"

  /settings/role/{id}:      
    put:
      tags:
        - "Settings"
      summary: "Update an existing Role"
      description: "This endpoint allows you to update the details of an existing role by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateRole"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the role to be updated."
          schema:
            type: integer
            example: 12345
      requestBody:
        description: "The updated information for the role. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/RoleUpdateRequest"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Role has been updated successfully."
        "400":
          description: "Invalid input data, role not found, or role already exists."
        "404":
          description: "Role not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Settings"
      summary: "Delete an existing Role"
      description: "This endpoint allows you to delete an existing role by providing its `id` in the URL path."
      operationId: "DeleteRole"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the role to be deleted."
          schema:
            type: integer
            example: 12345
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Role has been deleted successfully."
        "400":
          description: "Invalid input data or role not found."
        "404":
          description: "Role not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    RoleCreateRequest:
      type: object
      properties:
        departmentId:
          type: integer
          example: 3
        name:
          type: string
          example: "Manager"
        reportTo:
          type: integer
          example: 2
      required:
        - departmentId
        - name
    RoleUpdateRequest:
      type: object
      properties:
        departmentId:
          type: integer
          example: 3
        name:
          type: string
          example: "Manager"
        reportTo:
          type: integer
          example: 2