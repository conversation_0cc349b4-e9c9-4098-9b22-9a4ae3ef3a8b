module.exports = (sequelize, DataTypes) => {
  const WorkOrderBOQMapping = sequelize.define(
    'WorkOrderBOQMapping',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      boqRate: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
      underscored: false,
    }
  );

  WorkOrderBOQMapping.associate = (models) => {
    WorkOrderBOQMapping.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    WorkOrderBOQMapping.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    WorkOrderBOQMapping.belongsTo(models.WorkOrder, {
      foreignKey: 'workOrderId',
      as: 'workOrder',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    WorkOrderBOQMapping.belongsTo(models.BoqEntry, {
      foreignKey: 'boqItemId',
      as: 'boqItem',
      onDelete: 'CASCADE',
    });
  };

  return WorkOrderBOQMapping;
};
