const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskTagController = require('@controllers/v1/task/TaskTag');
const TaskTagSchema = require('@schema-validation/task/TaskTag');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/custom-tag',
  checkSchema(TaskTagSchema.createCustomTag),
  ErrorHandleHelper.requestValidator,
  TaskTagController.createCustomTag
);

router.put(
  '/custom-tag/:id',
  checkSchema(TaskTagSchema.updateCustomTag),
  ErrorHandleHelper.requestValidator,
  TaskTagController.updateCustomTag
);

router.delete(
  '/custom-tag/:id',
  ErrorHandleHelper.requestValidator,
  TaskTagController.deleteCustomTag
);

router.get('', TaskTagController.getTaskTags);

module.exports = router;
