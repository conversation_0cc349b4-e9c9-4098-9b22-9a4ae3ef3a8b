const {
  BoqEntry,
  Project,
  BOQCategory,
  BOQMetric,
  WorkOrderBOQMapping,
} = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { Op } = require('sequelize');
const options = require('@config/options');

exports.createBoqEntry = async (projectId, data, loggedInUser) => {
  try {
    const project = await Project.findOne({
      where: {
        id: projectId,
      },
    });
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('project'),
      };
    }

    if (data.boqCategoryId) {
      const category = await BOQCategory.findOne({
        where: {
          id: data.boqCategoryId,
        },
      });
      if (!category) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('category'),
        };
      }
    }

    if (data.boqSubCategoryId) {
      const subCategory = await BOQCategory.findOne({
        where: {
          id: data.boqSubCategoryId,
        },
      });
      if (!subCategory) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('subcategory'),
        };
      }
    }

    if (data.boqMetricId) {
      const metric = await BOQMetric.findOne({
        where: {
          id: data.boqMetricId,
        },
      });
      if (!metric) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('metric'),
        };
      }
    }

    const costPayload =
      data.cost?.map((costItem, index) => ({
        ...costItem,
        id: index + 1,
      })) || [];

    const boqEntryPayload = {
      ...data,
      cost: costPayload,
      projectId,
      organizationId: project.organizationId,
      createdBy: loggedInUser.id,
    };

    const boqEntry = await BoqEntry.create(boqEntryPayload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('BOQ Entry'),
      data: boqEntry,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateBoqEntry = async (boqEntryId, data) => {
  try {
    const boqItem = await BoqEntry.findOne({
      where: {
        id: boqEntryId,
      },
    });

    if (!boqItem) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('BOQ Entry'),
      };
    }

    const project = await Project.findOne({
      where: {
        id: boqItem.projectId,
      },
    });

    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('project'),
      };
    }

    if (data.boqCategoryId) {
      const category = await BOQCategory.findOne({
        where: {
          id: data.boqCategoryId,
        },
      });
      if (!category) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('category'),
        };
      }
    }

    if (data.boqSubCategoryId) {
      const subCategory = await BOQCategory.findOne({
        where: {
          id: data.boqSubCategoryId,
        },
      });
      if (!subCategory) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('subcategory'),
        };
      }
    }

    if (data.boqMetricId) {
      const metric = await BOQMetric.findOne({
        where: {
          id: data.boqMetricId,
        },
      });
      if (!metric) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('metric'),
        };
      }
    }

    if (data.cost) {
      const costPayload = data.cost.map((costItem, index) => ({
        ...costItem,
        id: index + 1,
      }));
      data.cost = costPayload;
    }

    Object.assign(boqItem, data);

    await boqItem.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('BOQ Entry'),
      data: boqItem,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.listBoqItems = async (query) => {
  const {
    status,
    categoryId,
    organizationId,
    projectId,
    categoryName,
    boqName,
    boqDescription,
    subCategoryName,
    listType = 'all',
    start = 0,
    limit = 10,
  } = query;

  try {
    const whereMainCategory = {
      ...(categoryId ? { id: categoryId } : {}),
      ...(projectId ? { projectId } : {}),
      ...(organizationId ? { organizationId } : {}),
      ...(categoryName ? { name: categoryName } : {}),
      ...(subCategoryName ? { name: subCategoryName } : {}),
      parentCategoryId: null,
    };

    let mappedBoqItemIds = [];
    if (listType !== 'all') {
      const mappings = await WorkOrderBOQMapping.findAll({
        attributes: ['boqItemId'],
      });
      mappedBoqItemIds = mappings.map((mapping) => mapping.boqItemId);
    }

    const whereBoqItems = {
      ...(status ? { status } : {}),
      ...(boqName ? { name: { [Op.like]: `%${boqName}%` } } : {}),
      ...(boqDescription
        ? { description: { [Op.like]: `%${boqDescription}%` } }
        : {}),
      ...(listType === 'mapped' ? { id: { [Op.in]: mappedBoqItemIds } } : {}),
      ...(listType === 'unmapped'
        ? { id: { [Op.notIn]: mappedBoqItemIds } }
        : {}),
    };

    const categories = await BOQCategory.findAll({
      where: whereMainCategory,
      attributes: [
        'id',
        'name',
        'status',
        'projectId',
        'organizationId',
        'createdAt',
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
      include: [
        {
          model: BOQCategory,
          as: 'subCategories',
          required: boqName || boqDescription ? true : false,
          attributes: ['id', 'name', 'status', 'projectId', 'organizationId'],
          include: [
            {
              model: BoqEntry,
              as: 'boqItems',
              required: boqName || boqDescription ? true : false,
              where: whereBoqItems,
              attributes: [
                'id',
                'name',
                'description',
                'status',
                'total',
                'projectId',
                'organizationId',
                'cost',
              ],
            },
          ],
        },
      ],
    });

    const totalCategoriesCount = await BOQCategory.count({
      where: whereMainCategory,
    });

    const processedData = categories.map((category) => {
      let categoryCompletedQty = 0;
      let categoryTotalQty = 0;
      let categoryTotalEstimatedCost = 0;
      let categoryCompletedEstimatedCost = 0;

      const subCategoryData = category.subCategories.map((subCategory) => {
        let subCategoryCompletedQty = 0;
        let subCategoryTotalQty = 0;
        let subCategoryTotalEstimatedCost = 0;
        let subCategoryCompletedEstimatedCost = 0;

        const items = subCategory.boqItems.map((item) => {
          const totalEstimatedCost =
            item.cost?.reduce(
              (sum, costItem) =>
                sum +
                parseFloat(costItem.estimatedRate || 0) *
                  parseFloat(item.total || 0),
              0
            ) || 0;
          const completedEstimatedCost =
            item.cost?.reduce(
              (sum, costItem) =>
                sum +
                (item.status === 'completed'
                  ? parseFloat(costItem.estimatedRate || 0) *
                    parseFloat(item.total || 0)
                  : 0),
              0
            ) || 0;

          const completedQty =
            item.status === 'completed' ? parseFloat(item.total || 0) : 0;
          const totalQty = parseFloat(item.total || 0);

          subCategoryCompletedQty += completedQty;
          subCategoryTotalQty += totalQty;
          subCategoryTotalEstimatedCost += totalEstimatedCost;
          subCategoryCompletedEstimatedCost += completedEstimatedCost;

          return {
            type: 'item',
            id: item.id,
            name: item.name,
            description: item.description,
            progress: totalQty > 0 ? (completedQty / totalQty) * 100 : 0,
            budgetUsed:
              totalEstimatedCost > 0
                ? (completedEstimatedCost / totalEstimatedCost) * 100
                : 0,
            estimatedQty: totalQty,
            rate: totalQty > 0 ? totalEstimatedCost / totalQty : 0,
            totalEstimatedCost: totalEstimatedCost,
            status: item.status,
            projectId: item.projectId,
            organizationId: item.organizationId,
          };
        });

        categoryCompletedQty += subCategoryCompletedQty;
        categoryTotalQty += subCategoryTotalQty;
        categoryTotalEstimatedCost += subCategoryTotalEstimatedCost;
        categoryCompletedEstimatedCost += subCategoryCompletedEstimatedCost;

        return {
          type: 'subCategory',
          id: subCategory.id,
          name: subCategory.name,
          progress:
            subCategoryTotalQty > 0
              ? (subCategoryCompletedQty / subCategoryTotalQty) * 100
              : 0,
          budgetUsed:
            subCategoryTotalEstimatedCost > 0
              ? (subCategoryCompletedEstimatedCost /
                  subCategoryTotalEstimatedCost) *
                100
              : 0,
          estimatedQty: subCategoryTotalQty,
          rate:
            subCategoryTotalQty > 0
              ? subCategoryTotalEstimatedCost / subCategoryTotalQty
              : 0,
          totalEstimatedCost: subCategoryTotalEstimatedCost,
          status: subCategory.status,
          projectId: subCategory.projectId,
          organizationId: subCategory.organizationId,
          items: items,
        };
      });

      return {
        type: 'category',
        id: category.id,
        name: category.name,
        progress:
          categoryTotalQty > 0
            ? (categoryCompletedQty / categoryTotalQty) * 100
            : 0,
        budgetUsed:
          categoryTotalEstimatedCost > 0
            ? (categoryCompletedEstimatedCost / categoryTotalEstimatedCost) *
              100
            : 0,
        estimatedQty: categoryTotalQty,
        rate:
          categoryTotalQty > 0
            ? categoryTotalEstimatedCost / categoryTotalQty
            : 0,
        totalEstimatedCost: categoryTotalEstimatedCost,
        status: category.status,
        organizationId: category.organizationId,
        projectId: category.projectId,
        subCategories: subCategoryData,
      };
    });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE(
        'category, subcategory, and BOQ items'
      ),
      data: {
        rows: processedData,
        pagination: {
          totalCount: totalCategoriesCount,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw error;
  }
};

exports.deleteBOQItem = async (boqItemId) => {
  try {
    const boqItem = await BoqEntry.findOne({ where: { id: boqItemId } });

    if (!boqItem) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('BOQItem'),
      };
    }

    await boqItem.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('BOQItem'),
      data: boqItem,
    };
  } catch (error) {
    throw error;
  }
};

exports.getBoqItemById = async (boqItemId) => {
  try {
    const boqItem = await BoqEntry.findOne({
      where: { id: boqItemId },
      attributes: [
        'id',
        'name',
        'description',
        'status',
        'total',
        'projectId',
        'organizationId',
        'cost',
        'quantity',
        'length',
        'breadth',
        'height',
      ],
    });

    if (!boqItem) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('boqItemId'),
      };
    }

    const totalEstimatedCost =
      boqItem.cost?.reduce(
        (sum, costItem) =>
          sum +
          parseFloat(costItem.estimatedRate || 0) *
            parseFloat(boqItem.total || 0),
        0
      ) || 0;
    const completedEstimatedCost =
      boqItem.cost?.reduce(
        (sum, costItem) =>
          sum +
          (boqItem.status === 'completed'
            ? parseFloat(costItem.estimatedRate || 0) *
              parseFloat(boqItem.total || 0)
            : 0),
        0
      ) || 0;

    const budgetUsed =
      totalEstimatedCost > 0
        ? (completedEstimatedCost / totalEstimatedCost) * 100
        : 0;

    const totalQty = parseFloat(boqItem.total || 0);
    const completedQty = boqItem.status === 'completed' ? totalQty : 0;
    const progress = totalQty > 0 ? (completedQty / totalQty) * 100 : 0;
    const rate = totalQty > 0 ? totalEstimatedCost / totalQty : 0;

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('boqItemDetails'),
      data: {
        type: 'item',
        id: boqItem.id,
        name: boqItem.name,
        description: boqItem.description,
        progress: progress,
        budgetUsed: budgetUsed,
        estimatedQty: totalQty,
        rate: rate,
        totalEstimatedCost: totalEstimatedCost,
        status: boqItem.status,
        projectId: boqItem.projectId,
        organizationId: boqItem.organizationId,
        cost: boqItem.cost,
        quantity: boqItem.quantity,
        length: boqItem.length,
        breadth: boqItem.breadth,
        height: boqItem.height,
      },
    };
  } catch (error) {
    throw error;
  }
};

exports.softDeleteBOQItem = async (boqItemId) => {
  try {
    const boqItem = await BoqEntry.findOne({ where: { id: boqItemId } });

    if (!boqItem) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('BOQItem'),
      };
    }

    boqItem.status = 'deleted';
    await boqItem.save();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('BOQItem'),
      data: boqItem,
    };
  } catch (error) {
    throw error;
  }
};

exports.getBoqStatusCount = async (query) => {
  const { organizationId, projectId } = query;

  try {
    const data = {
      total: 0,
      status: {},
    };

    let statusMap = Object.fromEntries(
      [
        options.defaultStatus.UN_ASSIGNED,
        options.defaultStatus.CONTRACTED,
        options.defaultStatus.ONGOING,
        options.defaultStatus.COMPLETED,
        options.defaultStatus.DELETED,
        options.defaultStatus.QUALIFIED,
      ].map((status) => [status, 0])
    );

    let queryOptions = {
      attributes: [
        'status',
        [
          BoqEntry.sequelize.fn('COUNT', BoqEntry.sequelize.col('status')),
          'count',
        ],
      ],
      where: {},
      group: ['status'],
    };

    if (organizationId) {
      queryOptions.where.organizationId = organizationId;
    }
    if (projectId) {
      queryOptions.where.projectId = projectId;
    }

    const boqStatusCounts = await BoqEntry.findAll(queryOptions);

    boqStatusCounts.forEach((row) => {
      const status = row.dataValues.status;
      const count = parseInt(row.dataValues.count, 10);
      if (statusMap.hasOwnProperty(status)) {
        statusMap[status] = count;
      }
    });

    data.status = statusMap;
    data.total = Object.values(statusMap).reduce(
      (acc, count) => acc + count,
      0
    );

    return {
      success: true,
      message: successMessage.FETCH_SUCCESS_MESSAGE('boq-status-count'),
      data,
    };
  } catch (error) {
    throw error;
  }
};

exports.updateBoqStatus = async (boqEntryId, data) => {
  try {
    const boqEntry = await BoqEntry.findOne({
      where: { id: boqEntryId },
    });

    if (!boqEntry) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('BOQ Entry'),
      };
    }

    if (
      !data.status ||
      !Object.values(options.defaultStatus).includes(data.status)
    ) {
      return {
        success: false,
        message: errorMessage.INVALID_CREDENTIALS('status'),
      };
    }

    boqEntry.status = data.status;
    await boqEntry.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('BOQ Status'),
      data: boqEntry,
    };
  } catch (error) {
    throw new Error(error);
  }
};
