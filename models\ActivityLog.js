const { activityType } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const ActivityLog = sequelize.define(
    'ActivityLog',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      actionType: {
        type: DataTypes.ENUM(activityType.getActivityTypeArray()),
        allowNull: true,
        defaultValue: activityType.MARKED,
      },
      activityDescription: {
        type: DataTypes.TEXT,
        allowNull: true,
        defaultValue: 'Dummy Task',
      },
      activityOn: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      recordId: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ActivityLog.associate = (models) => {
    ActivityLog.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      allowNull: true,
    });
    ActivityLog.belongsTo(models.Organization, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    ActivityLog.belongsTo(models.Tasks, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    ActivityLog.belongsTo(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    ActivityLog.belongsTo(models.Project, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    ActivityLog.belongsTo(models.WorkOrder, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
    ActivityLog.belongsTo(models.Item, {
      foreignKey: 'recordId',
      constraints: false,
      onDelete: 'CASCADE',
    });
  };

  return ActivityLog;
};
