const { paymentPlanType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const PaymentPlan = sequelize.define(
    'PaymentPlan',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      planType: {
        type: DataTypes.ENUM(...paymentPlanType.paymentPlanTypeArray()),
        allowNull: false,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      additionalTerm: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  PaymentPlan.associate = (models) => {
    PaymentPlan.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
    });

    PaymentPlan.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });

    PaymentPlan.hasMany(models.PaymentStage, {
      foreignKey: 'paymentPlanId',
      as: 'paymentStages',
    });
  };

  return PaymentPlan;
};
