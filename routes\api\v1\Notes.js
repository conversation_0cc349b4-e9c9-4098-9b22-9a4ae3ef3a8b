const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const NotesController = require('@controllers/v1/crm/Notes');
const NotesSchema = require('@schema-validation/Notes');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(NotesSchema.createNotes),
  ErrorHandleHelper.requestValidator,
  NotesController.createNotes
);

router.put(
  '/:id',
  checkSchema(NotesSchema.updateNotes),
  ErrorHandleHelper.requestValidator,
  NotesController.updateNotes
);

router.delete(
  '/:id',
  checkSchema(NotesSchema.deleteNotes),
  ErrorHandleHelper.requestValidator,
  NotesController.deleteNotes
);

module.exports = router;
