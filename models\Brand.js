'use strict';

module.exports = (sequelize, DataTypes) => {
  const Brand = sequelize.define(
    'Brand',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
    },
    {
      tableName: 'Brand',
      timestamps: true,
    }
  );

  Brand.associate = function (models) {
    Brand.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });

    Brand.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'createdBy',
    });
  };
  return Brand;
};
