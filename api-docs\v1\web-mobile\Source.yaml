paths:
  /crm/source:
    post:
      tags:
        - "CRM"
      summary: "Create a new Source"
      description: "This endpoint allows you to create a new source by providing the required name and logo."
      operationId: "CreateSource"
      requestBody:
        description: "The details of the new source."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/add-update-source"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Source created successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/add-update-source'
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error"

  /crm/source/{id}:
    put:
      tags:
        - "CRM"
      summary: "Update an existing Source"
      description: "This endpoint allows you to update an existing source by providing its `id` in the URL path and the new details in the request body."
      operationId: "UpdateSource"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the source to be updated."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The updated information for the source."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/add-update-source"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Source updated successfully."
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/add-update-source'
        "400":
          description: "Invalid input data or source not found."
        "404":
          description: "Source not found."
        "500":
          description: "Internal Server Error"
  /crm/source/lead/{id}:
    put:
      tags:
        - "CRM"
      summary: "Update the source for a specific lead"
      description: "This endpoint allows you to update the source for a lead by providing the `id` of the lead and the new source details in the request body."
      operationId: "UpdateLeadSource"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the lead whose source needs to be updated."
          schema:
            type: integer
            example: 12345
      requestBody:
        description: "The updated source details for the lead."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/update-lead-source"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Lead source updated successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Lead source updated successfully"
        "400":
          description: "Invalid input data or lead not found."
        "404":
          description: "Lead not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    add-update-source:
      type: object
      properties:
        name:
          type: string
          description: "The name of the source."
          example: "Source A"
        logo:
          type: string
          format: uri
          description: "The URL of the source's logo."
          example: "https://example.com/logo.png"
        parentSourceId:
          type: integer
          description: "The ID of the parent source (optional)."
          example: 456
        userId:
          type: integer
          description: "The ID of the user (optional)."
          example: 1
      required: []
      additionalProperties: false
    update-lead-source:
      type: object
      properties:
        sourceId:
          type: integer
          description: "The ID of the new source for the lead."
          example: 456
        subSourceId:
          type: integer
          description: "The ID of the sub-source for the lead, if applicable."
          example: 789
      required: []
      additionalProperties: false
