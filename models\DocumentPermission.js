'use strict';
module.exports = (sequelize, DataTypes) => {
  const DocumentPermission = sequelize.define(
    'DocumentPermission',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      canView: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      canEdit: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      canDelete: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  DocumentPermission.associate = (models) => {
    DocumentPermission.belongsTo(models.Document, {
      foreignKey: 'documentId',
      as: 'document',
    });

    DocumentPermission.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return DocumentPermission;
};
