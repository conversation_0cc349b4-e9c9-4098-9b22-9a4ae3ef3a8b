const { DocumentActivityLog } = require('..');
const { successMessage, actionType } = require('../../config/options');

exports.findOne = async (query) => await DocumentActivityLog.findOne(query);

exports.findAll = async (query) => await DocumentActivityLog.findAll(query);

exports.findAndCountAll = async (query) =>
  await DocumentActivityLog.findAndCountAll(query);

exports.bulkCreate = async (data) => await DocumentActivityLog.bulkCreate(data);

exports.addBulkDocumentActivityLogs = async (data, documentId, performedBy) => {
  try {
    const activityLogPayload = data.map((activity) => ({
      actionType: activity.canEdit
        ? actionType.ADD_USER_WITH_EDIT
        : actionType.ADD_USER_WITH_READ,
      performedBy: performedBy,
      performedOn: activity.userId,
      documentId: documentId,
    }));

    await this.bulkCreate(activityLogPayload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Document Activity Logs'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.addActivityLog = async (
  actionType,
  documentId,
  performedBy,
  performedOn = null
) => {
  const activityLogPayload = {
    actionType: actionType,
    performedBy: performedBy,
    performedOn: performedOn,
    documentId: documentId,
  };
  await DocumentActivityLog.create(activityLogPayload);

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Document Activity Logs'),
  };
};
