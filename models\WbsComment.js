module.exports = (sequelize, DataTypes) => {
  const WbsComment = sequelize.define(
    'WbsComment',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      comment: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  WbsComment.associate = (models) => {
    WbsComment.belongsTo(models.WbsActivity, {
      foreignKey: 'wbsActivityId',
      as: 'wbsActivity',
    });

    WbsComment.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
    });
  };

  return WbsComment;
};
