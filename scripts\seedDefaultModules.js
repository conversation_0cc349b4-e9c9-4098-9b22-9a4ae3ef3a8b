const { Module, sequelize } = require('../models/index');

const defaultModules = [
  {
    name: 'Edit Dashboard',
    isDefault: true,
  },
  {
    name: 'Tasks',
    isDefault: true,
  },
  {
    name: 'Requests',
    isDefault: true,
  },
  {
    name: 'Projects',
    isDefault: true,
  },
  {
    name: 'Files',
    isDefault: true,
  },
  {
    name: 'Communication',
    isDefault: true,
  },
  {
    name: 'Contacts',
    isDefault: false,
  },
  {
    name: 'Sitework',
    isDefault: false,
    childModules: [
      {
        name: 'BOQ',
        isDefault: false,
      },
      {
        name: 'Workorder',
        isDefault: false,
      },
      {
        name: 'WBS',
        isDefault: false,
      },
      {
        name: 'DPR',
        isDefault: false,
      },
    ],
  },
  {
    name: 'Material',
    isDefault: false,
    childModules: [
      {
        name: 'Indent',
        isDefault: false,
      },
      {
        name: 'Stock Adjustment',
        isDefault: false,
      },
      {
        name: 'Request For Quote',
        isDefault: false,
      },
      {
        name: 'Purchase Order',
        isDefault: false,
      },
      {
        name: 'Goods Receipt',
        isDefault: false,
      },
    ],
  },
  {
    name: 'Legal',
    isDefault: false,
    childModules: [
      {
        name: 'Court Cases',
        isDefault: false,
      },
      {
        name: 'Land',
        isDefault: false,
      },
      {
        name: 'Sanctions',
        isDefault: false,
      },
    ],
  },
  {
    name: 'Finance',
    isDefault: false,
    childModules: [
      {
        name: 'Account',
        isDefault: false,
      },
      {
        name: 'Journal Entry',
        isDefault: false,
      },
      {
        name: 'Invoice',
        isDefault: false,
      },
      {
        name: 'Credit Note',
        isDefault: false,
      },
      {
        name: 'Payment',
        isDefault: false,
      },
      {
        name: 'Budget',
        isDefault: false,
      },
      {
        name: 'Expense',
        isDefault: false,
      },
      {
        name: 'Cheques',
        isDefault: false,
      },
      {
        name: 'Reconciliation',
        isDefault: false,
      },
      {
        name: 'Loans',
        isDefault: false,
      },
      {
        name: 'Audit',
        isDefault: false,
      },
    ],
  },
  {
    name: 'Reports',
    isDefault: false,
  },
  {
    name: 'Settings',
    isDefault: false,
    childModules: [
      {
        name: 'Organisation Settings',
        isDefault: false,
      },
      {
        name: 'Roles & Permissions',
        isDefault: false,
      },
      {
        name: 'User Management',
        isDefault: false,
      },
      {
        name: 'Integrations',
        isDefault: false,
      },
      {
        name: 'Approval Workflow',
        isDefault: false,
      },
      {
        name: 'Templates',
        isDefault: false,
      },
      {
        name: 'Billing & Plans',
        isDefault: false,
      },
      {
        name: 'Danger Zone',
        isDefault: false,
      },
    ],
  },
  {
    name: 'CRM',
    childModules: [
      {
        name: 'Leads',
        isDefault: false,
      },
      {
        name: 'Sold-Unsold',
        isDefault: false,
      },
      {
        name: 'Customers',
        isDefault: false,
      },
    ],
  },
  {
    name: 'Employees',
    isDefault: false,
    childModules: [
      {
        name: 'All Employees',
        isDefault: false,
      },
      {
        name: 'Attendance',
        isDefault: false,
      },
      {
        name: 'Payroll',
        isDefault: false,
      },
    ],
  },
  {
    name: 'Sales Management',
    isDefault: false,
    childModules: [
      {
        name: 'Quotations',
        isDefault: false,
      },
      {
        name: 'Orders',
        isDefault: false,
      },
      {
        name: 'Sales Partners',
        isDefault: false,
      },
      {
        name: 'Catalogues',
        isDefault: false,
      },
      {
        name: 'Brands',
        isDefault: false,
      },
    ],
  },
];

async function createModuleWithChildren(moduleData, transaction) {
  const existingModule = await Module.findOne({
    where: {
      name: moduleData.name,
    },
  });

  if (existingModule) {
    console.log(`Module "${moduleData.name}" already exists. Updating data.`);
    await existingModule.update(moduleData, { transaction });
    return existingModule;
  }

  const parentModule = await Module.create(
    {
      ...moduleData,
    },
    { transaction }
  );

  if (moduleData.childModules && moduleData.childModules.length > 0) {
    for (let child of moduleData.childModules) {
      child.parentId = parentModule.id;
      await createModuleWithChildren(child, transaction);
    }
  }
  return parentModule;
}

async function seedDefaultModules() {
  const transaction = await sequelize.transaction();
  try {
    for (const moduleData of defaultModules) {
      await createModuleWithChildren(moduleData, transaction);
    }
    await transaction.commit();
    console.log('Default modules have been seeded successfully.');
    return {
      success: true,
      message: 'Default modules have been seeded successfully.',
    };
  } catch (error) {
    console.error('Error seeding default modules:', error);
    await transaction.rollback();
  }
}

module.exports = { seedDefaultModules };
