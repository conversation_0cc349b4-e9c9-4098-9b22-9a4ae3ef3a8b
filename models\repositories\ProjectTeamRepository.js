const { Project, User, ProjectTeam } = require('..');
const { successMessage, errorMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');
const { defaultStatus } = require('@config/options');

exports.validateAndSendInvitation = async (projectId, userId, loggedInUser) => {
  try {
    const project = await checkExistence(Project, { id: projectId });
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(`Project with id: ${projectId}`),
      };
    }

    const user = await checkExistence(User, { id: userId });
    if (!user) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `User ${user.firstName} ${user.lastName}`
        ),
      };
    }

    const existingTeamMember = await ProjectTeam.findOne({
      where: { projectId, userId },
    });
    if (existingTeamMember) {
      return {
        success: false,
        message: errorMessage.USER_ALREADY_IN_PROJECT(
          `${user.firstName} ${user.lastName}`
        ),
      };
    }

    const payload = {
      projectId,
      userId,
      invitedBy: loggedInUser.id,
      status: defaultStatus.ACCEPTED,
    };
    const createdProjectTeam = await ProjectTeam.create(payload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Project Team'),
      data: createdProjectTeam,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateInvitationStatus = async (invitationId, data) => {
  try {
    const projectTeam = await checkExistence(ProjectTeam, { id: invitationId });
    if (!projectTeam) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `ProjectTeam with id: ${invitationId}`
        ),
      };
    }

    const updatedProjectTeam = Object.assign(projectTeam, data);
    await projectTeam.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Project Team'),
      data: updatedProjectTeam,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.validateAndRemoveTeamMember = async (projectTeamId, loggedInUser) => {
  try {
    const query = {
      where: {
        id: projectTeamId,
      },
      include: [
        {
          model: User,
          as: 'user',
        },
      ],
    };
    const projectTeam = await ProjectTeam.findOne(query);
    if (!projectTeam) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          `Project team member with id: ${projectTeamId}`
        ),
      };
    }

    const project = await Project.findByPk(projectTeam.projectId);
    if (!project) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('Project'),
      };
    }

    const userInProject = await ProjectTeam.findOne({
      where: { projectId: projectTeam.projectId, userId: loggedInUser.id },
    });

    if (!userInProject) {
      return {
        success: false,
        message: errorMessage.NO_PERMISSION('remove team members'),
      };
    }

    if (project.createdBy === projectTeam.user.id) {
      return {
        success: false,
        message: errorMessage.CANNOT_REMOVE_PROJECT_CREATOR,
      };
    }

    await ProjectTeam.destroy({
      where: { id: projectTeamId },
    });

    return {
      success: true,
      message: successMessage.REMOVED_SUCCESS_MESSAGE('Project Team Member'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
