'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(`
      CREATE OR REPLACE FUNCTION create_account_on_user_insert()
      RETURNS TRIGGER AS $$
      BEGIN
        -- Insert a new record into the Account table with default values and userId from the new User record
        INSERT INTO "Account" ("userId", "debitAmount", "creditAmount", "currantBalanceAmount", "createdAt", "updatedAt")
        VALUES (NEW.id, 0.00, 0.00, 0.00, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP);
        
        RETURN NEW;
      END;
      $$ LANGUAGE plpgsql;
    `);

    await queryInterface.sequelize.query(`
      CREATE TRIGGER user_account_creation
      AFTER INSERT ON "User"
      FOR EACH ROW
      EXECUTE FUNCTION create_account_on_user_insert();
    `);
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.sequelize.query(
      `DROP TRIGGER IF EXISTS user_account_creation ON "User";`
    );
    await queryInterface.sequelize.query(
      `DROP FUNCTION IF EXISTS create_account_on_user_insert();`
    );
  },
};
