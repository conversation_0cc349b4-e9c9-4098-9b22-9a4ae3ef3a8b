exports.addUpdateSource = {
  name: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1 },
      errorMessage: 'Name must be at least 1 character long',
    },
    trim: true,
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Logo must be a string',
    },
    trim: true,
  },
  parentSourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Parent Source Id must be a valid integer',
    },
  },
  userId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'userId must be a valid integer',
    },
  },
};

exports.updateLeadSource = {
  sourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Source Id must be a valid integer',
    },
  },
  subSourceId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Sub Source Id must be a valid integer',
    },
  },
};
