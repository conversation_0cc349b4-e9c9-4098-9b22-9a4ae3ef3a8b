paths:
  /admin/sub-admin/send-otp:
    post:
      tags:
        - "Admin"
      summary: "Send OTP for admin user"
      description: "Send OTP to admin for user creation and update confirmation"
      operationId: "sendOtpForAdminUser"
      requestBody:
        description: "Request body for sending OTP"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/sendOtp"
        required: false
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "OTP sent successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /admin/sub-admin:
    get:
      tags:
        - "Admin"
      summary: "Get Admin"
      description: "Get Admin"
      operationId: "getAdmin"
      produces:
        - "application/json"
      parameters:
        - in: "query"
          name: "search"
          schema:
            type: string
        - in: "query"
          name: "start"
          schema:
            type: integer
        - in: "query"
          name: "limit"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "get Admin"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    post:
      tags:
        - "Admin"
      summary: "Create Admin"
      operationId: "createAdmin"
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/createAdmin"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Admin Created successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    put:
      tags:
        - "Admin"
      summary: "Update Profile"
      operationId: "updateProfile"
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/updateProfile"
        required: true
      produces:
        - "application/json"
      parameters: []
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Admin Updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /admin/sub-admin/{id}:
    get:
      tags:
        - "Admin"
      summary: "Get Admin by id"
      description: "Get Admin by id"
      operationId: "getAdminById"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "get Admin"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"  
    put:
      tags:
        - "Admin"
      summary: "Update Admin"
      description: "Update Admin"
      operationId: "putUpdateAdmin"
      requestBody:
        description: Payload is required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/updateAdmin"
        required: true
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Admin Updated Successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Admin"
      summary: "Delete Admin"
      operationId: deleteAdmin
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Admin Deleted successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    patch:
      tags:
        - "Admin"
      summary: "patch Admin Status"
      operationId: patchAdminStatus
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Status Updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /admin/sub-admin/change-password/{id}:
    patch:
      tags:
        - "Admin"
      summary: "change password"
      operationId: "changePassword"
      requestBody:
        description: change password
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/change-password"
        required: true
      produces:
        - "application/json" 
      parameters:
        - in: "path"
          name: "id"
          schema:
            type: integer
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Password saved successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
components:
  schemas:
    createAdmin:
      type: object
      properties:
        tempOtp:
          type: string
          description: Enter OTP
          example: "555555"
        firstName:
          type: string
          description: Enter First Name
          example: "John"
        lastName:
          type: string
          description: Enter Last Name
          example: "Doe"
        countryCode:
          type: string
          description: Enter Country Code
          example: "+91"
        locationId:
          type: integer
          description: Enter Location Id
          example: 1
        mobileNumber:
          type: integer
          description: Enter Mobile Number
          example: 1234567890
        email:
          type: string
          description: Enter Email
          example: "<EMAIL>"
        password:  # New field added
          type: string
          description: Enter Password
          example: "password123"
        confirmPassword:  # New field added
          type: string
          description: Confirm Password
          example: "password123"
        designationId:
          type: integer
          description: Enter Designation Id
          example: 1
      required:
        -firstName
        -lastName
        -countryCode
        -locationId
        -mobileNumber
        -email
        -password
        -confirmPassword
        -designationId
    updateProfile:
      type: object
      properties:
        firstName:
          type: string
          description: Enter First Name
        lastName:
          type: string
          description: Enter Last Name                
        profilePicture:
          type: string
          description: Enter profile picture url
    updateAdmin:
      type: object
      properties:
        tempOtp:
          type: string
          description: Enter OTP
          example: "555555"
        firstName:
          type: string
          description: Enter First Name
          example: "John"
        lastName:
          type: string
          description: Enter Last Name
          example: "Doe"
        countryCode:
          type: string
          description: Enter Country Code
          example: "+91"
        locationId:
          type: integer
          description: Enter Location Id
          example: 1
        mobileNumber:
          type: integer
          description: Enter Mobile Number
          example: 1234567890
        designationId:
          type: integer
          description: Enter Designation Id
          example: 1
      required:
        -firstName
        -lastName
        -countryCode
        -locationId
        -mobileNumber
        -designationId
    change-password:
      type: object
      properties:
        password:
            type: string
            description: enter password
        confirmPassword:
          type: string
          description: enter confirm password
      required:
        - password
        - confirmPassword
    sendOtp:
      type: object
      properties:
        email:
          type: string
          description: Enter Email
          example: "<EMAIL>"
        countryCode:
          type: string
          description: Enter Country Code
          example: "+91"
        mobileNumber:
          type: string
          description: Enter Mobile Number
          example: 12345567890
      required:
      - mobileNumber
      - email
      - countryCode
      
       
     