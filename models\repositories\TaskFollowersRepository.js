const { TaskFollowers } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.addFollower = async (data) => {
  try {
    const follower = await TaskFollowers.create(data);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('follower'),
      data: follower,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteFollower = async (userId, taskId) => {
  try {
    const follower = await TaskFollowers.findOne({
      where: { userId, taskId },
    });

    if (!follower) {
      return {
        success: false,
        message: errorMessage.NO_USER('follower'),
      };
    }

    await follower.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('follower'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
