const {
  priceChargeCalculationType,
  paymentStageCalculationType,
  paymentTriggerType,
  customerStatusTriggerType,
} = require('../config/options');

exports.createAndUpdatePaymentRevision = {
  name: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name is required',
    },
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  startDate: {
    in: ['body'],
    trim: true,
    isDate: {
      options: { format: 'YYYY-MM-DD' },
      errorMessage: 'Invalid startDate format',
    },
  },
  endDate: {
    in: ['body'],
    trim: true,
    isDate: {
      options: { format: 'YYYY-MM-DD' },
      errorMessage: 'Invalid endDate format',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  'pricingCharges.*.name': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'pricingCharges.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  'pricingCharges.*.chargeType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [priceChargeCalculationType.priceChargeCalculationTypeArray()],
      errorMessage: 'Invalid chargeType',
    },
  },
  'pricingCharges.*.rate': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'rate cannot be empty',
    },
    isString: {
      errorMessage: 'rate must be string',
    },
  },
  'pricingCharges.*.hsnCode': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'hsnCode must be string',
    },
  },
  'pricingCharges.*.isTaxable': {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isTaxable must be boolean',
    },
  },
  'pricingCharges.*.taxRate': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'taxRate must be string',
    },
  },
};

exports.createOrUpdatePaymentPlan = {
  name: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'name is required',
    },
    isString: {
      errorMessage: 'name must be a string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  additionalTerm: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'additionalTerm must be string',
    },
  },
  'paymentStages.*.name': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  'paymentStages.*.description': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  'paymentStages.*.calculationType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [paymentStageCalculationType.paymentStageCalculationTypeArray()],
      errorMessage: 'Invalid calculationType',
    },
  },
  'paymentStages.*.amount': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'amount cannot be empty',
    },
    isDecimal: {
      errorMessage: 'amount must be a decimal number',
    },
  },
  'paymentStages.*.triggerType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isIn: {
      options: [paymentTriggerType.paymentTriggerTypeArray()],
      errorMessage: 'Invalid triggerType',
    },
  },
  'paymentStages.*.dueOn': {
    in: ['body'],
    optional: { options: { nullable: true } },
    notEmpty: {
      errorMessage: 'dueOn cannot be empty',
    },
    isString: {
      errorMessage: 'dueOn must be string',
    },
  },
};

exports.createCustomerStatus = {
  name: {
    in: ['body'],
    optional: true,
    notEmpty: {
      errorMessage: 'name cannot be empty',
    },
    isString: {
      errorMessage: 'name must be string',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'description must be string',
    },
  },
  triggerType: {
    in: ['body'],
    isIn: {
      options: [customerStatusTriggerType.customerStatusTriggerTypeArray()],
      errorMessage: 'Invalid triggerType',
    },
  },
  isApprovedRequired: {
    in: ['body'],
    isBoolean: {
      errorMessage: 'isApprovedRequired must be boolean',
    },
  },
  dueOn: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'dueOn cannot be empty',
    },
    isString: {
      errorMessage: 'dueOn must be string',
    },
  },
};
