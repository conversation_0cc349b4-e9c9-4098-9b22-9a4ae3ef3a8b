'use strict';
const { grnMediaType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('GrnMedia', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      mediaType: {
        type: Sequelize.ENUM(...grnMediaType.getValues()),
        allowNull: false,
        defaultValue: grnMediaType.GRN,
      },
      fileName: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      fileType: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      filePath: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      fileSize: {
        type: Sequelize.DECIMAL(10, 2),
        allowNull: false,
      },
      grnId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Grn',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      grnItemId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'GrnItem',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('GrnMedia');
  },
};
