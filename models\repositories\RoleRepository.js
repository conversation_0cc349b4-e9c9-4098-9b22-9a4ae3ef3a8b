const { Op } = require('sequelize');
const { Department, Designation, sequelize } = require('..');
const { errorMessage, successMessage } = require('@config/options');
const { checkExistence } = require('@helpers/QueryHelper');

exports.validateAndCreateRole = async (data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const { reportTo, departmentId } = data;
  const selectFields = ['id'];
  const transaction = await sequelize.transaction();
  try {
    const validationPromises = [
      checkExistence(Department, { id: departmentId }, selectFields).then(
        (checkDepartment) => {
          if (!checkDepartment) {
            throw new Error(
              errorMessage.DOES_NOT_EXIST(
                `Department with id: ${departmentId} does not exist`
              )
            );
          }
        }
      ),

      checkExistence(
        Designation,
        { organizationId, name: { [Op.iLike]: data.name } },
        selectFields
      ).then((duplicateNameCheck) => {
        if (duplicateNameCheck) {
          throw new Error(
            errorMessage.ALREADY_EXIST(
              `Role with this name already exists in the department: ${data.name}`
            )
          );
        }
      }),

      reportTo
        ? checkExistence(Designation, { id: reportTo }, selectFields).then(
            (checkReportTo) => {
              if (!checkReportTo) {
                throw new Error(
                  errorMessage.DOES_NOT_EXIST(
                    `Role to report to with id: ${reportTo} does not exist`
                  )
                );
              }
            }
          )
        : Promise.resolve(),
    ];

    await Promise.all(validationPromises);
    const createdRole = await Designation.create(
      { ...data, organizationId, createdBy: loggedInUser.id },
      { transaction }
    );

    await transaction.commit();
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Role'),
      data: createdRole,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while creating the role',
    };
  }
};

exports.validateAndUpdateRole = async (roleId, data, loggedInUser) => {
  const organizationId = loggedInUser.currentOrganizationId;
  const { reportTo, departmentId, name } = data;
  const selectFields = ['id'];
  const transaction = await sequelize.transaction();

  try {
    const role = await checkExistence(Designation, { id: roleId });
    if (!role) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`Role with id: ${roleId} does not exist`)
      );
    }

    // const currentDepartmentId = departmentId || role.departmentId;
    const validations = [
      departmentId
        ? checkExistence(Department, { id: departmentId }, selectFields).then(
            (checkDepartment) => {
              if (!checkDepartment) {
                throw new Error(
                  errorMessage.DOES_NOT_EXIST(
                    `Department with id: ${departmentId} does not exist`
                  )
                );
              }
            }
          )
        : Promise.resolve(),

      name
        ? checkExistence(
            Designation,
            {
              organizationId,
              // departmentId: currentDepartmentId,
              name: { [Op.iLike]: name },
              id: { [Op.ne]: roleId },
            },
            selectFields
          ).then((checkName) => {
            if (checkName) {
              throw new Error(
                errorMessage.ALREADY_EXIST(
                  `Role with this name already exists: ${name}`
                )
              );
            }
          })
        : Promise.resolve(),

      reportTo
        ? checkExistence(Designation, { id: reportTo }, selectFields).then(
            (checkReportTo) => {
              if (!checkReportTo) {
                throw new Error(
                  errorMessage.DOES_NOT_EXIST(
                    `Role to report to with id: ${reportTo} does not exist`
                  )
                );
              }
            }
          )
        : Promise.resolve(),
    ];

    await Promise.all(validations);

    Object.assign(role, data);
    await role.save({ transaction });
    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Role'),
      data: role,
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while updating the role',
    };
  }
};

exports.deleteRole = async (roleId, loggedInUser) => {
  const transaction = await sequelize.transaction();
  try {
    const role = await checkExistence(Designation, { id: roleId }, ['id']);
    if (!role) {
      throw new Error(
        errorMessage.DOES_NOT_EXIST(`Role with id: ${roleId} does not exist`)
      );
    }

    await Designation.destroy({ where: { id: roleId }, transaction });
    await transaction.commit();
    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Role'),
    };
  } catch (error) {
    await transaction.rollback();
    console.log('deleteRole....', error);
    return {
      success: false,
      message: error.message || 'An error occurred while deleting the role',
    };
  }
};
