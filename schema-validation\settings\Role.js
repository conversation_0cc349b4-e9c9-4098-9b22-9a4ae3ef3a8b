exports.createRole = {
  departmentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'DepartmentId must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Role name cannot be empty',
    isString: {
      errorMessage: 'Role name must be a string',
    },
    isLength: {
      options: { max: 255 },
      errorMessage: 'Role name must be at most 255 characters long',
    },
  },
  reportTo: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'ReportTo must be a valid integer',
    },
  },
};

exports.updateRole = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Role Id is required',
    },
    isInt: {
      errorMessage: 'Role Id must be a valid integer',
    },
  },
  departmentId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'DepartmentId must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    trim: true,
    notEmpty: {
      errorMessage: 'Role name cannot be empty',
    },
    isString: {
      errorMessage: 'Role name must be a string',
    },
    isLength: {
      options: { max: 255 },
      errorMessage: 'Role name must be at most 255 characters long',
    },
  },
  reportTo: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'ReportTo must be a valid integer',
    },
  },
};

exports.deleteRole = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Role Id is required',
    },
    isInt: {
      errorMessage: 'Role Id must be a valid integer',
    },
  },
};
