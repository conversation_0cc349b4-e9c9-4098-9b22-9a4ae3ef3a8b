/* eslint-disable camelcase */
const axios = require('axios');
const MSG91_API_URL = 'https://control.msg91.com/api/v5/email/send';

exports.sendEmail = async (data, template_id) => {
  try {
    const options = {
      to: [
        {
          email: data.to,
          name: data.name || '',
        },
      ],
      from: {
        email: process.env.MSG91_FROM_EMAIL,
        name: process.env.MSG91_FROM_NAME,
      },
      domain: process.env.MSG91_DOMAIN,
      template_id: template_id,
      variables: {
        ...data.variables,
      },
    };

    if (!options.template_id) {
      options.content = [
        {
          type: 'text/html',
          value: data.content,
        },
      ];
    }

    const response = await axios.post(MSG91_API_URL, options, {
      headers: {
        accept: 'application/json',
        'content-type': 'application/json',
        authkey: process.env.MSG91_AUTH_KEY,
      },
    });

    if (response.data && response.data.status === 'success') {
      return response.data;
    } else {
      throw new Error(response.data.message || 'Failed to send email');
    }
  } catch (error) {
    console.error('Email Error:', error.response?.data || error.message);
    throw new Error(
      `Failed to send email: ${error.response?.data?.message || error.message}`
    );
  }
};
