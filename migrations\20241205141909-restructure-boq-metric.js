'use strict';
const { metricType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('BOQMetric', 'metricType', {
      type: Sequelize.ENUM(metricType.getMetricTypeArray()),
      allowNull: false,
      defaultValue: metricType.VOLUME,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQMetric', 'metricType');
    await queryInterface.sequelize.query(
      'DROP TYPE "enum_BOQMetric_metricType";'
    );
  },
};
