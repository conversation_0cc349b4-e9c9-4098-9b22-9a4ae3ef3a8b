'use strict';

const { projectTypeCategory } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('ProjectType', 'projectCategory', {
      type: Sequelize.ENUM(
        ...projectTypeCategory.getProjectTypeCategoryArray()
      ),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('ProjectType', 'projectCategory');

    await queryInterface.sequelize.query(
      'DROP TYPE "enum_ProjectType_projectCategory";'
    );
  },
};
