const { defaultStatus } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('WorkOrder', 'status', {
      type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
      allowNull: false,
      defaultValue: defaultStatus.ACTIVE,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('WorkOrder', 'status');
  },
};
