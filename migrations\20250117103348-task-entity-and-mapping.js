'use strict';

const { taskStatus, taskPriority } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Tasks', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      title: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM(taskStatus.getValues()),
        allowNull: false,
        defaultValue: taskStatus.TODO,
      },
      priority: {
        type: Sequelize.ENUM(taskPriority.getValues()),
        allowNull: false,
        defaultValue: taskPriority.MEDIUM,
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      projectId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Project',
          key: 'id',
        },
        allowNull: true,
      },
      assignedTo: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      assignedBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      updatedBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        allowNull: false,
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.createTable('TaskTagMapping', {
      taskId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Tasks',
          key: 'id',
        },
        primaryKey: true,
      },
      tagId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'TaskTags',
          key: 'id',
        },
        primaryKey: true,
      },
    });

    await queryInterface.createTable('TaskUnitMapping', {
      taskId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Tasks',
          key: 'id',
        },
        primaryKey: true,
      },
      unitId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Unit',
          key: 'id',
        },
        primaryKey: true,
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('TaskUnitMapping');
    await queryInterface.dropTable('TaskTagMapping');
    await queryInterface.dropTable('Tasks');
  },
};
