const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TemplateControl = require('@controllers/v1/settings/Template');
const TemplateSchema = require('@schema-validation/settings/Template');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/salary-template',
  checkSchema(TemplateSchema.createOrUpdateSalaryTemplate),
  ErrorHandleHelper.requestValidator,
  TemplateControl.createSalaryTemplate
);

router.put(
  '/salary-template/:id',
  checkSchema(TemplateSchema.createOrUpdateSalaryTemplate),
  ErrorHandleHelper.requestValidator,
  TemplateControl.putSalaryTemplate
);

router.get('/salary-template/:id', TemplateControl.getSalaryTemplate);

router.delete('/salary-template/:id', TemplateControl.deleteTemplate);
router.delete('/leave-template/:id', TemplateControl.deleteTemplate);

router.post(
  '/leave-template',
  checkSchema(TemplateSchema.createOrUpdateLeaveTemplate),
  ErrorHandleHelper.requestValidator,
  TemplateControl.createLeaveTemplate
);

router.put(
  '/leave-template/:id',
  checkSchema(TemplateSchema.createOrUpdateLeaveTemplate),
  ErrorHandleHelper.requestValidator,
  TemplateControl.putLeaveTemplate
);

module.exports = router;
