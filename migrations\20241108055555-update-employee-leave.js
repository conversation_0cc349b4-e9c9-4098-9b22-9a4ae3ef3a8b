'use strict';

const { leaveStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('EmployeeLeave', 'totalDays', {
      type: Sequelize.DECIMAL(3, 1),
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeLeave', 'appliedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeLeave', 'approvedAt', {
      type: Sequelize.DATE,
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeLeave', 'status', {
      type: Sequelize.ENUM(leaveStatus.leaveStatusTypeArray()),
      allowNull: false,
      defaultValue: leaveStatus.PENDING,
    });

    await queryInterface.addColumn('EmployeeLeave', 'reason', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('EmployeeLeave', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('EmployeeLeave', 'totalDays');
    await queryInterface.removeColumn('EmployeeLeave', 'appliedAt');
    await queryInterface.removeColumn('EmployeeLeave', 'approvedAt');
    await queryInterface.removeColumn('EmployeeLeave', 'status');
    await queryInterface.removeColumn('EmployeeLeave', 'reason');
    await queryInterface.removeColumn('EmployeeLeave', 'createdBy');
  },
};
