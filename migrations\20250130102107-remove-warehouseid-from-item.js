'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeConstraint('Item', 'Item_warehouseId_fkey');

    await queryInterface.removeColumn('Item', 'warehouseId');
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.addColumn('Item', 'warehouseId', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'Warehouse',
        key: 'id',
      },
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  },
};
