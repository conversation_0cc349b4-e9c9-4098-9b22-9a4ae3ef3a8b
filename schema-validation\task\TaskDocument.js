exports.addTaskDocuments = {
  taskId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Task ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Task ID must be an integer',
    },
  },
  'document.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'document.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'document.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'document.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};
