{"local": {"use_env_variable": "LOCAL_DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": true, "seederStorage": "sequelize", "dialectOptions": {"ssl": false}, "pool": {"max": 20, "min": 0, "acquire": 60000, "idle": 10000}}, "development": {"use_env_variable": "DEV_DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": true, "seederStorage": "sequelize", "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}, "pool": {"max": 20, "min": 0, "acquire": 60000, "idle": 10000}}, "test": {"use_env_variable": "TEST_DATABASE_URL", "logging": false, "sync": false, "dialect": "postgres", "ssl": true, "seederStorage": "sequelize", "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}, "pool": {"max": 20, "min": 0, "acquire": 60000, "idle": 10000}}, "production": {"use_env_variable": "DATABASE_URL", "logging": false, "sync": false, "seederStorage": "sequelize", "dialect": "postgres", "ssl": true, "dialectOptions": {"ssl": {"require": true, "rejectUnauthorized": false}}, "pool": {"max": 20, "min": 0, "acquire": 60000, "idle": 10000}}}