const { recordStatus, journalState } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Journal = sequelize.define(
    'Journal',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      organizationId: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
      date: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      journalNumber: {
        type: DataTypes.STRING,
        defaultValue: null,
        allowNull: false,
      },
      notes: {
        type: DataTypes.STRING,
        defaultValue: null,
        allowNull: false,
      },
      totalAmount: {
        type: DataTypes.DECIMAL(20, 2),
        defaultValue: 0,
        allowNull: true,
      },
      journalState: {
        type: DataTypes.ENUM(journalState.getValues()),
        defaultValue: journalState.DRAFT,
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(recordStatus.getValues()),
        defaultValue: recordStatus.ACTIVE,
        allowNull: false,
      },
      createdBy: {
        type: DataTypes.INTEGER,
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Journal.associate = (models) => {
    Journal.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'createdByUser',
      onDelete: 'NO ACTION',
    });

    Journal.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'NO ACTION',
    });

    Journal.hasMany(models.TransactionEntry, {
      foreignKey: 'referenceId',
      constraints: false,
      scope: {
        referenceType: 'journal',
      },
      as: 'transactionEntries',
    });
    Journal.hasOne(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        requestType: 'journal_request',
      },
      as: 'request',
    });
  };

  return Journal;
};
