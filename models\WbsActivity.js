'use strict';

const OPTIONS = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const WbsActivity = sequelize.define(
    'WbsActivity',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      activityType: {
        type: DataTypes.ENUM(...OPTIONS.wbsActivityType.getActivityTypeArray()),
        allowNull: false,
      },
      name: {
        type: DataTypes.TEXT,
        allowNull: false,
      },
      wbsCode: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      shortCode: {
        type: DataTypes.STRING(20),
        allowNull: true,
      },
      color: {
        type: DataTypes.STRING(50),
        allowNull: true,
      },
      iconUrl: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(...OPTIONS.defaultStatus.getDefaultStatusArray()),
        allowNull: true,
      },
      startDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      endDate: {
        type: DataTypes.DATE,
        allowNull: true,
      },
      metricValue: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      metricType: {
        type: DataTypes.STRING(255),
        allowNull: true,
      },
      rate: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      total: {
        type: DataTypes.DECIMAL(15, 2),
        allowNull: true,
      },
      estimatedActivityBudget: {
        type: DataTypes.DECIMAL(20, 2),
        allowNull: true,
      },
      level: {
        type: DataTypes.INTEGER,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
      hooks: {
        beforeCreate: (activity) => {
          if (activity.wbsCode) {
            activity.level = activity.wbsCode.split('.').length - 1;
          } else {
            activity.level = 0;
          }
        },
        beforeUpdate: (activity) => {
          if (activity.wbsCode) {
            activity.level = activity.wbsCode.split('.').length - 1;
          } else {
            activity.level = 0;
          }
        },
      },
    }
  );

  WbsActivity.associate = (models) => {
    WbsActivity.belongsTo(models.WbsActivity, {
      foreignKey: 'parentWbsActivityId',
      as: 'parentWbsActivity',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivity.belongsTo(models.WbsActivity, {
      foreignKey: 'wbsCategoryId',
      as: 'wbsCategory',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivity.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    WbsActivity.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivity.belongsTo(models.Account, {
      foreignKey: 'accountId',
      as: 'account',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });

    WbsActivity.hasMany(models.WbsActivity, {
      foreignKey: 'parentWbsActivityId',
      as: 'subWbsActivities',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasMany(models.WbsActivityDependency, {
      foreignKey: 'wbsActivityId',
      as: 'dependencies',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasMany(models.WbsActivityDependency, {
      foreignKey: 'activityId',
      as: 'dependentWbsActivities',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasMany(models.WbsActivityProjectMapping, {
      foreignKey: 'wbsActivityId',
      as: 'projectMappings',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasMany(models.WbsComment, {
      foreignKey: 'wbsActivityId',
      as: 'comments',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasMany(models.WbsDocument, {
      foreignKey: 'wbsActivityId',
      as: 'documents',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasMany(models.WbsActivityQualityControl, {
      foreignKey: 'wbsActivityId',
      as: 'qualityControls',
      onDelete: 'SET NULL',
      onUpdate: 'CASCADE',
    });
    WbsActivity.hasOne(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        requestType: 'wbs_request',
      },
      as: 'request',
    });
  };

  return WbsActivity;
};
