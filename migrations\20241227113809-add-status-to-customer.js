'use strict';
const OPTIONS = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Customer', 'status', {
      type: Sequelize.ENUM(OPTIONS.customerStatus.getCustomerStatusArray()),
      allowNull: false,
      defaultValue: OPTIONS.customerStatus.NEW_LEAD,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Customer', 'status');
  },
};
