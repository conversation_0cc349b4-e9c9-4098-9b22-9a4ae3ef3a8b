/* eslint-disable camelcase */
const sequelize = require('sequelize');
const chalk = require('chalk');
const crypto = require('crypto');

const {
  User,
  EmployeeDocument,
  Address,
  UserOrganization,
  sequelize: Sequelize,
} = require('..');
const { Op } = sequelize;
const {
  errorMessage,
  defaultStatus,
  usersRoles,
  successMessage,
  emailType,
  notificationType,
  documentType,
  addressType,
} = require('../../config/options');
const UserHelper = require('../helpers/UserHelper');

const options = require('../../config/options');
const SMSHelper = require('../helpers/SMSHelper');
const EmailHelper = require('../helpers/EmailHelper');
const NotificationHelper = require('../helpers/NotificationHelper');
const {
  parseMobileNumber,
  generatePassword,
} = require('../helpers/UtilHelper');
const WorkspaceRepository = require('./WorkspaceRepository');
const ModulePermissionRepository = require('./ModulePermissionRepository');
const BankDetailsRepository = require('./BankDetailsRepository');

const {
  createOrganization,
  createBrand,
  validateOrganization,
  handleMediaUpload,
  updateUserDetails,
  createAddressForOrganization,
  createUserOrganization,
} = require('@helpers/RegisterBrandHelper');

exports.getUsersAndCount = async (query) => await User.findAndCountAll(query);

exports.getAllUser = async (query) => await User.findAndCountAll(query);

exports.getUser = async (query) => await User.findOne(query);

exports.findAll = async (query) => await User.findAll(query);

exports.createAndUpdateUser = async (
  data,
  userId = null,
  transaction = null
) => {
  try {
    let existingUser;
    if (userId) {
      existingUser = await this.getUser({
        where: {
          id: userId,
        },
      });
    }
    const payload = {
      firstName: data.firstName,
      lastName: data.lastName,
      countryCode: data.countryCode,
      mobileNumber: data.mobileNumber,
      profilePicture: data.profilePicture || null,
      role: data.role || existingUser.role || usersRoles.USER,
      status: data.status || defaultStatus.ACTIVE,
      email: data.email || existingUser.email,
      registrationPlatform: data.registrationPlatform || 'web',
      isFromAdmin: data.isFromAdmin || false,
      locationId: data.locationId,
      designationId: data.designationId,
      organizationId: data.organizationId,
      currentOrganizationId: data.organizationId,
      gender: data.gender,
      middleName: data.middleName,
      personalEmail: data.personalEmail,
      alternateCountryCode: data.alternateCountryCode,
      alternateMobileNumber: data.alternateMobileNumber,
      dateOfBirth: data.dateOfBirth,
      about: data.about,
      maritalStatus: data.maritalStatus,
      panNumber: data.panNumber,
      // createdAt: data.dateOfJoining | new Date(),
      locationId: data.locationId,
    };
    if (existingUser) {
      Object.assign(existingUser, payload);
      await existingUser.save({ transaction });
      return existingUser;
    } else {
      if (transaction) {
        return await User.create(payload, { transaction });
      } else {
        return await User.create(payload);
      }
    }
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateUser = async (query, data) => {
  try {
    const existingUser = await User.findOne(query);

    if (!existingUser) {
      return { success: false, message: errorMessage.DOES_NOT_EXIST('User') };
    }

    if (data.mobileNumber) {
      const duplicateUser = await this.checkDuplicateUserByMobileNumber(
        existingUser,
        data
      );

      if (!duplicateUser.success) {
        return duplicateUser;
      }
    }

    Object.assign(existingUser, data);

    if (data.password) {
      existingUser.password = await generatePassword(data.password);
    }

    await existingUser.save();
    return {
      success: true,
      data: UserHelper.modifyOutputData(existingUser),
      message: successMessage.UPDATE_SUCCESS_MESSAGE('User'),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.checkDuplicateUserByMobileNumber = async (user, data) => {
  const mobileNumberQuery = {
    where: {
      countryCode: data.countryCode,
      mobileNumber: data.mobileNumber,
      id: { [Op.not]: user.id },
    },
  };

  const existingUser = await this.getUser(mobileNumberQuery);

  return {
    success: !existingUser,
    message: existingUser ? errorMessage.MOBILE_NUMBER_ALREADY_EXISTS : null,
  };
};

exports.updateContactsDetails = async (query, data) => {
  try {
    const existingUser = await User.findOne(query);
    if (!existingUser) {
      return { success: false, message: errorMessage.DOES_NOT_EXIST('User') };
    }
    existingUser.dateOfBirth = data.dateOfBirth;
    existingUser.twitterURL = data.twitterURL;
    existingUser.linkedInURL = data.linkedInURL;
    existingUser.facebookURL = data.facebookURL;
    existingUser.researchGateURL = data.researchGateURL;
    existingUser.address = data.address;
    existingUser.city = data.city;
    existingUser.state = data.state;
    existingUser.pincode = data.pincode;
    existingUser.countryName = data.countryName;
    existingUser.websiteURL = data.websiteURL;
    existingUser.miscellaneousLink = data.miscellaneousLink;
    await existingUser.save();
    return { success: true, data: existingUser };
  } catch (e) {
    throw new Error(e);
  }
};

exports.findAndCountAll = async (query) => await User.findAndCountAll(query);

exports.findAll = async (query) => await User.findAll(query);

exports.patchUpdateStatus = async (existingUser, status, isDelete, user) => {
  try {
    existingUser.status = status;
    if (isDelete) {
      existingUser.status = defaultStatus.DELETED;
      existingUser.mobileNumber = `${existingUser.mobileNumber}${Date.now()}'${
        defaultStatus.DELETED
      }'`;
      existingUser.email = `${existingUser.email}${Date.now()}'${
        defaultStatus.DELETED
      }'`;
      if (existingUser.secondaryEmail) {
        existingUser.secondaryEmail = `${
          existingUser.secondaryEmail
        }${Date.now()}'${defaultStatus.DELETED}'`;
      }
      if (existingUser.secondaryCountryCode) {
        existingUser.secondaryCountryCode = `${
          existingUser.secondaryCountryCode
        }${Date.now()}'${defaultStatus.DELETED}'`;
      }
      if (existingUser.secondaryMobileNumber) {
        existingUser.secondaryMobileNumber = `${
          existingUser.secondaryMobileNumber
        }${Date.now()}'${defaultStatus.DELETED}'`;
      }
    }
    await existingUser.save();
    if (
      existingUser.role === usersRoles.USER &&
      existingUser.status !== defaultStatus.DELETED
    ) {
      const emailTypeMap = {
        [defaultStatus.ACTIVE]: emailType.EMAIL_PROFILE_APPROVED,
        [defaultStatus.REJECTED]: emailType.EMAIL_PROFILE_REJECTED,
        [defaultStatus.INACTIVE]: emailType.EMAIL_ACCOUNT_BLOCKED,
      };
      EmailHelper.sendEmail(
        {
          id: existingUser.id,
          firstName: existingUser.firstName,
          lastName: existingUser.lastName,
          email: existingUser.email,
        },
        emailTypeMap[existingUser.status]
      );
      if (existingUser.status === defaultStatus.ACTIVE) {
        NotificationHelper.sendNotification(
          {
            id: existingUser.id,
            receiverId: existingUser.id,
            sender: {
              id: user.id,
            },
            type: notificationType.ACCOUNT_APPROVED,
          },
          notificationType.ACCOUNT_APPROVED
        );
      }
    }
    return existingUser;
  } catch (error) {
    throw new Error(error);
  }
};

exports.checkAndAdminLoginWithPassword = async (body) => {
  const query = {
    where: {
      status: { [Op.notIn]: [defaultStatus.DELETED] },
      email: body.email,
      role: [usersRoles.SUPER_ADMIN, usersRoles.ADMIN],
    },
  };
  const existingUser = await this.getUser(query);
  if (!existingUser) {
    return {
      success: false,
      message: errorMessage.NO_USER('data'),
    };
  } else if (!existingUser.validPassword(body.password)) {
    return {
      success: false,
      message: errorMessage.INVALID_CREDENTIALS,
    };
  } else if (existingUser && existingUser.status === defaultStatus.INACTIVE) {
    return {
      success: false,
      message: errorMessage.USER_ACCOUNT_BLOCKED,
    };
  } else {
    existingUser.lastSignInAt = new Date();
    await existingUser.save();
    return {
      success: true,
      message: successMessage.LOG('logged in'),
      data: UserHelper.modifyOutputData(existingUser),
    };
  }
};

exports.checkAndLoginWithPassword = async (
  body,
  sendOtp = false,
  requestDetails = {}
) => {
  const query = {
    where: {
      status: { [Op.notIn]: [defaultStatus.DELETED] },
      [Op.or]: [
        body.email && { email: body.email },
        body.mobileNumber && {
          [Op.and]: [
            { mobileNumber: body.mobileNumber },
            { countryCode: body.countryCode },
          ],
        },
      ],
    },
  };
  const existingUser = await this.getUser(query);
  if (!existingUser) {
    return {
      success: false,
      message: errorMessage.NO_USER('data'),
    };
  }
  if (!existingUser.validPassword(body.password)) {
    return {
      success: false,
      message: errorMessage.INVALID_CREDENTIALS,
    };
  }
  if (existingUser && existingUser.status === defaultStatus.BLOCKED) {
    return {
      success: false,
      message: errorMessage.USER_ACCOUNT_BLOCKED,
    };
  }

  const userData = UserHelper.modifyOutputData(existingUser);
  existingUser.lastSignInAt = new Date();
  await existingUser.save();

  let message = successMessage.LOG('logged in');
  if (sendOtp && existingUser.isMfaEnabled === true) {
    const isEmail = body.email ? true : false;
    await this.generateAndSendOtp(existingUser, isEmail);
    // userData.token = null;
    message = successMessage.OTP_SEND('email');
  }

  if (existingUser.isMfaEnabled != true) {
    await UserHelper.saveLoginHistoryAndSession(
      existingUser,
      userData.token,
      requestDetails
    );
  }

  const permissions = await ModulePermissionRepository.findPermissionsByRoleId(
    userData.designationId
  );
  userData.permissions = permissions;
  return {
    success: true,
    message: message,
    data: userData,
  };
};

exports.checkAndCreate = async (body) => {
  const query = {
    where: {
      status: { [Op.notIn]: [defaultStatus.DELETED] },
      [Op.or]: [
        body.email && { email: body.email },
        body.mobileNumber && {
          [Op.and]: [
            { mobileNumber: body.mobileNumber },
            { countryCode: body.countryCode },
          ],
        },
      ],
    },
    attributes: [
      'id',
      'firstName',
      'lastName',
      'countryCode',
      'mobileNumber',
      'email',
      'profilePicture',
      'isMobileNumberVerified',
      'isEmailVerified',
      'status',
      'lastSignInAt',
    ],
  };
  const existingUser = await this.getUser(query);
  if (!existingUser) {
    const newUser = await this.createAndUpdateUser(body);
    const userData = UserHelper.modifyOutputData(newUser);
    await UserHelper.createEmployeeIfNotExists(userData);

    // send email
    await this.generateAndSendOtp(newUser, true);
    const message = options.successMessage.ADD_SUCCESS_MESSAGE('User');
    return {
      success: true,
      message,
      data: userData,
      isNew: true,
    };
  }

  await this.generateAndSendOtp(existingUser, true);

  if (existingUser.status === defaultStatus.PENDING) {
    const userData = UserHelper.modifyOutputData(existingUser);
    return {
      success: true,
      message: options.successMessage.ADD_SUCCESS_MESSAGE('User'),
      data: userData,
    };
  }

  if (existingUser && existingUser.status === defaultStatus.BLOCKED) {
    return {
      success: false,
      message: errorMessage.USER_ACCOUNT_BLOCKED,
    };
  }
  return {
    success: false,
    message: errorMessage.EXISTS_USER('email or mobile number'),
  };
};

exports.generateAndSendOtp = async (existingUser, isEmail = false) => {
  const todayDate = new Date();
  const tempOtp = options.genOtp();
  todayDate.setDate(todayDate.getDate() + options.otpExpireInDays);
  existingUser.tempOtp = tempOtp;
  existingUser.tempOtpExpiresAt = todayDate;
  await existingUser.save();

  if (isEmail) {
    const emailData = {
      to: existingUser.email,
      variables: {
        otp: tempOtp,
      },
    };
    EmailHelper.sendEmail(emailData, 'bricko_otp_verification_email');
  } else {
    const user = {
      id: existingUser.id,
      firstName: existingUser.firstName,
      lastName: existingUser.lastName,
      mobileNumber: existingUser.mobileNumber,
      countryCode: existingUser.countryCode,
    };
    SMSHelper.sendMobileOtp(user, tempOtp);
  }
};

exports.checkUserAndLoginWithOtp = async (body) => {
  try {
    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        role: [usersRoles.USER],
        ...(body.email && {
          email: body.email,
        }),
        ...(body.mobileNumber && {
          [Op.and]: {
            mobileNumber: body.mobileNumber,
            countryCode: body.countryCode,
          },
        }),
      },
    };
    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER('register mobile number'),
      };
    } else if (existingUser && existingUser.status === defaultStatus.INACTIVE) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    }
    await this.generateAndSendOtp(existingUser, false);
    return {
      success: true,
      message: successMessage.OTP_SEND('register mobile number'),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.checkAndVerifyOtp = async (
  body,
  isEmail = false,
  requestDetails = {}
) => {
  try {
    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        tempOtp: body.tempOtp,
        tempOtpExpiresAt: { [Op.gte]: new Date() },
        ...(!isEmail
          ? { mobileNumber: body.mobileNumber, countryCode: body.countryCode }
          : { email: body.email }),
      },
    };
    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }
    existingUser.tempOtp = null;
    existingUser.lastSignInAt = new Date();
    existingUser.tempOtpExpiresAt = null;
    if (isEmail) {
      existingUser.isEmailVerified = true;
    } else {
      existingUser.isMobileNumberVerified = true;
    }
    await existingUser.save();

    const userData = UserHelper.modifyOutputData(existingUser);
    await UserHelper.saveLoginHistoryAndSession(
      existingUser,
      userData.token,
      requestDetails
    );

    const permissions =
      await ModulePermissionRepository.findPermissionsByRoleId(
        existingUser.designationId
      );
    userData.permissions = permissions;

    return {
      success: true,
      message: successMessage.OTP_VERIFIED(),
      data: userData,
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.checkUserAndSendOtp = async (body, id) => {
  try {
    const query = {
      where: {
        id,
      },
      attributes: [
        'id',
        'status',
        'email',
        'mobileNumber',
        'countryCode',
        'firstName',
        'lastName',
        'tempOtp',
        'tempOtpExpiresAt',
      ],
    };
    const contactType = body.email ? 'email' : 'mobile number';
    const existingUser = await this.getUser(query);
    if (!existingUser && body.email) {
      return {
        success: false,
        message: errorMessage.NO_USER(contactType),
      };
    } else if (
      existingUser &&
      [defaultStatus.BLOCKED, defaultStatus.INACTIVE].includes(
        existingUser.status
      )
    ) {
      return {
        success: false,
        message: errorMessage.USER_ACCOUNT_BLOCKED,
      };
    }
    if (body.mobileNumber) {
      const mobileQuery = {
        where: {
          mobileNumber: body.mobileNumber,
          countryCode: body.countryCode,
          id: { [Op.ne]: id },
        },
      };

      const otherUserWithSameNumber = await this.getUser(mobileQuery);
      if (otherUserWithSameNumber) {
        return {
          success: false,
          message: errorMessage.ALREADY_EXIST('Mobile number'),
        };
      }
    }
    existingUser.mobileNumber = body.mobileNumber;
    existingUser.countryCode = body.countryCode;
    await this.generateAndSendOtp(existingUser, body.email);
    return {
      success: true,
      message: successMessage.OTP_SEND(contactType),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.resendOtp = async (body) => {
  const { type, countryCode, mobileNumber, email } = body;
  try {
    const contactType = type === 'email' ? 'email' : 'mobile number';

    const query = {
      where: {
        ...(type === 'email' ? { email } : { mobileNumber, countryCode }),
      },
      attributes: [
        'id',
        'status',
        'email',
        'mobileNumber',
        'countryCode',
        'firstName',
        'lastName',
        'tempOtp',
        'tempOtpExpiresAt',
      ],
    };

    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER(contactType),
      };
    }

    if (contactType === 'email') {
      await this.generateAndSendOtp(existingUser, true);
    } else {
      await this.generateAndSendOtp(existingUser, false);
    }

    return {
      success: true,
      message: successMessage.OTP_SEND(contactType),
    };
  } catch (e) {
    throw new Error('Failed to resend OTP. Please try again later.');
  }
};

exports.updateProfilePicture = async (existingUser, data) => {
  try {
    existingUser.profilePicture = data.profilePicture;
    await existingUser.save();
    this.processUserDataChange(existingUser.id, true).then();
    return existingUser;
  } catch (e) {
    throw new Error(e);
  }
};

exports.bulkCreate = async (users = []) => {
  try {
    console.log(chalk.yellow('#'), 'Bulk data of length: ', users.length);
    for (const user of users) {
      const payload = {
        firstName: user['First Name'],
        lastName: user['Last Name'],
        email: user.Email,
        role: usersRoles.USER,
        registrationPlatform: 'upload',
        isFromAdmin: true,
      };
      const parseNumberData = parseMobileNumber(`+${user.Mobile.toString()}`);
      if (
        parseNumberData &&
        parseNumberData.possible &&
        parseNumberData.valid
      ) {
        payload.mobileNumber = parseNumberData.number.significant;
        payload.countryCode = parseNumberData.countryCode.toString();
      }
      console.log(chalk.yellow('#'), 'payload', payload);
      const query = {
        where: {
          status: { [Op.notIn]: [defaultStatus.DELETED] },
          [Op.or]: [
            payload.email && {
              email: payload.email,
            },
            payload.mobileNumber && {
              [Op.and]: {
                mobileNumber: payload.mobileNumber,
                countryCode: payload.countryCode,
              },
            },
          ],
        },
        attributes: [
          'id',
          'firstName',
          'lastName',
          'countryCode',
          'mobileNumber',
          'email',
          'profilePicture',
          'userName',
          'isMobileNumberVerified',
          'isEmailVerified',
          'status',
          'lastSignInAt',
        ],
      };
      const existingUser = await this.getUser(query);
      if (!existingUser) {
        const newUser = await this.createAndUpdateUser(payload);
        console.log(
          chalk.green('✓'),
          options.successMessage.ADD_SUCCESS_MESSAGE('User'),
          ':',
          JSON.stringify(newUser)
        );
      } else {
        console.log(
          chalk.red('X'),
          errorMessage.EXISTS_USER('email'),
          ':',
          JSON.stringify(payload)
        );
        existingUser.lastName = payload.lastName;
        existingUser.firstName = payload.firstName;
        if (
          parseNumberData &&
          parseNumberData.possible &&
          parseNumberData.valid
        ) {
          existingUser.mobileNumber = parseNumberData.number.significant;
          existingUser.countryCode = parseNumberData.countryCode.toString();
        }
        await existingUser.save();
        console.log(
          chalk.green('✓'),
          'Updating first name and last name',
          ':',
          JSON.stringify(existingUser)
        );
      }
    }
    return;
  } catch (error) {
    throw new Error(error);
  }
};

exports.verifyUserAndSendEmailOtp = async (body, role) => {
  try {
    const { email } = body;
    const query = {
      where: {
        role: role,
        email: email,
      },
    };

    const existingUser = await this.getUser(query);

    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER('email'),
      };
    }

    await this.generateAndSendOtp(existingUser, true);
    return {
      success: true,
      message: successMessage.OTP_SEND('email'),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.validateUserAndVerifyOtp = async (body, isEmail = false) => {
  try {
    const query = {
      where: {
        tempOtp: body.tempOtp,
        tempOtpExpiresAt: { [Op.gte]: new Date() },
        email: body.email,
      },
    };
    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }
    return {
      success: true,
      message: successMessage.OTP_VERIFIED(),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.resetPasswordWithEmailOtp = async (body, isEmail = false) => {
  try {
    const query = {
      where: {
        tempOtp: body.tempOtp,
        tempOtpExpiresAt: { [Op.gte]: new Date() },
        email: body.email,
      },
    };
    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }

    existingUser.password = await generatePassword(body.newPassword);
    existingUser.tempOtp = null;
    existingUser.lastSignInAt = new Date();
    existingUser.tempOtpExpiresAt = null;
    if (isEmail) {
      existingUser.isEmailVerified = true;
    }

    await existingUser.save();
    return {
      success: true,
      message: successMessage.RESET_SUCCESS_MESSAGE('password'),
      data: UserHelper.modifyOutputData(existingUser),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.verifyOtp = async (tempOtp, email, isEmail = false) => {
  try {
    const query = {
      where: {
        tempOtp: tempOtp,
        tempOtpExpiresAt: { [Op.gte]: new Date() },
        email: email,
      },
    };
    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
        data: null,
      };
    }

    existingUser.tempOtp = null;
    existingUser.lastSignInAt = new Date();
    existingUser.tempOtpExpiresAt = null;
    if (isEmail) {
      existingUser.isEmailVerified = true;
    }

    await existingUser.save();
    return {
      success: true,
      message: successMessage.OTP_VERIFIED(),
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.checkUser = async (body, userId = null) => {
  const query = {
    where: {
      ...(userId
        ? {
            id: { [Op.not]: userId },
          }
        : {}),
      [Op.or]: [
        body.email && { email: body.email },
        body.mobileNumber && {
          [Op.and]: [
            { mobileNumber: body.mobileNumber },
            { countryCode: body.countryCode },
          ],
        },
      ],
    },
  };
  const existingUser = await this.getUser(query);
  if (existingUser) {
    let message = errorMessage.ALREADY_EXIST('user');
    if (existingUser.email === body.email) {
      message = errorMessage.SAME_EMAIL_MOBILE_EXISTS('email');
    } else if (existingUser.mobileNumber === body.mobileNumber) {
      message = errorMessage.SAME_EMAIL_MOBILE_EXISTS('mobile number');
    }
    return {
      success: true,
      message: message,
    };
  }
  return {
    success: false,
    message: errorMessage.NO_USER('user'),
  };
};

exports.sendOtp = async (data) => {
  const query = {
    where: {
      id: data.id,
    },
  };
  const existingUser = await this.getUser(query);
  await this.generateAndSendOtp(existingUser);
};

exports.updateUserWithWorkspace = async (query, data, user) => {
  try {
    let existingWorkspace;
    if (data.organization && data.organization.workspaceId) {
      const workspaceQuery = {
        where: {
          id: data.organization.workspaceId,
        },
      };

      existingWorkspace = await WorkspaceRepository.findOne(workspaceQuery);

      if (!existingWorkspace) {
        return {
          success: false,
          message: errorMessage.DOES_NOT_EXIST('Workspace'),
        };
      }
    }

    const payload = {
      firstName: data.firstName,
      lastName: data.lastName,
      password: data.password,
      role: existingWorkspace?.category || user.role,
      countryCode: data.countryCode,
      mobileNumber: data.mobileNumber,
      organizationId: data.organizationId,
      currentOrganizationId: data.organizationId,
      status: data.status || defaultStatus.ACTIVE,
    };

    const result = await this.updateUser(query, payload);

    return {
      success: result.success,
      data: result.data,
      message: result.success
        ? successMessage.UPDATE_SUCCESS_MESSAGE('User')
        : result.message,
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.validateUserAndSendOtp = async (
  body,
  isEmail = false,
  requestDetails = {}
) => {
  try {
    const query = {
      where: {
        status: { [Op.notIn]: [defaultStatus.DELETED] },
        [Op.or]: [
          body.email && { email: body.email },
          body.mobileNumber && {
            [Op.and]: [
              { mobileNumber: body.mobileNumber },
              { countryCode: body.countryCode },
            ],
          },
        ],
      },
    };

    const existingUser = await this.getUser(query);
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.NO_USER(isEmail ? 'email' : 'mobileNumber'),
      };
    }

    await this.generateAndSendOtp(existingUser, isEmail);
    const userData = UserHelper.modifyOutputData(existingUser);

    if (existingUser.isMfaEnabled != true) {
      await UserHelper.saveLoginHistoryAndSession(
        existingUser,
        userData.token,
        requestDetails
      );
    }

    const permissions =
      await ModulePermissionRepository.findPermissionsByRoleId(
        userData.designationId
      );
    userData.permissions = permissions;

    return {
      success: true,
      message: successMessage.OTP_SEND(isEmail ? 'email' : 'mobileNumber'),
      data: userData,
    };
  } catch (e) {
    throw new Error(e);
  }
};

exports.updatePassword = async (user, newPassword) => {
  user.password = await generatePassword(newPassword);
  return user.save();
};

exports.onboardInvitedUser = async (data, loggedInUser) => {
  const { address, documents } = data;
  try {
    if (data.mobileNumber && data.countryCode) {
      const duplicateUser = await User.findOne({
        where: {
          status: { [Op.notIn]: [defaultStatus.DELETED] },
          mobileNumber: data.mobileNumber,
          countryCode: data.countryCode,
          id: { [Op.not]: loggedInUser.id },
        },
      });

      if (duplicateUser) {
        return {
          success: false,
          message: errorMessage.MOBILE_NUMBER_ALREADY_EXISTS,
        };
      }
    }

    const existingUser = loggedInUser;
    if (!existingUser) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('User'),
      };
    }

    const updatedUserData = { ...existingUser.toJSON(), ...data };
    const { password, id, ...userDataToUpdate } = updatedUserData;
    Object.assign(existingUser, userDataToUpdate);
    if (password) {
      existingUser.password = await generatePassword(password);
    }
    await existingUser.save();

    // save data in employee documentation
    if (documents && Array.isArray(documents)) {
      const documentPromises = documents.map(async (doc) => {
        return await EmployeeDocument.create({
          cardNumber: doc.cardNumber,
          name: doc.name,
          documentPath: doc.documentPath,
          userId: existingUser.id,
          organizationId: existingUser.currentOrganizationId,
          documentType: documentType.USER,
        });
      });
      await Promise.all(documentPromises);
    }

    // save data address table
    if (address) {
      await Address.create({
        ...address,
        addressType: addressType.INVITED_USER,
        userId: existingUser.id,
        organizationId: existingUser.currentOrganizationId,
      });
    }

    // Update UserOrganization
    if (existingUser.currentOrganizationId) {
      await UserOrganization.update(
        { isProfileCompleted: true },
        {
          where: {
            userId: existingUser.id,
            organizationId: existingUser.currentOrganizationId,
          },
        }
      );
    }

    const permissions =
      await ModulePermissionRepository.findPermissionsByRoleId(
        existingUser.designationId
      );
    const userData = UserHelper.modifyOutputData(existingUser);
    userData.permissions = permissions;

    return {
      success: true,
      message: successMessage.USER_ONBOARDED,
      data: userData,
    };
  } catch (error) {
    console.error(`Error onboarding invited user: ${error.message}`);
    throw new Error(
      `An error occurred while onboarding the invited user: ${error.message}`
    );
  }
};

exports.toggleMfa = async (data, loggedInUser) => {
  const { isMfaEnabled } = data;
  try {
    const mfa =
      isMfaEnabled === true || isMfaEnabled === 'true' ? 'enable' : 'disable';

    if (loggedInUser.email) {
      const otp = options.genOtp();
      // Logic to send OTP to the email
      const emailData = {
        to: loggedInUser.email,
        variables: {
          user_name:
            `${loggedInUser.firstName || ''} ${loggedInUser.lastName || ''}`.trim() ||
            'user',
          mfa_status: mfa,
          otp: otp,
        },
      };
      await EmailHelper.sendEmail(emailData, 'email_mfa');

      // Save OTP and expiration time in the loggedInUser record
      loggedInUser.tempOtp = otp;

      // tempOtpExpiresAt 2 minutes
      loggedInUser.tempOtpExpiresAt = new Date(Date.now() + 2 * 60 * 1000);
      await loggedInUser.save();

      return {
        success: true,
        message: successMessage.OTP_SENT_TO_EMAIL(loggedInUser.email, mfa),
      };
    } else {
      return {
        success: false,
        message: errorMessage.NO_VALID_EMAIL_FOR_OTP,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error.message || 'An error occurred while toggling MFA.',
    };
  }
};

exports.verifyMfaOtp = async (data, loggedInUser) => {
  const { otp } = data;
  const { id } = loggedInUser;
  try {
    const serverCurrentTime = new Date();

    const user = await User.findByPk(id);
    if (!user) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('user'),
      };
    }
    const { tempOtp, isMfaEnabled, tempOtpExpiresAt } = user;
    if (tempOtp === Number(otp) && tempOtpExpiresAt > serverCurrentTime) {
      user.isMfaEnabled = !isMfaEnabled;
      user.tempOtp = null;
      user.tempOtpExpiresAt = null;
      await user.save();

      return {
        success: true,
        message: successMessage.MFA_TOGGLE_SUCCESS(user.isMfaEnabled),
      };
    } else {
      return {
        success: false,
        message: errorMessage.OTP_INVALID,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: error.message || 'An error occurred while verifying the OTP.',
    };
  }
};

exports.switchOrganization = async (organizationId, loggedInUser) => {
  const { id } = loggedInUser;
  try {
    const user = await User.findByPk(id);
    if (!user) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('user'),
      };
    }

    user.currentOrganizationId = organizationId;
    await user.save();
    const token = user.genToken();
    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Organization'),
      token,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.message || 'An error occurred while updating the organization.',
    };
  }
};

exports.forgotPassword = async (email) => {
  try {
    const user = await User.findOne({
      where: {
        email,
      },
    });
    if (!user) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('user'),
      };
    }

    // const resetToken = user.genToken();
    const resetToken = crypto.randomBytes(32).toString('hex');

    // Set token expiration time (e.g., 24 hours)
    const tokenExpiry = Date.now() + 24 * 60 * 60 * 1000; // 24 hours from now

    await user.update({
      resetPasswordToken: resetToken,
      resetPasswordTokenExpiry: tokenExpiry,
    });

    const resetPasswordLink = `${process.env.FRONTEND_URL}/forget-password?token=${resetToken}`;
    const emailData = {
      to: email,
      variables: {
        reset_password_link: resetPasswordLink,
        recipient_email: email,
        name: `${user.firstName} ${user.lastName}`,
      },
    };
    await EmailHelper.sendEmail(emailData, 'reset_password_32');

    return {
      success: true,
      message: successMessage.RESET_PASSWORD_LINK_SENT,
    };
  } catch (error) {
    return {
      success: false,
      message: error.message || 'An error occurred while updating the user.',
    };
  }
};

exports.resetPassword = async (data) => {
  const { token, password } = data;
  try {
    const user = await User.findOne({
      where: {
        resetPasswordToken: token,
      },
    });
    if (!user) {
      return {
        success: false,
        message: errorMessage.INVALID_RESET_TOKEN,
      };
    }

    if (user.resetPasswordTokenExpiry < Date.now()) {
      return {
        success: false,
        message: errorMessage.TOKEN_EXPIRED,
      };
    }

    // Hash the new password before saving
    const hashPassword = await generatePassword(password);

    await user.update({
      password: hashPassword,
      resetPasswordToken: null,
      resetPasswordTokenExpiry: null,
    });

    return {
      success: true,
      message: successMessage.PASSWORD_RESET_SUCCESS,
    };
  } catch (error) {
    return {
      success: false,
      message:
        error.message || 'An error occurred while resetting the password.',
    };
  }
};

exports.getBankDetails = async (userId) => {
  const query = {
    where: {
      id: userId,
    },
  };

  const checkUser = await User.findOne(query);

  if (!checkUser) {
    return {
      success: false,
      message: errorMessage.DOES_NOT_EXIST('User'),
    };
  }

  const userBankDetails = await BankDetailsRepository.getBankDetails(
    checkUser.id
  );

  return {
    success: true,
    message: successMessage.DETAIL_MESSAGE('Bank Details'),
    data: userBankDetails,
  };
};

exports.registerBrand = async (data, loggedInUser) => {
  const {
    firstName,
    lastName,
    password,
    countryCode,
    mobileNumber,
    organization: { name, gstinNumber, address, city, pincode, workspaceId },
    organizationMedia,
    brandName,
    brandMedia,
  } = data;
  const transaction = await Sequelize.transaction();
  try {
    await validateOrganization(name, gstinNumber);

    // Create the organization
    const organization = await createOrganization(
      {
        name,
        gstinNumber,
        address,
        city,
        pincode,
        workspaceId,
        countryCode,
        mobileNumber,
      },
      transaction
    );

    // Create or update the address for the organization
    await createAddressForOrganization(
      { address, city, pincode },
      organization.id,
      transaction
    );

    // Create the user-organization relationship
    await createUserOrganization(loggedInUser, organization.id, transaction);

    // Handle organization media if any
    await handleMediaUpload(
      organizationMedia,
      organization.id,
      loggedInUser.id,
      false,
      transaction
    );

    // Create the brand
    const brand = await createBrand(
      brandName,
      organization.id,
      loggedInUser.id,
      transaction
    );

    // Handle brand media if any
    await handleMediaUpload(
      brandMedia,
      brand.id,
      loggedInUser.id,
      true,
      transaction
    );

    await updateUserDetails(
      loggedInUser.id,
      {
        firstName,
        lastName,
        password,
        countryCode,
        mobileNumber,
        organizationId: organization.id,
      },
      transaction
    );

    await transaction.commit();
    return {
      success: true,
      message: 'Brand registered successfully',
    };
  } catch (error) {
    await transaction.rollback();
    return {
      success: false,
      message: error.message || 'An error occurred while registering brand',
    };
  }
};
