const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const ExpenseController = require('../../../../controllers/api/v1/accounts/Expense');
// const AccountController = require('../../../../controllers/api/v1/accounts/Accounts');
const ExpenseSchema = require('../../../../schema-validation/account/Expense');
// const AccountSchema = require('../../../../schema-validation/account/Account');
const ErrorHandleHelper = require('../../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(ExpenseSchema.createExpense),
  ErrorHandleHelper.requestValidator,
  ExpenseController.createExpense
);

router.get(
  '/',
  checkSchema(ExpenseSchema.getExpense),
  ErrorHandleHelper.requestValidator,
  ExpenseController.getExpense
);

router.get(
  '/:expenseId',
  checkSchema(ExpenseSchema.getExpenseById),
  ErrorHandleHelper.requestValidator,
  ExpenseController.getExpenseById
);

router.delete(
  '/:expenseId',
  checkSchema(ExpenseSchema.deleteExpenseById),
  ErrorHandleHelper.requestValidator,
  ExpenseController.deleteExpense
);

router.put(
  '/:expenseId',
  checkSchema(ExpenseSchema.updateExpense),
  ErrorHandleHelper.requestValidator,
  ExpenseController.updateExpenseDetails
);

module.exports = router;
