const { defaultStatus } = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const ProjectTeam = sequelize.define(
    'ProjectTeam',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      projectId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'Project',
          key: 'id',
        },
        allowNull: false,
      },
      userId: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: false,
      },
      invitedBy: {
        type: DataTypes.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: false,
      },
      status: {
        type: DataTypes.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: defaultStatus.PENDING,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ProjectTeam.associate = (models) => {
    ProjectTeam.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onDelete: 'CASCADE',
    });

    ProjectTeam.belongsTo(models.User, {
      foreignKey: 'userId',
      as: 'user',
      onDelete: 'CASCADE',
    });

    ProjectTeam.belongsTo(models.User, {
      foreignKey: 'invitedBy',
      as: 'invitedByUser',
      onDelete: 'SET NULL',
    });

    ProjectTeam.belongsTo(models.UserOrganization, {
      foreignKey: 'userOrganizationId',
      as: 'userOrganization',
      onDelete: 'CASCADE',
    });
  };

  return ProjectTeam;
};
