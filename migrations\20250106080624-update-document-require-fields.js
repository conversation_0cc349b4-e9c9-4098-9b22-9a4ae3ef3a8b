'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('DocumentRequire', 'isRequiredField', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
    });

    await queryInterface.changeColumn('DocumentRequire', 'isUploadRequired', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: null,
    });

    await queryInterface.removeColumn('DocumentRequire', 'inputValue');
    await queryInterface.removeColumn('DocumentRequire', 'fileUpload');
    await queryInterface.removeColumn('DocumentRequire', 'isInputRequired');
    await queryInterface.removeColumn('DocumentRequire', 'isRequired');
    await queryInterface.removeColumn(
      'DocumentRequire',
      'isVerificationRequired'
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('DocumentRequire', 'inputValue', {
      type: Sequelize.TEXT,
      allowNull: true,
    });
    await queryInterface.addColumn('DocumentRequire', 'fileUpload', {
      type: Sequelize.JSON,
      allowNull: true,
    });
    await queryInterface.addColumn('DocumentRequire', 'isInputRequired', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    });
    await queryInterface.addColumn('DocumentRequire', 'isRequired', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: true,
    });
    await queryInterface.addColumn(
      'DocumentRequire',
      'isVerificationRequired',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: true,
      }
    );

    await queryInterface.removeColumn('DocumentRequire', 'isRequiredField');
    await queryInterface.changeColumn('DocumentRequire', 'isUploadRequired', {
      type: Sequelize.BOOLEAN,
      allowNull: true,
      defaultValue: false,
    });
  },
};
