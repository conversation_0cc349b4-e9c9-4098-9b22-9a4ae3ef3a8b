'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Project', 'description', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Project', 'featuresAndSpecifications', {
      type: Sequelize.TEXT,
      allowNull: true,
    });

    await queryInterface.addColumn('Project', 'additionalFields', {
      type: Sequelize.JSONB,
      allowNull: true,
    });

    await queryInterface.createTable('ProjectAmenities', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Project',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      amenityId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Amenities',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Project', 'description');
    await queryInterface.removeColumn('Project', 'featuresAndSpecifications');
    await queryInterface.removeColumn('Project', 'additionalFields');
    await queryInterface.dropTable('ProjectAmenities');
  },
};
