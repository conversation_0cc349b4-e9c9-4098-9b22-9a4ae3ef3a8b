'use strict';

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('WorkOrder', 'scheduleDate');
    await queryInterface.addColumn('WorkOrder', 'fromDate', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('WorkOrder', 'toDate', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.addColumn('WorkOrder', 'activities', {
      type: Sequelize.JSON,
      allowNull: true,
      defaultValue: null,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('WorkOrder', 'scheduleDate', {
      type: Sequelize.DATE,
      allowNull: true,
    });
    await queryInterface.removeColumn('WorkOrder', 'fromDate');
    await queryInterface.removeColumn('WorkOrder', 'toDate');
    await queryInterface.removeColumn('WorkOrder', 'activities');
  },
};
