paths:
  /user/login:
    post:
      tags:
        - "User"
      summary: "Login with password - mobile & web"
      description: ""
      operationId: "loginWithPassword"
      parameters:
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
      requestBody:
        description:  Login with password
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/password-login"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "Logged in successfully"
        "400":
          description: "Invalid Request | No user with this Email or Mobile number"
        "500":
          description: "Internal Server Error"
  /user/verify-email:
    post:
      tags:
        - "User"
      summary: "verify email for builder and vendor"
      description: ""
      operationId: "verifyEmail"
      requestBody:
        description: 
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/verify-email"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "An OTP has been send to your email"
        "400":
          description: "Account is blocked | Email or mobile number already taken"
        "500":
          description: "Internal Server Error"
  /user/register:
    post:
      tags:
        - "User"
      summary: "Register a new user"
      description: ""
      operationId: "RegisterUser"
      requestBody:
        description: 
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/register"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: [ ]
      parameters: []
      responses:
        "200":
          description: "Your account has been created successfully"
        "400":
          description: "Account is blocked | Email or mobile number already taken"
        "500":
          description: "Internal Server Error"
  /user/sign-up:
    post:
      tags:
        - "User"
      summary: "New User sign up"
      description: ""
      operationId: "newUserSignUp"
      requestBody:
        description: 
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/sign-up"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "Your account has been created successfully"
        "400":
          description: "Account is blocked | Email or mobile number already taken"
        "500":
          description: "Internal Server Error"
  /user/send-otp:
    post:
      tags:
        - "User"
      summary: "send otp"
      description: ""
      operationId: "sendOtp"
      requestBody:
        description:  send otp
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/send-otp"
        required: true
      produces:
        - "application/json"
      parameters: []
      responses:
        "200":
          description: "An OTP has been sent"
        "400":
          description: "Invalid Request | No user with this Email or mobile number"
        "500":
          description: "Internal Server Error"
  /user/verify-otp:
    patch:
      tags:
        - "User"
      summary: "Verify otp"
      description: ""
      operationId: "verifyOtp"
      requestBody:
        description: verify otp
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/verify-otp"
        required: true
      security:
        - bearerAuth: [ ]
      produces:
        - "application/json"
      parameters: [ ]
      responses:
        "200":
          description: "OTP verified successfully"
        "400":
          description: "Invalid Request or Incorrect OTP"
        "500":
          description: "Internal Server Error"
  /user:
    put:
      tags:
        - "User"
      summary: "update profile"
      description: ""
      operationId: "update profile"
      requestBody:
        description: Payload required
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/profile-update"
        required: true
      produces:
        - "application/json"
      parameters: []  
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "user updated successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
    get:
     tags:
       - "User"
     summary: "get profile by id"
     description: ""
     operationId: "get user profile"
     produces:
       - "application/json"
     parameters: [ ]
     security:
       - bearerAuth: [ ]
     responses:
       "200":
         description: "get user profile "
       "400":
         description: "Invalid Request"
       "500":
         description: "Internal Server Error"
  /user/generate-password:
    put:
      tags:
        - "User"
      summary: "create password"
      description: ""
      operationId: "generatePassword"
      requestBody:
        description: create password
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/create-password"
        required: true
      produces:
        - "application/json" 
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Password saved successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /user/change-password:
    patch:
      tags:
        - "User"
      summary: "change password"
      description: ""
      operationId: "changePassword"
      requestBody:
        description: change password
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/change-password"
        required: true
      produces:
        - "application/json" 
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Password saved successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /user/close-account:
    patch:
      tags:
        - "User"
      summary: "Delete User Account"
      operationId: deleteUserAccount
      produces:
        - "application/json"
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "User Account Deleted successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /user/required-documents:
    get:
      tags:
        - "User"
      summary: "Get required documents"
      operationId: getRequiredDocuments
      produces:
        - "application/json"
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Required documents retrieved successfully"
        "400":
          description: "Invalid Request"
        "401":
          description: "Unauthorized"
        "500":
          description: "Internal Server Error"
  /user/logout:
    delete:
      tags:
        - "User"
      summary: "Logout the current user"
      description: "Logs out the user by invalidating the session token (JWT) and removing the device session entry from the database."
      operationId: "logoutUser"
      security:
        - bearerAuth: [ ]
      produces:
        - "application/json"
      responses:
        "200":
          description: "Logged out successfully."
        "401":
          description: "Unauthorized access - No valid token provided."
        "500":
          description: "Internal Server Error - Something went wrong during the logout process."
  /user/device-session:
    get:
      tags:
        - "User"
      summary: "Get Device Session Details"
      description: "Retrieve details of active device sessions for the user with pagination."
      operationId: "getDeviceSession"
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
            description: "The starting index for pagination"
            example: 0
        - in: query
          name: limit
          schema:
            type: integer
            description: "The number of records to retrieve"
            example: 10
      responses:
        "200":
          description: "Device session details retrieved successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      message:
                        type: string
                        description: "A message indicating the success of the operation"
                        example: "Fetched Device sessions details successfully"
                      data:
                        type: object
                        properties:
                          rows:
                            type: array
                            description: "Array of device session objects"
                            items:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  description: "Session ID"
                                  example: 25
                                deviceName:
                                  type: string
                                  description: "Device name or user agent"
                                  example: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"
                                ipAddress:
                                  type: string
                                  description: "IP Address of the session"
                                  example: "192:168:1:1"
                                loginType:
                                  type: string
                                  description: "Type of login"
                                  example: "credentials_login"
                                lastActivityAt:
                                  type: string
                                  format: date-time
                                  description: "Timestamp of the last activity in the session"
                                  example: "2025-01-21T10:43:41.186Z"
                                createdAt:
                                  type: string
                                  format: date-time
                                  description: "Timestamp when the session was created"
                                  example: "2025-01-21T10:43:41.186Z"
                                updatedAt:
                                  type: string
                                  format: date-time
                                  description: "Timestamp when the session was last updated"
                                  example: "2025-01-21T10:43:41.186Z"
                          pagination:
                            type: object
                            description: "Pagination details"
                            properties:
                              totalCount:
                                type: integer
                                description: "Total number of device sessions available"
                                example: 20
                              start:
                                type: integer
                                description: "Starting index for the current page"
                                example: 0
                              limit:
                                type: integer
                                description: "Number of records fetched per page"
                                example: 10
        "401":
          description: "Unauthorized access - Invalid or missing token."
        "500":
          description: "Internal Server Error - Unable to fetch session details."
  /user/login-history:
    get:
      tags:
        - "User"
      summary: "Get Login History"
      description: "Retrieve details of login history for the user with pagination."
      operationId: "getLoginHistory"
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: start
          schema:
            type: integer
            description: "The starting index for pagination"
            example: 0
        - in: query
          name: limit
          schema:
            type: integer
            description: "The number of records to retrieve"
            example: 10
      responses:
        "200":
          description: "Login History details retrieved successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  result:
                    type: object
                    properties:
                      message:
                        type: string
                        description: "A message indicating the success of the operation"
                        example: "Fetched Login History details successfully"
                      data:
                        type: object
                        properties:
                          rows:
                            type: array
                            description: "Array of Login History objects"
                            items:
                              type: object
                              properties:
                                id:
                                  type: integer
                                  description: "History ID"
                                  example: 25
                                loginType:
                                  type: string
                                  description: "Type of login"
                                  example: "credentials_login"
                                ipAddress:
                                  type: string
                                  description: "IP Address of the history"
                                  example: "192:168:1:1"
                                location:
                                  type: string
                                  description: "Location Information"
                                  example: "Surat - Gujarat"
                                clientInfo:
                                  type: string
                                  description: "Device name or user agent"
                                  example: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)"
                                createdAt:
                                  type: string
                                  format: date-time
                                  description: "Timestamp when the session was created"
                                  example: "2025-01-21T10:43:41.186Z"
                                updatedAt:
                                  type: string
                                  format: date-time
                                  description: "Timestamp when the session was last updated"
                                  example: "2025-01-21T10:43:41.186Z"
                          pagination:
                            type: object
                            description: "Pagination details"
                            properties:
                              totalCount:
                                type: integer
                                description: "Total number of Login History available"
                                example: 20
                              start:
                                type: integer
                                description: "Starting index for the current page"
                                example: 0
                              limit:
                                type: integer
                                description: "Number of records fetched per page"
                                example: 10
        "401":
          description: "Unauthorized access - Invalid or missing token."
        "500":
          description: "Internal Server Error - Unable to fetch session details."
  /user/{id}/logout:
    delete:
      tags:
        - "User"
      summary: "Logout Device Session by ID"
      description: "Log out a specific device session using its ID."
      operationId: "removeDeviceSessionById"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the device session to log out."
          schema:
            type: string
            example: "12345"
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
      responses:
        "200":
          description: "Device session logged out successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: "Confirmation message."
                    example: "Device session logged out successfully."
        "400":
          description: "Invalid request - Session ID is missing or incorrect."
        "401":
          description: "Unauthorized access - Invalid or missing token."
        "500":
          description: "Internal Server Error - Unable to log out the session."
  /user/{id}/bank-details:
    get:
      tags:
        - "User"
      summary: "Get Bank Details for User"
      description: "Get the bank details for a specific user."
      operationId: "getBankDetailsForUser"
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the user to get bank details for."
          schema:
            type: string
            example: "12345"
        - $ref: '#/components/parameters/xBrickoApplicationHeader'
      responses:
        "200":
          description: "Bank details fetched successfully."
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    description: "Confirmation message."
                    example: "Bank details fetched successfully."
                  data:
                    type: object
                    description: "Bank details of the user."
                    example: {"accountName": "John Doe", "accountNumber": "**********", "ifscCode": "ABCD1234", "bankName": "ABC Bank"}
        "400":
          description: "Invalid request - User ID is missing or incorrect."
        "401":
          description: "Unauthorized access - Invalid or missing token."
        "500":
          description: "Internal Server Error - Unable to fetch bank details."
  /user/test-email:
    post:
      tags:
        - "User"
      deprecated: true
      summary: "Test email functionality"
      description: "Send a test email to verify email configuration"
      operationId: "testEmail"
      requestBody:
        description: Test email request
        content:
          "application/json":
            schema:
              type: object
              properties:
                email:
                  type: string
                  format: email
                  description: "Email address to send test email to"
                name:
                  type: string
                  description: "Optional name of the recipient"
              required:
                - email
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "Test email sent successfully"
        "400":
          description: "Invalid Request | Email is required"
        "500":
          description: "Internal Server Error"
  /user/onboard-user:
    put:
      tags:
        - "User"
      summary: "Onboard a new user"
      description: ""
      operationId: "onboardUser"
      security:
        - bearerAuth: []
      requestBody:
        description: Onboard user payload
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/onboard-user"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "User onboarded successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /user/toggle-mfa:
    patch:
      tags:
        - "User"
      summary: "Enable or disable Multi-Factor Authentication (MFA)"
      description: "Toggle the MFA setting for the user."
      operationId: "toggleMfa"
      security:
        - bearerAuth: []
      requestBody:
        description: "Payload to enable or disable MFA"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/toggle-mfa"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "MFA has been toggled successfully."
        "400":
          description: "Invalid Request | enableMfa is required."
        "500":
          description: "Internal Server Error"
  /user/verify-mfa:
    patch:
      tags:
        - "User"
      summary: "Verify OTP for Multi-Factor Authentication (MFA)"
      description: "Verify the OTP to enable or disable MFA."
      operationId: "verifyMfa"
      security:
        - bearerAuth: []
      requestBody:
        description: "Payload containing the OTP for verification"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/verify-mfa"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "OTP verified successfully, MFA status updated."
        "400":
          description: "Invalid Request | Incorrect OTP."
        "500":
          description: "Internal Server Error"
  /user/switch-organization/{organizationId}:
    patch:
      tags:
        - "User"
      summary: "Switch Organization"
      description: "Change the user's current organization"
      operationId: "switchOrganization"
      produces:
        - "application/json"
      parameters:
        - in: "path"
          name: "organizationId"
          required: true
          schema:
            type: string
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Organization switched successfully"
        "400":
          description: "Invalid Request"
        "500":
          description: "Internal Server Error"
  /user/forgot-password:
    post:
      tags:
        - "User"
      summary: "Forgot Password"
      description: "Request a password reset link via email."
      operationId: "forgotPassword"
      requestBody:
        description: Email address for password reset
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/forgotPasswordRequest"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "Reset link sent to your email"
        "400":
          description: "Invalid Request | No user with this email"
        "500":
          description: "Internal Server Error"
  /user/reset-password:
    patch:
      tags:
        - "User"
      summary: "Reset Password"
      description: "Reset the user's password using a reset token."
      operationId: "resetPassword"
      requestBody:
        description: Reset password payload
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/resetPasswordRequest"
        required: true
      produces:
        - "application/json"
      responses:
        "200":
          description: "Password has been successfully reset."
        "400":
          description: "Invalid Request | Token is invalid or expired."
        "500":
          description: "Internal Server Error"  

  /user/register-brand:
    post:
      tags:
        - "User"
      summary: "Register a new brand"
      description: "Register a new brand along with its associated organization."
      operationId: "registerBrand"
      requestBody:
        description: "Payload for registering a brand"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/registerBrand"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: [ ]
      responses:
        "200":
          description: "Brand registered successfully"
        "400":
          description: "Invalid Request | Organization or brand details are incorrect"
        "500":
          description: "Internal Server Error"

components:
  parameters:
    organizationId:
      name: organizationId
      in: path
      required: true
      description: The ID of the organization to switch to
      schema:
        type: string
  schemas:
    send-otp:
      type: object
      properties:
        type:
          type: string
          description: enter type
          enum: ['email', 'mobileNumber']
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        email:
          type: string
          description: enter email
      required:
        - type
    verify-otp:
      type: object
      properties:
        type:
          type: string
          description: enter type
          enum: ['email', 'mobileNumber']
          example: 'email'
        countryCode:
          type: string
          description: enter country code
          example: '+91'
        mobileNumber:
          type: string
          description: enter mobile Number
          example: '**********'
        email:
          type: string
          description: enter email
          example: '<EMAIL>'
        tempOtp:
          type: integer
          description: enter otp
          example: 555555
      required:
        - tempOtp
        - type
    password-login:
      type: object
      properties:
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        email:
          type: string
          description: enter email
          example: "<EMAIL>"
        password:
          type: string
          description: enter password
          example: "Password123!"
    sign-up:
      type: object
      properties:
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        email:
          type: string
          description: enter email address
        firstName:
          type: string
          description: enter first name        
        lastName:
          type: string
          description: enter last name
    create-password:
      type: object
      properties:
        password:
            type: string
            description: enter password
        confirmPassword:
          type: string
          description: enter confirm password
      required:
        - password
        - confirmPassword
    change-password:
      type: object
      properties:
        newPassword:
            type: string
            description: enter new password
        currentPassword:
            type: string
            description: enter current password
        confirmPassword:
          type: string
          description: enter confirm password
      required:
        - password
        - confirmPassword
    profile-update:
      type: object
      properties:
        countryCode:
          type: string
          description: enter country code
        mobileNumber:
          type: string
          description: enter mobile Number
        email:
          type: string
          description: enter email address
        firstName:
          type: string
          description: enter first name        
        lastName:
          type: string
          description: enter last name
        dateOfBirth:
          type: string
          description: enter Date of Birth 
        heading:
          type: string
          description: enter heading
        pincode:
          type: string
          description: enter pincode
        countryName:
          type: string
          description: enter country name    
        state:
          type: string
          description: enter state
        city:
          type: string
          description: enter city
    verify-email:
      type: object
      properties:
        email:
          type: string
          description: enter email address
          example: <EMAIL>

    register:
      type: object
      properties:
        firstName:
          type: string
          description: enter first name
          example: John
        lastName:
          type: string
          description: enter last name
          example: Doe
        password:
          type: string
          description: enter new password
          example: Password123!
        confirmPassword:
          type: string
          description: confirm new password
          example: Password123!
        countryCode:
          type: string
          description: enter country code
          example: +1
        mobileNumber:
          type: string
          description: enter mobile number
          example: **********
        organization:
          type: object
          properties:
            name:
              type: string
              description: enter organization name
              example: Example Corp
            gstinNumber:
              type: string
              description: enter GSTIN number
              example: 22AAAAA0000A1Z5
            gstinDocument:
              type: string
              description: enter GSTIN document
              example: GSTIN_Document.pdf
            address:
              type: string
              description: enter organization address
              example: 123 Example Street
            city:
              type: string
              description: enter city
              example: Example City
            pincode:
              type: string
              description: enter pincode
              example: 123456
            workspaceId:
              type: number
              description: enter workspace ID
              example: 1
        organizationMedia:
          type: array
          items:
            type: object
            properties:
              filePath:
                type: string
                description: File path
                example: uploads/1727163056695wvry3sBrandsStrategy.pdf
              fileType:
                type: string
                description: File type
                example: application/pdf
              fileName:
                type: string
                description: File name
                example: document.pdf
              inputValue:
                type: string
                description: value in the input field
                example: AWXCV**********
              fileSize:
                type: number
                description: File size in bytes
                example: 1024
              requiredDocumentId:
                type: integer
                description: Required document ID
                example: 1
      required:
        - name
        - password
        - confirmPassword
        - mobileNumber
        - organization

    onboard-user:
      type: object
      required:
        - firstName
        - lastName
        - password
        - confirmPassword
        - countryCode
        - mobileNumber
      properties:
        firstName:
          type: string
          description: "User's first name"
          example: "John"
        lastName:
          type: string
          description: "User's last name"
          example: "Doe"
        password:
          type: string
          description: "User's password"
          example: "Password123!"
        confirmPassword:
          type: string
          description: "Confirm password"
          example: "Password123!"
        countryCode:
          type: string
          description: "Country code for mobile number"
          example: "+1"
        mobileNumber:
          type: string
          description: "User's mobile number"
          example: "**********"
        isBusiness:
          type: boolean
          description: "Indicates if the user is a business"
          example: false
        businessName:
          type: string
          description: "Name of the business"
          example: "N/A"
        documents:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                description: "Name associated with the document"
                example: "Passport"
              cardNumber:
                type: string
                description: "Card number associated with the document"
                example: "A**********"
              documentPath:
                type: string
                description: "Path to the document"
                example: "/uploads/documents/passport.pdf"
          description: "Array of documents associated with the user"
          example: 
            - { 
                name: "Passport", 
                cardNumber: "A**********", 
                documentPath: "/uploads/documents/passport.pdf" 
              }
        address:
          type: object
          properties:
            address:
              type: string
              description: "Primary address"
              example: "123 Main St"
            addressLine2:
              type: string
              description: "Additional address information"
              example: "Apt 4B"
            landmark:
              type: string
              description: "Notable landmark"
              example: "Near Central Park"
            city:
              type: string
              description: "City"
              example: "New York"
            state:
              type: string
              description: "State"
              example: "NY"
            pincode:
              type: string
              description: "Postal code"
              example: "10001"
            country:
              type: string
              description: "Country"
              example: "USA"
            latitude:
              type: number
              format: float
              description: "Latitude coordinate"
              example: 40.7128
            longitude:
              type: number
              format: float
              description: "Longitude coordinate"
              example: -74.0060
          description: "Address details of the user"
          example: 
            address: "123 Main St"
            addressLine2: "Apt 4B"
            landmark: "Near Central Park"
            city: "New York"
            state: "NY"
            pincode: "10001"
            country: "USA"
            latitude: 40.7128
            longitude: -74.0060
    
    toggle-mfa:
      type: object
      properties:
        isMfaEnabled:
          type: boolean
          description: "Enable or disable MFA"
      required:
        - isMfaEnabled
    
    verify-mfa:
      type: object
      properties:
        otp:
          type: integer
          description: "OTP for verification"
      required:
        - otp
    forgotPasswordRequest:
      type: object
      properties:
        email:
          type: string
          format: email
          description: "The email address of the user."
      required:
        - email
    resetPasswordRequest:
      type: object
      properties:
        token:
          type: string
          description: "The reset token sent to the user's email."
        password:
          type: string
          description: "The new password for the user."
      required:
        - token
        - password

    registerBrand:
      type: object
      properties:
        firstName:
          type: string
          description: "User's first name"
          example: "John"
        lastName:
          type: string
          description: "User's last name"
          example: "Doe"
        password:
          type: string
          description: "User's password"
          example: "Password123!"
        confirmPassword:
          type: string
          description: "Confirm password"
          example: "Password123!"
        countryCode:
          type: string
          description: "Country code for mobile number"
          example: "+1"
        mobileNumber:
          type: string
          description: "User's mobile number"
          example: "**********"
        organization:
          type: object
          properties:
            name:
              type: string
              description: "Organization name"
              example: "Example Corp"
            gstinNumber:
              type: string
              description: "GSTIN number"
              example: "22AAAAA0000A1Z5"
            gstinDocument:
              type: string
              description: "GSTIN document"
              example: "GSTIN_Document.pdf"
            address:
              type: string
              description: "Organization address"
              example: "123 Example Street"
            city:
              type: string
              description: "City"
              example: "Example City"
            pincode:
              type: string
              description: "Pincode"
              example: "123456"
            workspaceId:
              type: number
              description: "Workspace ID"
              example: 1
        organizationMedia:
          type: array
          items:
            type: object
            properties:
              filePath:
                type: string
                description: File path
                example: uploads/1727163056695wvry3sBrandsStrategy.pdf
              fileType:
                type: string
                description: File type
                example: application/pdf
              fileName:
                type: string
                description: File name
                example: document.pdf
              inputValue:
                type: string
                description: value in the input field
                example: AWXCV**********
              fileSize:
                type: number
                description: File size in bytes
                example: 1024
              requiredDocumentId:
                type: integer
                description: Required document ID
                example: 1
        brandName:
          type: string
          description: "Name of the brand being registered"
          example: "BrandName"
        brandMedia:
          type: array
          items:
            type: object
            properties:
              filePath:
                type: string
                description: "File path"
                example: "uploads/brand_document.pdf"
              fileType:
                type: string
                description: "File type"
                example: "application/pdf"
              fileName:
                type: string
                description: "File name"
                example: "document.pdf"
              description:
                type: string
                description: "File description"
                example: "Pan Card"
              inputValue:
                type: string
                description: "Input value associated with the file"
                example: "AWXCV**********"
              fileSize:
                type: integer
                description: "Size of the file in bytes"
                example: 1024
      required:
        - firstName
        - lastName
        - password
        - confirmPassword
        - countryCode
        - mobileNumber
        - organization
        - brandName

