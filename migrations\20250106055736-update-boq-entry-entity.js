'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameTable('BOQItems', 'BoqEntry');

    await queryInterface.addColumn('BoqEntry', 'cost', {
      type: Sequelize.JSON,
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BoqEntry', 'cost');

    await queryInterface.renameTable('BoqEntry', 'BOQItems');
  },
};
