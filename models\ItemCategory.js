'use strict';

module.exports = (sequelize, DataTypes) => {
  const ItemCategory = sequelize.define(
    'ItemCategory',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  ItemCategory.associate = (models) => {
    ItemCategory.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    ItemCategory.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
  };

  return ItemCategory;
};
