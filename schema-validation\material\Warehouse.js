exports.createWarehouse = {
  name: {
    in: ['body'],
    notEmpty: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'About must be a valid string',
    },
  },
  'address.city': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'City must be a valid string',
    },
  },
  'address.state': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'State must be a valid string',
    },
  },
  'address.pincode': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Pincode must be a valid string',
    },
  },
  'address.country': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Country must be a valid string',
    },
  },
  'address.address': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Address must be a valid string',
    },
  },
  'address.addressLine2': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Address Line 2 must be a valid string',
    },
  },
  'address.landmark': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Landmark must be a valid string',
    },
  },
  'address.latitude': {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Latitude must be a valid float',
    },
  },
  'address.longitude': {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Longitude must be a valid float',
    },
  },
};

exports.updateWarehouse = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'WarehouseId is required',
    },
    isInt: {
      errorMessage: 'WarehouseId must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    optional: true,
    errorMessage: 'Name is required',
    isString: {
      errorMessage: 'Name must be a valid string',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'About must be a valid string',
    },
  },
  'address.city': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'City must be a valid string',
    },
  },
  'address.state': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'State must be a valid string',
    },
  },
  'address.pincode': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Pincode must be a valid string',
    },
  },
  'address.country': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Country must be a valid string',
    },
  },
  'address.address': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Address must be a valid string',
    },
  },
  'address.addressLine2': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Address Line 2 must be a valid string',
    },
  },
  'address.landmark': {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Landmark must be a valid string',
    },
  },
  'address.latitude': {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Latitude must be a valid float',
    },
  },
  'address.longitude': {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'Longitude must be a valid float',
    },
  },
};

exports.deleteWarehouse = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'WarehouseId is required',
    },
    isInt: {
      errorMessage: 'WarehouseId must be a valid integer',
    },
  },
};
