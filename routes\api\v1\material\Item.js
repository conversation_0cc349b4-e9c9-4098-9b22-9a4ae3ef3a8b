const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ItemController = require('@controllers/v1/material/Item');
const ItemSchema = require('@schema-validation/material/Item');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/',
  checkSchema(ItemSchema.createItem),
  ErrorHandleHelper.requestValidator,
  ItemController.createItem
);

router.post(
  '/:id/clone',
  checkSchema(ItemSchema.cloneItem),
  ErrorHandleHelper.requestValidator,
  ItemController.cloneItem
);

router.put(
  '/:id',
  checkSchema(ItemSchema.updateItem),
  ErrorHandleHelper.requestValidator,
  ItemController.updateItem
);

router.patch(
  '/:id',
  checkSchema(ItemSchema.updateItemStatus),
  ErrorHandleHelper.requestValidator,
  ItemController.updateItemStatus
);

router.delete(
  '/:id',
  checkSchema(ItemSchema.deleteItem),
  ErrorHandleHelper.requestValidator,
  ItemController.deleteItem
);

router.delete(
  '/media/:id',
  checkSchema(ItemSchema.deleteItemMedia),
  ErrorHandleHelper.requestValidator,
  ItemController.deleteItemMedia
);

module.exports = router;
