'use strict';

const { getDefaultBudgetPeriods } = require('../config/defaultData');
const {
  accountCategory,
  accountType,
  recordStatus,
  budgetEntryType,
} = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add altering commands here.
     *
     * Example:
     * await queryInterface.createTable('users', { id: Sequelize.INTEGER });
     */
    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.createTable(
        'AccountData',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
          },
          organizationId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'Organization',
              key: 'id',
            },
          },
          accountCategory: {
            type: Sequelize.ENUM(accountCategory.getValues()),
            defaultValue: accountCategory.DEFAULT_VALUE,
            allowNull: false,
          },
          accountType: {
            type: Sequelize.ENUM(accountType.getValues()),
            defaultValue: accountType.DEFAULT_VALUE,
            allowNull: false,
          },
          name: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: false,
          },
          code: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: false,
            unique: true,
          },
          description: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: true,
          },
          bankAccountNumber: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: true,
          },
          ifscCode: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: true,
          },
          totalCredit: {
            type: Sequelize.DECIMAL(20, 2),
            defaultValue: 0,
            allowNull: true,
          },
          totalDebit: {
            type: Sequelize.DECIMAL(20, 2),
            defaultValue: 0,
            allowNull: true,
          },
          openingBalance: {
            type: Sequelize.DECIMAL(20, 2),
            defaultValue: 0,
            allowNull: true,
          },
          status: {
            type: Sequelize.ENUM(recordStatus.getValues()),
            defaultValue: recordStatus.ACTIVE,
            allowNull: false,
          },
          createdBy: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'User',
              key: 'id',
            },
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        {
          transaction: t,
        }
      );

      await queryInterface.createTable(
        'Journal',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
          },
          organizationId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'Organization',
              key: 'id',
            },
          },
          date: {
            type: Sequelize.DATE,
            allowNull: false,
          },
          journalNumber: {
            type: Sequelize.STRING,
            defaultValue: null,
            allowNull: false,
          },
          notes: {
            type: Sequelize.STRING,
            defaultValue: null,
            allowNull: false,
          },
          status: {
            type: Sequelize.ENUM(recordStatus.getValues()),
            defaultValue: recordStatus.ACTIVE,
            allowNull: false,
          },
          createdBy: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'User',
              key: 'id',
            },
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        {
          transaction: t,
        }
      );

      await queryInterface.createTable(
        'TransactionEntry',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
          },

          referenceId: {
            type: Sequelize.INTEGER,
            allowNull: false,
          },
          referenceType: {
            type: Sequelize.STRING,
            allowNull: false,
          },
          accountId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'AccountData',
              key: 'id',
            },
          },
          description: {
            type: Sequelize.STRING,
            defaultValue: null,
            allowNull: true,
          },
          debitAmount: {
            type: Sequelize.DECIMAL(20, 2),
            defaultValue: 0,
            allowNull: false,
          },
          creditAmount: {
            type: Sequelize.DECIMAL(20, 2),
            defaultValue: 0,
            allowNull: false,
          },
          status: {
            type: Sequelize.ENUM(recordStatus.getValues()),
            defaultValue: recordStatus.ACTIVE,
            allowNull: false,
          },
          createdBy: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'User',
              key: 'id',
            },
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        {
          transaction: t,
        }
      );

      await queryInterface.createTable(
        'Budget',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
          },
          organizationId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'Organization',
              key: 'id',
            },
          },
          name: {
            type: Sequelize.STRING,
            defaultValue: '',
            allowNull: false,
          },
          year: {
            type: Sequelize.INTEGER,
            defaultValue: 0,
            allowNull: false,
          },
          period: {
            type: Sequelize.ENUM(
              getDefaultBudgetPeriods().map(
                (objEachPeriod) => objEachPeriod.key
              )
            ),
            defaultValue: getDefaultBudgetPeriods()[0].key,
            allowNull: false,
          },
          status: {
            type: Sequelize.ENUM(recordStatus.getValues()),
            defaultValue: recordStatus.ACTIVE,
            allowNull: false,
          },
          createdBy: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'User',
              key: 'id',
            },
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        {
          transaction: t,
        }
      );

      await queryInterface.createTable(
        'BudgetEntry',
        {
          id: {
            type: Sequelize.INTEGER,
            autoIncrement: true,
            primaryKey: true,
            allowNull: false,
          },
          budgetId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'Budget',
              key: 'id',
            },
          },
          accountId: {
            type: Sequelize.INTEGER,
            allowNull: false,
            references: {
              model: 'AccountData',
              key: 'id',
            },
          },
          budgetEntryType: {
            type: Sequelize.ENUM(budgetEntryType.getValues()),
            defaultValue: budgetEntryType.INCOME,
            allowNull: false,
          },
          budgetData: {
            type: Sequelize.JSONB,
            defaultValue: '',
            allowNull: false,
          },
          status: {
            type: Sequelize.ENUM(recordStatus.getValues()),
            defaultValue: recordStatus.ACTIVE,
            allowNull: false,
          },
          createdAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
          updatedAt: {
            type: Sequelize.DATE,
            allowNull: false,
            defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
            onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
          },
        },
        {
          transaction: t,
        }
      );
    });
  },

  async down(queryInterface, Sequelize) {
    /**
     * Add reverting commands here.
     *
     * Example:
     * await queryInterface.dropTable('users');
     */
    await queryInterface.sequelize.transaction(async (t) => {
      await queryInterface.dropTable('BudgetEntry', { transaction: t });
      await queryInterface.dropTable('Budget', { transaction: t });
      await queryInterface.dropTable('TransactionEntry', { transaction: t });
      await queryInterface.dropTable('Journal', { transaction: t });
      await queryInterface.dropTable('AccountData', { transaction: t });
    });
  },
};
