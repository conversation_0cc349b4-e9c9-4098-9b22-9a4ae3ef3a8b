const { UserLeaveSummary } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.createLeaveSummary = async (data, loggedInUser) => {
  try {
    const leaveSummaryPayload = {
      ...data,
      userId: loggedInUser.id,
      remaining: data.maxAllowed - data.used,
    };

    const category = await UserLeaveSummary.create(leaveSummaryPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Leave-Summary'),
      data: category,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateleaveSummary = async (leaveSummaryId, data) => {
  try {
    const leaveSummary = await UserLeaveSummary.findOne({
      where: {
        id: leaveSummaryId,
      },
    });
    if (!leaveSummary) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST(
          'leaveSummary with ID: ' + leaveSummaryId
        ),
      };
    }

    Object.assign(leaveSummary, data);
    if (data.maxAllowed !== undefined || data.used !== undefined) {
      leaveSummary.remaining =
        (data.maxAllowed ?? leaveSummary.maxAllowed) -
        (data.used ?? leaveSummary.used);
    }
    await leaveSummary.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('leaveSummary'),
      data: leaveSummary,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteLeaveSummary = async (leaveSummaryId) => {
  try {
    const leaveSummary = await UserLeaveSummary.findByPk(leaveSummaryId);
    if (!leaveSummary) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('leaveSummary'),
      };
    }

    await leaveSummary.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('leaveSummary'),
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.getLeaveSummaries = async (userId, query) => {
  try {
    const { start = 0, limit = 10 } = query;

    const { count, rows: leaveSummaries } =
      await UserLeaveSummary.findAndCountAll({
        where: { userId },
        limit: limit,
        offset: start,
        order: [['createdAt', 'DESC']],
      });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('leavesummeries'),
      data: {
        rows: leaveSummaries,
        pagination: {
          totalCount: count,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};
