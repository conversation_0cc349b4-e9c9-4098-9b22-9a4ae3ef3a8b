const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();

const AuthControl = require('../../../controllers/api/v1/Auth');
const AuthHandler = require('../../../models/helpers/AuthHelper');
const AuthSchema = require('../../../schema-validation/Auth');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '/verify',
  AuthHandler.verifyInvitationToken,
  checkSchema(AuthSchema.sendOtp),
  ErrorHandleHelper.requestValidator,
  AuthControl.verifyEmailMobile
);

router.post(
  '/resend-otp',
  checkSchema(AuthSchema.sendOtp),
  ErrorHandleHelper.requestValidator,
  AuthControl.resendOtp
);

router.patch(
  '/verify-otp',
  checkSchema(AuthSchema.verifyOtp),
  ErrorHandleHelper.requestValidator,
  AuthControl.verifyOtp
);

module.exports = router;
