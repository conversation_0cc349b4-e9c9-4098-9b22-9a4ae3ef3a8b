'use strict';

const { quotationStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Quotation');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Quotation" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Quotation_status_new') THEN
          CREATE TYPE "enum_Quotation_status_new" AS ENUM (${quotationStatus
            .getValues()
            .map((status) => `'${status}'`)
            .join(', ')});
        END IF;
      END $$;
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Quotation" ALTER COLUMN "status" TYPE "enum_Quotation_status_new"
        USING status::text::"enum_Quotation_status_new";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Quotation_status') THEN
          DROP TYPE "enum_Quotation_status";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Quotation_status_new" RENAME TO "enum_Quotation_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Quotation" ALTER COLUMN "status" SET DEFAULT '${quotationStatus.DRAFT}';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Quotation');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Quotation" ALTER COLUMN "status" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Quotation_status_old') THEN
          CREATE TYPE "enum_Quotation_status_old" AS ENUM ('draft');
        END IF;
      END $$;
    `);

    if (tableDescription.status) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Quotation" ALTER COLUMN "status" TYPE "enum_Quotation_status_old"
        USING status::text::"enum_Quotation_status_old";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$ 
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Quotation_status') THEN
          DROP TYPE "enum_Quotation_status";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Quotation_status_old" RENAME TO "enum_Quotation_status";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Quotation" ALTER COLUMN "status" SET DEFAULT 'draft';
    `);
  },
};
