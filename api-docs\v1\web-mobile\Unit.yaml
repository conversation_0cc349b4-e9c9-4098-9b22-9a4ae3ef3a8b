paths:
  /project/{id}/unit:
    post:
      tags:
        - "Project"
      summary: "Create a new Unit"
      description: "This endpoint allows you to register a new Unit by providing all necessary details."
      operationId: "CreateUnit"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the project to which the unit belongs."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The details of the new Unit to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/unit"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Unit has been created successfully."
        "400":
          description: "Invalid input data or unit already exists."
        "500":
          description: "Internal Server Error"

  /project/unit/{id}:
    put:
      tags:
        - "Project"
      summary: "Update an existing Unit"
      description: "This endpoint allows you to update the details of an existing Unit by providing its `unitId` in the URL path and new information in the request body."
      operationId: "UpdateUnit"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the unit to be updated."
          schema:
            type: integer
            example: 456
      requestBody:
        description: "The updated information for the Unit. The 'unitId' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/unit-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Unit has been updated successfully."
        "400":
          description: "Invalid input data, unit not found, or unit already exists."
        "404":
          description: "Unit not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Project"
      summary: "Delete an existing Unit"
      description: "This endpoint allows you to delete an existing Unit by providing its `unitId` in the URL path."
      operationId: "DeleteUnit"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the unit to be deleted."
          schema:
            type: integer
            example: 456
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Unit has been deleted successfully."
        "400":
          description: "Invalid unit ID provided."
        "404":
          description: "Unit not found."
        "500":
          description: "Internal Server Error"
    
  /project/{id}/unit/{unitId}/duplicate:
    post:
      summary: Duplicate a floor
      description: Creates a duplicate of the specified floor under a given ID.
      tags:
        - Project
      security:
        - bearerAuth: []
      parameters:
        - name: id
          in: path
          description: The ID of the resource to which the floor belongs.
          required: true
          schema:
            type: string
        - name: unitId
          in: path
          description: The ID of the unit to duplicate.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successfully duplicated the floor.
        '400':
          description: The floor does not exist.
        '500':
          description: Internal server error.

  /project/unit/{id}/drawing:
    post:
      summary: Add drawings to a Unit
      description: Adds drawings (files or URLs) to an existing unit in a specific project.
      operationId: addDrawingsToUnit
      tags:
        - Project
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the unit to which drawings are added."
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The list of drawings (files or URLs) to be added to the unit."
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/add-drawings-to-unit"
        required: true
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Drawings added successfully to the unit."
        "400":
          description: "Invalid request, drawings data is malformed."
        "404":
          description: "Unit not found."
        "500":
          description: "Internal Server Error"


components:
  schemas:
    unit:
      type: object
      properties:
        floorId:
          type: integer
          description: "The ID of the floor where the unit is located."
          example: 1
        unitTypeId:
          type: integer
          description: "The ID of the unit type (e.g., 1BHK, 2BHK)."
          example: 1
        name:
          type: string
          description: "The name of the unit."
          example: "1BHK Apartment"
          minLength: 1
          maxLength: 255
        isSaleable:
          type: boolean
          description: "Indicates if the unit is saleable."
          example: true
        isNamingFormat:
          type: boolean
          description: "Indicates if the unit follows a naming format."
          example: true
        superBuiltUpArea:
          type: number
          format: float
          description: "The super built-up area of the unit in square feet or square meters."
          example: 1200.5
        builtUpArea:
          type: number
          format: float
          description: "The built-up area of the unit in square feet or square meters."
          example: 1000.0
        carpetArea:
          type: number
          format: float
          description: "The carpet area of the unit in square feet or square meters."
          example: 800.0
        facing:
          type: string
          enum: 
            - "North"
            - "South"
            - "East"
            - "West"
            - "North-East"
            - "South-East"
            - "South-West"
            - "North-West"
          description: "The direction the unit is facing."
          example: "North-East"
        amenities:
          type: array
          items:
            type: integer
          description: "The IDs of the amenities associated with the unit."
          example: [1, 2]
        additionalFields:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                description: "The key of the additional fields."
                example: "Zoning"
              value:
                type: string
                description: "A value of the additional fields."
                example: "Semi detached."
        description:
          type: string
          description: "A detailed description of the unit."
          example: "A spacious 1BHK apartment with modern amenities."
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
        plotArea:
          type: number
          format: float
          description: "The plot area of the unit."
          example: 1500.5
        roadTangent:
          type: number
          format: float
          description: "The tangent of the road near the unit."
          example: 20.5
        remainingArea:
          type: number
          format: float
          description: "The remaining area of the unit after deducting the built-up area."
          example: 500.0
        proRataFsiFactor:
          type: number
          format: float
          description: "The pro-rata FSI (Floor Space Index) factor of the unit."
          example: 2.5
        zoning:
          type: string
          description: "The zoning classification for the unit."
          example: "Residential"
        basicFsi:
          type: number
          format: float
          description: "The basic Floor Space Index (FSI) for the unit."
          example: 1.5
        permissibleFsi:
          type: string
          description: "The permissible FSI (Floor Space Index) value."
          example: "200,23"
      required:
        - name
        - isSaleable
        - isNamingFormat
      additionalProperties: false

    unit-update:
      type: object
      properties:
        projectId:
          type: integer
          description: "The ID of the project the unit belongs to."
          example: 1
        floorId:
          type: integer
          description: "The ID of the floor where the unit is located."
          example: 1
        unitTypeId:
          type: integer
          description: "The ID of the unit type (e.g., 1BHK, 2BHK)."
          example: 1
        name:
          type: string
          description: "The name of the unit."
          example: "2BHK Apartment"
          minLength: 1
          maxLength: 255
        isSaleable:
          type: boolean
          description: "Indicates if the unit is saleable."
          example: true
        isNamingFormat:
          type: boolean
          description: "Indicates if the unit follows a naming format."
          example: true
        superBuiltUpArea:
          type: number
          format: float
          description: "The super built-up area of the unit in square feet or square meters."
          example: 1300.0
        builtUpArea:
          type: number
          format: float
          description: "The built-up area of the unit in square feet or square meters."
          example: 1100.0
        carpetArea:
          type: number
          format: float
          description: "The carpet area of the unit in square feet or square meters."
          example: 900.0
        facing:
          type: string
          enum: 
            - "North"
            - "South"
            - "East"
            - "West"
            - "North-East"
            - "South-East"
            - "South-West"
            - "North-West"
          description: "The direction the unit is facing."
          example: "South"
        amenities:
          type: array
          items:
            type: integer
          description: "The IDs of the amenities associated with the unit."
          example: [1, 2]
        additionalFields:
          type: array
          items:
            type: object
            properties:
              key:
                type: string
                description: "The key of the additional fields."
                example: "Zoning"
              value:
                type: string
                description: "A value of the additional fields."
                example: "Semi detached."
        description:
          type: string
          description: "A detailed description of the unit."
          example: "A 2BHK apartment with modern amenities, including a gym and swimming pool."
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
        plotArea:
          type: number
          format: float
          description: "The plot area of the unit."
          example: 1500.5
        roadTangent:
          type: number
          format: float
          description: "The tangent of the road near the unit."
          example: 20.5
        remainingArea:
          type: number
          format: float
          description: "The remaining area of the unit after deducting the built-up area."
          example: 500.0
        proRataFsiFactor:
          type: number
          format: float
          description: "The pro-rata FSI (Floor Space Index) factor of the unit."
          example: 2.5
        zoning:
          type: string
          description: "The zoning classification for the unit."
          example: "Residential"
        basicFsi:
          type: number
          format: float
          description: "The basic Floor Space Index (FSI) for the unit."
          example: 1.5
        permissibleFsi:
          type: string
          description: "The permissible FSI (Floor Space Index) value."
          example: "200,23"
      additionalProperties: false

    add-drawings-to-unit:
      type: object
      properties:
        drawings:
          type: array
          items:
            type: object
            properties:
              fileName:
                type: string
                example: "drawing1.png"
              fileType:
                type: string
                example: "image/png"
              fileSize:
                type: integer
                example: 1024
              filePath:
                type: string
                example: "documents/1739466382169-R.png"
