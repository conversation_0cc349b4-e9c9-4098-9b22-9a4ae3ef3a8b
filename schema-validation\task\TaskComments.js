exports.createTaskCommentValidation = {
  comment: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Comment is required and cannot be empty',
    },
    isString: {
      errorMessage: 'Comment must be a valid string',
    },
    trim: true,
  },
  taskId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Task ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Task ID must be a valid integer',
    },
    toInt: true,
  },
};

exports.updateTaskCommentValidation = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Comment ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Comment ID must be an integer',
    },
    toInt: true,
  },
  comment: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Comment is required and cannot be empty',
    },
    isString: {
      errorMessage: 'Comment must be a valid string',
    },
    trim: true,
  },
};

exports.deleteTaskCommentValidation = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'Comment ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'Comment ID must be an integer',
    },
    toInt: true,
  },
};
