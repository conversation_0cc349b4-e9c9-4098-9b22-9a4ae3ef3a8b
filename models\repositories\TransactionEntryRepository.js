const _ = require('lodash');
const sequelize = require('sequelize');
const { Op } = require('sequelize');

const { TransactionEntry } = require('..');
const options = require('@config/options');
const { revertAccountTransactionEntry } = require('./AccountRepository');

exports.getTransactionEntry = async (query) =>
  await TransactionEntry.findOne(query);

exports.getAllTransactionEntry = async (query) =>
  await TransactionEntry.findAll(query);

exports.findAndCountAll = async (query) =>
  await TransactionEntry.findAndCountAll(query);

exports.handleTransactionsEnteries = async (objParams, transaction = null) => {
  try {
    const {
      transactionId,
      referenceId,
      referenceType,
      accountId,
      description,
      creditAmount,
      debitAmount,
      createdBy,
    } = objParams;

    let returnData;
    // add case
    if (_.toInteger(transactionId) === 0) {
      returnData = await TransactionEntry.create({
        referenceId,
        referenceType,
        accountId,
        description,
        debitAmount,
        creditAmount,
        createdBy,
        status: options.recordStatus.ACTIVE,
      });

      const objParamRetainAccountTransactionEntry = {
        accountId,
        creditAmount,
        debitAmount,
        direction: options.transactionEntryDirection.RETAIN,
      };

      await revertAccountTransactionEntry(
        objParamRetainAccountTransactionEntry,
        transaction
      );
    }
    // edit case
    else {
      // get old transation entry to compare
      const objParamsForOldTransactionEntry = {
        where: {
          id: transactionId,
        },
      };

      const oldTransactionEntry = await this.getTransactionEntry(
        objParamsForOldTransactionEntry
      );

      const {
        accountId: oldAccountId,
        debitAmount: oldDebitAmount,
        creditAmount: oldCreditAmount,
      } = oldTransactionEntry;

      // this transactions account has been changed
      if (oldAccountId !== accountId) {
        // revert old account opening balance
        const objParamRevertAccountTransactionEntry = {
          accountId: oldAccountId,
          creditAmount: oldCreditAmount,
          debitAmount: oldDebitAmount,
          direction: options.transactionEntryDirection.REVERT,
        };
        await revertAccountTransactionEntry(
          objParamRevertAccountTransactionEntry,
          transaction
        );
      }
      // this transactions account not been changed
      else {
        // revert old account opening balance
        const objParamRevertAccountTransactionEntry = {
          accountId: oldAccountId,
          creditAmount: oldCreditAmount,
          debitAmount: oldDebitAmount,
          direction: options.transactionEntryDirection.REVERT,
        };
        await revertAccountTransactionEntry(
          objParamRevertAccountTransactionEntry,
          transaction
        );

        // retain old account with new account opening balance
        const objParamRetainAccountTransactionEntry = {
          accountId,
          creditAmount,
          debitAmount,
          direction: options.transactionEntryDirection.RETAIN,
        };
        await revertAccountTransactionEntry(
          objParamRetainAccountTransactionEntry,
          transaction
        );
      }

      returnData = await TransactionEntry.update(
        {
          accountId,
          description,
          debitAmount,
          creditAmount,
        },
        {
          where: { id: transactionId },
        },
        {
          transaction,
        }
      );
    }

    return returnData;
  } catch (error) {
    throw error;
  }
};

exports.deleteJournalTransactionEntry = async (journalId, transaction) => {
  try {
    // get old transation entry to compare
    const objParamsForOldTransactionEntry = {
      where: {
        referenceId: journalId,
        referenceType: options.transactionTypeForEntries.JOURNAL,
        status: options.recordStatus.ACTIVE,
      },
    };

    const oldTransactionEntry = await this.getAllTransactionEntry(
      objParamsForOldTransactionEntry
    );

    if (!_.isEmpty(oldTransactionEntry)) {
      await Promise.allSettled(
        oldTransactionEntry.map(async (objEachTransactionEntry) => {
          const { accountId, creditAmount, debitAmount } =
            objEachTransactionEntry;

          const objParamRevertAccountTransactionEntry = {
            accountId,
            creditAmount,
            debitAmount,
            direction: options.transactionEntryDirection.REVERT,
          };

          return await revertAccountTransactionEntry(
            objParamRevertAccountTransactionEntry,
            transaction
          );
        })
      );
    }

    await TransactionEntry.update(
      {
        status: options.recordStatus.DELETED,
      },
      {
        where: {
          referenceId: journalId,
          referenceType: options.transactionTypeForEntries.JOURNAL,
          status: options.recordStatus.ACTIVE,
        },
      },
      {
        transaction,
      }
    );
    return true;
  } catch (error) {
    throw error;
  }
};

exports.deleteTransactionEntry = async (transactionId, transaction) => {
  try {
    // get old transation entry to compare
    const objParamsForOldTransactionEntry = {
      where: {
        id: transactionId,
      },
    };

    const oldTransactionEntry = await this.getTransactionEntry(
      objParamsForOldTransactionEntry
    );

    const {
      id: transactionId,
      accountId,
      creditAmount,
      debitAmount,
    } = oldTransactionEntry;

    const objParamRevertAccountTransactionEntry = {
      accountId,
      creditAmount,
      debitAmount,
      direction: options.transactionEntryDirection.REVERT,
    };

    await revertAccountTransactionEntry(
      objParamRevertAccountTransactionEntry,
      transaction
    );

    await TransactionEntry.update(
      {
        status: options.recordStatus.DELETED,
      },
      {
        where: {
          id: transactionId,
        },
      },
      {
        transaction,
      }
    );
    return true;
  } catch (error) {
    throw error;
  }
};

exports.getAllTransactionEntries = async (objParams) => {
  try {
    const {
      start = 0,
      limit = 10,
      accountId,
      startDate = null,
      endDate = null,
      transactionType = '',
    } = objParams;

    const query = {
      where: {
        ...(accountId && { accountId }),
        ...(!_.isEmpty(transactionType) && {
          referenceType: transactionType.split(','),
        }),
        status: options.recordStatus.ACTIVE,
      },
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
    };

    if (startDate && endDate) {
      query.where.createdDate = {
        [Op.between]: [startDate, endDate], // Filters transactions between startDate and endDate
      };
    } else if (startDate) {
      query.where.createdDate = {
        [Op.gte]: startDate, // Filters transactions after startDate
      };
    } else if (endDate) {
      query.where.createdDate = {
        [Op.lte]: endDate, // Filters transactions before endDate
      };
    }

    const { rows, count } = await this.findAndCountAll(query, { logger: true });

    return {
      success: true,
      message: options.successMessage.DETAIL_MESSAGE('Account transaction'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw error;
  }
};

exports.getReferenceIdFromTransactionEntryWithAccountAndReferenceType = async (
  objParams
) => {
  try {
    const { accountIds, referenceType } = objParams;

    const referenceIds = await TransactionEntry.findAll({
      attributes: [
        [sequelize.fn('DISTINCT', sequelize.col('referenceId')), 'referenceId'],
      ],
      where: {
        referenceType: referenceType,
        accountId: {
          [Op.in]: accountIds,
        },
      },
      raw: true,
    });

    return referenceIds;
  } catch (error) {
    throw error;
  }
};
