'use strict';
const { unitOfMeasurement, reOrderPointScope } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('Item', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      sku: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      unitOfMeasurement: {
        type: Sequelize.ENUM(...unitOfMeasurement.getValues()),
        allowNull: false,
        defaultValue: unitOfMeasurement.BOX,
      },
      hsn: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      taxRate: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      reOrderPoint: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      reOrderPointScope: {
        type: Sequelize.ENUM(...reOrderPointScope.getValues()),
        allowNull: false,
        defaultValue: reOrderPointScope.ALL_WAREHOUSES,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      createdBy: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'SET NULL',
        onUpdate: 'NO ACTION',
      },
      organizationId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      warehouseId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Warehouse',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      categoryId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ItemCategory',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'NO ACTION',
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('Item');
  },
};
