const { subTaskStatus } = require('@config/options');

exports.createSubTaskValidation = {
  title: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'Title is required and cannot be empty',
    },
    isString: {
      errorMessage: 'Title must be a valid string',
    },
    trim: true,
  },
  status: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [subTaskStatus.getValues()],
      errorMessage: `Status must be one of the following: ${subTaskStatus.getValues().join(', ')}`,
    },
  },
  startDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Start Date must be a valid ISO 8601 date',
    },
    toDate: true,
  },
  dueDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Due Date must be a valid ISO 8601 date',
    },
    toDate: true,
  },
};

exports.updateSubTaskValidation = {
  id: {
    in: ['params'],
    notEmpty: {
      errorMessage: 'SubTask ID is required and cannot be empty',
    },
    isInt: {
      errorMessage: 'SubTask ID must be an integer',
    },
    toInt: true,
  },
  title: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Title must be a valid string',
    },
    trim: true,
  },
  status: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [subTaskStatus.getValues()],
      errorMessage: `Status must be one of the following: ${subTaskStatus.getValues().join(', ')}`,
    },
  },
  startDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Start Date must be a valid ISO 8601 date',
    },
    toDate: true,
  },
  dueDate: {
    in: ['body'],
    optional: true,
    isISO8601: {
      errorMessage: 'Due Date must be a valid ISO 8601 date',
    },
    toDate: true,
  },
};
