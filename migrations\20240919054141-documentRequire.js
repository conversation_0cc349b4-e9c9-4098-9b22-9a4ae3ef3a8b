'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('DocumentRequire', {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: Sequelize.INTEGER,
      },
      designationId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Designation',
          key: 'id',
        },
        allowNull: false,
      },
      name: {
        type: Sequelize.STRING(50),
        allowNull: false,
      },
      inputValue: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fileUpload: {
        type: Sequelize.JSON,
        allowNull: true,
      },
      isRequired: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isVerificationRequired: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      isUploadRequired: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('DocumentRequire');
  },
};
