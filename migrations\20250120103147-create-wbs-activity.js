'use strict';
const { defaultStatus, wbsActivityType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('WbsActivity', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      parentWbsActivityId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'WbsActivity',
          key: 'id',
        },
        allowNull: true,
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      organizationId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
      },
      projectId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Project',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      activityType: {
        type: Sequelize.ENUM(...wbsActivityType.getActivityTypeArray()),
        allowNull: false,
      },
      name: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      wbsCode: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      shortCode: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      color: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      iconUrl: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM(...defaultStatus.getDefaultStatusArray()),
        allowNull: true,
      },
      startDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      endDate: {
        type: Sequelize.DATE,
        allowNull: true,
      },
      metricValue: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      metricType: {
        type: Sequelize.STRING(255),
        allowNull: true,
      },
      rate: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      total: {
        type: Sequelize.DECIMAL(15, 2),
        allowNull: true,
      },
      estimatedActivityBudget: {
        type: Sequelize.DECIMAL(20, 2),
        allowNull: true,
      },
      accountId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Account',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('WbsActivity');
  },
};
