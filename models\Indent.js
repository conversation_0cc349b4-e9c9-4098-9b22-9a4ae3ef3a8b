'use strict';
const OPTIONS = require('@config/options');

module.exports = (sequelize, DataTypes) => {
  const Indent = sequelize.define(
    'Indent',
    {
      id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        allowNull: false,
      },
      requiredByDate: {
        type: DataTypes.DATE,
        allowNull: false,
      },
      priority: {
        type: DataTypes.ENUM(OPTIONS.indentPriority.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.indentPriority.MEDIUM,
      },
      note: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
      status: {
        type: DataTypes.ENUM(OPTIONS.indentStatus.getValues()),
        allowNull: false,
        defaultValue: OPTIONS.indentStatus.DRAFT,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Indent.associate = (models) => {
    Indent.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onDelete: 'SET NULL',
      onUpdate: 'NO ACTION',
    });
    Indent.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Indent.belongsTo(models.Warehouse, {
      foreignKey: 'warehouseId',
      as: 'warehouse',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Indent.belongsTo(models.BoqEntry, {
      foreignKey: 'boqId',
      as: 'boq',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Indent.belongsTo(models.WorkOrder, {
      foreignKey: 'workOrderId',
      as: 'workOrder',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Indent.belongsTo(models.Tasks, {
      foreignKey: 'taskId',
      as: 'tasks',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Indent.hasMany(models.IndentItem, {
      foreignKey: 'indentId',
      as: 'IndentItem',
      onDelete: 'CASCADE',
      onUpdate: 'NO ACTION',
    });
    Indent.hasOne(models.Request, {
      foreignKey: 'recordId',
      constraints: false,
      scope: {
        requestType: 'indent_mr_request',
      },
      as: 'request',
    });
  };

  return Indent;
};
