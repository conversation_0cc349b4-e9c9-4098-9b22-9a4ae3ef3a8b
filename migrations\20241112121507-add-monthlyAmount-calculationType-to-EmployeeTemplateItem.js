'use strict';
const { calculationType } = require('../config/options');
/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addColumn('EmployeeTemplateItem', 'monthlyAmount', {
      type: Sequelize.DECIMAL(10, 2),
      allowNull: true,
    });
    await queryInterface.addColumn('EmployeeTemplateItem', 'calculationType', {
      type: Sequelize.ENUM(calculationType.getCalculationTypeArray()),
      allowNull: true,
    });
    await queryInterface.addColumn('EmployeeTemplateItem', 'createdBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
    await queryInterface.addColumn('EmployeeTemplateItem', 'updatedBy', {
      type: Sequelize.INTEGER,
      allowNull: true,
      references: {
        model: 'User',
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('EmployeeTemplateItem', 'monthlyAmount');
    await queryInterface.removeColumn(
      'EmployeeTemplateItem',
      'calculationType'
    );
    await queryInterface.removeColumn('EmployeeTemplateItem', 'createdBy');
    await queryInterface.removeColumn('EmployeeTemplateItem', 'updatedBy');
  },
};
