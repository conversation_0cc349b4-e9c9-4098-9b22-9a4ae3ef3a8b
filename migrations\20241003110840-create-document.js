'use strict';
const { defaultStatus } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('Document', {
      id: {
        type: Sequelize.INTEGER,
        autoIncrement: true,
        primaryKey: true,
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      description: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      isFolder: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      },
      projectId: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      parentFolderId: {
        type: Sequelize.INTEGER,
        references: {
          model: 'Document',
          key: 'id',
        },
        allowNull: true,
        onDelete: 'SET NULL',
      },
      basePath: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fileType: {
        type: Sequelize.STRING(50),
        allowNull: true,
      },
      fileSize: {
        type: Sequelize.BIGINT,
        allowNull: true,
      },
      filePath: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      fileName: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      status: {
        type: Sequelize.ENUM(defaultStatus.getDefaultStatusArray()),
        allowNull: false,
        defaultValue: 'active',
      },
      createdBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      updatedBy: {
        type: Sequelize.INTEGER,
        references: {
          model: 'User',
          key: 'id',
        },
        allowNull: true,
      },
      createdAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.dropTable('Document');
  },
};
