const { taskTagType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const TaskTags = sequelize.define(
    'TaskTags',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      type: {
        type: DataTypes.ENUM(taskTagType.getValues()),
        allowNull: false,
        defaultValue: taskTagType.TASK_TAG,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TaskTags.associate = (models) => {
    TaskTags.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      onDelete: 'CASCADE',
    });
    TaskTags.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
    });
  };

  return TaskTags;
};
