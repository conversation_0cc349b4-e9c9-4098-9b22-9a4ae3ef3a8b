'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Designation');

    // // Remove foreign key constraint for departmentId if it exists
    // if (tableDescription['departmentId']) {
    //   await queryInterface.removeConstraint(
    //     'Designation',
    //     'Designation_departmentId_fkey'
    //   );
    // }

    await queryInterface.changeColumn('Designation', 'name', {
      type: Sequelize.STRING(255),
      allowNull: false,
    });

    if (tableDescription['modules']) {
      await queryInterface.removeColumn('Designation', 'modules');
    }

    if (!tableDescription['icon']) {
      await queryInterface.addColumn('Designation', 'icon', {
        type: Sequelize.TEXT,
        allowNull: true,
      });
    }

    if (!tableDescription['shortName']) {
      await queryInterface.addColumn('Designation', 'shortName', {
        type: Sequelize.STRING(50),
        allowNull: true,
      });
    }

    if (!tableDescription['reportTo']) {
      await queryInterface.addColumn('Designation', 'reportTo', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Designation',
          key: 'id',
        },
        onDelete: 'CASCADE',
      });
    }

    if (!tableDescription['createdBy']) {
      await queryInterface.addColumn('Designation', 'createdBy', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      });
    }

    if (!tableDescription['organizationId']) {
      await queryInterface.addColumn('Designation', 'organizationId', {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onDelete: 'CASCADE',
      });
    }

    if (tableDescription['departmentId']) {
      await queryInterface.removeColumn('Designation', 'departmentId');
    }

    // // Add 'departmentId' column if it doesn't exist
    // if (!tableDescription['departmentId']) {
    //   await queryInterface.addColumn('Designation', 'departmentId', {
    //     type: Sequelize.INTEGER,
    //     allowNull: true,
    //     references: {
    //       model: 'Department',
    //       key: 'id',
    //     },
    //     onDelete: 'CASCADE',
    //   });
    // }
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Designation');

    if (tableDescription['reportTo']) {
      await queryInterface.removeConstraint(
        'Designation',
        'Designation_reportTo_fkey'
      );
    }
    if (tableDescription['createdBy']) {
      await queryInterface.removeConstraint(
        'Designation',
        'User_createdBy_fkey'
      );
    }
    if (tableDescription['organizationId']) {
      await queryInterface.removeConstraint(
        'Designation',
        'Organization_organizationId_fkey'
      );
    }
    // if (tableDescription['departmentId']) {
    //   await queryInterface.removeConstraint(
    //     'Designation',
    //     'Designation_departmentId_fkey'
    //   );
    // }

    if (tableDescription['icon']) {
      await queryInterface.removeColumn('Designation', 'icon');
    }
    if (tableDescription['shortName']) {
      await queryInterface.removeColumn('Designation', 'shortName');
    }
    if (tableDescription['reportTo']) {
      await queryInterface.removeColumn('Designation', 'reportTo');
    }
    if (tableDescription['createdBy']) {
      await queryInterface.removeColumn('Designation', 'createdBy');
    }
    if (tableDescription['organizationId']) {
      await queryInterface.removeColumn('Designation', 'organizationId');
    }
    // if (tableDescription['departmentId']) {
    //   await queryInterface.removeColumn('Designation', 'departmentId');
    // }

    await queryInterface.changeColumn('Designation', 'name', {
      type: Sequelize.STRING(100),
      allowNull: false,
    });

    if (!tableDescription['modules']) {
      await queryInterface.addColumn('Designation', 'modules', {
        type: Sequelize.JSON,
        allowNull: true,
      });
    }
  },
};
