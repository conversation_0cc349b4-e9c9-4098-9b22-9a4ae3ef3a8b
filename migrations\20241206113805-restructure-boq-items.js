'use strict';

const { boqCalculationType } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQItems', 'calculationType');
    await queryInterface.removeColumn('BOQItems', 'labourCost');
    await queryInterface.removeColumn('BOQItems', 'materialCost');
    await queryInterface.removeColumn('BOQItems', 'totalCost');

    await queryInterface.addColumn('BOQItems', 'categoryRate', {
      type: Sequelize.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeColumn('BOQItems', 'categoryRate');

    await queryInterface.addColumn('BOQItems', 'calculationType', {
      type: Sequelize.ENUM(boqCalculationType.getBoqCalculationTypeArray()),
      allowNull: false,
      defaultValue: boqCalculationType.METRIC,
    });

    await queryInterface.addColumn('BOQItems', 'labourCost', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: true,
    });

    await queryInterface.addColumn('BOQItems', 'materialCost', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: true,
    });

    await queryInterface.addColumn('BOQItems', 'totalCost', {
      type: Sequelize.DECIMAL(12, 2),
      allowNull: true,
    });
  },
};
