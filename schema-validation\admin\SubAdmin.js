exports.createAdmin = {
  tempOtp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'OTP cannot be empty',
    isString: {
      errorMessage: 'OTP must be a string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'OTP must be 6 digits',
    },
  },
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  locationId: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Location ID cannot be empty',
    isString: {
      errorMessage: 'Location ID must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be a string',
    },
    isEmail: {
      errorMessage: 'Invalid email format',
    },
  },
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Confirm password cannot be empty',
    custom: {
      options: (value, { req }) => {
        if (value !== req.body.password) {
          throw new Error('Confirm password must match new password');
        }
        return true;
      },
    },
  },
  designationId: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Designation ID cannot be empty',
    isString: {
      errorMessage: 'Designation ID must be string',
    },
  },
};

exports.updateAdmin = {
  tempOtp: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'OTP cannot be empty',
    isString: {
      errorMessage: 'OTP must be a string',
    },
    isLength: {
      options: { min: 6, max: 6 },
      errorMessage: 'OTP must be 6 digits',
    },
  },
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  locationId: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Location ID cannot be empty',
    isString: {
      errorMessage: 'Location ID must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  designationId: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Designation ID cannot be empty',
    isString: {
      errorMessage: 'Designation ID must be string',
    },
  },
};
exports.updateProfile = {
  firstName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'First name cannot be empty',
    isString: {
      errorMessage: 'First name must be string',
    },
  },
  lastName: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Last name cannot be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
  profilePicture: {
    in: ['body'],
    trim: true,
    notEmpty: false,
    errorMessage: 'Last name can be empty',
    isString: {
      errorMessage: 'Last name must be string',
    },
  },
};
exports.changePassword = {
  password: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Password cannot be empty',
  },
  confirmPassword: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Confirm Password cannot be empty',
    custom: {
      options: (value, { req }) =>
        req.body.confirmPassword === req.body.password,
      errorMessage: 'Confirm Password does not match',
    },
  },
};
exports.sendOtp = {
  countryCode: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
  },
  mobileNumber: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Mobile number cannot be empty',
    isString: {
      errorMessage: 'Mobile number must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    notEmpty: true,
    errorMessage: 'Email cannot be empty',
    isString: {
      errorMessage: 'Email must be a string',
    },
    isEmail: {
      errorMessage: 'Invalid email format',
    },
  },
};
