const { genRes, errorMessage, resCode } = require('@config/options');
const ProjectRepository = require('@repo/ProjectRepository');

exports.createProject = async (req, res) => {
  try {
    const { success, message, data } =
      await ProjectRepository.createProjectAndSubProject(req.body, req.user);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.getProjects = async (req, res) => {
  try {
    const { organizationId } = req.query;

    const { success, message, data } = await ProjectRepository.getAllProjects(
      req.query,
      req.user,
      organizationId
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.createSubProject = async (req, res) => {
  const { id } = req.params;
  try {
    const { success, message, data } =
      await ProjectRepository.createProjectAndSubProject(
        req.body,
        req.user,
        id
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.detectWeather = async (req, res) => {
  const { latitude, longitude } = req.query;
  try {
    const { success, message, data } = await ProjectRepository.detectWeather(
      latitude,
      longitude
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

//TODO: Implement this function for accu weather
// exports.getAccuWeatherData = async (req, res) => {
//   const { latitude, longitude } = req.query;
//   const apiKey = 'z3yCMk2FjKNFHPbWHexzPJYlSgVh0YEz';
//   const baseUrl = 'http://dataservice.accuweather.com';
//   try {
//     const url =
//       'http://dataservice.accuweather.com/locations/v1/cities/geoposition/search';

//     const locationResponse = await axios.get(url, {
//       params: {
//         apikey: apiKey,
//         q: `${latitude},${longitude}`,
//       },
//     });
//     const locationKey = locationResponse.data.Key;

//     const weatherResponse = await axios.get(
//       `${baseUrl}/currentconditions/v1/${locationKey}`,
//       {
//         params: {
//           apikey: apiKey,
//         },
//       }
//     );

//     const currentWeather = weatherResponse.data[0];
//     return res.status(200).json({
//       success: true,
//       message: 'Weather data fetched successfully',
//       data: currentWeather,
//     });
//   } catch (error) {
//     console.error('Error fetching weather data:', error);
//     throw new Error('Unable to fetch weather data.');
//   }
// };

exports.addDrwaingsToProject = async (req, res) => {
  const { id: projectId } = req.params;
  try {
    const { success, message } = await ProjectRepository.addDrwaingsToProject(
      projectId,
      req.body,
      req.user
    );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.putProject = async (req, res) => {
  try {
    const { id } = req.params;
    const { success, message, data } =
      await ProjectRepository.putProjectWithDrawing(id, req.body, req.user);

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
