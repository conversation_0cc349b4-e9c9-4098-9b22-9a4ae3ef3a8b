const UserRepository = require('../../../models/repositories/UserRepository');
const {
  genRes,
  errorMessage,
  errorTypes,
  resCode,
} = require('../../../config/options');
const { getRequestDeviceDetails } = require('@models/helpers/UtilHelper');

exports.verifyEmailMobile = async (req, res) => {
  try {
    const { success, message } = await UserRepository.checkUserAndSendOtp(
      req.body,
      req.user.id
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.resendOtp = async (req, res) => {
  try {
    const { success, message } = await UserRepository.resendOtp(req.body);
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(
            resCode.HTTP_BAD_REQUEST,
            message,
            errorTypes.ACCESS_DENIED_EXCEPTION
          )
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.verifyOtp = async (req, res) => {
  try {
    const isEmail = req.body.type === 'email';
    const headers = req.headers;
    const requestDetails = await getRequestDeviceDetails(headers);
    const { success, message, data } = await UserRepository.checkAndVerifyOtp(
      req.body,
      isEmail,
      requestDetails
    );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(
          genRes(resCode.HTTP_BAD_REQUEST, message, errorTypes.INPUT_VALIDATION)
        );
    }
    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
