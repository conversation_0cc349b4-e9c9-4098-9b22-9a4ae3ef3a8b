'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.addConstraint('Employee', {
      fields: ['userId'],
      type: 'unique',
      name: 'unique_userId_constraint',
    });
    await queryInterface.addConstraint('Address', {
      fields: ['userId'],
      type: 'unique',
      name: 'unique_userId_constraint_address',
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.removeConstraint(
      'Employee',
      'unique_userId_constraint'
    );
    await queryInterface.removeConstraint(
      'Address',
      'unique_userId_constraint_address'
    );
  },
};
