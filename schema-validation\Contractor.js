const { contractorType } = require('../config/options');

exports.createContractor = {
  contractorType: {
    in: ['body'],
    isIn: {
      options: [contractorType.contractorTypeArray()],
      errorMessage: 'Invalid contractorType',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'logo must be string',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'businessName must be string',
    },
  },
  firstName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'firstName must be string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'lastName must be string',
    },
  },
  countryCode: {
    in: ['body'],
    optional: true,
    trim: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'lastName must be string',
    },
  },
  email: {
    in: ['body'],
    trim: true,
    isString: {
      errorMessage: 'Email must be string',
    },
  },
  gstNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Email must be string',
    },
  },
  panNumber: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'panNumber must be string',
    },
  },
  address: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'about must be string',
    },
  },
  pinCode: {
    in: ['body'],
    optional: true,
    isLength: {
      options: { max: 6 },
      errorMessage: 'Pin code should be less than 6 characters',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'about must be string',
    },
  },
};

exports.updateContractor = {
  contractorType: {
    in: ['body'],
    optional: true,
    isIn: {
      options: [contractorType.contractorTypeArray()],
      errorMessage: 'Invalid contractorType',
    },
  },
  logo: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'logo must be string',
    },
  },
  businessName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'businessName must be string',
    },
  },
  firstName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'firstName must be string',
    },
  },
  lastName: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'lastName must be string',
    },
  },
  countryCode: {
    in: ['body'],
    optional: true,
    trim: true,
    errorMessage: 'Country code cannot be empty',
    isString: {
      errorMessage: 'Country code must be string',
    },
    customSanitizer: {
      options: (value, { req, location, path }) => {
        return value.charAt(0) === '+'
          ? value.substring(1, value.length)
          : value;
      },
    },
  },
  mobileNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'mobileNumber must be string',
    },
  },
  email: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'Email must be string',
    },
  },
  gstNumber: {
    in: ['body'],
    optional: true,
    trim: true,
    isString: {
      errorMessage: 'GST number must be string',
    },
  },
  panNumber: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'PAN number must be string',
    },
  },
  address: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Address must be string',
    },
  },
  pinCode: {
    in: ['body'],
    optional: true,
    isLength: {
      options: { max: 6 },
      errorMessage: 'Pin code should be less than 6 characters',
    },
  },
  about: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'About must be string',
    },
  },
};
