const express = require('express');
const router = express.Router();
const { checkSchema } = require('express-validator');

const UnitTypeController = require('../../../controllers/api/v1/UnitType');
const UnitTypeSchema = require('../../../schema-validation/UnitType');
const ErrorHandleHelper = require('../../../models/helpers/ErrorHandleHelper');

router.post(
  '',
  checkSchema(UnitTypeSchema.createUnitType),
  ErrorHandleHelper.requestValidator,
  UnitTypeController.createUnitType
);

router.put(
  '/:id',
  checkSchema(UnitTypeSchema.updateUnitType),
  ErrorHandleHelper.requestValidator,
  UnitTypeController.updateUnitType
);

module.exports = router;
