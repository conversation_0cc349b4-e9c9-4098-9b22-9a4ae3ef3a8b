'use strict';
const { priority } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const TeamAssignment = sequelize.define(
    'TeamAssignment',
    {
      id: {
        allowNull: false,
        autoIncrement: true,
        primaryKey: true,
        type: DataTypes.INTEGER,
      },
      priority: {
        type: DataTypes.ENUM(priority.getPriorityArray()),
        allowNull: true,
      },
      autoAssign: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      notes: {
        type: DataTypes.TEXT,
        allowNull: true,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  TeamAssignment.associate = (models) => {
    TeamAssignment.belongsTo(models.Customer, {
      foreignKey: 'customerId',
      as: 'customer',
      onDelete: 'SET NULL',
    });

    TeamAssignment.belongsTo(models.Requirement, {
      foreignKey: 'requirementId',
      as: 'requirement',
      onDelete: 'SET NULL',
    });

    TeamAssignment.belongsTo(models.Employee, {
      foreignKey: 'memberId',
      as: 'member',
      onDelete: 'SET NULL',
    });

    TeamAssignment.belongsTo(models.Organization, {
      foreignKey: 'organizationId',
      as: 'organization',
      allowNull: true,
      onDelete: 'SET NULL',
    });
  };

  return TeamAssignment;
};
