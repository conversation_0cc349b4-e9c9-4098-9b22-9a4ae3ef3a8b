'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    return queryInterface.bulkInsert('MasterDocument', [
      {
        name: 'PAN Card',
        inputType: 'text',
        description:
          'Permanent Account Number card issued by the Indian Income Tax Department',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Aadhaar Card',
        inputType: 'text',
        description:
          'Unique identification number issued by the Unique Identification Authority of India',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'GST Document',
        inputType: 'text',
        description: 'Goods and Services Tax registration document',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Voter ID',
        inputType: 'text',
        description:
          'Electoral Photo ID Card issued by the Election Commission of India',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Driving License',
        inputType: 'text',
        description:
          'Official document permitting an individual to operate motor vehicles',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Passport',
        inputType: 'text',
        description: 'Official travel document issued by a country government',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Bank Statement',
        inputType: 'file',
        description: 'Document issued by a bank showing account transactions',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Utility Bill',
        inputType: 'file',
        description: 'Bill for utilities like electricity, water, or gas',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Income Certificate',
        inputType: 'file',
        description: 'Official document certifying an individual income',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Address Proof',
        inputType: 'file',
        description: 'Any document that proves the current residential address',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  async down(queryInterface, Sequelize) {
    return queryInterface.bulkDelete('MasterDocument', null, {});
  },
};
