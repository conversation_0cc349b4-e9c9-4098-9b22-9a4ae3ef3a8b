const ModulePermissionRepository = require('@repo/ModulePermissionRepository');
const { genRes, errorMessage, resCode } = require('@config/options');

exports.assignModulePermission = async (req, res) => {
  try {
    const { success, message, data } =
      await ModulePermissionRepository.validateAndAssignModulePermission(
        req.body,
        req.user
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

exports.updateModulePermission = async (req, res) => {
  const { roleId } = req.params;
  try {
    const { success, message, data } =
      await ModulePermissionRepository.updateModulePermissionsByRoleId(
        roleId,
        req.body
      );

    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
        data,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};

// exports.updateModulePermissionByRoleId = async (req, res) => {
//   const { id: roleId } = req.params;
//   try {
//     console.log('updateModulePermissionByRoleId....');
//     const { success, message, data } =
//       await ModulePermissionRepository.updateModulePermissionsByRoleId(
//         roleId,
//         req.body
//       );

//     if (!success) {
//       return res
//         .status(resCode.HTTP_BAD_REQUEST)
//         .json(genRes(resCode.HTTP_BAD_REQUEST, message));
//     }

//     return res.status(resCode.HTTP_OK).json(
//       genRes(resCode.HTTP_OK, {
//         message,
//         data,
//       })
//     );
//   } catch (e) {
//     customErrorLogger(e);
//     return res
//       .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
//       .json(
//         genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
//       );
//   }
// };

exports.removeModulePermission = async (req, res) => {
  const { id: modulePermissionId } = req.params;
  try {
    const { success, message } =
      await ModulePermissionRepository.removeModulePermission(
        modulePermissionId
      );
    if (!success) {
      return res
        .status(resCode.HTTP_BAD_REQUEST)
        .json(genRes(resCode.HTTP_BAD_REQUEST, message));
    }

    return res.status(resCode.HTTP_OK).json(
      genRes(resCode.HTTP_OK, {
        message,
      })
    );
  } catch (e) {
    customErrorLogger(e);
    return res
      .status(resCode.HTTP_INTERNAL_SERVER_ERROR)
      .json(
        genRes(resCode.HTTP_INTERNAL_SERVER_ERROR, errorMessage.SERVER_ERROR)
      );
  }
};
