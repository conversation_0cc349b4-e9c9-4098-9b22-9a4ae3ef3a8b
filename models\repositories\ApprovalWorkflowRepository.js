const { ApprovalWorkflow } = require('..');
const { successMessage, errorMessage } = require('@config/options');

exports.createApprovalWorkflow = async (data, loggedInUser) => {
  try {
    const approvalPayload = {
      ...data,
      organizationId: data.organizationId ?? loggedInUser.currentOrganizationId,
    };

    const approvalWorkflow = await ApprovalWorkflow.create(approvalPayload);

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Approval Workflow'),
      data: approvalWorkflow,
    };
  } catch (error) {
    throw error;
  }
};

exports.updateApprovalWorkflow = async (approvalWorkflowId, data) => {
  try {
    const existingWorkflow = await ApprovalWorkflow.findOne({
      where: { id: approvalWorkflowId },
    });

    if (!existingWorkflow) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('ApprovalWorkflow'),
      };
    }

    Object.assign(existingWorkflow, data);
    await existingWorkflow.save();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Approval Workflow'),
      data: existingWorkflow,
    };
  } catch (error) {
    throw error;
  }
};

exports.addApprovalWorkflows = async (workflowData, transaction) => {
  try {
    await ApprovalWorkflow.bulkCreate(workflowData, {
      ignoreDuplicates: true,
      transaction,
    });
  } catch (error) {
    throw error;
  }
};
