paths:
  /project/{id}/charge-type:
    post:
      tags:
        - Project
      summary: "Create a new ChargeType"
      description: "This endpoint allows you to register a new ChargeType by providing the necessary details."
      operationId: "CreateChargeType"
      parameters:
        - name: id
          in: path
          required: true
          description: The ID of the project to which the floor belongs.
          schema:
            type: integer
            example: 123
      requestBody:
        description: "The details of the new ChargeType to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/charge-type"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "ChargeType has been created successfully."
        "400":
          description: "Invalid input data or ChargeType already exists."
        "500":
          description: "Internal Server Error"

  /project/charge-type/{id}:
    put:
      tags:
        - Project
      summary: "Update an existing ChargeType"
      description: "This endpoint allows you to update the details of an existing ChargeType by providing its id and new details."
      operationId: "UpdateChargeType"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the ChargeType to be updated."
          schema:
            type: integer
            example: 1
      requestBody:
        description: "The updated information for the ChargeType."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/charge-type-update"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "ChargeType has been updated successfully."
        "400":
          description: "Invalid input data, ChargeType not found, or ChargeType already exists."
        "404":
          description: "ChargeType not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - Project
      summary: "Delete a ChargeType"
      description: "This endpoint allows you to delete an existing ChargeType by providing its ID."
      operationId: "DeleteChargeType"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the ChargeType to be deleted."
          schema:
            type: integer
            example: 1
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "ChargeType has been deleted successfully."
        "400":
          description: "Invalid ID or ChargeType not found."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    charge-type:
      type: object
      properties:
        name:
          type: string
          description: "The name of the ChargeType."
          example: "Premium Charge"
        amount:
          type: string
          description: "The amount associated with the ChargeType."
          example: "1000"
        isIncludedInBase:
          type: boolean
          description: "Indicates if the charge is included in the base."
          example: true
        isIncludedInAgreement:
          type: boolean
          description: "Indicates if the charge is included in the agreement."
          example: true
        isBrokerageConsidered:
          type: boolean
          description: "Indicates if brokerage is considered for this charge."
          example: false
        isTdsApplicable:
          type: boolean
          description: "Indicates if TDS is applicable for this charge."
          example: true
        allowDiscount:
          type: boolean
          description: "Indicates if discount is allowed for this charge."
          example: true
        sacNumber:
          type: number
          description: "The SAC (Service Accounting Code) value associated with the charge."
          example: 9985
      required:
        - name
        - amount

    charge-type-update:
      type: object
      properties:
        name:
          type: string
          description: "The new name of the ChargeType."
          example: "Premium Charge Updated"
        projectId:
          type: integer
          description: "The ID of the project associated with the ChargeType."
          example: 123
        amount:
          type: string
          description: "The updated amount associated with the ChargeType."
          example: "1200"
        isIncludedInBase:
          type: boolean
          description: "Indicates if the charge is included in the base."
          example: false
        isIncludedInAgreement:
          type: boolean
          description: "Indicates if the charge is included in the agreement."
          example: false
        isBrokerageConsidered:
          type: boolean
          description: "Indicates if brokerage is considered for this charge."
          example: true
        isTdsApplicable:
          type: boolean
          description: "Indicates if TDS is applicable for this charge."
          example: false
        allowDiscount:
          type: boolean
          description: "Indicates if discount is allowed for this charge."
          example: false
        sacNumber:
          type: number
          description: "The updated SAC (Service Accounting Code) value."
          example: 9986
