exports.createNotes = {
  customerId: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'customerId cannot be empty',
    },
    isInt: {
      errorMessage: 'customerId must be an integer',
    },
  },
  note: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'note cannot be empty',
    },
    isString: {
      errorMessage: 'note must be a string',
    },
  },
};

exports.updateNotes = {
  note: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'note must be a string',
    },
  },
};

exports.deleteNotes = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Notes Id is required',
    },
    isInt: {
      errorMessage: 'Notes Id must be a valid integer',
    },
  },
};
