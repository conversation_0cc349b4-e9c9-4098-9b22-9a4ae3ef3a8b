paths:
  /admin/marketplace/category:
    post:
      tags:
        - "Marketplace"
      summary: "Create a new marketplace category"
      description: "This endpoint allows you to create a new marketplace category in the system"
      operationId: "CreateMarketplaceCategory"
      requestBody:
        description: "Marketplace category creation details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/CreateMarketplaceCategoryRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Marketplace category created successfully"
        "400":
          description: "Invalid input data"
        "500":
          description: "Internal Server Error"

  /admin/marketplace/category/{id}:
    put:
      tags:
        - "Marketplace"
      summary: "Update an existing marketplace category"
      description: "This endpoint allows you to update an existing marketplace category in the system"
      operationId: "UpdateMarketplaceCategory"
      parameters:
        - $ref: "#/components/parameters/MarketplaceCategoryIdParam"
      requestBody:
        description: "Marketplace category update details"
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/UpdateMarketplaceCategoryRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Marketplace category updated successfully"
        "400":
          description: "Invalid input data"
        "404":
          description: "Marketplace category not found"
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Marketplace"
      summary: "Delete an existing marketplace category"
      description: "This endpoint allows you to delete an existing marketplace category in the system"
      operationId: "DeleteMarketplaceCategory"
      parameters:
        - $ref: "#/components/parameters/MarketplaceCategoryIdParam"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Marketplace category deleted successfully"
        "404":
          description: "Marketplace category not found"
        "500":
          description: "Internal Server Error"

components:
  schemas:
    CreateMarketplaceCategoryRequest:
      type: object
      properties:
        name:
          type: string
          example: "Interior"
        imageUrl:
          type: string
          example: "image.png"
        parentId:
          type: integer
          example: 1
      required:
        - name
    UpdateMarketplaceCategoryRequest:
      type: object
      properties:
        name:
          type: string
          example: "Finishing"
        imageUrl:
          type: string
          example: "image.png"
        parentId:
          type: integer
          example: 1
  parameters:
    MarketplaceCategoryIdParam:
      name: "id"
      in: "path"
      description: "ID of the marketplace category to be managed"
      required: true
      schema:
        type: string