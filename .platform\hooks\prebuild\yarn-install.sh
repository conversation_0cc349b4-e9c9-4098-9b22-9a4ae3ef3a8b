#!/bin/bash

# Install Yarn using npm (bypass GPG errors)
if ! command -v yarn &> /dev/null
then
  echo "Yarn not found, installing..."
  npm install --global yarn
  # Install build tools required by node-gyp
  sudo yum groupinstall -y "Development Tools"
  sudo yum install -y gcc-c++ make python3

  # Ensure node-gyp is globally available
  npm install -g node-gyp
else
  echo "Yarn is already installed."
fi

# Use Yarn to install dependencies
yarn install