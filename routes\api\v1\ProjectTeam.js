const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const ProjectTeamController = require('@controllers/v1/ProjectTeam');
const ProjectTeamSchema = require('@schema-validation/ProjectTeam');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/:id/team/:userId/invite',
  checkSchema(ProjectTeamSchema.sendInvitation),
  ErrorHandleHelper.requestValidator,
  ProjectTeamController.sendInvitaion
);

router.patch(
  '/team/invite/:id',
  checkSchema(ProjectTeamSchema.updateInvitationStatus),
  ErrorHandleHelper.requestValidator,
  ProjectTeamController.updateInvitationStatus
);

router.delete(
  '/team/:id',
  checkSchema(ProjectTeamSchema.validateAndRemoveTeamMember),
  ErrorHandleHelper.requestValidator,
  ProjectTeamController.validateAndRemoveTeamMember
);

module.exports = router;
