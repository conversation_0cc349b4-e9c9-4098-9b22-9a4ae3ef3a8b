'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.renameColumn('BOQMetric', 'name', 'metricValue');

    await queryInterface.addColumn('BOQMetric', 'label', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });
  },

  async down(queryInterface, Sequelize) {
    await queryInterface.renameColumn('BOQMetric', 'metricValue', 'name');
    await queryInterface.removeColumn('BOQMetric', 'label');
  },
};
