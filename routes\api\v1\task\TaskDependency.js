const express = require('express');
const { checkSchema } = require('express-validator');

const router = express.Router();
const TaskDependencyController = require('@controllers/v1/task/TaskDependency');
const TaskDependencySchema = require('@schema-validation/task/TaskDependency');
const ErrorHandleHelper = require('@models/helpers/ErrorHandleHelper');

router.post(
  '/:id/dependency',
  checkSchema(TaskDependencySchema.createTaskDependency),
  ErrorHandleHelper.requestValidator,
  TaskDependencyController.createTaskDependency
);

router.patch(
  '/dependency/:id',
  checkSchema(TaskDependencySchema.updateTaskDependency),
  ErrorHandleHelper.requestValidator,
  TaskDependencyController.updateTaskDependency
);

router.delete(
  '/dependency/:id',
  ErrorHandleHelper.requestValidator,
  TaskDependencyController.deleteDependency
);

module.exports = router;
