'use strict';

const { expenseState } = require('../config/options');

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Expense');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Expense" ALTER COLUMN "expenseState" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Expense_expenseState_new') THEN
          CREATE TYPE "enum_Expense_expenseState_new" AS ENUM (${expenseState
            .getValues()
            .map((state) => `'${state}'`)
            .join(', ')});
        END IF;
      END $$;
    `);

    if (tableDescription['expenseState'] || tableDescription['expensestate']) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Expense" ALTER COLUMN "expenseState" TYPE "enum_Expense_expenseState_new"
        USING "expenseState"::text::"enum_Expense_expenseState_new";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Expense_expenseState') THEN
          DROP TYPE "enum_Expense_expenseState";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Expense_expenseState_new" RENAME TO "enum_Expense_expenseState";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Expense" ALTER COLUMN "expenseState" SET DEFAULT '${expenseState.DRAFT}';
    `);
  },

  down: async (queryInterface, Sequelize) => {
    const tableDescription = await queryInterface.describeTable('Expense');

    await queryInterface.sequelize.query(`
      ALTER TABLE "Expense" ALTER COLUMN "expenseState" DROP DEFAULT;
    `);

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Expense_expenseState_old') THEN
          CREATE TYPE "enum_Expense_expenseState_old" AS ENUM ('saved');
        END IF;
      END $$;
    `);

    if (tableDescription.expenseState) {
      await queryInterface.sequelize.query(`
        ALTER TABLE "Expense" ALTER COLUMN "expenseState" TYPE "enum_Expense_expenseState_old"
        USING "expenseState"::text::"enum_Expense_expenseState_old";
      `);
    }

    await queryInterface.sequelize.query(`
      DO $$
      BEGIN
        IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'enum_Expense_expenseState') THEN
          DROP TYPE "enum_Expense_expenseState";
        END IF;
      END $$;
    `);

    await queryInterface.sequelize.query(`
      ALTER TYPE "enum_Expense_expenseState_old" RENAME TO "enum_Expense_expenseState";
    `);

    await queryInterface.sequelize.query(`
      ALTER TABLE "Expense" ALTER COLUMN "expenseState" SET DEFAULT 'saved';
    `);
  },
};
