const { TaskUnitMapping, Tasks, Unit } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.findOne = async (query) => await Tasks.findOne(query);

exports.addTaskUnitMapping = async (taskId, data) => {
  try {
    const query = {
      where: {
        id: taskId,
      },
    };
    const task = await this.findOne(query);
    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('task'),
      };
    }

    const unitExists = await Unit.findOne({
      where: {
        id: data.unitId,
      },
    });
    if (!unitExists) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('unit'),
      };
    }

    const existingMapping = await TaskUnitMapping.findOne({
      where: {
        taskId,
        unitId: data.unitId,
      },
    });

    if (existingMapping) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('unitId for this task'),
      };
    }

    const taskUnitPayload = {
      taskId,
      ...data,
    };

    const taskunitmapping = await TaskUnitMapping.create(taskUnitPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('taskUnitMapping'),
      data: taskunitmapping,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteTaskUnitMapping = async (taskId, unitId) => {
  try {
    const taskunitmapping = await TaskUnitMapping.findOne({
      where: {
        taskId,
        unitId,
      },
    });
    if (!taskunitmapping) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('taskunitmapping'),
      };
    }

    await taskunitmapping.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('taskunitmapping'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
