'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('Unit', 'plotArea', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn('Unit', 'roadTangent', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn('Unit', 'remainingArea', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn('Unit', 'proRataFsiFactor', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn('Unit', 'zoning', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });
    await queryInterface.addColumn('Unit', 'basicFsi', {
      type: Sequelize.FLOAT,
      allowNull: true,
    });
    await queryInterface.addColumn('Unit', 'permissibleFsi', {
      type: Sequelize.STRING(255),
      allowNull: true,
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn('Unit', 'plotArea');
    await queryInterface.removeColumn('Unit', 'roadTangent');
    await queryInterface.removeColumn('Unit', 'remainingArea');
    await queryInterface.removeColumn('Unit', 'proRataFsiFactor');
    await queryInterface.removeColumn('Unit', 'zoning');
    await queryInterface.removeColumn('Unit', 'basicFsi');
    await queryInterface.removeColumn('Unit', 'permissibleFsi');
  },
};
