'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.addColumn('DesignationModulePermission', 'isEnabled', {
      type: Sequelize.BOOLEAN,
      defaultValue: false,
      allowNull: false,
    });
    await queryInterface.addColumn(
      'DesignationModulePermission',
      'isAssigned',
      {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
        allowNull: false,
      }
    );
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.removeColumn(
      'DesignationModulePermission',
      'isEnabled'
    );
    await queryInterface.removeColumn(
      'DesignationModulePermission',
      'isAssigned'
    );
  },
};
