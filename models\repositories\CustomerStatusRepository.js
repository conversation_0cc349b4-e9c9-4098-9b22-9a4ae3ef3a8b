const { PaymentPlan, CustomerStatus } = require('..');
const { successMessage } = require('@config/options');
const ProjectRepository = require('./ProjectRepository');
exports.findOne = async (query) => await PaymentPlan.findOne(query);
exports.findAndCountAll = async (query = {}) => {
  return await CustomerStatus.findAndCountAll(query);
};
exports.findAll = async (query = {}) => {
  return await CustomerStatus.findAll(query);
};

exports.createCustomerStatus = async (projectId, data, loggedInUser) => {
  const { success, message } =
    await ProjectRepository.checkAndGetProject(projectId);

  if (!success) {
    return {
      success: false,
      message,
    };
  }

  const payload = {
    ...data,
    projectId: projectId,
    createdBy: loggedInUser.id,
  };

  const customerStatus = await CustomerStatus.create(payload);

  return {
    success: true,
    message: successMessage.ADD_SUCCESS_MESSAGE('Payment Plan'),
    data: customerStatus,
  };
};
