const { floorType } = require('../config/options');

module.exports = (sequelize, DataTypes) => {
  const Floor = sequelize.define(
    'Floor',
    {
      id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
      },
      name: {
        type: DataTypes.STRING(255),
        allowNull: false,
      },
      buildUpArea: {
        type: DataTypes.FLOAT,
        allowNull: true,
      },
      isNamingFormat: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      namingPrefix: {
        type: DataTypes.STRING(10),
        allowNull: true,
      },
      isAlphabets: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      isNumeric: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      floorType: {
        type: DataTypes.ENUM(floorType.getFloorTypeArray()),
        allowNull: true,
      },
      orderIndex: {
        type: DataTypes.INTEGER,
        allowNull: true,
        defaultValue: 0,
      },
      isBasement: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
    },
    {
      timestamps: true,
      freezeTableName: true,
    }
  );

  Floor.associate = (models) => {
    Floor.belongsTo(models.Project, {
      foreignKey: 'projectId',
      as: 'project',
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    });

    Floor.belongsTo(models.Unit, {
      foreignKey: 'unitId',
      as: 'floorsInUnit',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Floor.belongsTo(models.User, {
      foreignKey: 'createdBy',
      as: 'creator',
      onUpdate: 'CASCADE',
      onDelete: 'SET NULL',
    });

    Floor.hasMany(models.Drawing, {
      foreignKey: 'floorId',
      as: 'drawings',
      onDelete: 'CASCADE',
    });

    Floor.hasMany(models.Unit, {
      foreignKey: 'floorId',
      as: 'unitsInFloor',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });

    Floor.hasMany(models.Space, {
      foreignKey: 'floorId',
      as: 'spaces',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  };

  return Floor;
};
