const { approver } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('ApprovalWorkflow', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      approver: {
        type: Sequelize.ENUM(approver.approverArray()),
        allowNull: false,
        defaultValue: approver.MEMBER,
      },
      approverUserId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      approverMemberId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'ProjectTeam',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      escalationUserId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'User',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      escalationDesignationId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Designation',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      isApprovalRequired: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      isEscalationRequired: {
        type: Sequelize.BOOLEAN,
        allowNull: false,
        defaultValue: false,
      },
      activityName: {
        type: Sequelize.STRING(255),
        allowNull: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('ApprovalWorkflow');
  },
};
