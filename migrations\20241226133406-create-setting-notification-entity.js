const { notificationCategory } = require('../config/options');

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.createTable('NotificationSettings', {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      organizationId: {
        type: Sequelize.INTEGER,
        allowNull: true,
        references: {
          model: 'Organization',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'User',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'SET NULL',
      },
      category: {
        type: Sequelize.ENUM(notificationCategory.notificationCategoryArray()),
        allowNull: false,
        defaultValue: notificationCategory.GENERAL,
      },
      taskUpdate: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      systemAnnouncement: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      securityUpdates: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      projectProgress: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      alertsCriticalEvents: {
        type: Sequelize.BOOLEAN,
        defaultValue: false,
      },
      createdAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updatedAt: {
        allowNull: false,
        type: Sequelize.DATE,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
        onUpdate: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
    });
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.dropTable('NotificationSettings');
  },
};
