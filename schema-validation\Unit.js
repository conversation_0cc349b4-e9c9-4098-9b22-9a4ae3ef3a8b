const { facing } = require('../config/options');

exports.createUnit = {
  floorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Floor ID must be a valid integer',
    },
  },
  unitTypeId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Unit Type ID must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    notEmpty: {
      errorMessage: 'Name cannot be empty',
    },
    isString: {
      errorMessage: 'Name must be a string',
    },
    isLength: {
      options: { min: 1, max: 255 },
      errorMessage: 'Name must be between 1 and 255 characters',
    },
  },
  isSaleable: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'isSaleable is required',
    },
    isBoolean: {
      errorMessage: 'isSaleable must be a boolean value',
    },
  },
  isNamingFormat: {
    in: ['body'],
    notEmpty: {
      errorMessage: 'isNamingFormat is required',
    },
    isBoolean: {
      errorMessage: 'isNamingFormat must be a boolean value',
    },
  },
  superBuiltUpArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'SuperBuiltUpArea must be a valid number',
    },
    toFloat: true,
  },
  builtUpArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'BuiltUpArea must be a valid number',
    },
    toFloat: true,
  },
  carpetArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'CarpetArea must be a valid number',
    },
    toFloat: true,
  },
  facing: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = facing.getFacingArray();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid facing value: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid facing value provided',
    },
  },
  amenities: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Amenities must be an array of IDs',
    },
    custom: {
      options: (value) => {
        if (Array.isArray(value)) {
          return value.every((item) => Number.isInteger(item));
        }
        return true;
      },
      errorMessage: 'Each item in Amenities must be a valid integer ID',
    },
  },
  additionalFields: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'additionalFields must be an array of objects',
    },
    custom: {
      options: (value) => {
        if (value) {
          return value.every((item) => item.key && item.value);
        }
        return true;
      },
      errorMessage:
        'Each item in additionalFields must contain a key and value',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
    isLength: {
      options: { min: 0, max: 1000 },
      errorMessage: 'Description must be between 0 and 1000 characters',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
  plotArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'plotArea must be a valid number',
    },
    toFloat: true,
  },
  roadTangent: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'roadTangent must be a valid number',
    },
    toFloat: true,
  },
  remainingArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'remainingArea must be a valid number',
    },
    toFloat: true,
  },
  proRataFsiFactor: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'proRataFsiFactor must be a valid number',
    },
    toFloat: true,
  },
  zoning: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'zoning must be a string',
    },
  },
  basicFsi: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'basicFsi must be a valid number',
    },
    toFloat: true,
  },
  permissibleFsi: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'permissibleFsi must be a string',
    },
  },
};

exports.updateUnit = {
  projectId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Project ID must be a valid integer',
    },
  },
  floorId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Floor ID must be a valid integer',
    },
  },
  unitTypeId: {
    in: ['body'],
    optional: true,
    isInt: {
      errorMessage: 'Unit Type ID must be a valid integer',
    },
  },
  name: {
    in: ['body'],
    trim: true,
    optional: true,
    isLength: {
      options: { min: 1, max: 255 },
      errorMessage: 'Name must be between 1 and 255 characters',
    },
  },
  isSaleable: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isSaleable must be a boolean value',
    },
  },
  isNamingFormat: {
    in: ['body'],
    optional: true,
    isBoolean: {
      errorMessage: 'isNamingFormat must be a boolean value',
    },
  },
  superBuiltUpArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'SuperBuiltUpArea must be a valid number',
    },
    toFloat: true,
  },
  builtUpArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'BuiltUpArea must be a valid number',
    },
    toFloat: true,
  },
  carpetArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'CarpetArea must be a valid number',
    },
    toFloat: true,
  },
  facing: {
    in: ['body'],
    optional: true,
    custom: {
      options: (value) => {
        const allowedValues = facing.getFacingArray();
        if (!allowedValues.includes(value)) {
          console.log(
            `Invalid facing value: ${value}. Allowed values are: ${allowedValues.join(', ')}`
          );
          return false;
        }
        return true;
      },
      errorMessage: 'Invalid facing value provided',
    },
  },
  amenities: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'Amenities must be an array of IDs',
    },
    custom: {
      options: (value) => {
        if (Array.isArray(value)) {
          return value.every((item) => Number.isInteger(item));
        }
        return true;
      },
      errorMessage: 'Each item in Amenities must be a valid integer ID',
    },
  },
  additionalFields: {
    in: ['body'],
    optional: true,
    isArray: {
      errorMessage: 'additionalFields must be an array of objects',
    },
    custom: {
      options: (value) => {
        if (value) {
          return value.every((item) => item.key && item.value);
        }
        return true;
      },
      errorMessage:
        'Each item in additionalFields must contain a key and value',
    },
  },
  description: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'Description must be a string',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
  plotArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'plotArea must be a valid number',
    },
    toFloat: true,
  },
  roadTangent: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'roadTangent must be a valid number',
    },
    toFloat: true,
  },
  remainingArea: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'remainingArea must be a valid number',
    },
    toFloat: true,
  },
  proRataFsiFactor: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'proRataFsiFactor must be a valid number',
    },
    toFloat: true,
  },
  zoning: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'zoning must be a string',
    },
  },
  basicFsi: {
    in: ['body'],
    optional: true,
    isFloat: {
      errorMessage: 'basicFsi must be a valid number',
    },
    toFloat: true,
  },
  permissibleFsi: {
    in: ['body'],
    optional: true,
    isString: {
      errorMessage: 'permissibleFsi must be a string',
    },
  },
};

exports.validateRemoveUnit = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Unit Id is required',
    },
    isInt: {
      errorMessage: 'Unit Id must be a valid integer',
    },
  },
};

exports.validateAddDrawingsToUnit = {
  id: {
    in: ['path'],
    notEmpty: {
      errorMessage: 'Unit Id is required',
    },
    isInt: {
      errorMessage: 'Unit Id must be a valid integer',
    },
  },
  'drawings.*.fileType': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileType must be string',
    },
  },
  'drawings.*.fileSize': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isInt: {
      errorMessage: 'fileSize must be number',
    },
  },
  'drawings.*.filePath': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'filePath must be string',
    },
  },
  'drawings.*.fileName': {
    in: ['body'],
    optional: { options: { nullable: true } },
    isString: {
      errorMessage: 'fileName must be string',
    },
  },
};
