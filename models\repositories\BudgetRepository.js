const sequelize = require('sequelize');
const { Op } = sequelize;
const _ = require('lodash');
const { Budget, BudgetEntry } = require('..');
const {
  successMessage,
  recordStatus,
  budgetEntryType,
} = require('../../config/options');

const {
  bulkCreateBudgetEntry,
  deleteBulkBudgetEntry,
  deleteBudgetEntryData,
  createBudgetEntry,
  updateBudgetEntry,
} = require('./BudgetEntryRepository');

const { sequelize: sequelizeTransaction } = require('../../models/index');

exports.getBudget = async (query) => await Budget.findOne(query);

exports.findAndCountAll = async (query) => await Budget.findAndCountAll(query);

exports.CreateBudget = async (data) => {
  try {
    const objParamsForBudget = {
      organizationId: data.organizationId,
      name: data.name,
      year: data.year,
      period: data.period,
      createdBy: data.createdBy,
    };
    const createdBudget = await Budget.create(objParamsForBudget);

    if (!_.isEmpty(data.income)) {
      await bulkCreateBudgetEntry({
        arrBudgetEntry: data.income,
        budgetId: createdBudget.id,
        type: budgetEntryType.INCOME,
      });
    }

    if (!_.isEmpty(data.expense)) {
      await bulkCreateBudgetEntry({
        arrBudgetEntry: data.expense,
        budgetId: createdBudget.id,
        type: budgetEntryType.EXPENSE,
      });
    }

    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('Budget'),
      data: createdBudget,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.updateBudget = async (data) => {
  const transaction = await sequelizeTransaction.transaction();
  try {
    const { deletedBudgetEntry, budgetId, createdBy } = data;
    const objParamsForBudget = {
      name: data.name,
      year: data.year,
      period: data.period,
    };
    const createdBudget = await Budget.update(
      { ...objParamsForBudget },
      {
        where: { id: budgetId },
      },
      { transaction }
    );

    if (!_.isEmpty(deletedBudgetEntry)) {
      const arrBudgetEntryIdsToDelete = deletedBudgetEntry.split(',');
      arrBudgetEntryIdsToDelete.map(async (eachBudgetEntryIdToDelete) => {
        await deleteBudgetEntryData(eachBudgetEntryIdToDelete, transaction);
      });
    }

    if (!_.isEmpty(data.income)) {
      data.income.map(async (objEachBudgetEntry) => {
        const { budgetId } = objEachBudgetEntry;
        // new account added
        if (!_.toInteger(budgetId) === 0) {
          await createBudgetEntry(
            {
              ...objEachBudgetEntry,
              budgetId,
              type: budgetEntryType.INCOME,
              createdBy,
            },
            transaction
          );
        } else {
          await updateBudgetEntry(
            {
              ...objEachBudgetEntry,
              budgetId,
              type: budgetEntryType.INCOME,
              createdBy,
            },
            transaction
          );
        }
      });
    }

    if (!_.isEmpty(data.expense)) {
      data.expense.map(async (objEachBudgetEntry) => {
        const { budgetId } = objEachBudgetEntry;
        // new account added
        if (!_.toInteger(budgetId) === 0) {
          await createBudgetEntry(
            {
              ...objEachBudgetEntry,
              budgetId,
              type: budgetEntryType.EXPENSE,
              createdBy,
            },
            transaction
          );
        } else {
          await updateBudgetEntry(
            {
              ...objEachBudgetEntry,
              budgetId,
              type: budgetEntryType.EXPENSE,
              createdBy,
            },
            transaction
          );
        }
      });
    }

    await transaction.commit();

    return {
      success: true,
      message: successMessage.UPDATE_SUCCESS_MESSAGE('Budget'),
      data: createdBudget,
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};

exports.getBudgetList = async (objParams) => {
  try {
    const {
      organizationId = null,
      search = '',
      start = 0,
      limit = 10,
      budgetId = null,
    } = objParams;

    const query = {
      where: {
        ...(organizationId && { organizationId }),
        ...(budgetId && { id: budgetId }),
        status: recordStatus.ACTIVE,
      },
      include: [
        {
          model: BudgetEntry,
          as: 'budgetEntries',
          required: false,
          where: {
            status: recordStatus.ACTIVE,
          },
        },
      ],
      order: [['createdAt', 'DESC']],
      limit: limit,
      offset: start,
    };

    if (search) {
      query.where = {
        ...query.where,
        [Op.or]: [
          { name: { [Op.iLike]: `%${search}%` } },
          { year: { [Op.iLike]: `%${year}%` } },
          { period: { [Op.iLike]: `%${search}%` } },
        ],
      };
    }

    const { rows, count } = await this.findAndCountAll(query, { logger: true });

    return {
      success: true,
      message: successMessage.DETAIL_MESSAGE('Budget'),
      data: {
        rows: rows,
        pagination: {
          totalCount: count,
          start: start,
          limit: limit,
        },
      },
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteBudget = async (budgetId) => {
  const transaction = await sequelizeTransaction.transaction();
  try {
    await Budget.update(
      {
        status: recordStatus.DELETED,
      },
      {
        where: {
          id: budgetId,
        },
      },
      {
        transaction,
      }
    );

    await deleteBulkBudgetEntry(
      {
        budgetId,
        budgetEntryType: budgetEntryType.INCOME,
      },
      transaction
    );
    await deleteBulkBudgetEntry(
      {
        budgetId,
        budgetEntryType: budgetEntryType.EXPENSE,
      },
      transaction
    );

    await transaction.commit();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('Budget'),
      data: {},
    };
  } catch (error) {
    await transaction.rollback();
    throw new Error(error);
  }
};
