const { TaskTagMapping, Tasks, TaskTags } = require('..');
const { successMessage, errorMessage } = require('../../config/options');

exports.findOne = async (query) => await Tasks.findOne(query);

exports.addTaskTag = async (taskId, data) => {
  try {
    const query = {
      where: {
        id: taskId,
      },
    };
    const task = await this.findOne(query);
    if (!task) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('task'),
      };
    }

    const tagExists = await TaskTags.findOne({
      where: {
        id: data.tagId,
      },
    });
    if (!tagExists) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('tag'),
      };
    }

    const existingMapping = await TaskTagMapping.findOne({
      where: {
        taskId,
        tagId: data.tagId,
      },
    });

    if (existingMapping) {
      return {
        success: false,
        message: errorMessage.ALREADY_EXIST('tagId for this task'),
      };
    }

    const taskTagPayload = {
      taskId,
      ...data,
    };

    const tasktagmapping = await TaskTagMapping.create(taskTagPayload);
    return {
      success: true,
      message: successMessage.ADD_SUCCESS_MESSAGE('taskTagMapping'),
      data: tasktagmapping,
    };
  } catch (error) {
    throw new Error(error);
  }
};

exports.deleteTaskTag = async (taskId, tagId) => {
  try {
    const tasktagmapping = await TaskTagMapping.findOne({
      where: {
        taskId,
        tagId,
      },
    });
    if (!tasktagmapping) {
      return {
        success: false,
        message: errorMessage.DOES_NOT_EXIST('tasktagmapping'),
      };
    }

    await tasktagmapping.destroy();

    return {
      success: true,
      message: successMessage.DELETE_SUCCESS_MESSAGE('tasktagmapping'),
    };
  } catch (error) {
    throw new Error(error);
  }
};
