paths:
  /settings/module:
    post:
      tags:
        - "Settings"
      summary: "Create a new Module"
      description: "This endpoint allows you to create a new module by providing all necessary details."
      operationId: "CreateModule"
      requestBody:
        description: "The details of the new module to be created."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/ModuleCreateRequest"
        required: true
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Module has been created successfully."
        "400":
          description: "Invalid input data or module already exists."
        "500":
          description: "Internal Server Error"

    get:
      deprecated: true
      tags:
        - "Settings"
      summary: "Get a list of Modules"
      description: "This endpoint allows you to retrieve a list of modules, with pagination support using offset and limit."
      operationId: "GetModules"
      parameters:
        - $ref: "#/components/parameters/organizationId"
        - $ref: "#/components/parameters/offset"
        - $ref: "#/components/parameters/limit"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "A list of modules has been retrieved successfully."
        "400":
          description: "Invalid input data."
        "500":
          description: "Internal Server Error"

  /settings/module/{id}:
    put:
      tags:
        - "Settings"
      summary: "Update an existing Module"
      description: "This endpoint allows you to update the details of an existing module by providing its `id` in the URL path and new information in the request body."
      operationId: "UpdateModule"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the module to be updated."
          schema:
            type: integer
            example: 12345
      requestBody:
        description: "The updated information for the module. The 'id' is part of the URL and is not required in the body."
        content:
          "application/json":
            schema:
              $ref: "#/components/schemas/ModuleUpdateRequest"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Module has been updated successfully."
        "400":
          description: "Invalid input data, module not found, or module already exists."
        "404":
          description: "Module not found."
        "500":
          description: "Internal Server Error"
    delete:
      tags:
        - "Settings"
      summary: "Delete an existing Module"
      description: "This endpoint allows you to delete an existing module by providing its `id` in the URL path."
      operationId: "DeleteModule"
      parameters:
        - name: id
          in: path
          required: true
          description: "The ID of the module to be deleted."
          schema:
            type: integer
            example: 12345
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "200":
          description: "Module has been deleted successfully."
        "400":
          description: "Invalid input data or module not found."
        "404":
          description: "Module not found."
        "500":
          description: "Internal Server Error"

  /settings/module/seed:
    post:
      tags:
        - "Settings"
      summary: "Seed the Module data"
      description: "This endpoint allows you to seed the module data by providing all necessary details."
      operationId: "SeedModule"
      produces:
        - "application/json"
      security:
        - bearerAuth: []
      responses:
        "201":
          description: "Module data has been seeded successfully."
        "400":
          description: "Invalid input data or module data already exists."
        "500":
          description: "Internal Server Error"

components:
  schemas:
    ModuleCreateRequest:
      type: object
      properties:
        name:
          type: string
          example: "New Module"
        parentId:
          type: integer
          example: 1
      required:
        - name

    ModuleUpdateRequest:
      type: object
      properties:
        name:
          type: string
          example: "Updated Module"
        parentId:
          type: integer
          example: 1

  parameters:
    organizationId:
      name: organizationId
      in: query
      required: true
      description: "The ID of the organization to fetch modules for."
      schema:
        type: integer
        example: 1

    offset:
      name: offset
      in: query
      required: false
      description: "The offset for pagination (skip this many records)."
      schema:
        type: integer
        example: 0

    limit:
      name: limit
      in: query
      required: false
      description: "The limit on the number of records to retrieve."
      schema:
        type: integer
        example: 10
